<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="
           http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context-3.2.xsd">

    <!--
        Application Context Configuration
        This file imports all the Spring configuration files that are referenced
        in the web.xml contextConfigLocation parameter.

        This serves as a fallback in case Spring looks for the default applicationContext.xml
        instead of using the contextConfigLocation parameter properly.
    -->

    <!-- Import JNDI References Configuration -->
    <import resource="classpath:jndi-references.xml"/>

    <!-- Import MongoDB Configuration -->
    <import resource="classpath:spring-mongo.xml"/>

    <!-- Import Services Configuration -->
    <import resource="classpath:spring-services.xml"/>

    <!-- Import JDBC Configuration -->
    <import resource="classpath:spring-jdbc.xml"/>

    <!-- Import JPA Configuration -->
    <import resource="classpath:spring-jpa.xml"/>

    <!-- Enable annotation-driven configuration -->
    <context:annotation-config/>

    <!-- Component scanning for Spring beans (excluding web controllers) -->
    <context:component-scan base-package="com.smilebrands.callcentersupport.service"/>
    <context:component-scan base-package="com.smilebrands.callcentersupport.repo"/>
    <context:component-scan base-package="com.smilebrands.callcentersupport.util"/>
    <context:component-scan base-package="com.smilebrands.callcentersupport.scheduler"/>
    <context:component-scan base-package="com.smilebrands.callcentersupport.async"/>

    <!-- Exclude controllers as they are handled by the DispatcherServlet context -->
    <!-- This prevents duplicate bean registration conflicts -->

</beans>
