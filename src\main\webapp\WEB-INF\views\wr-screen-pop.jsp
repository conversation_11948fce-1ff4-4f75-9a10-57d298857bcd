<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="../customTag.tld" prefix="custom" %>
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="${pageContext.request.contextPath}/favicon.ico">

    <title>${languageText} Call for ${brandName}</title>

    <!-- Bootstrap core CSS -->
        <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/bootstrap.min.css" >
        <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/style/redmond/jquery-ui-1.8.20.custom.css" />

        <!-- Custom styles for this template -->
        <link href="${pageContext.request.contextPath}/bootstrap/css/call-template.css" rel="stylesheet">

  </head>

  <body>

        <input type="text" style="display:none;" id="targetFacilityId" value="${facility.facilityId}"/>
        <input type="text" style="display:none;" id="uuid" value="${uniqueCallId}"/>
        <input type="text" style="display:none;" id="employeeNumber" value="${employeeNumber}"/>
        <input type="text" style="display:none;" id="language" value="${language}"/>
        <input type="text" style="display:none;" id="numberPress" value="${numberPress}"/>
        <input type="text" style="display:none;" id="ani" value="${ani}"/>
        <input type="text" style="display:none;" id="screenType" value="wr"/>
        <input type="text" style="display:none;" id="isOld" value="${isOld}"/>

        <div class="modal fade" id="facilitySearchByZipCode" tabindex="-1" role="dialog" aria-labelledby="facilitySearchModalLabel" aria-hidden="true" style="top:100px;">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                        <h4 class="modal-title" id="facilitySearchModalLabel">Search Office by Zip Code</h4>
                    </div>
                    <div class="modal-body" style="padding-bottom:0px;">
                        <div class="input-group">
                            <span class="input-group-addon">Enter zip code:</span>
                            <input id="fsbzInput" type="text" class="form-control" placeholder="5-digit zip code">
                        </div>
                        <div id="display_list" class="list-group" style="padding-top:20px;" display-position="bottom">

                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                        <button id="setOfficeBtn" type="button" class="btn btn-primary">Set Office</button>
                    </div>
                </div>
            </div>
        </div>

        <jsp:include page="components/smsPopWindow.jsp"/>

        <div class="modal fade" id="callDetail" tabindex="-1" role="dialog" aria-labelledby="callDetailModalLabel" aria-hidden="true" style="top:100px;">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                        <h4 class="modal-title" id="callDetailModalLabel">Set Call Detail</h4>
                    </div>
                    <div class="modal-body" style="padding-bottom:0px;">
                        <form class="form-horizontal call-info-form" role="form">
                            <div class="form-group">
                                <label for="aniInput" class="col-sm-3 control-label">Phone Number
                                    <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="Phone Number" data-placement="right">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <input id="aniInput" type="text" class="form-control input-sm required" placeholder="Phone Number" data-type="number" length=10 />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="reasonInput" class="col-sm-3 control-label">Reason for Call</label>
                                <div class="col-sm-9">
                                    <select id="reasonInput" class="form-control input-sm">
                                        <c:forEach var="reason" items="${reasonList}">
                                            <option value="${reason.code}"> ${reason.description}</option>
                                        </c:forEach>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="resolutionInput" class="col-sm-3 control-label">Call Resolution</label>
                                <div class="col-sm-9">
                                    <select id="resolutionInput" class="form-control input-sm">
                                        <c:forEach var="resolutionCode" items="${resolutionCodeList}">
                                            <option value="${resolutionCode.code}"> ${resolutionCode.description}</option>
                                        </c:forEach>
                                    </select>
                                </div>
                            </div>
                        </form>
                        <hr/>

                        <div class="panel panel-default">
                            <div class="panel-heading" style="font-weight:bold; font-size:15px;">
                                <span>Patient Information<span id="addPatientInfoBtn" style="cursor:pointer;" class="glyphicon glyphicon-plus fr" data-toggle="tooltip" data-placement="left" title="Add Patient"></span></span>
                            </div>
                            <div id="patientInfoBody" class="panel-body" style="max-height:400px; overflow:auto;">

                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                        <button id="saveCallDetailBtn" type="button" class="btn btn-primary">Save</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="popover fade bottom provider-detail" role="tooltip" style="display: block;">
            <div class="arrow"></div>
            <div class="popover-content">
                <div class="row">
                    <div class="col-md-2 gray bold" style="text-align: right;">Provider</div>
                    <div class="col-md-10 action fsp-id"></div>
                </div>
                <div class="row" style="padding-left: 20px;">
                    <b class="col-md-12 action fsp-schedule-note"></b>
                </div>
                <div class="row">
                    <div class="col-md-2 gray bold" style="text-align: right;">Gender</div>
                    <div class="col-md-10 action fsp-gender"></div>
                </div>
                <div class="row">
                    <div class="col-md-2 gray bold" style="text-align: right;">Language</div>
                    <div class="col-md-10 action fsp-language"></div>
                </div>
                <div class="row">
                    <div class="col-md-2 gray bold" style="text-align: right;">Education</div>
                    <div class="col-md-10 action fsp-education"></div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="providerInsuranceView" tabindex="-1" role="dialog" aria-labelledby="providerInsuranceLabel" aria-hidden="true">
            <div class="modal-dialog" style="width: 80%;">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                        <h4 class="modal-title" id="providerInsuranceLabel">Provider Insurances for ${facility.name}</h4>
                        <div class="row filter-cmp" style="display: none; padding-top: 10px;">
                            <div class="col-md-1 col-sm-1" style="width: 80px; padding-right: 0px;">Search By:</div>
                            <div class="col-md-3 col-sm-3">
                                <select class="filter-criteria" style="width:100%;">
                                    <option value="payer">Payer</option>
                                    <option value="provider">Provider</option>
                                </select>
                            </div>
                            <div class="col-md-5 col-sm-5">
                                <input type="text" class="filter-value" style="width:90%;"/>
                                <span class="glyphicon glyphicon-remove pointer remove-filter-insurance"></span>
                            </div>
                        </div>
                    </div>
                    <div class="modal-body" style="overflow-y: scroll; max-height: 500px;">
                        <div class="progress loading-insurances">
                            <div class="progress-bar progress-bar-striped active"  role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%">
                                <span class="progress-message">Please wait while loading accepted insurances</span>
                            </div>
                        </div>
                        <div class="insurance-list">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <select id="facilityIDs">
                <c:forEach var="facilityId" items="${facilityList}">
                    <option value="${facilityId}"> ${facilityId}</option>
                </c:forEach>
            </select>
            <div class="panel panel-${langButton}">
                <div class="panel-heading">
                  <h3 class="panel-title">This is a ${languageText} Call
                    <a id="sms-btn" href="javascript:void(0);" data-toggle="tooltip" data-original-title="Send SMS" class="fr" data-placement="right">
                        <span class="glyphicon glyphicon-comment"></span>
                    </a>
                  </h3>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-7">
                            <h2 class="blue top">
                                ${brandName}
                                <small>${extraBrandNameInfo}</small>
                                <c:if test="${isCampaign}">
                                    <small>
                                        (${campaignName})
                                    </small>
                                </c:if>
                            </h2>
                            <div class="alert alert-success" role="alert">
                                <c:if test="${webRequest.zipCode != null}">
                                    <div class="row">
                                        <div class="col-md-3"><strong>Zip Code Entered </strong></div>
                                        <div class="col-md-8">${webRequest.zipCode}.</div>
                                    </div>
                                </c:if>
                                <jsp:include page="patientInfo.jsp"/>
                            </div>
                        </div>

                        <c:choose>
                            <c:when test="${hasSchedulerUrl == true}">
                                <input type="text" style="display:none;" id="schedulerUrl" value="${schedulerUrl}"/>
                                <div class="col-md-5 ctl-btn">
                                    <div class="btn-group">
                                        <button id="callDetailsBtn" type="button" class="btn btn-${langButton} btn">Record Call Details</button>
                                        <button id="zipCodeSearchBtn" type="button" class="btn btn-${langButton} btn">Zip Code Search</button>
                                    </div>
                                    <button id="libSchedulerBtn" type="button" class="btn btn-success btn">${schedulerName} Scheduler</button>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <div class="col-md-offset-1 ctl-btn">
                                    <div class="btn-group">
                                        <button id="callDetailsBtn" type="button" class="btn btn-${langButton} btn">Record Call Details</button>
                                        <button id="zipCodeSearchBtn" type="button" class="btn btn-${langButton} btn">Zip Code Search</button>
                                    </div>
                                </div>
                            </c:otherwise>
                        </c:choose>

                    </div>

                    <c:forEach var="alert" items="${facility.alerts}">
                        <c:choose>
                            <c:when test="${alert.attributeId == 26458}">
                                <div class="alert alert-danger" role="alert">
                            </c:when>
                            <c:otherwise>
                                <div class="alert alert-warning" role="alert">
                            </c:otherwise>
                        </c:choose>
                            <span attributeId="${alert.attributeId}">${alert.value}</span><span class="fr"> (created on <custom:fmtDateTime date="${alert.createdOn}"/> by ${alert.createdByName})</span>
                        </div>
                    </c:forEach>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="panel panel-default ">
                                <div class="panel-heading">
                                    <h3 class="panel-title gray">Web Request Details
                                        <span class="fr f14" id="wrRequestOn">
                                            Requested On: <custom:fmtDateTime date="${webRequest.createDateTime}" dateFormat="EEEE MM-dd-yyyy hh:mm:ss a"/>
                                        </span>
                                        <span class="fr h3" id="wrEmailStatus">
                                            <c:if test="${webRequest.emailDateTime != null}">
                                                Email sent on ${webRequest.emailDateTime}
                                            </c:if>
                                        </span>
                                  </h3>
                                </div>
                                <div class="panel-body alert-info">
                                    <div class="col-md-6">
                                        <!--div class="row">
                                            <div class="col-md-4 gray bold">Request On</div>
                                            <div class="col-md-8"><custom:fmtDateTime date="${webRequest.createDateTime}" dateFormat="EEEE MM-dd-yyyy hh:mm:ss a"/></div>
                                        </div-->
                                        <!--div class="row">
                                            <div class="col-md-4 gray bold">Appt. Preference</div>
                                            <div class="col-md-8">${webRequest.appointmentDate} (${webRequest.preferredTime})</div>
                                        </div-->
                                        <!--div class="row">
                                            <div class="col-md-4 gray bold">Appt. Type</div>
                                            <div class="col-md-8">${webRequest.reasonForAppointment}</div>
                                        </div-->
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Name</div>
                                            <div class="col-md-8">${webRequest.firstName} ${webRequest.lastName}</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Birthdate - Gender</div>
                                            <div class="col-md-8">${webRequest.dateOfBirth} - ${webRequest.gender}</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Phone</div>
                                            <div class="col-md-8"><custom:fmtPhone phone="${webRequest.phone}"/> (${webRequest.phoneType})</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Email Address</div>
                                            <div class="col-md-8">${webRequest.emailAddress}</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Address</div>
                                            <div class="col-md-8">${webRequest.patientStreet} ${webRequest.patientStreet2}</div>
                                        </div>
                                         <div class="row">
                                            <div class="col-md-4 gray bold"></div>
                                            <div class="col-md-8">${webRequest.patientCity} ${webRequest.patientState} ${webRequest.patientZipCode}
                                            </div>
                                        </div>
                                        <div class="row" style="margin-top: 15px">
                                            <div class="col-md-4 gray bold">Appt. Preference</div>
                                            <div class="col-md-8">${webRequest.appointmentDate} (${webRequest.preferredTime})</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Appt. Type</div>
                                            <div class="col-md-8">${webRequest.reasonForAppointment}</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Comment</div>
                                            <div class="col-md-8">${webRequest.comment}</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Insurance</div>
                                            <div class="col-md-8">${webRequest.insuranceCompany} - ${webRequest.insuranceType}</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Subscriber ID</div>
                                            <div class="col-md-8">${webRequest.subscriberId}</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Employer Name</div>
                                            <div class="col-md-8">${webRequest.employerName}</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Relationship</div>
                                            <div class="col-md-8">${webRequest.relationship}</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Subscriber</div>
                                             <div class="col-md-8">${webRequest.subscriberFirstName} ${webRequest.subscriberLastName}</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold"></div>
                                            <div class="col-md-8">${webRequest.subscriberDateOfBirth} ${webRequest.subscriberGender}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                  <h3 class="panel-title gray">Facility Details (TEST)
                                    <c:if test="${facility.website != null}">
                                        <a target="_blank" href="https://www.brightnow.com/dental-office/${fn:replace(fn:toLowerCase(facility.city), ' ', '-')}-dentist/${facility.facilityId}" data-toggle="tooltip" data-original-title="View Web Page" class="fr" data-placement="right">
                                            <img class="map-link" style="margin-top: -5px; width: 28px;" src="${pageContext.request.contextPath}/images/earth.png">
                                        </a>
                                    </c:if>
                                    <c:if test="${facility.facilityId > 0}">
                                        <a target="_blank" href="mailto:${facility.facilityId}-<EMAIL>" data-toggle="tooltip" data-original-title="Email Front Office" class="fr" data-placement="right" style="margin-right: 10px;">
                                            <img class="map-link" style="margin-top: -8px; padding-left: 10px; width: 35px;" src="${pageContext.request.contextPath}/images/email_template.png">
                                        </a>
                                    </c:if>
                                    <a id="employerSearchLink" target="_blank" href="https://cti.smilebrands.com/employerPlanSearch" data-toggle="tooltip" data-original-title="Search Employee Plans" class="fr" data-placement="right" style="margin-right: 2px;">
                                        <img class="ins-search-link" style="margin-top: -5px; width: 27px;" src="${pageContext.request.contextPath}/images/insurance-search-2.png">
                                    </a>
                                    <c:if test="${facility.facilityId > 0}">
                                        <a target="_blank" href="https://smilebrands.service-now.com/sp?id=sc_cat_item&sys_id=d03015451bda3b00f0f48661cd4bcb4b&sysparm_category=eae0d5811bda3b00f0f48661cd4bcbf5" data-toggle="tooltip" data-original-title="Capture Patient Feedback" class="fr" data-placement="right" style="margin-right: 2px;">
                                            <img class="ins-search-link" style="margin-top: -5px; margin-right: 5px; width: 25px;" src="${pageContext.request.contextPath}/images/file_upload.png">
                                        </a>
                                        <a target="_blank" href="https://openbook.smilebrands.com/treatments/viewer/?siteId=${facility.facilityId}" data-toggle="tooltip" data-toggle="tooltip" data-original-title="Launch Open Book Treatment Estimator" class="fr" data-placement="right" style="margin-right: 2px;">
                                            <img class="ins-search-link" style="margin-top: -5px; margin-right: 5px; width: 25px;" src="${pageContext.request.contextPath}/images/calculator.png">
                                        </a>
                                    </c:if>
                                  </h3>
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-md-4 gray bold">Facility ID</div>
                                        <div class="col-md-8">${facility.facilityId}</div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4 gray bold">Facility Name</div>
                                        <div class="col-md-8 blue action">${facility.name}
                                            <c:set var="fullAddress" value="${facility.address}, ${facility.city}, ${facility.state} ${facility.zip}" />
                                            <a target="_blank" 
                                               href="https://www.google.com/maps/search/?api=1&query=${fn:replace(fullAddress, ' ', '+')}" 
                                               data-toggle="tooltip" 
                                               data-original-title="View Location Map" 
                                               data-placement="right">
                                                <img class="map-link" src="${pageContext.request.contextPath}/images/google-map-logo2.png">
                                            </a>
                                        </div>
                                    </div>
                                    <c:if test="${facility.qsiClinicId != null && facility.qsiClinicId > 0}">
                                        <div class="row">
                                            <div class="col-md-4 gray bold">QSI ID</div>
                                            <div class="col-md-8">${facility.qsiClinicId}</div>
                                        </div>
                                    </c:if>
                                    <div class="row">
                                        <div class="col-md-4 gray bold">Address</div>
                                        <div class="col-md-8">${facility.address}</div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4 gray bold"></div>
                                        <div class="col-md-8">${facility.city}, ${facility.state} ${facility.zip}</div>
                                    </div>
                                    <c:if test="${facility.addressDescription != null}">
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Directions</div>
                                            <div class="col-md-8">${facility.addressDescription}</div>
                                        </div>
                                    </c:if>
                                    <c:if test="${facility.crossStreets != null}">
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Cross Streets</div>
                                            <div class="col-md-8">${facility.crossStreets}</div>
                                        </div>
                                    </c:if>
                                    <c:if test="${facility.specialNotes != null}">
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Notes</div>
                                            <div class="col-md-8">${facility.specialNotes}</div>
                                        </div>
                                    </c:if>
                                    <c:if test="${facility.languagesSpoken != null}">
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Languages</div>
                                            <div class="col-md-8">${facility.languagesSpoken}</div>
                                        </div>
                                    </c:if>
                                    <c:if test="${facility.doctorOnCallName != null}">
                                        <div class="row">
                                            <div class="col-md-4 gray bold">On Call Doctor</div>
                                            <div class="col-md-8">${facility.doctorOnCallName}</div>
                                        </div>
                                    </c:if>
                                    <c:if test="${facility.doctorOnCallNumber != null}">
                                        <div class="row">
                                            <div class="col-md-4 gray bold">On Call Number</div>
                                            <div class="col-md-8">${facility.doctorOnCallNumber}</div>
                                        </div>
                                    </c:if>
                                    <c:if test="${facility.acceptedStatePlans != null}">
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Accepted State Plans</div>
                                            <div class="col-md-8">${facility.acceptedStatePlans}</div>
                                        </div>
                                    </c:if>
                                    <c:if test="${facility.ageLimit != null}">
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Age Limit</div>
                                            <div class="col-md-8">${facility.ageLimit}</div>
                                        </div>
                                    </c:if>
                                    <c:if test="${facility.nitrousOffered != null}">
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Nitrous Offered</div>
                                            <div class="col-md-8">${facility.nitrousOffered}</div>
                                        </div>
                                    </c:if>
                                    <c:set var="specialServiceTxt" value=""/>
                                    <c:forEach var="specialService" items="${facility.specialServices}">
                                        <c:if test="${specialService.value != null && (fn:toLowerCase(specialService.value) == 'true' || fn:toLowerCase(specialService.value) == 'yes')}">
                                            <c:set var="specialServiceTxt" value="${specialServiceTxt}<li>${specialService.name}</li>"/>
                                        </c:if>
                                    </c:forEach>
                                    <c:if test="${facility.specialServices != null && fn:length(facility.specialServices) > 0 && fn:length(specialServiceTxt) > 0}">
                                        <c:set var="specialServiceTxt" value="<ul>${specialServiceTxt}</ul>"/>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">
                                                Special Services
                                                <span class="glyphicon glyphicon-tag pointer padding-left-10 facility-special-services" data-toggle="popover" data-html=true data-container=".facility-special-services"
                                                    data-content="${specialServiceTxt}"
                                                >
                                                </span>
                                            </div>
                                        </div>
                                    </c:if>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                  <h3 class="panel-title gray">Office Numbers & Hours</h3>
                                </div>
                                <div class="panel-body">
                                    <div class="col-md-4" style="padding-left: 0px; padding-right: 0px; text-align: right; width: 40%;">
                                        <div class="row">
                                            <div class="col-md-3 gray bold">Phone</div>
                                            <div class="col-md-8"><custom:fmtPhone phone="${facility.phoneNumber}"/></div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-3 gray bold">Transfer</div>
                                            <c:if test="${facility.associatedPhoneNumber != null}">
                                                <div class="col-md-8"><custom:fmtPhone phone="${facility.associatedPhoneNumber.transferNumber}"/></div>
                                            </c:if>

                                        </div>
                                        <div class="row">
                                            <div class="col-md-3 gray bold">Fax</div>
                                            <div class="col-md-8"><custom:fmtPhone phone="${facility.faxNumber}"/></div>
                                        </div>
                                    </div>
                                    <div class="col-md-8" style="width: 60%; padding-right: 0px; border-left: 1px solid #ccc;">
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Monday</div>
                                            <div class="col-md-8">${facility.mondayHour}</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Tuesday</div>
                                            <div class="col-md-8">${facility.tuesdayHour}</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Wednesday</div>
                                            <div class="col-md-8">${facility.wednesdayHour}</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Thursday</div>
                                            <div class="col-md-8">${facility.thursdayHour}</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Friday</div>
                                            <div class="col-md-8">${facility.fridayHour}</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Saturday</div>
                                            <div class="col-md-8">${facility.saturdayHour}</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Sunday</div>
                                            <div class="col-md-8">${facility.sundayHour}</div>
                                        </div>
                                        <c:if test="${facility.hoursNotes != null}">
                                            <div class="row">
                                                <div class="col-md-4 gray bold" style="text-decoration: underline;">*Notes:</div>
                                                <div class="col-md-8">${facility.hoursNotes}</div>
                                            </div>
                                        </c:if>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                  <h3 class="panel-title gray">Management</h3>
                                </div>
                                <div class="panel-body">
                                    <c:forEach var="manager" items="${facility.employees.Managers}">
                                        <p>${manager.firstName} ${manager.lastName} - <i>${manager.title}</i> (${manager.email})</p>
                                    </c:forEach>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                  <h3 class="panel-title gray">Providers
                                    <c:if test="${facility.employees.Dentists != null && fn:length(facility.employees.Dentists) > 0}">
                                        <a href="javascript:void(0);" class="view-provider-insurance fr" style="color: #257DB0">View Credentialing</a>
                                    </c:if>
                                  </h3>
                                </div>
                                <div class="panel-body">
                                    <c:forEach var="dentist" items="${facility.employees.Dentists}">
                                        <p>${dentist.prefix} ${dentist.firstName} ${dentist.lastName} - <i>${dentist.title}</i>
                                        <span class="glyphicon glyphicon-tag pointer padding-left-10 provider-detail" providerId="${dentist.serviceProviderId}"></span>
                                        <c:if test="${dentist.scheduleNotes != null}">
                                            <br/><b style="padding-left: 20px;">${dentist.scheduleNotes}</b>
                                        </c:if>
                                        </p>
                                        <div class="provider-info"
                                            providerId="${dentist.serviceProviderId}" qsiId="${dentist.qsiProviderId}"
                                            providerName="${dentist.prefix} ${dentist.firstName} ${dentist.lastName}"
                                            scheduleNote="${dentist.scheduleNotes}" gender="${dentist.gender}"
                                            languages="${dentist.languages}" institution="${dentist.institutionName}"
                                            degreeSuffix="${dentist.degreeSuffix}" degree="${dentist.degreeEarned}"
                                            graduatedIn="${dentist.degreeEarnedYear}" startPracticeIn="${dentist.startPracticeYear}"
                                            >
                                        </div>
                                    </c:forEach>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel panel-default">
                        <div class="panel-heading">
                          <h3 class="panel-title gray">Messages</h3>
                        </div>
                        <div class="panel-body">
                            <p>${facility.message}</p>
                        </div>
                      </div>


                    <div class="panel panel-default">
                        <div class="panel-heading">
                          <h3 class="panel-title gray">Neighboring Facilities</h3>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <c:forEach var="office" items="${leftNeighboringFacilities}" varStatus="status">
                                        <p>
                                            <div class="blue bold">
                                                <a href="${office.screenPopUrl}" class="other-site">${office.name} (${office.distance} miles away)</a>
                                            </div>
                                            <div>${office.address}</div>
                                            <div>${office.city}, ${office.state}  ${office.zip}</div>
                                            <div><custom:fmtPhone phone="${office.phoneNumber}"/></div>
                                            <div>
                                                <c:forEach var="ss" items="${office.specialtySchedule}">
                                                    <span style="font-weight: bold;">${ss.name}: </span><span style="font-style: italic;">${ss.note}</span><br/>
                                                </c:forEach>
                                            </div>
                                        </p>
                                    </c:forEach>
                                </div>
                                <div class="col-md-6">
                                    <c:forEach var="office" items="${rightNeighboringFacilities}" varStatus="status">
                                        <p>
                                            <div class="blue bold">
                                                <a href="${office.screenPopUrl}" class="other-site">${office.name} (${office.distance} miles away)</a>
                                            </div>
                                            <div>${office.address}</div>
                                            <div>${office.city}, ${office.state}  ${office.zip}</div>
                                            <div>P: <custom:fmtPhone phone="${office.phoneNumber}"/></div>
                                            <div>
                                                <c:forEach var="ss" items="${office.specialtySchedule}">
                                                    <span style="font-weight: bold;">${ss.name}: </span><span style="font-style: italic;">${ss.note}</span><br/>
                                                </c:forEach>
                                            </div>
                                        </p>
                                    </c:forEach>
                                </div>
                            </div>
                        </div>
                    </div>
        </div>

    </div><!-- /.container -->


    <!-- Bootstrap core JavaScript
    ================================================== -->
    <!-- Placed at the end of the document so the pages load faster -->
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-1.7.2.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-ui-1.8.20.custom.min.js"></script>
    <script src="${pageContext.request.contextPath}/bootstrap/js/bootstrap.js"></script>

    <script type="text/javascript" src="${pageContext.request.contextPath}/script/screenPopActions.js?_id=20190918"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/sbi-script.js?_id=20190918"></script>
  </body>
</html>