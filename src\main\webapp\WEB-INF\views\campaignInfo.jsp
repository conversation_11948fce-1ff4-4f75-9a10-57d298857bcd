<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<%@ taglib uri="../customTag.tld" prefix="custom" %>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Smile Brands Call Center</title>
    <link type="text/css" rel="stylesheet" media="all" href="style/style.css" />
    <link type="text/css" href="style/redmond/jquery-ui-1.8.20.custom.css" rel="stylesheet" />
    <script type="text/javascript" src="script/jquery-1.7.2.min.js"></script>
    <script type="text/javascript" src="script/jquery-ui-1.8.20.custom.min.js"></script>
    <script type="text/javascript" src="script/actions.js?_id=${buildNumber}"></script>
</head>

<div id="office_by_zip_dialog" title="Set resolution for your call">
    <div>
        <ul id="display_list" class="neighbor-office-info-list">
        </ul>
    </div>
</div>
<div class="main">

    <h4 class="group">&nbsp;</h4>

    <div class="block">
        <div class="left fl">
            <ul class="info-list">
                <li><span class="label fl">Camp Name:</span><span class="content fr">${campaign.campaignName}</span></li>

                <li><span class="label fl">DNIS:</span><span id='dnisLbl' class="content fr">${campaign.campaignPhoneNumber}</span></li>
                <li><span class="label fl">CALL CENTER:</span><span id='callCenterLbl' class="content fr">${campaign.callCenter}</span></li>
                <li><span class="label fl">Limited Support:</span><span id='limitedSupportLbl' class="content fr green">${campaign.limitedSupport}</span></li>
            </ul>
        </div><!--/left-->
        <div class="right fr">
            <div class="link-container">
               <form class="search-form" >
                   <input id="zipInput" type="text" name="name" placeholder="Enter Zip Code" />
                   <input id="searchByZipcodeBtn" type="submit" value="Search" style="float:none"/>
               </form>
            </div>
        </div>
        <div class="clear"></div>
    </div><!--/block-->

    <div class="block">
        <h2>Offices:</h2>

        <c:forEach var="facility" items="${campaign.facilities}" varStatus="status">
            <c:choose>
                <c:when test="${status.count % 2 == 1}">
                    <div class="neighbor-office fl">
                </c:when>
                <c:otherwise>
                    <div class="neighbor-office fr">
                </c:otherwise>
            </c:choose>
                <ul class="neighbor-office-info-list">

                    <li><span class="label fl">Site name:</span><a href="load-screen-pop/psr/${uuid}/${employeeNumber}/${language}/${numberPress}/${facility.facilityId}" target='_blank'><span class="content fr title">${facility.name} (${facility.facilityId})</span></a></li>

                    <li><span class="label fl">Address:</span><span class="content fr">${facility.fullAddress}</span></li>

                </ul>
            </div>

        </c:forEach>

        <div class="clear"></div>
    </div><!--/block-->
</div><!--/main-->

</body>
</html>
