CREATE OR REPLACE PROCEDURE "CISCOADM"."SP_VERIFY_APPOINTMENTS"(
P_CALL_LOG_ID           IN      NUMBER,
P_FACILITY_ID           IN      NUMBER,
P_<PERSON><PERSON><PERSON><PERSON>_ID             IN      NUMBER,
P_UNIQUE_ID             IN      NUMBER,
P_CALL_CENTER_TIME      IN      TIMESTAMP,
P_CLOSE_DATE            IN      DATE,
P_APPT_DATE             IN      DATE DEFAULT NULL,
P_IS_ORTHO              IN      NUMBER DEFAULT 0,
P_CREATE_DATETIME       IN      TIMESTAMP,
IO_CALL_LOG_STATUS      IN OUT  VARCHAR2,
IO_APPT_LOG_STATUS      IN OUT  VARCHAR2,
IO_CONTINUE_PROCESSING  IN OUT  NUMBER)

/*---------------------------------------------------------------------------------*/
/* Procedure: <PERSON>_VERIFY_APPOINTMENTS                                               */
/*                                                                                 */
/* Author: <PERSON>                                                          */
/* Date Written: 08/08/2014                                                        */
/*                                                                                 */
/* Purpose: This Routine Verifys Unverified Appointments (Non Liberty)             */
/*                                                                                 */
/*                                                                                 */
/* Input Variables:                                                                */
/*                                                                                 */
/*                                                                                 */
/*                                                                                 */
/*                                                                                 */
/* Returns:                                                                        */
/*                                                                                 */
/*                                                                                 */
/* Revisions                                                                       */
/*                                                                                 */
/*                                                                                 */
/*                                                                                 */
/*---------------------------------------------------------------------------------*/

AS

V_UNIQUE_ID                     PATIENTS.UNIQUE_ID%TYPE := P_UNIQUE_ID;
V_FACILITY_ID                   PATIENTS.FACILITY_ID%TYPE := P_FACILITY_ID;
V_CLINIC_ID                     APPT_CALL_LOG.CLINIC_ID%TYPE := P_CLINIC_ID;
V_CALL_LOG_ID                   AGENT_CALL_LOG.CALL_LOG_ID%TYPE := P_CALL_LOG_ID;
V_APPT_LOG_STATUS               APPT_CALL_LOG.APPT_LOG_STATUS%TYPE := IO_APPT_LOG_STATUS;
V_CALL_LOG_STATUS               AGENT_CALL_LOG.CALL_LOG_STATUS%TYPE := IO_CALL_LOG_STATUS;
V_CREATE_DATETIME               AGENT_CALL_LOG.CREATE_DATETIME%TYPE := P_CREATE_DATETIME;
V_APPT_LOG_ID                   APPT_CALL_LOG.APPT_LOG_ID%TYPE;
V_CALL_CENTER_TIME              AGENT_CALL_LOG.CALL_TIME%TYPE := P_CALL_CENTER_TIME;
V_IS_ORIGINAL                   AGENT_CALL_LOG.IS_ORIGINAL%TYPE;
V_START_DATE                    DATE := TRUNC(P_CALL_CENTER_TIME);
V_CLOSE_DATE                    DATE:= P_CLOSE_DATE;
V_TODAY                         DATE := TRUNC(SYSDATE);

V_AGENT_TYPE                    AGENT_CALL_LOG.AGENT_TYPE%TYPE;
V_ALLOW_CREDIT                  AGENT_CALL_LOG.ALLOW_CREDIT%TYPE;
V_ADMIN_OVERRIDDEN              AGENT_CALL_LOG.ADMIN_OVERRIDDEN%TYPE;

V_PROVIDER_ID                   APPOINTMENTS.PROVIDER_ID%TYPE;
V_APPT_DATE                     APPOINTMENTS.APPT_DATE%TYPE;
V_UPDATE_LOG                    NUMBER(1,0) := 0;
V_APPT_FOUND                    NUMBER(1,0) := 0;
V_RECORD_FOUND                  NUMBER(1,0) := 0;

ERR_NUM                         NUMBER;
ERR_MSG                         VARCHAR2(512);
V_STORED_PROCEDURE              VARCHAR2(30) := $$PLSQL_UNIT;


BEGIN

    IO_CONTINUE_PROCESSING := 1;
/*---------------------------------------------------------------------------*/
/* Test for Original Call                                                    */
/*---------------------------------------------------------------------------*/
    BEGIN

    V_IS_ORIGINAL := 1;

    SELECT 0
      INTO V_IS_ORIGINAL
    FROM AGENT_CALL_LOG
    WHERE FACILITY_ID = V_FACILITY_ID AND
          UNIQUE_ID = V_UNIQUE_ID AND
          CALL_LOG_STATUS <> 'CLOSED' AND
          CALL_LOG_ID <> V_CALL_LOG_ID AND
          CREATE_DATETIME < V_CREATE_DATETIME AND
          ROWNUM = 1;

    EXCEPTION WHEN OTHERS THEN

    V_IS_ORIGINAL := 1;

    END;
/*---------------------------------------------------------------------------*/
/* Get Allow_Credit and Admin_Overridden                                     */
/*---------------------------------------------------------------------------*/
    BEGIN
        V_ALLOW_CREDIT := 0;
        V_ADMIN_OVERRIDDEN := 0;
        V_AGENT_TYPE := ' ';

        SELECT nvl(LOWER(AGENT_TYPE), ' '), nvl(ALLOW_CREDIT, 0), nvl(ADMIN_OVERRIDDEN, 0)
            INTO V_AGENT_TYPE, V_ALLOW_CREDIT, V_ADMIN_OVERRIDDEN
        FROM AGENT_CALL_LOG
        WHERE CALL_LOG_ID = V_CALL_LOG_ID;

    EXCEPTION WHEN OTHERS THEN
        V_AGENT_TYPE := ' ';
        V_ALLOW_CREDIT := 0;
        V_ADMIN_OVERRIDDEN := 0;

    END;
/*---------------------------------------------------------------------------*/
/* Update IS_ORIGINAL on Other ROWS                                          */
/*---------------------------------------------------------------------------*/
    IF V_IS_ORIGINAL = 1 THEN

    BEGIN

    UPDATE AGENT_CALL_LOG
       SET IS_ORIGINAL = 0
    WHERE FACILITY_ID = V_FACILITY_ID AND
          UNIQUE_ID = V_UNIQUE_ID AND
          CALL_LOG_STATUS <> 'CLOSED' AND
          CALL_LOG_ID <> V_CALL_LOG_ID AND
          CREATE_DATETIME > V_CREATE_DATETIME;

    EXCEPTION WHEN OTHERS THEN

    ERR_NUM := SQLCODE;
    ERR_MSG := SUBSTR(SQLERRM, 1, 512);

    END;
    END IF;

/*---------------------------------------------------------------------------*/
/* Find Appointments                                                         */
/*---------------------------------------------------------------------------*/
    BEGIN

    SELECT APPT_DATE,
           PROVIDER_ID

      INTO V_APPT_DATE,
           V_PROVIDER_ID

    FROM APPOINTMENTS

    WHERE CLINIC_ID = V_CLINIC_ID AND
          UNIQUE_ID = V_UNIQUE_ID AND

          /*TRUNC(V_START_DATE) to get same date appointment*/
          TRUNC(APPT_DATE) >= TRUNC(V_START_DATE) AND

          ROWNUM = 1;

    V_APPT_FOUND := 1;
    V_APPT_LOG_STATUS := 'VERIFIED';
    V_UPDATE_LOG := 1;

    IF V_ADMIN_OVERRIDDEN = 0 AND V_AGENT_TYPE = 'psr' THEN
        V_ALLOW_CREDIT := 1;
    END IF;


    EXCEPTION WHEN OTHERS THEN

    V_APPT_DATE := NULL;
    V_PROVIDER_ID := NULL;
    V_APPT_FOUND := 0;
    V_UPDATE_LOG := 0;

    END;
/*---------------------------------------------------------------------------*/
/* Test for Close Date -  No Appt Found                                      */
/*---------------------------------------------------------------------------*/

    IF V_APPT_FOUND = 0 AND
       V_CLOSE_DATE < V_TODAY THEN

    BEGIN

    V_APPT_LOG_STATUS := 'CLOSED';
    V_CALL_LOG_STATUS := 'CLOSED';
    V_APPT_DATE       := P_APPT_DATE;
    V_UPDATE_LOG := 1;

    IF V_ADMIN_OVERRIDDEN = 0 THEN
        V_ALLOW_CREDIT := 0;
    END IF;

    END;

    END IF;
/*---------------------------------------------------------------------------*/
/* Add new status to Appt Call Log                                           */
/*---------------------------------------------------------------------------*/

    IF V_UPDATE_LOG = 1 THEN

    IO_APPT_LOG_STATUS := V_APPT_LOG_STATUS;
    IO_CONTINUE_PROCESSING := 0;

/*---------------------------------------------------------------------------*/
/* Add new status to Appt Call Log                                           */
/*---------------------------------------------------------------------------*/

    BEGIN

    INSERT INTO APPT_CALL_LOG(
                APPT_LOG_ID,
                CALL_LOG_ID,
                CLINIC_ID,
                UNIQUE_ID,
                PROVIDER_ID,
                APPT_LOG_STATUS,
                APPT_DATE,
                APPT_VALUE,
                LAST_UPD_PROGRAM,
                CREATE_EMPLOYEE,
                CREATE_DATETIME,
                UPDATE_EMPLOYEE,
                UPDATE_TIMESTAMP,
                IS_ORTHO)

	     VALUES(APPT_CALL_LOG_ID_SEQ.NEXTVAL,
                V_CALL_LOG_ID,
                V_CLINIC_ID,
                V_UNIQUE_ID,
                V_PROVIDER_ID,
                V_APPT_LOG_STATUS,
                V_APPT_DATE,
                0,
                V_STORED_PROCEDURE,
                110026,
                SYSDATE,
                NULL,
                NULL,
                P_IS_ORTHO)
        RETURNING APPT_LOG_ID
        INTO V_APPT_LOG_ID;

    END;


/*---------------------------------------------------------------------------*/
/* Close Agent Call Log on Terminal Event                                    */
/*---------------------------------------------------------------------------*/

    BEGIN

    UPDATE AGENT_CALL_LOG
       SET CALL_LOG_STATUS = V_CALL_LOG_STATUS,
           LAST_APPT_LOG_ID = V_APPT_LOG_ID,
           UPDATE_EMPLOYEE = 110026,
           UPDATE_TIMESTAMP = SYSDATE,
           IS_ORIGINAL = V_IS_ORIGINAL,
           ALLOW_CREDIT = V_ALLOW_CREDIT
    WHERE CALL_LOG_ID = V_CALL_LOG_ID;

    END;
/*---------------------------------------------------------------------------*/
/* End of Update                                                             */
/*---------------------------------------------------------------------------*/
    END IF;

/*---------------------------------------------------------------------------*/
/* Insert Into Process Log                                                   */
/*---------------------------------------------------------------------------*/

    INSERT INTO CALL_PROCESS_LOG(
                CALLED_PROC,
                CALL_LOG_ID,
                FACILITY_ID,
                CLINIC_ID,
                UNIQUE_ID,
                CALL_TIME,
                CLOSE_DATE,
                CREATE_DATE,
                CALL_LOG_STATUS,
                APPT_LOG_STATUS,
                CONTINUE_PROCESSING,
                UPDATE_LOG,
                APPT_FOUND,
                TODAYS_DATE)

	     VALUES(V_STORED_PROCEDURE,
                P_CALL_LOG_ID,
                P_FACILITY_ID,
                P_CLINIC_ID,
                P_UNIQUE_ID,
                P_CALL_CENTER_TIME,
                P_CLOSE_DATE,
                NULL,
                IO_CALL_LOG_STATUS,
                IO_APPT_LOG_STATUS,
                IO_CONTINUE_PROCESSING,
                V_UPDATE_LOG,
                V_APPT_FOUND,
                V_TODAY);

/*---------------------------------------------------------------------------*/
/* Error Occured - Add error to log                                          */
/*---------------------------------------------------------------------------*/

    EXCEPTION WHEN OTHERS THEN

    ERR_NUM := SQLCODE;
    ERR_MSG := SUBSTR(SQLERRM, 1, 512);

    INSERT INTO APPLICATION_ERROR_LOG(
                ERROR_ID,
                STORED_PROCEDURE,
                ERROR_NUMBER,
                ERROR_MSG,
                ERROR_DATE)
      VALUES(
               APPLICATION_ERROR_LOG_SEQ.NEXTVAL,
               V_STORED_PROCEDURE,
               ERR_NUM,
               ERR_MSG,
               SYSDATE);
END;