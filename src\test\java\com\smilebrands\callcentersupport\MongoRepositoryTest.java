package com.smilebrands.callcentersupport;

import com.mongodb.*;
import com.smilebrands.callcentersupport.domain.*;
import com.smilebrands.callcentersupport.domain.report.InboundCallSummary;
import com.smilebrands.callcentersupport.domain.schedule.Task;
import com.smilebrands.callcentersupport.repo.FacilityRepository;
import com.smilebrands.callcentersupport.repo.MongoRepository;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.jdbc.core.JdbcTemplate;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

/**
 * User: <PERSON><PERSON>
 * Date: 6/26/14
 */
public class MongoRepositoryTest extends BaseTest {

    @Autowired
    MongoRepository mongoRepository;

    @Autowired
    MongoOperations mongoOperations;

    @Autowired
    FacilityRepository facilityRepository;

    @Autowired
    protected JdbcTemplate jdbcTemplate;


    @Test
    public void rebuildMongoTest() {
        mongoRepository.rebuildMongoEnvironment();
    }

    @Test
    public void reasonResolutionCodeTest() {
        logger.debug(mongoRepository.getCallReasonCodes(null).toString());
        logger.debug(mongoRepository.getCallResolutionCodes(null).toString());
    }

    @Test
    public void queryForSpamNumber() {
        logger.debug("************ check " + mongoRepository.getPhoneNumberRoutingRules(9495054654L));
        logger.debug("************ check " + mongoRepository.getPhoneNumberRoutingRules(2144465257L).getDescription());
        logger.debug("************ check " + mongoRepository.getPhoneNumberRoutingRules(7133946000L).getDescription());
    }

    @Test
    public void zipToFacilityTest() {
        mongoRepository.rebuildZipCodeFacilityAssociations();
    }

    @Test
    public void testBuildCallCenterEmployee(){
        mongoRepository.rebuildCallCenterEmployees();
    }

    @Test
    public void testFindCallCenterEmployeeByNumber(){
        Integer employeeNumber = 102029;
        CallCenterEmployee cce = mongoRepository.findByEmployeeNumber(employeeNumber);
        logger.debug("call center employee for [" + employeeNumber + "] is " + (cce != null ? cce.getEmployeeName() : " not found"));
        employeeNumber = 116531;
        cce = mongoRepository.findByEmployeeNumber(employeeNumber);
        logger.debug("call center employee for [" + employeeNumber + "] is " + (cce != null ? cce.getEmployeeName() : " not found"));


    }

    @Test
    public void findZipFacilities() {
        logger.debug(mongoRepository.findFacilitiesByZipCode("92656", 10).toString());
    }

    @Test
    public void testLookupDistinct(){
        logger.debug("phoneType: {}", mongoRepository.getDistinctField("phoneNumber", "phoneType"));
        logger.debug("brandName: {}", mongoRepository.getDistinctField("phoneNumber", "brandName"));
        logger.debug("callCenterName: {}", mongoRepository.getDistinctField("phoneNumber", "callCenterName"));
        logger.debug("callCenterQueueName: {}", mongoRepository.getDistinctField("phoneNumber", "callCenterQueueName"));
        logger.debug("voiceMailRoute: {}", mongoRepository.getDistinctField("phoneNumber", "voiceMailRoute"));

        logger.debug("facilityId: {}", mongoRepository.getDistinctField("facility", "_id"));
    }

    @Test
    public void testLookupNpaNxx(){
        Long ani = 9493315916L;
        Npa result = mongoRepository.getNpaByAni(ani);
        logger.debug("npa: {}", result);
    }

    @Test
    public void testMapReduce() {
        Calendar cal = Calendar.getInstance();
        cal.set(2017, Calendar.APRIL, 22);
        long current = new Date().getTime();
        boolean keepGoing = false;
        do{
            mongoRepository.inboundCallSummaryMapReduce(cal.getTime());
            cal.add(Calendar.DAY_OF_YEAR, 1);
            keepGoing = current >= cal.getTime().getTime();
        }while(keepGoing);
//        cal = Calendar.getInstance();
//        cal.add(Calendar.DAY_OF_MONTH, -1);
//        mongoRepository.inboundCallSummaryMapReduce(cal.getTime());


    }

    @Test
    public void testGetLogToArchive(){
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -1);
        List<ActionLog> list = mongoRepository.getActionLogToArchive(cal.getTime());
        logger.debug("there are {} logs lte " + cal.getTime(), list.size());
    }

    @Test
    public void testArchiveLog(){
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -1);
        List<ActionLog> list = mongoRepository.getActionLogToArchive(cal.getTime());
        logger.debug("there are {} logs lte " + cal.getTime(), list.size());
        int cnt = 0;
        int size = list.size();
        for(ActionLog log : list){
            mongoRepository.archiveLog(log);
            cnt++;
            if((cnt % 1000) == 0) {
                System.out.println(new Date() + ":::Processed " + cnt + " out of " + size);
            }
        }
    }

    @Test
    public void testSearchAgents(){
        Calendar cal = Calendar.getInstance();
//        cal.add(Calendar.DAY_OF_MONTH, -1);
        cal.set(2017, Calendar.OCTOBER, 25);
        List<AgentCall> agentCalls = mongoRepository.searchAgentCalls(VelocityUtils.getDateAsString(cal.getTime(), null), null, 0);
        int numberOfAgentCall = agentCalls.size();
        int numberOfCallLog = 0;
        int numberOfMissingCallLog = 0;
        int numberOfAppointments = 0;
        int numberOfMissingAppointments = 0;
        for(AgentCall agentCall : agentCalls){
            for(LoadedEmployee le : agentCall.getLoadedEmployees()){
                if(le.getCallLogId() != null){
                    numberOfCallLog++;
                }else{
                    numberOfMissingCallLog++;
                    logger.debug("missing call log for employee[" + le.getEmployeeNumber() + "] with UUID[" + agentCall.getUuid() + "]");
                }
            }
            for(Patient patient : agentCall.getPatients()){
                if(patient.getAppointmentCallLogId() != null){
                    numberOfAppointments++;
                }else{
                    numberOfMissingAppointments++;
                    logger.debug("missing appointment call log for UUID[" + agentCall.getUuid() + "] with Patient ID[" + patient.getTempPatientId() + "] " +
                            "and employee number[" + patient.getEmpEntered() + "] " +
                            "at " + patient.getFacilityId() + " on [" + patient.getNextAppointmentDate() + " " + patient.getNextAppointmentTime() + "]");
                }
            }
        }
        System.out.println("number of agent call: " + numberOfAgentCall);
        System.out.println("number of call log: " + numberOfCallLog);
        System.out.println("number of missing call log: " + numberOfMissingCallLog);
        System.out.println("number of appointments: " + numberOfAppointments);
        System.out.println("number of missing appointments: " + numberOfMissingAppointments);
    }

    @Test
    public void testLoadCallSummary(){
//        Query query = new Query(Criteria.where("_id.phoneType").is("ML"));
//        query.addCriteria(Criteria.where("_id.callCenterName").is("Irvine"));
//        query.with(new Sort(Sort.Direction.ASC, "_id.processDate"));
//        List<InboundCallSummary> list = mongoOperations.find(query, InboundCallSummary.class);
//        Map<String, Object> map = new HashMap<String, Object>();
//        for(InboundCallSummary summary : list){
//            String dateKey = VelocityUtils.getDateAsString(summary.getSummaryId().getProcessDate(), null);
//            Map<String, Object> value = (Map<String,Object>)map.get(dateKey);
//            if(value == null){
//                value = new HashMap<String, Object>();
//            }
//            Integer menuPress = summary.getSummaryId().getMenuPress();
//            value.put(menuPress.toString(), value.get(menuPress) != null
//                                                        ? ((Integer)value.get(menuPress)) + summary.getValue().getCallCount()
//                                                        : summary.getValue().getCallCount());
//            map.put(dateKey, value);
//        }
//        logger.debug("{}", map);
        List<InboundCallSummary> list = mongoRepository.getInboundCallSummary(null, "ML", null, null);
        for(InboundCallSummary summary : list){
            logger.debug(summary.getSummaryId().getCallCenterName() + ":::" + summary.getSummaryId().getProcessDate() + ":::" + summary.getSummaryId().getMenuPress() + ":::" + summary.getValue().getCallCount());
        }
    }

    @Test
    public void testGetInboundCallBySessionID(){
        String sessionId = "hello world";
        InboundCall call = mongoRepository.getInboundCallBySessionId(sessionId);
        logger.debug("call: {}", call);
        sessionId = "25000607873";
        call = mongoRepository.getInboundCallBySessionId(sessionId);
        logger.debug("call: {}", call);
    }

    @Test
    public void testGetUpdateRequest(){
//        List<UpdateRequest> list =  mongoRepository.getRequestsToUpdateFacility();
        List<UpdateRequest> list =  new ArrayList<UpdateRequest>(){{
            UpdateRequest ur = new UpdateRequest();
            ur.setEntityId(10300L);
            add(ur);
        }};
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for(UpdateRequest ur : list){
            logger.debug(ur.getEntityId() + ":::" + ur.getRequestDateTime());
            Facility facility = facilityRepository.findFacilityByFacilityId(ur.getEntityId().intValue());
            logger.debug("{}", facility);
            Date processDateTime = new Date();
            boolean processResult = mongoRepository.doUpdateFacility(ur);
            if(processResult){
//                ur.setProcessDateTime(df.format(processDateTime));
//                mongoRepository.markUpdateRequestAsProcessed(ur);
            }
        }
    }

    @Test
    public void testGetFacilityInfo(){
        Integer facilityId = 10440;
        Facility facility = facilityRepository.findFacilityByFacilityId(facilityId);
        logger.debug("{}", facility);

        /*List<Employee> emps = facility.getEmployees().get("Dentists");
        for (Employee e : emps) {
            Provider p = (Provider) e;
            logger.debug(p.getFirstName());
            for (ProviderInsurance pi : p.getProviderInsurances()) {
                logger.debug(pi.getPayerName() + " --> " + pi.getPayerStatus());
            }
        }*/

        for (Facility nb : facility.getNeighborFacilities()) {
            logger.debug("{}", nb);
        }

        //mongoOperations.save(facility);
    }

    @Test
    public void testGetScreenPopHealthCheckToClose(){
        List<ScreenPopHealthCheck> list = mongoRepository.getScreenPopHealthCheckToClose(100);
        for(ScreenPopHealthCheck sp : list){
            logger.debug(sp.getUuid() + ":::" + sp.getEmployeeNumber() + ":::" + sp.getScreenType() + ":::" + sp.getPopDateTime() + ":::" + sp.getCheckDateTime() + ":::" + sp.getNumberOfCheck());
        }
    }

    @Test
    public void testGetAllTask(){
        List<Task> tasks = mongoRepository.getAllTask();
        logger.debug("task size: {}", tasks.size());
    }

    @Test
    public void testReleasePendingWebRequest(){
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MINUTE, -2);
        String pendingProcessedDateTime = VelocityUtils.getDateAsString(cal.getTime(), "yyyy-MM-dd HH:mm:ss");
        Criteria criteria = Criteria.where("processedDateTime").is(null)
                .and("pendingProcessedDateTime").lt(pendingProcessedDateTime);
        Query query = new Query(criteria);
        List<WebRequest> pendingWebRequests = mongoOperations.find(query, WebRequest.class);
        logger.debug("pending web requests: {}", pendingWebRequests.size());
        Map<String, Integer> counts = new HashMap<String, Integer>();
        String sqlQuery = "select p.CLINIC_ID, p.UNIQUE_ID, p.LOCATION_ID, trim(p.NAME_LAST) || ', ' || trim(p.NAME_FIRST) as patient_name\n" +
                ", p.PHONE_NUMBER1 " +
                ", a.APPT_DATE, a.APPT_DATE_CREATED " +
                ", fac.FACILITY_ID " +
                "from patients p " +
                "join FACILITY fac on fac.QSI_CLINIC_ID = p.CLINIC_ID and ((fac.QSI_CLINIC_ID < 1100 and fac.QSI_LOCATION_ID = '00') " +
                "                                                            or (fac.QSI_CLINIC_ID >= 1100 and fac.QSI_LOCATION_ID = p.LOCATION_ID)) " +
                "join appointments a on a.CLINIC_ID = p.CLINIC_ID and p.UNIQUE_ID = a.UNIQUE_ID " +
                "                    and ((a.CLINIC_ID < 1100) " +
                "                                            or (a.CLINIC_ID >= 1100 and a.LOCATION_ID = p.LOCATION_ID)) " +
                "where 1=1 " +
                "and (fac.FACILITY_ID = ? or phone_number1 = ? or PHONE_NUMBER2 = ?) " +
                "and trim(lower(NAME_LAST)) = ? and trim(lower(NAME_FIRST)) = ? " +
                "and trunc(a.appt_date) >= trunc(SYSDATE) " +
                "order by a.appt_date desc";
        String cutoffDate = VelocityUtils.getDateAsString(new Date(), "yyyy-MM-dd");
        int needToRemoved = 0;
        for(WebRequest wr : pendingWebRequests){
            if(counts.containsKey(wr.getPendingProcessedDateTime())){
                counts.put(wr.getPendingProcessedDateTime(), counts.get(wr.getPendingProcessedDateTime()) + 1);
            }else{
                counts.put(wr.getPendingProcessedDateTime(), 1);
            }
            Facility facility = mongoRepository.findFacilityDetail(wr.getOffice());
            String formatedPhoneNumber = phoneNumberToQSIFormat(wr.getPhone());
            List<Map<String, Object>> x = jdbcTemplate.queryForList(sqlQuery, new Object[]{wr.getOffice(), formatedPhoneNumber, formatedPhoneNumber, wr.getLastName().toLowerCase(), wr.getFirstName().toLowerCase()});
            if(facility.isLibertySupported()){
                logger.debug("{} is liberty supported office", wr.getUuid());
            }
            if(x.size() > 0){
                logger.debug(wr.getUuid() + " has appointment...");
                wr.setProcessedDateTime(VelocityUtils.getDateAsString(new Date(), "yyyy-MM-dd HH:mm:ss"));
//                wr.setProcessedNote("Clear old web request which was held by pending process, already contacted");
//                mongoRepository.saveWebRequest(wr);
            }else if(wr.getAppointmentDate().indexOf("1970") != 0 && wr.getAppointmentDate().compareTo(cutoffDate)<=0){
                logger.debug(wr.getUuid() + " with appointment [" + wr.getAppointmentDate() + "] need to be removed...");
                needToRemoved++;
                wr.setProcessedDateTime(VelocityUtils.getDateAsString(new Date(), "yyyy-MM-dd HH:mm:ss"));
//                wr.setProcessedNote("Clear old web request which was held by pending process");
//                mongoRepository.saveWebRequest(wr);
            }else{
                wr.setPendingProcessedDateTime(null);
                wr.setPendingProcessedBy(null);
//                mongoRepository.saveWebRequest(wr);
            }


        }
        logger.debug("need to remove: {}", needToRemoved);
        logger.debug("{}", counts);
    }

    @Test
    public void testGetInboundCallByDate(){
        for(int i = 2014; i<=2017; i++){
            Criteria criteria = Criteria.where("createDate").regex(Pattern.compile(i+"$"));
            Query query = new Query(criteria);
            logger.debug("inboundCalls size in " + i + ": {}", mongoOperations.count(query, InboundCall.class));
        }

    }

    @Test
    public void testPurgeInboundCall(){
        int month = 1;
        int year = 2016;
        int removed = mongoRepository.purgeInboundCall(month, year);
        logger.debug("number of inbound calls removed from [" + month + "-" + year + "]: " + removed);
    }

    @Test
    public void testRemoveActionLogArchive(){
        int month = 2;
        int year = 2015;
        int removed = mongoRepository.purgeArchivedLogByTimePeriod(month, year);
        logger.debug("number of logs removed from [" + month + "-" + year + "]: " + removed);
    }

    @Test
    public void testGetArchiveActionLogByDate(){
        for(int i = 2014; i<=2017; i++) {
            Criteria criteria = Criteria.where("createDate").regex(Pattern.compile(i+""));
            Query query = new Query(criteria);
            logger.debug("action log archived for " + i + ": {}", mongoOperations.count(query, ActionLogArchive.class));
        }
    }

    @Test
    public void testCalculateTimePeriodToPurge(){
        Calendar cal = Calendar.getInstance();
        logger.debug("month: {}", cal.get(Calendar.MONTH));
        logger.debug("year: {}", cal.get(Calendar.YEAR));
    }

    private String phoneNumberToQSIFormat(Long phoneNumber) {
        String s = String.valueOf(phoneNumber);
        if (s.length() == 10) {
            return "(" + s.substring(0,3) + ")" + s.substring(3,6) + "-" + s.substring(6);
        }

        return null;
    }
}
