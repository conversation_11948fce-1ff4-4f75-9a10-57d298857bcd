package com.smilebrands.callcentersupport.domain.mandrill;

/**
 * Created by phongpham on 6/2/15.
 */
public class SendRequest {
    private String key = "7mNUofqplDRZ4QSrPejTTw";
    private Message message = new Message();
    private boolean async = false;
    private String ipPool = "Main Pool";

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Message getMessage() {
        return message;
    }

    public void setMessage(Message message) {
        this.message = message;
    }

    public boolean isAsync() {
        return async;
    }

    public void setAsync(boolean async) {
        this.async = async;
    }

    public String getIpPool() {
        return ipPool;
    }

    public void setIpPool(String ipPool) {
        this.ipPool = ipPool;
    }
}