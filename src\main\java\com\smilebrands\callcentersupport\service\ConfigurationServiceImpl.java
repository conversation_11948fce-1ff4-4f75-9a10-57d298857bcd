package com.smilebrands.callcentersupport.service;

import com.smilebrands.callcentersupport.domain.*;
import com.smilebrands.callcentersupport.repo.FacilityRepository;
import com.smilebrands.callcentersupport.repo.JdbcRepository;
import com.smilebrands.callcentersupport.repo.MongoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * User: <PERSON><PERSON>
 * Date: 6/25/14
 */
@Service
public class ConfigurationServiceImpl extends BaseService implements ConfigurationService {

    @Autowired
    private MongoRepository mongoRepository;

    @Autowired
    private JdbcRepository jdbcRepository;

    @Autowired
    private FacilityRepository facilityRepository;

    @Override
    public void rebuildMongoEnvironment() {
        mongoRepository.rebuildMongoEnvironment();
    }

    @Override
    public Facility updateFacilityFacilityMessage(Integer facilityID, String message) {
        Facility f = mongoRepository.findFacilityDetail(facilityID);
        f.setMessage(message);

        mongoRepository.persistFacilityDetail(f);
        return f;
    }

    @Override
    public List<PhoneNumber> getAllPhoneNumbers() {
        return mongoRepository.findAllPhoneNumbers();
    }

    @Override
    public PhoneNumber persistPhoneNumber(PhoneNumber phoneNumber) {
        PhoneNumber existing = getPhoneNumber(phoneNumber.getPhoneNumber());
        if(existing == null){
            existing = new PhoneNumber();
            existing.setPhoneNumber(phoneNumber.getPhoneNumber());
        }
        existing.setBrandName(phoneNumber.getBrandName());
        existing.setCallCenterName(phoneNumber.getCallCenterName());
        existing.setCallCenterQueueName(phoneNumber.getCallCenterQueueName());
        existing.setCallCenterSupported(phoneNumber.isCallCenterSupported());
        existing.setCampaignName(phoneNumber.getCampaignName());
        existing.setFacilityId(phoneNumber.getFacilityId());
        existing.setLanguage(phoneNumber.getLanguage());
        existing.setPhoneType(phoneNumber.getPhoneType());
        existing.setTransferNumber(phoneNumber.getTransferNumber());
        existing.setVoiceMailRoute(phoneNumber.getVoiceMailRoute());

        existing.setActive(phoneNumber.isActive());
        existing.setInactivatedBy(phoneNumber.getInactivatedBy());
        existing.setInactivatedOn(phoneNumber.getInactivatedOn());

        return mongoRepository.persistPhoneNumber(phoneNumber);
    }

    @Override
    public PhoneNumber getPhoneNumber(Long number) {
        return mongoRepository.findPhoneNumber(number);
    }

    @Override
    public Campaign persistCampaign(Campaign campaign) {
        return mongoRepository.persistCampaign(campaign);
    }

    @Override
    public Campaign getCampaign(Long number) {
        return mongoRepository.findCampaign(number);
    }

    @Override
    public CallResolutionCode getCallResolutionCode(String code) {
        return mongoRepository.getCallResolutionCode(code);
    }

    @Override
    public CallResolutionCode persistCallResolutionCode(CallResolutionCode code){
        return mongoRepository.persistCallResolutionCode(code);
    }

    @Override
    public List<CallResolutionCode> getCallReasonResolutionsCodes() {
        return mongoRepository.getCallReasonResolutionsCodes();
    }

    @Override
    public List<CallResolutionCode> getCallReasonCodes(String screenType) { return mongoRepository.getCallReasonCodes(screenType); }

    @Override
    public List<CallResolutionCode> getCallResolutionCodes(String screenType) {
        return mongoRepository.getCallResolutionCodes(screenType);
    }

    @Override
    public List<Campaign> findAllCampaign() {
        return mongoRepository.findAllCampaign();
    }

    @Override
    public List<Facility> findAllFacility() {
        return mongoRepository.findAllFacility();
    }

    @Override
    public List<CallCenterEmployee> getSupervisedAgents(Integer supervisorId) {
        List<CallCenterEmployee> list = jdbcRepository.getSupervisedAgents(supervisorId);
        boolean containSelf = false;
        for(CallCenterEmployee cce : list){
            if(cce.getEmployeeNumber().equals(supervisorId)){
                containSelf = true;
                break;
            }
        }
        if(!containSelf){
            CallCenterEmployee supervisor = jdbcRepository.getCallCenterEmployeeByNumber(supervisorId, false);
            if(supervisor != null){
                list.add(0, supervisor);
            }
        }
        return list;
    }

    @Override
    public List<Map<String, Object>> getTeamHierarchy() {
        List<Map<String, Object>> team = jdbcRepository.getTeamHierarchy();
        for(Map<String, Object> m : team) {
            logger.debug(m.toString());
        }

        Map<Integer, Object> map = new HashMap<Integer, Object>();
        Map<Integer, Object> managers = new HashMap<Integer, Object>();
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        for(Map<String, Object> record : team){
            int employeeNumber = (Integer) record.get("EMPLOYEE_NUMBER");
            int managerNumber = (Integer) record.get("SUPERVISOR_EMPLOYEE_NUMBER");
            Map<Integer, Object> agentKey = getAgentNode(employeeNumber, map);
            Map<String, Object> agentValue = null;
            if(agentKey == null){
                agentKey = new HashMap<Integer, Object>();
                agentValue = new HashMap<String, Object>();
                agentValue.put("employeeNumber", employeeNumber);
                agentValue.put("employeeName", record.get("EMPLOYEE_NAME"));
                agentValue.put("managerNumber", managerNumber);
                if(managers.get(employeeNumber) != null){
                    agentValue.put("team", managers.get(employeeNumber));
                    managers.remove(employeeNumber);
                    for(Map<Integer, Object> a : (List<Map<Integer, Object>>)agentValue.get("team")){
                        for(Integer key : a.keySet()){
                            if(map.containsKey(key)){
                                map.remove(key);
                            }
                        }
                    }
                }else{
                    agentValue.put("team", new ArrayList<Map<Integer, Object>>());
                }
                agentKey.put(employeeNumber, agentValue);
            }
            Map<Integer, Object> manager = getAgentNode(managerNumber, map);
            if(manager == null && agentValue != null){
                map.put(employeeNumber, agentValue);
                if(managers.get(managerNumber) == null){
                    managers.put(managerNumber, new ArrayList<Map<Integer, Object>>());
                }
                ((List)managers.get(managerNumber)).add(agentKey);
            }else{
                if(manager != null && manager.get("team") != null){
                    ((List)manager.get("team")).add(agentKey);
                    if(map.containsKey(employeeNumber)){
                        map.remove(agentKey);
                    }
                }
            }
        }
        for(Object obj : map.values()){
            Map<String, Object> agent = (Map)obj;
            finalizeTeamHierarchy(agent, "", result);
//            System.out.println();
        }
        return result;
    }

    @Override
    public List<CallCenterEmployee> getCallCenterResource(boolean supervisor) {
        return jdbcRepository.getCallCenterResource(supervisor);
    }

    @Override
    public List<PhoneNumberRoutingRules> findAllRoutingRules() {
        return mongoRepository.findAllRoutingRules();
    }

    @Override
    public PhoneNumberRoutingRules getRoutingRuleByNumber(Long phoneNumber) {
        return mongoRepository.getPhoneNumberRoutingRules(phoneNumber);
    }

    @Override
    public PhoneNumberRoutingRules persistRoutingRule(PhoneNumberRoutingRules routingRules) {
        return mongoRepository.persistRoutingRules(routingRules);
    }

    @Override
    public boolean updateCallCenterEmployee(Integer supervisorId, String agentsInTeam) {
        boolean result = true;

        CallCenterEmployee supervisor = jdbcRepository.getCallCenterEmployeeByNumber(supervisorId, false);
        logger.debug("Supervisor: " + supervisor);
        logger.debug("Supervisor's Supervisor: " + supervisor.getSupervisorNumber() + " --> " + supervisor.getSupervisorName());

        List<CallCenterEmployee> currentAgents = getSupervisedAgents(supervisorId);
        logger.debug(currentAgents.toString());

        List<Integer> listCurrentAgents = new ArrayList<Integer>();
        List<Integer> listChangeAgents = new ArrayList<Integer>();
        List<Integer> listCommonAgents = new ArrayList<Integer>();

        for (CallCenterEmployee e : currentAgents) {
            listCurrentAgents.add(e.getEmployeeNumber());
        }

        String ids[] = StringUtils.commaDelimitedListToStringArray(agentsInTeam);
        for (String s : ids) {
            listChangeAgents.add(Integer.valueOf(s));
        }

        // -- remove the added supervisor id
        listCurrentAgents.remove(supervisorId);

        // -- find common list agents between the to groups
        listCommonAgents.addAll(listChangeAgents);
        listCommonAgents.retainAll(listCurrentAgents);

        // -- find new agents in the current list
        listChangeAgents.removeAll(listCommonAgents);

        // -- find existing agents that are not in the new list
        listCurrentAgents.removeAll(listCommonAgents);


        logger.debug("Supervised Agents being Removed:  " + listCurrentAgents.toString());
        logger.debug("Supervised Agents being Added:  " + listChangeAgents.toString());


        // -- Itr agents to remove and inactive the Oracle and Mongo records.  Agents are linked to supervisor's supervisor
        for (Integer toRemove : listCurrentAgents) {
            jdbcRepository.removeCallCenterEmployeeAssociation(toRemove, true);
        }

        // -- Itr agents to and create new Oracle and Mongo records.
        for (Integer toAdd : listChangeAgents) {
            jdbcRepository.addCallCenterEmployeeAssociation(toAdd, supervisorId, true);
        }

        // -- If all goes well return true
        return result;

        /***
        List<CallCenterEmployee> managedAgents = jdbcRepository.getSupervisedAgents(supervisorId);
        logger.debug(managedAgents.toString());

        CallCenterEmployee supervisor = jdbcRepository.getCallCenterEmployeeByNumber(supervisorId, false);
        logger.debug(supervisor.toString());

        List<Integer> agentIds = new ArrayList<Integer>();
        if(agentsInTeam != null && agentsInTeam.trim().length() > 0){
            String[] temp = agentsInTeam.split(",");
            for(int i=0; i<temp.length; i++){
                agentIds.add(Integer.valueOf(temp[i]));
                result = jdbcRepository.updateCallCenterEmployee(supervisorId, Integer.valueOf(temp[i])) > 0;
                if(!result){
                    break;
                }
            }
        }

        logger.debug(agentIds.toString());

        if(supervisor != null && supervisor.getSupervisorNumber() != null){
            Integer newSupervisorId = supervisor.getSupervisorNumber();
            for(CallCenterEmployee cce : managedAgents){
                if(cce.getSupervisorNumber().equals(supervisorId)
                    && agentIds.indexOf(cce.getEmployeeNumber()) == -1){
                    logger.debug("moving emp[" + cce.getEmployeeNumber() + "] from [" + supervisorId + "] to [" + newSupervisorId + "]");
                    result = jdbcRepository.updateCallCenterEmployee(newSupervisorId, cce.getEmployeeNumber()) > 0;
                }
            }
        }
        ***/
    }

    @Override
    public void processUpdateFacilityRequest(Integer facilityId) {

        List<UpdateRequest> list =  new ArrayList<UpdateRequest>();
        if(facilityId != null && facilityId > 0){
            UpdateRequest ur = new UpdateRequest();
            ur.setEntityId(Long.valueOf(facilityId));
            list.add(ur);
        }else{
            list = mongoRepository.getRequestsToUpdateFacility();
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for(UpdateRequest ur : list){
            logger.debug(ur.getEntityId() + ":::" + ur.getRequestDateTime());
            Date processDateTime = new Date();
            boolean processResult = mongoRepository.doUpdateFacility(ur);
            if(processResult){
                ur.setProcessDateTime(df.format(processDateTime));
                mongoRepository.markUpdateRequestAsProcessed(ur);
            }
        }
    }

    @Override
    public List<CommunicationTemplate> getCommunicationTemplates() {
        return mongoRepository.getCommunicationTemplates();
    }

    public Map<Integer, Object> getAgentNode(int agentId, Map<Integer, Object> map){
        Map<Integer, Object> result = null;
        Set<Integer> ids = map.keySet();
        for(Integer id : ids){
            logger.debug("ID: " + id);
            Map<Integer, Object> agentKey = (Map)map.get(id);
            logger.debug("Agent Key: " + agentKey);
            if (id.equals(agentId)) {
                result = agentKey;
                break;
            } else if (agentKey != null && agentKey.get("team") != null) {
                List<Map<Integer, Object>> agents = (List) agentKey.get("team");
                for (Map<Integer, Object> agent : agents) {
                    result = getAgentNode(agentId, agent);
                    if (result != null) {
                        break;
                    }
                }
            }
        }
        return result;
    }

    private void finalizeTeamHierarchy(Map<String, Object> agent, String tab, List<Map<String, Object>> list){
//        System.out.println(tab + agent.get("employeeNumber") + ":::" + agent.get("employeeName"));
        list.add(agent);
        if(agent != null && agent.get("team") != null){
            List<Map<Integer, Object>> agentTeam = (List)agent.get("team");
            List<Map<String, Object>> teamList = new ArrayList<Map<String, Object>>();
            for(Map<Integer, Object> at : agentTeam){
                for(Integer key : at.keySet()){
                    finalizeTeamHierarchy((Map<String, Object>) at.get(key), tab + "\t", teamList);
                }
            }
            agent.put("team", teamList);
        }
    }
}
