package com.smilebrands.callcentersupport.controllers;

import com.smilebrands.callcentersupport.domain.*;
import com.smilebrands.callcentersupport.service.CallService;
import com.smilebrands.callcentersupport.service.ConfigurationService;
import com.smilebrands.callcentersupport.service.WebRequestService;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import org.springframework.web.util.UriComponents;

import javax.servlet.http.HttpServletRequest;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * User: <PERSON><PERSON>
 * Date: 6/24/14
 */
@Controller
public class ScreenPopController extends AbstractController {

    private final static Format dateFormat = new SimpleDateFormat("EEE, MMM d, ''yy ' at ' h:mma");

    @Autowired
    private CallService callService;
    @Autowired
    private WebRequestService webRequestService;

    @Autowired
    private ConfigurationService configurationService;

    @RequestMapping(value="load-screen-pop/csr/{uuid}/{agentId}/{lang}/{numberPress}", method= RequestMethod.GET)
    public String csrScreenPop(ModelMap model,
                               @PathVariable String uuid,
                               @PathVariable Integer agentId,
                               @PathVariable String lang,
                               @PathVariable Integer numberPress,
                               @RequestParam(required = false) String sessionId,
                               HttpServletRequest request) {

        model.addAttribute("language", lang);
        model.addAttribute("languageText", (lang.equalsIgnoreCase("sp") ? "Spanish" : "English"));
        model.addAttribute("langButton", (lang.equalsIgnoreCase("sp") ? "info" : "primary"));

        model.addAttribute("type", "csr");
        model.addAttribute("numberPress", numberPress);
        model.addAttribute("numberPressText", getMenuPressToText(numberPress));

        getBasicDataForScreenPop(model, uuid, agentId, "csr");

        InboundCall call = callService.findInboundCall(uuid);

        if(call.getMainMenuNumberPresses().size() > 1){
            model.addAttribute("numberPress", StringUtils.join(call.getMainMenuNumberPresses(), ":"));
        }

        String phoneTypeText = getPhoneTypeText(call);
        if(call.getPhoneNumber() != null){
            model.addAttribute("brandName", call.getPhoneNumber().getBrandName());
            model.addAttribute("extraBrandNameInfo", getExtraBrandNameInfo(call.getPhoneNumber().getBrandName()));

            if (phoneTypeText.equalsIgnoreCase("Campaign Number")) {
                model.addAttribute("isCampaign", true);
                model.addAttribute("campaignName", call.getPhoneNumber().getCampaignName());
            }
        }else{
            model.addAttribute("brandName", "Smile Brands");
            model.addAttribute("isCampaign", true);
            String msgForNotFoundNumber = "Phone not found";
            if(call.getMainMenuNumberPress() != null){
                if(call.getMainMenuNumberPress().equals(91)){
                    msgForNotFoundNumber += " for ANI " + call.getAni();
                }else{
                    msgForNotFoundNumber += " for DNIS " + call.getDnis();
                }
            }
            model.addAttribute("campaignName", msgForNotFoundNumber);
        }
        model.addAttribute("phoneType", call.getPhoneType());
        model.addAttribute("phoneTypeText", phoneTypeText);
        model.addAttribute("isTF", (call.getPhoneType() != null && call.getPhoneType().equalsIgnoreCase("TF") ? true : false));

        if(phoneTypeText.equalsIgnoreCase("Campaign Number")){
            model.addAttribute("isCampaign", true);
            model.addAttribute("campaignName", call.getPhoneNumber() != null ? call.getPhoneNumber().getCampaignName() : "");
        }

        model.addAttribute("zipCode", call.getZipCode());


        model.addAttribute("ani", call.getAni());

        AgentCall agentCall = callService.findAgentCall(uuid);

        if (agentCall == null) {
            Integer targetFacilityId = call.getTargetFacilityId();
            callService.startAgentCall(uuid, agentId, targetFacilityId, "csr", sessionId);
        } else {
            LoadedEmployee employee = callService.findLoadedEmployee(agentCall, agentId, "csr");
            if(employee != null){
                model.addAttribute("reasonCode", employee.getReasonCode());
                model.addAttribute("resolutionCode", employee.getResolutionCode());
            }else{
                callService.updateAgentCallWithEmployeeNumber(uuid, agentId, "csr");
            }
        }

        if (call.getTargetFacilityId() != null && !call.getTargetFacilityId().equals(0)){
            Facility facility = callService.findFacilityByFacilityId(call.getTargetFacilityId());
            if(facility != null){
                facility.setAssociatedPhoneNumber(callService.findPhoneNumberByFacilityId(facility.getFacilityId(), uuid));
                logger.debug("add facility to model: {}", facility.getName() + " (" + facility.getFacilityId() + ")");
                model.addAttribute("facility", facility);
            }
        }
        getPatientInfo(call, model, call.getTargetFacilityId());
        logAction(uuid, ActionLog.SCREEN_POP_ACTION, "load csr screen pop for employee[" + agentId + "] with UUID[" + uuid + "] and number press[" + numberPress + "]");
        model.addAttribute("screenType", "csr");
        callService.doScreenPopHealthCheck(uuid, agentId, "csr", null, null);
        model.addAttribute("isOld", callService.isOldScreenPop(null, uuid, 0));

        return "csr-screen-pop";
    }

    @RequestMapping(value="load-screen-pop/psr/{uuid}/{agentId}/{lang}/{numberPress}/{targetFacilityId}", method= RequestMethod.GET)
    public String psrScreenPop(ModelMap model,
                            @PathVariable String uuid,
                            @PathVariable Integer agentId,
                            @PathVariable String lang,
                            @PathVariable Integer numberPress,
                            @PathVariable Integer targetFacilityId,
                            @RequestParam(required = false) String sessionId,
                            HttpServletRequest request) {

        InboundCall call = null;
        logger.debug("load psr screen pop for employee[" + agentId + "] with UUID[" + uuid + "]");
        logAction(uuid, ActionLog.SCREEN_POP_ACTION, "load psr screen pop for employee[" + agentId + "] with UUID[" + uuid + "] and facility ID[" + targetFacilityId + "]");
        if(uuid.equals("abc123")){
            uuid = "test-" + UUID.randomUUID().toString();
            call = new InboundCall();
            call.setAni(9493315916L);
            call.setUuid(uuid);
            call.setDnis(7148245005L);
            call.setSessionId("123");

            PhoneNumber phoneNumber = callService.findPhoneNumberByFacilityId(targetFacilityId, uuid);
            /*call.setMainMenuNumberPress(92);
            phoneNumber.setPhoneType("CM");
            phoneNumber.setCampaignName("Sample Campaign");*/

            call.setPhoneNumber(phoneNumber);
            call.setTargetFacilityId(targetFacilityId);
            call.setMatched(targetFacilityId != null && !targetFacilityId.equals(0));
            callService.updateInboundCall(call);
            String newUrl = "/load-screen-pop/psr/" + uuid + "/" + agentId + "/" + lang + "/" + numberPress + "/" + targetFacilityId;
            return "redirect:" + newUrl;
        }else{
            call = callService.findInboundCall(uuid);
        }
        model.addAttribute("language", lang);
        model.addAttribute("languageText", (lang.equalsIgnoreCase("sp") ? "Spanish" : "English"));
        model.addAttribute("langButton", (lang.equalsIgnoreCase("sp") ? "info" : "primary"));

        if(!call.getMainMenuNumberPressOverride().equals(-1)){
            numberPress = call.getMainMenuNumberPressOverride();
        }
        model.addAttribute("type", "psr");
        model.addAttribute("numberPress", numberPress);
        model.addAttribute("numberPressText", getMenuPressToText(numberPress));
        if(call.getMainMenuNumberPresses().size() > 1){
            model.addAttribute("numberPress", StringUtils.join(call.getMainMenuNumberPresses(), ":"));
        }

        getBasicDataForScreenPop(model, uuid, agentId, "psr");

        model.addAttribute("phoneType", call.getPhoneType());

        model.addAttribute("zipCode", call.getZipCode());
        String phoneTypeText = getPhoneTypeText(call);
        if(call.getPhoneNumber() != null){
            model.addAttribute("brandName", call.getPhoneNumber().getBrandName());
            model.addAttribute("extraBrandNameInfo", getExtraBrandNameInfo(call.getPhoneNumber().getBrandName()));

            if (phoneTypeText.equalsIgnoreCase("Campaign Number")) {
                model.addAttribute("isCampaign", true);
                model.addAttribute("campaignName", call.getPhoneNumber().getCampaignName());
            }
        }else{
            model.addAttribute("brandName", "Smile Brands");
            model.addAttribute("isCampaign", true);
            String msgForNotFoundNumber = "Phone not found";
            if(call.getMainMenuNumberPress() != null){
                if(call.getMainMenuNumberPress().equals(91)){
                    msgForNotFoundNumber += " for ANI " + call.getAni();
                }else{
                    msgForNotFoundNumber += " for DNIS " + call.getDnis();
                }
            }
            model.addAttribute("campaignName", msgForNotFoundNumber);
        }
        model.addAttribute("phoneTypeText", phoneTypeText);

        model.addAttribute("screenType", "psr");

        model.addAttribute("ani", call.getAni());

        AgentCall agentCall = callService.findAgentCall(uuid);

        if (agentCall == null) {
            targetFacilityId = call.getTargetFacilityId();
            agentCall = callService.startAgentCall(uuid, agentId, targetFacilityId, "psr", sessionId);
        } else {
            boolean potentialRefresh = false;
            if(targetFacilityId != null && !targetFacilityId.equals(agentCall.getCurrentFacilityId())){
                callService.updateAgentCallWithFacility(uuid, targetFacilityId);
            }else if(targetFacilityId != null && targetFacilityId.equals(agentCall.getCurrentFacilityId())){
                potentialRefresh = true;
            }
            LoadedEmployee loadedEmployee = callService.findLoadedEmployee(agentCall, agentId, "psr");
            if(loadedEmployee == null){
                callService.updateAgentCallWithEmployeeNumber(uuid, agentId, "psr");
            }else if(potentialRefresh){
                logger.debug("potential page refresh with UUID [" + uuid + "] and facility ID[" + targetFacilityId + "]");
                logAction(uuid, ActionLog.SCREEN_POP_ACTION, "potential page refresh with UUID [" + uuid + "], facility ID[" + targetFacilityId + "] and employee[" + agentId + "]");
            }
        }
        model.addAttribute("isOld", callService.isOldScreenPop(null, uuid, 0));

        Facility facility = callService.findFacilityByFacilityId(targetFacilityId);
        if(facility != null){
            facility.setAssociatedPhoneNumber(callService.findPhoneNumberByFacilityId(facility.getFacilityId(), uuid));
            model.addAttribute("facility", facility);
            model.addAttribute("libertySupported", facility.isLibertySupported());

            populateNeighboringOffice(
                    model,
                    facility,
                    request,
                    uuid,
                    agentId,
                    lang,
                    call.isWireless(),
                    numberPress,
                    "psr",
                    call.getAni(),
                    agentCall.getCallCenterName(),
                    (call.getPhoneType() != null && call.getPhoneType().equalsIgnoreCase("TF") ? true : false));

            getPatientInfo(call, model, facility.getFacilityId());
            callService.doScreenPopHealthCheck(uuid, agentId, "psr", null, null);

            return "psr-screen-pop";
        }else{
            callService.doScreenPopHealthCheck(uuid, agentId, "psr", null, null);

            return "tool-screen-pop";
        }
    }

    @RequestMapping(value="load-screen-pop/wr/{uuid}/{agentId}/{lang}/{facilityId}", method= RequestMethod.GET)
    public String wrScreenPop(ModelMap model,
                               @PathVariable String uuid,
                               @PathVariable Integer agentId,
                               @PathVariable String lang,
                               @PathVariable Integer facilityId,
                               @RequestParam(required = false) String sessionId,
                               HttpServletRequest request) {

        model.addAttribute("language", lang);
        model.addAttribute("languageText", (lang.equalsIgnoreCase("sp") ? "Spanish" : "English"));
        model.addAttribute("langButton", (lang.equalsIgnoreCase("sp") ? "info" : "primary"));


        getBasicDataForScreenPop(model, uuid, agentId, "wr");

        WebRequest wr = webRequestService.findWebRequest(uuid);

        Date appDate = VelocityUtils.parseDate(wr.getAppointmentDate(), "yyyy-MM-dd");
        wr.setAppointmentDate(VelocityUtils.getDateAsString(appDate, "EEEE MM-dd-yyyy"));

        model.addAttribute("webRequest", wr);
        model.addAttribute("screenType", "wr");
        if(facilityId == null || facilityId <= 0){
            facilityId = wr.getOffice();
        }

        AgentCall agentCall = callService.findAgentCall(uuid);

        if (agentCall == null) {
            agentCall = callService.startAgentCall(uuid, agentId, facilityId, "wr", sessionId);
        } else {
            boolean potentialRefresh = false;
            if(facilityId != null && !facilityId.equals(agentCall.getCurrentFacilityId())){
                callService.updateAgentCallWithFacility(uuid, facilityId);
            }else if(facilityId != null && facilityId.equals(agentCall.getCurrentFacilityId())){
                potentialRefresh = true;
            }
            LoadedEmployee loadedEmployee = callService.findLoadedEmployee(agentCall, agentId, "wr");
            if(loadedEmployee == null){
                callService.updateAgentCallWithEmployeeNumber(uuid, agentId, "wr");
            }else if(potentialRefresh){
                logger.debug("potential page refresh with UUID [" + uuid + "] and facility ID[" + facilityId + "]");
                logAction(uuid, ActionLog.SCREEN_POP_ACTION, "potential page refresh with UUID [" + uuid + "], facility ID[" + facilityId + "] and employee[" + agentId + "]");
            }
        }

        if (facilityId != null && !facilityId.equals(0)){
            Facility facility = callService.findFacilityByFacilityId(facilityId);

            if (facility != null) {
                facility.setAssociatedPhoneNumber(callService.findPhoneNumberByFacilityId(facility.getFacilityId(), uuid));
                logger.debug("add facility to model: {}", facility.getName() + " (" + facility.getFacilityId() + ")");
                model.addAttribute("facility", facility);
                model.addAttribute("brandName", facility.getBrandName());

                populateNeighboringOffice(
                        model,
                        facility,
                        request,
                        uuid,
                        agentId,
                        lang,
                        false,
                        0,
                        "wr",
                        wr.getPhone(),
                        agentCall.getCallCenterName(),
                        false);
            }
        }
        callService.doScreenPopHealthCheck(uuid, agentId, "wr", null, null);
        model.addAttribute("isOld", callService.isOldScreenPop(null, uuid, 0));

        return "wr-screen-pop";
    }

    private String getMenuPressToText(Integer number) {
        switch (number) {
            case 0: return "The Caller pressed 0.";
            case 1: return "The Caller would like to schedule a new appointment.";
            case 2: return "The Caller would like to update an existing appointment.";
            case 3: return "The Caller would like to confirm an existing appointment, find office information.";
            case 4: return "The Caller has questions about billing, insurance coverage or payment information.";
            case 5: return "The Caller is not patient or has other inquiries.";
            case 6: return "The Caller requested to repeat the main menu.";
            case 7: return "The Caller pressed 7.";
            case 8: return "The Caller pressed 8.";
            case 9: return "The Caller pressed 9.";
            case 90: return "The Caller pressed an invalid option of * or #.";
            case 91: return "Office Transfer to the Call Center";
            case 92: return "Campaign Call Direct to PSR Queue";
            default: return "A menu option was not detected.";
        }
    }

    private String getPhoneTypeText(InboundCall call){
        String phoneTypeText = "Main Line Number";
        String phoneType = call.getPhoneType() != null ? call.getPhoneType()
                            : (call.getPhoneNumber() != null ? call.getPhoneNumber().getPhoneType() : null);
        if(phoneType != null){
            if(phoneType.equalsIgnoreCase("TF")){
                phoneTypeText = "Toll Free Number";
            }else if(phoneType.equalsIgnoreCase("CM")){
                phoneTypeText = "Campaign Number";
            }else if(phoneType.equalsIgnoreCase("CB")){
                phoneTypeText = "Call Center Back Line";
            }
        }
        return phoneTypeText;
    }

    private void getPatientInfo(InboundCall call, ModelMap model, Integer facilityId){
        if(call.getPatient() != null && call.getPatient().getPhoneNumber() != null){
            logger.debug("search appointment by Phone Number[" + call.getPatient().getPhoneNumber() + " with Facility ID[" + facilityId + "]");
            List<Appointment> appointments = callService.findAppointmentsByPatientPhoneNumber(call.getPatient().getPhoneNumber(), facilityId, null);
            if(appointments != null && appointments.size() > 0){
                Map<String, Map<String, Object>> patientMap = new HashMap<String, Map<String, Object>>();
                for(Appointment appointment : appointments){
                    String key = appointment.getPatient().getClinicId() + "-" + appointment.getPatient().getPatientId();
                    Map<String, Object> appointmentMap = patientMap.get(key);
                    if(appointmentMap == null){
                        appointmentMap = new HashMap<String, Object>();
                        appointmentMap.put("count", 1);
                        List<Appointment> patientAppointments = new ArrayList<Appointment>();
                        patientAppointments.add(appointment);
                        appointmentMap.put("appointments", patientAppointments);
                        patientMap.put(key, appointmentMap);
                    }else{
                        appointmentMap.put("count", (Integer)appointmentMap.get("count") + 1);
                        ((ArrayList)appointmentMap.get("appointments")).add(appointment);
                    }
                }
                Patient primaryPatient = appointments.get(0).getPatient();
                model.addAttribute("patientCount", patientMap.size());
                model.addAttribute("primaryPatient", primaryPatient);
                model.addAttribute("primaryAppointmentCount", patientMap.get(primaryPatient.getClinicId() + "-" + primaryPatient.getPatientId()).get("count"));
                model.addAttribute("nextAppointmentDateTime", appointments.get(0).getFormattedDateTime());
                model.addAttribute("facilityName", appointments.get(0).getFacility().getName());
                model.addAttribute("appointmentPhone", call.getPatient().getPhoneNumber());
                model.addAttribute("appointmentPhoneTxt", VelocityUtils.phoneFormatter(call.getPatient().getPhoneNumber().toString()));

            }else{
                model.addAttribute("patientCount", 0);
            }
        }else{
            model.addAttribute("patientCount", 0);
        }
        if(call.getAni() != null){
            AgentCall returningCall = callService.findAgentCallByAni(call.getUuid(), call.getAni());
            if(returningCall != null){
                model.addAttribute("previousCallOn", dateFormat.format(returningCall.getScreenPopOpenDateTime()));
            }
        }
    }

    public String getExtraBrandNameInfo(String brandName){
        String result = "";
        if(brandName != null && brandName.equalsIgnoreCase("Newport Dental")){
            result += " (part of the BrightNow! Dental network)";
        }
        return result;
    }

    private void getBasicDataForScreenPop(ModelMap model, String uuid, Integer agentId, String screenType){
        screenType = screenType.toUpperCase();
        model.addAttribute("uniqueCallId", uuid);
        model.addAttribute("employeeNumber", agentId);

        model.addAttribute("resolutionCodeList", configurationService.getCallResolutionCodes(screenType));
        model.addAttribute("reasonList", configurationService.getCallReasonCodes(screenType));
        model.addAttribute("facilityList", callService.getDistinctFieldFromCollection("facility", "_id"));
        model.addAttribute("templateList", configurationService.getCommunicationTemplates());
        model.addAttribute("openBookFacilities", callService.getOpenBookSupportedFacilities());
    }

    private void populateNeighboringOffice(ModelMap model, Facility facility, HttpServletRequest request, String uuid, Integer agentId, String lang, Boolean isMobile, Integer numberPress, String screenType, Long ani, String callCenterName, Boolean isTF){
        // -- Split the Neighboring Facilities into a left and right group
        List<Facility> left = new ArrayList<Facility>();
        List<Facility> right = new ArrayList<Facility>();

        StringBuilder url = new StringBuilder();
        CallCenter callCenter = callService.getCallCenterByName(callCenterName);
        String schedulerUrl = null;
        boolean hasSchedulerUrl = false;

        if (facility.isLibertySupported()) {
            if(callCenter != null && callCenter.getLibertyUrl() != null){
                schedulerUrl = callCenter.getLibertyUrl() + "/";
            }else{
                schedulerUrl = "http://liberty.bnd.corp/scheduler/";
            }
            url.append(schedulerUrl);
            model.addAttribute("schedulerName", "Liberty");
            hasSchedulerUrl = true;
            logger.debug("Facility [" + facility.getName() + " - " + facility.getFacilityId()
                    + " is Liberty supported.  Providing link to Liberty Scheduler using: " + url);
        } else if (facility.isOpenBookSchedulingEnabled()) {
            if(callCenter != null && callCenter.getOpenBookUrl() != null){
                schedulerUrl = callCenter.getOpenBookUrl() + "/";
            }else{
                schedulerUrl = "https://openbook.smilebrands.com/scheduling/ui/";
            }
            url.append(schedulerUrl);
            model.addAttribute("schedulerName", "Open Book");
            hasSchedulerUrl = true;
            logger.debug("Facility [" + facility.getName() + " - " + facility.getFacilityId()
                    + " is OpenBook supported.  Providing link to Liberty Scheduler using: " + url);
        }
        model.addAttribute("hasSchedulerUrl", hasSchedulerUrl);

        if(hasSchedulerUrl){
            url.append("?siteId=" + facility.getFacilityId());
            url.append("&agentId=" + agentId);
            url.append("&callerId=" + ani);
            url.append("&lang=" + lang);
            url.append("&mobile=" + (isMobile ? 1 : 0));
            url.append("&uuid=" + uuid);
            url.append("&isTF=" + (isTF ? 1 : 0));

            model.addAttribute("schedulerUrl", url);
        }

        boolean primary = true;
        for (Facility fac : facility.getNeighborFacilities()) {
            if(screenType.equalsIgnoreCase("psr")){
                UriComponents ucb = ServletUriComponentsBuilder.fromContextPath(request)
                        .path("/load-screen-pop/")
                        .path("psr/")
                        .path(uuid + "/")
                        .path(agentId + "/")
                        .path(lang + "/")
                        .path(numberPress + "/")
                        .path(fac.getFacilityId() + "")
                        .build();

                fac.setScreenPopUrl(ucb.toUriString());
            }else if(screenType.equalsIgnoreCase("wr")){
                UriComponents ucb = ServletUriComponentsBuilder.fromContextPath(request)
                        .path("/load-screen-pop/")
                        .path("wr/")
                        .path(uuid + "/")
                        .path(agentId + "/")
                        .path(lang + "/")
                        .path(fac.getFacilityId() + "")
                        .build();

                fac.setScreenPopUrl(ucb.toUriString());
            }
            Facility detailFacility = callService.findFacilityByFacilityId(fac.getFacilityId());
            if(detailFacility != null){
                fac.setEmployees(detailFacility.getEmployees());
            }

            if (primary) {
                left.add(fac);
                primary = false;
            } else {
                right.add(fac);
                primary = true;
            }
        }

        model.addAttribute("leftNeighboringFacilities", left);
        model.addAttribute("rightNeighboringFacilities", right);
    }
}
