/* Special Combo */

.cke_skin_v2 .cke_rcombo
{
	display: inline-block;
	margin-left: 2px;
	margin-right: 2px;
	margin-top: 2px;
	vertical-align: top;
}

.cke_skin_v2 .cke_browser_ie .cke_rcombo
{
	#display: inline;
}

.cke_skin_v2 .cke_rcombopanel
{
	border: 1px solid #316ac5;
	-moz-border-radius-topleft: 0;
	-webkit-border-top-left-radius: 0;
	border-top-left-radius: 0;
	/*margin-left: 1px;*/
	/*_margin-left: 0;*/
}

.cke_skin_v2 .cke_rcombo a
{
	display: inline-block;
	float: left;

	filter: alpha(opacity=70); /* IE */
	opacity: 0.70; /* Safari, Opera and Mozilla */
}

.cke_skin_v2 .cke_rtl .cke_rcombo a
{
	float: right;
}

.cke_skin_v2 .cke_hc .cke_rcombo a
{
	filter: alpha(opacity=100); /* IE */
	opacity: 1.0; /* <PERSON><PERSON>, Opera and Mozilla */
}

.cke_skin_v2 .cke_rcombo .cke_label
{
	float: left;
	line-height: 20px;
	line-height: 22px\9;
	height: 22px;
	padding-left: 4px;
	padding-right: 5px;
	filter: alpha(opacity=70); /* IE */
	opacity: 0.70; /* Safari, Opera and Mozilla */
	background-color: #f1f1e3;	/* Because of IE6+ClearType */
	cursor: default;
}

.cke_skin_v2 .cke_rtl .cke_rcombo .cke_label
{
	float: right;
	padding-right: 4px;
	padding-left: 5px;
}

.cke_skin_v2 .cke_hc .cke_rcombo .cke_label
{
	filter: alpha(opacity=100);
	opacity: 1.0;
}

.cke_skin_v2 .cke_rcombo .cke_inline_label
{
	color: #fff;
}

.cke_skin_v2 .cke_rcombo .cke_text
{
	border: 1px solid #8f8f73;
	background-color: #fff;
	height: 12px;
	width:60px;
	padding-top: 4px;
	padding-bottom: 4px;
	padding-left: 5px;
	padding-right: 5px;
	text-overflow: ellipsis;
	overflow: hidden;
	display: inline-block;
	vertical-align: top;
	cursor: default;
}

.cke_skin_v2 .cke_ltr .cke_rcombo .cke_text
{
	-moz-border-radius-topleft: 3px;
	-webkit-border-top-left-radius: 3px;
	border-top-left-radius: 3px;
	-moz-border-radius-bottomleft: 3px;
	-webkit-border-bottom-left-radius: 3px;
	border-bottom-left-radius: 3px;
}

.cke_skin_v2 .cke_rtl .cke_rcombo .cke_text
{
	-moz-border-radius-topright: 3px;
	-webkit-border-top-right-radius: 3px;
	border-top-right-radius: 3px;
	-moz-border-radius-bottomright: 3px;
	-webkit-border-bottom-right-radius: 3px;
	border-bottom-right-radius: 3px;
}

/* Fix for IE height */
.cke_skin_v2 .cke_browser_iequirks .cke_rcombo .cke_text
{
	height: 22px;
}

.cke_skin_v2 .cke_rcombo .cke_openbutton
{
	display: inline-block;
	border-top: 1px solid #8f8f73;
	border-bottom: 1px solid #8f8f73;
}

.cke_skin_v2 .cke_rcombo .cke_openbutton .cke_icon
{
	display: inline-block;
	background-position: 5px -715px;
	background-image: url(images/sprites.png);
	_background-image: url(images/sprites_ie6.png);
	background-repeat: no-repeat;

	width: 14px;
	height: 20px;
}

/* IE with zoom != 100% will distort the icons otherwise #4821 */
.cke_skin_v2 .cke_browser_ie .cke_rcombo .cke_openbutton .cke_icon
{
	filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod='scale');
}
.cke_skin_v2 .cke_browser_ie6 .cke_rcombo .cke_openbutton .cke_icon
{
	filter: ;
}
.cke_skin_v2 .cke_hc .cke_rcombo .cke_openbutton .cke_icon
{
	background: none;
	filter: ;
}

.cke_skin_v2 .cke_ltr .cke_rcombo .cke_openbutton
{
    border-right: 1px solid #8f8f73;
	-moz-border-radius-topright: 3px;
	-webkit-border-top-right-radius: 3px;
	border-top-right-radius: 3px;
	-moz-border-radius-bottomright: 3px;
	-webkit-border-bottom-right-radius: 3px;
	border-bottom-right-radius: 3px;
}

.cke_skin_v2 .cke_rtl .cke_rcombo .cke_openbutton
{
    border-left: 1px solid #8f8f73;
	-moz-border-radius-topleft: 3px;
	-webkit-border-top-left-radius: 3px;
	border-top-left-radius: 3px;
	-moz-border-radius-bottomleft: 3px;
	-webkit-border-bottom-left-radius: 3px;
	border-bottom-left-radius: 3px;
}

.cke_skin_v2 .cke_rcombo .cke_off a:hover,
.cke_skin_v2 .cke_rcombo .cke_off a:focus,
.cke_skin_v2 .cke_rcombo .cke_off a:active,
.cke_skin_v2 .cke_rcombo .cke_on a
{
	filter: alpha(opacity=100); /* IE */
	opacity: 1; /* Safari, Opera and Mozilla */
}

.cke_skin_v2 .cke_rcombo .cke_off a:hover .cke_text,
.cke_skin_v2 .cke_rcombo .cke_off a:focus .cke_text,
.cke_skin_v2 .cke_rcombo .cke_off a:active .cke_text,
.cke_skin_v2 .cke_rcombo .cke_on .cke_text
{
	border-color: #316ac5;
}

.cke_skin_v2 .cke_rcombo .cke_off a:hover .cke_openbutton,
.cke_skin_v2 .cke_rcombo .cke_off a:focus .cke_openbutton,
.cke_skin_v2 .cke_rcombo .cke_off a:active .cke_openbutton,
.cke_skin_v2 .cke_rcombo .cke_on .cke_openbutton
{
	border-color: #316ac5;
	background-color: #dff1ff;
}

.cke_skin_v2 .cke_rcombo .cke_on .cke_text
{
	-moz-border-radius-bottomleft: 0px;
	-webkit-border-bottom-left-radius: 0px;
	border-bottom-left-radius: 0px;
}

.cke_skin_v2 .cke_rcombo .cke_on .cke_openbutton
{
	-moz-border-radius-bottomright: 0px;
	-webkit-border-bottom-right-radius: 0px;
	border-bottom-right-radius: 0px;
}

.cke_skin_v2 .cke_rcombo .cke_disabled .cke_label
{
	filter: alpha(opacity=30); /* IE */
	opacity: 0.3; /* Safari, Opera and Mozilla */
}

.cke_skin_v2 .cke_hc .cke_rcombo .cke_disabled .cke_label
{
	filter: alpha(opacity=70);
	opacity: 0.7;
}

.cke_skin_v2 .cke_rcombo .cke_disabled .cke_text,
.cke_skin_v2 .cke_rcombo .cke_disabled .cke_openbutton
{
	filter: alpha(opacity=50); /* IE */
	opacity: 0.5; /* Safari, Opera and Mozilla */
}

/* IE with zoom != 100% will distort the icons otherwise #4821 */
.cke_skin_v2 .cke_browser_ie .cke_rcombo .cke_disabled .cke_openbutton
{
	filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod='scale'), alpha(opacity=50);
}
.cke_skin_v2 .cke_browser_ie6 .cke_rcombo .cke_disabled .cke_openbutton
{
	filter: alpha(opacity=50);
}

.cke_skin_v2 .cke_hc .cke_rcombo .cke_disabled .cke_text,
.cke_skin_v2 .cke_hc .cke_rcombo .cke_disabled .cke_openbutton
{
	filter: alpha(opacity=80);
	opacity: 0.8;
}

.cke_skin_v2 .cke_rcombo .cke_disabled .cke_text
{
	color: #fff;
}

/* Firefox 2 & WebKit Section */

.cke_skin_v2 .cke_browser_gecko18 .cke_rcombo,
.cke_skin_v2 .cke_browser_gecko18 .cke_rcombo .cke_label,
.cke_skin_v2 .cke_browser_gecko18 .cke_rcombo .cke_text,
.cke_skin_v2 .cke_browser_gecko18 .cke_rcombo .cke_openbutton,
.cke_skin_v2 .cke_browser_webkit .cke_rcombo .cke_label,
.cke_skin_v2 .cke_browser_webkit .cke_rcombo .cke_text,
.cke_skin_v2 .cke_browser_webkit .cke_rcombo .cke_openbutton
{
	display: block;
	float: left;
}

.cke_skin_v2 .cke_browser_gecko18 .cke_rtl .cke_rcombo,
.cke_skin_v2 .cke_browser_gecko18 .cke_rtl .cke_rcombo .cke_label,
.cke_skin_v2 .cke_browser_gecko18 .cke_rtl .cke_rcombo .cke_text,
.cke_skin_v2 .cke_browser_gecko18 .cke_rtl .cke_rcombo .cke_openbutton,
.cke_skin_v2 .cke_browser_webkit .cke_rtl .cke_rcombo .cke_label,
.cke_skin_v2 .cke_browser_webkit .cke_rtl .cke_rcombo .cke_text,
.cke_skin_v2 .cke_browser_webkit .cke_rtl .cke_rcombo .cke_openbutton
{
	float: right;
}

/*** IE ***/

.cke_skin_v2 .cke_browser_ie .cke_rtl .cke_rcombo,
.cke_skin_v2 .cke_browser_ie .cke_rtl .cke_rcombo a,
.cke_skin_v2 .cke_browser_ie .cke_rtl .cke_rcombo .cke_label
{
	float: none;
}

.cke_skin_v2 .cke_browser_iequirks .cke_rcombo .cke_openbutton
{
	height: 22px;
}

.cke_skin_v2 .cke_rtl .cke_rcombo .cke_font .cke_text,
.cke_skin_v2 .cke_rtl .cke_rcombo .cke_fontSize .cke_text
{
	direction: ltr;
}
