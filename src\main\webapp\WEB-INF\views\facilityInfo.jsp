<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<%@ taglib uri="../customTag.tld" prefix="custom" %>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Smile Brands Call Center</title>
    <link type="text/css" rel="stylesheet" media="all" href="style/style.css" />
    <link type="text/css" href="style/redmond/jquery-ui-1.8.20.custom.css" rel="stylesheet" />
    <script type="text/javascript" src="script/jquery-1.7.2.min.js"></script>
    <script type="text/javascript" src="script/jquery-ui-1.8.20.custom.min.js"></script>
    <script type="text/javascript" src="script/actions.js"></script>
</head>

<c:choose>
    <c:when test="${!empty param.lang && param.lang == 'sp'}">
        <body class="body-background-sp">
    </c:when>
    <c:otherwise>
        <body class="body-background-en">
    </c:otherwise>
</c:choose>

<div id="resolution_code_dialog" title="Set resolution for your call">
    <div>
        <div style="float:left">
            What is the reason for the call?
        </div>
        <div style="text-align:left; padding: 20 20 15 10;">
            <select id="reasonOptions">
                 <c:forEach var="reason" items="${reasonList}">
                     <option value="${reason.code}"> ${reason.description}</option>
                 </c:forEach>
            </select>
        </div>
    </div>
     <div style="height:10px;border-top: 1px solid #bdbdbd;"></div>
     <div>
        <div style="float:left">
            What is the call resolution?
        </div>
        <div style="text-align:left; padding: 20 20 10 10;">
            <select id="resolutionOptions">
                <c:forEach var="resolutionCode" items="${resolutionCodeList}">
                    <option value="${resolutionCode.code}"> ${resolutionCode.description}</option>
                </c:forEach>
            </select>
        </div>
     </div>
    <div class="clear"></div>
</div> <!-- /dialog -->
<div id="dialog_box">
    <div id='dialog_message'></div>
</div> <!-- /dialog box -->
<div class="main">
    <div id="parentTopDiv">
        <div class="block top fr search-form">
            <input id="resolutionBtn" type="submit" value="Set Resolution"/>
        </div>

        <div class="block top fr" style="background:#fff;border-top:0px"></div>
        <div class="block top fr">
            <div class="actions">
                <p class="fl linked" id="patientName">Patient Not Set</p>
                <ul class="actions-list fr">
                    <li><a href="#" class="link"></a></li>
                    <li><a href="#" class="email"></a></li>
                    <li><a href="#" class="phone"></a></li>
                </ul>
            </div>

            <div class="link-container" style="display: none;">
                <form class="search-form">
                    <input id="pid" type="text" name="name" placeholder="Enter Patient ID"/>
                    <input id="cancelLinkPatient" type="submit" value="Cancel"/>
                    <input id="linkQsiPatient" type="submit" value="Submit"/>
                </form>
            </div>

            <div class="phone-container" style="display: none;">
                <form class="search-form">
                    <input id="patientPhone" type="text" name="name" placeholder="Enter Patient Phone"/>
                    <input id="cancelPatientPhone" type="submit" value="Cancel"/>
                    <input id="submitPatientPhone" type="submit" value="Submit"/>
                </form>
            </div>

            <div class="email-container" style="display: none;">
                <form class="email-form">
                    <div class="email-div"><span class="email-label email-padding">Email Address:</span><span><input id="patientEmail" type="text" name="patientEmail" placeholder="Enter Patient Email"/></span></div>
                    <div class="email-div"><span class="email-label">Appointment Date:</span><input id="datepicker" type="text" name="datepicker" placeholder="Select Appt. Date"/></div>
                    <div class="email-div"><span class="email-label">Appointment Time:</span>
                        <select id="apptTime" class="time">
                            <option value="7:00am">7:00am</option>
                            <option value="7:10am">7:10am</option>
                            <option value="7:20am">7:20am</option>
                            <option value="7:30am">7:30am</option>
                            <option value="7:40am">7:40am</option>
                            <option value="7:50am">7:50am</option>
                            <option value="8:00am">8:00am</option>
                            <option value="8:10am">8:10am</option>
                            <option value="8:20am">8:20am</option>
                            <option value="8:30am">8:30am</option>
                            <option value="8:40am">8:40am</option>
                            <option value="8:50am">8:50am</option>
                            <option value="9:00am">9:00am</option>
                            <option value="9:10am">9:10am</option>
                            <option value="9:20am">9:20am</option>
                            <option value="9:30am">9:30am</option>
                            <option value="9:40am">9:40am</option>
                            <option value="9:50am">9:50am</option>
                            <option value="10:00am">10:00am</option>
                            <option value="10:10am">10:10am</option>
                            <option value="10:20am">10:20am</option>
                            <option value="10:30am">10:30am</option>
                            <option value="10:40am">10:40am</option>
                            <option value="10:50am">10:50am</option>
                            <option value="11:00am">11:00am</option>
                            <option value="11:10am">11:10am</option>
                            <option value="11:20am">11:20am</option>
                            <option value="11:30am">11:30am</option>
                            <option value="11:40am">11:40am</option>
                            <option value="11:50am">11:50am</option>
                            <option value="12:00pm">12:00pm</option>
                            <option value="12:10pm">12:10pm</option>
                            <option value="12:20pm">12:20pm</option>
                            <option value="12:30pm">12:30pm</option>
                            <option value="12:40pm">12:40pm</option>
                            <option value="12:50pm">12:50pm</option>
                            <option value="1:00pm">1:00pm</option>
                            <option value="1:10pm">1:10pm</option>
                            <option value="1:20pm">1:20pm</option>
                            <option value="1:30pm">1:30pm</option>
                            <option value="1:40pm">1:40pm</option>
                            <option value="1:50pm">1:50pm</option>
                            <option value="2:00pm">2:00pm</option>
                            <option value="2:10pm">2:10pm</option>
                            <option value="2:20pm">2:20pm</option>
                            <option value="2:30pm">2:30pm</option>
                            <option value="2:40pm">2:40pm</option>
                            <option value="2:50pm">2:50pm</option>
                            <option value="3:00pm">3:00pm</option>
                            <option value="3:10pm">3:10pm</option>
                            <option value="3:20pm">3:20pm</option>
                            <option value="3:30pm">3:30pm</option>
                            <option value="3:40pm">3:40pm</option>
                            <option value="3:50pm">3:50pm</option>
                            <option value="4:00pm">4:00pm</option>
                            <option value="4:10pm">4:10pm</option>
                            <option value="4:20pm">4:20pm</option>
                            <option value="4:30pm">4:30pm</option>
                            <option value="4:40pm">4:40pm</option>
                            <option value="4:50pm">4:50pm</option>
                            <option value="5:00pm">5:00pm</option>
                            <option value="5:10pm">5:10pm</option>
                            <option value="5:20pm">5:20pm</option>
                            <option value="5:30pm">5:30pm</option>
                            <option value="5:40pm">5:40pm</option>
                            <option value="5:50pm">5:50pm</option>
                            <option value="6:00pm">6:00pm</option>
                            <option value="6:10pm">6:00pm</option>
                            <option value="6:20pm">6:20pm</option>
                            <option value="6:30pm">6:30pm</option>
                            <option value="6:40pm">6:40pm</option>
                            <option value="6:50pm">6:50pm</option>
                            <option value="7:00pm">7:00pm</option>
                        </select>
                    </div>
                    <input id="cancelEmailPatient" type="submit" value="Cancel"/>
                    <input id="emailPatient" type="submit" value="Submit"/>
                </form>
            </div>
        </div>

        <c:if test="${facilityInfo.libertySupported == true}">
            <div class="block top fr" style="background:#fff;border-top:0px"></div>
            <div class="block top fr" style="background:#fff;border-top:0px; padding-right: 0px;">
                <div class="lib-btn" style="border-radius: 5px; margin-top: -5px;">
                    <em>
                        <button class="x-btn-center" type="button" role="button" style="border: 0px none;"
                                onclick=window.open('${schedulerUrl}')>
                            <span class="x-btn-inner">Liberty Scheduler</span>
                        </button>
                    </em>
                </div>

            </div>
        </c:if>

        <c:choose>
            <c:when test="${!empty param.lang && param.lang == 'sp'}">
                <h1 class="title">Spanish Call</h1>
            </c:when>
            <c:otherwise>
                <h1 class="title">English Call</h1>
            </c:otherwise>
        </c:choose>

    </div> <!-- /parentTopDiv-->
    <h4 class="group">&nbsp;</h4>

    <div class="block">
        <div class="left fl">
            <ul class="info-list">
                <li>
                    <span class="label fl">Site name:</span>
                    <span class="content title">${facilityInfo.name}
                        <a target="_new" href="http://www.brightnow.com/locations/dental-office/office/office/${facilityInfo.facilityId}/map"><img class="map-link" src="images/google-map-logo2.png"></a>
                    </span>
                </li>
                <li>
                    <span class="label fl">Address:</span>
                    <span class="content ">${facilityInfo.address}. ${facilityInfo.city}, ${facilityInfo.state} ${facilityInfo.zip}</span>
                </li>

                <c:choose>
                    <c:when test="${!empty param.lang && param.lang == 'sp'}">
                        <li>
                            <span class="label fl">Phone:</span>
                            <span class="content phone-sp"><custom:fmtPhone phone="${facilityInfo.phoneNumber}"/></span>
                        </li>
                        <li>
                            <span class="label fl">Fax:</span>
                            <span class="content phone-sp"><custom:fmtPhone phone="${facilityInfo.faxNumber}"/></span>
                        </li>
                    </c:when>
                    <c:otherwise>
                        <li>
                            <span class="label fl">Phone:</span>
                            <span class="content phone"><custom:fmtPhone phone="${facilityInfo.phoneNumber}"/></span>
                        </li>
                        <li>
                            <span class="label fl">Fax:</span>
                            <span class="content phone"><custom:fmtPhone phone="${facilityInfo.faxNumber}"/></span>
                        </li>
                    </c:otherwise>
                </c:choose>

                <li>
                    <span class="label fl">SiteID:</span>
                    <span id="fid" class="content">${facilityInfo.facilityId}</span>
                </li>
                <li>
                    <span class="label fl">QSI Name:</span>
                    <span class="content">${facilityInfo.qsiName}</span>
                </li>
                <!--li>
                    <span class="label fl">Languages:</span>
                    <span class="content green">English; Spanish</span>
                </li-->
            </ul>
        </div><!--/left-->
        <div class="right fr">
            <ul class="info-list">
                <li><span class="label fl">Office hours:</span><span class="content fr"><span class="content fr"><span class="day fl">Mon:</span>${facilityInfo.officeHours["Monday Office Hours"]}</span></li>
                <li><span class="content fr"><span class="day fl">Tue:</span>${facilityInfo.officeHours["Tuesday Office Hours"]}</span></li>
                <li><span class="content fr"><span class="day fl">Wed:</span>${facilityInfo.officeHours["Wednesday Office Hours"]}</span></li>
                <li><span class="content fr"><span class="day fl">Thur:</span>${facilityInfo.officeHours["Thursday Office Hours"]}</span></li>
                <li><span class="content fr"><span class="day fl">Fri:</span>${facilityInfo.officeHours["Friday Office Hours"]}</span></li>
                <li><span class="content fr"><span class="day fl">Sat:</span>${facilityInfo.officeHours["Saturday Office Hours"]}</span></li>
                <li><span class="content fr"><span class="day fl">Sun:</span>${facilityInfo.officeHours["Sunday Office Hours"]}</span></li>
            </ul>
        </div><!--/right-->
        <div class="clear"></div>
    </div><!--/block-->

    <div class="block">
        <h2>Staff:</h2>

        <span class="label fl">Providers</span>
        <ul class="staff-list fl">
            <c:forEach var="dentist" items="${facilityInfo.employees.Dentists}">
                <li>
                    <span class="name fl">
                        ${dentist.prefix} ${dentist.firstName} ${dentist.lastName}; ${dentist.title}
                    </span>
                    <!--span class="description fl green">Experience: 8 years; Language: English/Spanish; School; University of Pacific<br>Lorem ipsum dolor sit amet, consectetur adipisicing elit</span-->
                </li>
            </c:forEach>
        </ul><!--/staff-list-->

        <span class="label fl">Office Manager</span>
        <ul class="staff-list staff-list-last fl">
            <c:forEach var="manager" items="${facilityInfo.employees.Managers}">
                <li><span class="name fl">${manager.firstName} ${manager.lastName}</span></li>
            </c:forEach>
        </ul><!--/staff-list-->
        <div class="clear"></div>
    </div><!--/block-->

    <div class="block">
        <h2>Notes:</h2>
        <p>${facilityInfo.message}</p>
        <div class="clear"></div>
    </div><!--/block-->

    <div class="block">
        <h2>Neighbor Offices:</h2>

        <c:forEach var="office" items="${facilityInfo.neighborOffices}" varStatus="status">
            <c:choose>
                <c:when test="${status.count % 2 == 1}">
                    <div class="neighbor-office fl">
                </c:when>
                <c:otherwise>
                    <div class="neighbor-office fr">
                </c:otherwise>
            </c:choose>
                <ul class="neighbor-office-info-list">
                    <c:choose>
                        <c:when test="${!empty param.lang && param.lang == 'sp'}">
                            <li><span class="label fl">Site name:</span><a href="facilityinfo?dnis=${office.phone}&lang=sp&uniqueCallId=${uniqueCallId}&employeeNumber=${employeeNumber}" class="otherSite"><span class="content fr title">${office.name}</span></a></li>
                            <li><span class="label fl">Address:</span><span class="content fr">${office.address}</span></li>
                            <li><span class="label fl">Phone:</span><span class="content fr phone-sp"><custom:fmtPhone phone="${office.phone}"/></span></li>
                            <li><span class="label fl">SiteID:</span><span class="content fr">${office.facilityId}</span></li>
                            <li><span class="label fl">Distance:</span><span class="content fr phone-sp">${office.distance} miles</span></li>
                        </c:when>
                        <c:otherwise>
                            <li><span class="label fl">Site name:</span><a href="facilityinfo?dnis=${office.phone}&lang=en&uniqueCallId=${uniqueCallId}&employeeNumber=${employeeNumber}" class="otherSite"><span class="content fr title">${office.name}</span></a></li>
                            <li><span class="label fl">Address:</span><span class="content fr">${office.address}</span></li>
                            <li><span class="label fl">Phone:</span><span class="content fr phone"><custom:fmtPhone phone="${office.phone}"/></span></li>
                            <li><span class="label fl">SiteID:</span><span class="content fr">${office.facilityId}</span></li>
                            <li><span class="label fl">Distance:</span><span class="content fr phone">${office.distance} miles</span></li>
                        </c:otherwise>
                    </c:choose>
                </ul>
            </div>

        </c:forEach>

        <div class="clear"></div>
    </div><!--/block-->
</div><!--/main-->

</body>
</html>
