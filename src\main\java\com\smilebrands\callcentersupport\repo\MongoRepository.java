package com.smilebrands.callcentersupport.repo;

import com.smilebrands.callcentersupport.domain.*;
import com.smilebrands.callcentersupport.domain.partner.PartnerConfig;
import com.smilebrands.callcentersupport.domain.report.InboundCallSummary;
import com.smilebrands.callcentersupport.domain.schedule.Task;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * User: <PERSON><PERSON>
 * Date: 6/25/14
 */
public interface MongoRepository {

    public void rebuildMongoEnvironment();

    public int rebuildZipCodeFacilityAssociations();

    public int rebuildCallCenterEmployees();

    public Facility persistFacilityDetail(Facility facility);

    public Facility findFacilityDetail(Integer facilityId);

    public List<Facility> findFacilitiesByZipCode(String zipCode, Integer limit);

    public List<Facility> findAllFacility();

    public CallCenterEmployee findByEmployeeNumber(Integer employeeNumber);

    public CallCenterEmployee persistCallCenterEmployee(CallCenterEmployee cce);

    public PhoneNumber persistPhoneNumber(PhoneNumber number);

    public PhoneNumber findPhoneNumber(Long number);

    public PhoneNumber findPhoneNumberByFacility(Integer facilityId);

    public PhoneNumber findPhoneNumberByFacility(Integer facilityId, String phoneType);

    public List<PhoneNumber> findAllPhoneNumbers();

    public Campaign persistCampaign(Campaign campaign);

    public Campaign findCampaign(Long number);

    public List<Campaign> findAllCampaign();

    public Long countConfiguredPhoneNumbers();

    public Long countConfiguredFacilities();

    public AgentCall searchAgentCallByAni(String uuid, Long ani);

    public AgentCall registeredAgentCall(AgentCall agentCall);

    public AgentCall updateAgentCall(AgentCall agentCall);

    public AgentCall updateAgentCallWithNewFacility(String uuid, Integer facilityId);

    public AgentCall getAgentCall(String uuid);

    public int purgeAgentCall();

    public int purgeAgentCall(int month, int year);

    public List<AgentCall> searchAgentCalls(String registeredDate, List<Integer> employeeNumbers, int numberOfTrial);

    public InboundCall registerInboundCall(InboundCall inboundCall);

    public InboundCall getInboundCall(String uuid);

    public InboundCall getInboundCallBySessionId(String sessionId);

    public int purgeInboundCall();

    public int purgeInboundCall(int month, int year);

    public void updateInboundCall(InboundCall call);

    public void addInboundCallEvent(String uuid, String eventId);

    public void addInboundCallLanguage(String uuid, String language);

    public void addInboundCallMainMenuNumberPress(String uuid, Integer mainMenuNumberPress);

    public void addInboundCallSessionId(String uuid, String sessionId);

    public CallResolutionCode getCallResolutionCode(String code);

    public CallResolutionCode persistCallResolutionCode(CallResolutionCode code);

    public List<CallResolutionCode> getCallReasonResolutionsCodes();

    public List<CallResolutionCode> getCallReasonCodes(String screenType);

    public List<CallResolutionCode> getCallResolutionCodes(String screenType);

    public List<CommunicationTemplate> getCommunicationTemplates();

    public CommunicationTemplate getActiveCommunicationTemplateById(Long templateId);

    public List<Object> getDistinctField(String collection, String field);

    public Npa getNpaByAni(Long ani);

    public PhoneNumberRoutingRules getPhoneNumberRoutingRules(Long ani);

    public List<PhoneNumberRoutingRules> findAllRoutingRules();

    public PhoneNumberRoutingRules persistRoutingRules(PhoneNumberRoutingRules routingRules);

    public List<InboundCallSummary> getInboundCallSummary(String callCenterName, String phoneType, String menuPress, Date processDate);

    public CallCenter getCallCenterByName(String callCenterName);

    public void inboundCallSummaryMapReduce(Date processDate);

    public ScreenPopTime getScreenPopTime(String uuid, Integer employeeNumber, String screenType);

    public ScreenPopTime persistScreenPopTime(ScreenPopTime screenPopTime);

    public List<ScreenPopTime> getScreenPopTimeByDateRange(String start, String end);

    public void addLogAction(String uuid, String actionType, String message);

    public ActionLog getLogByUUID(String uuid, String actionType);

    public void persistLog(ActionLog log);

    public List<ActionLog> getActionLogToArchive(Date createDate);

    public void archiveLog(ActionLog actionLog);

    public int purgeArchivedLog();

    public int purgeArchivedLogByTimePeriod(int month, int year);

    public void saveWebRequest(WebRequest webRequest);

    public WebRequest findWebRequest(String uuid);

    public List<WebRequest> getTodayWebRequest(Date appointmentDate);
    public List<WebRequest> getWebRequestToProcess(Map<String, Object> conditions, int limit);
    public WebRequest findPendingWebRequestByPhoneNUmber(Long phoneNumber);
    public WebRequest findWebRequestByPhoneNUmberAndEmail(Long phoneNumber, String email);
    public List<WebRequest> findQueuedWebRequest(Map<String, Object> conditions);
    public WebRequest updateRequestForEmail(String uuid, boolean pending, String confirmation, String emailFrom);

    public List<UpdateRequest> getRequestsToUpdateFacility();
    public void markUpdateRequestAsProcessed(UpdateRequest updateRequest);
    public boolean doUpdateFacility(UpdateRequest updateRequest);
    public Long getNextSequenceValue(String sequenceName);

    public Task claimOpenTask(String acquiredBy, String acquiredOn, String taskName);
    public Task claimOpenTask(String acquiredBy, String acquiredOn, Long taskId);
    public void releaseTask(Task task);
    public List<Task> getAllClaimedTask(String nodeName);
    public List<Task> getAllTask();

    public PartnerConfig findPartnerById(String partnerId);

    public ScreenPopHealthCheck getScreenPopHealthCheck(String id);

    public ScreenPopHealthCheck persistScreenPopHealthCheck(ScreenPopHealthCheck record);

    public List<ScreenPopHealthCheck> getScreenPopHealthCheckToClose(int limit);

    public CallbackRequest getCallbackRequest(String uuid);

    public CallbackRequest persistCallbackRequest(CallbackRequest cbr);
}
