<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<div class="modal fade" id="obSchedulingDialog" tabindex="-1" role="dialog" aria-labelledby="obSchedulingModalLabel" aria-hidden="true" style="top:100px;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title" id="obSchedulingModalLabel">Launch Open Book Scheduler</h4>
            </div>
            <div class="modal-body" style="padding-bottom:0px;">
                <form class="form-horizontal call-info-form" role="form">
                    <div class="form-group">
                        <label for="obSchedulingFacility" class="col-sm-3 control-label">Select a Facility</label>
                        <div class="col-sm-9">
                            <select id="obSchedulingFacility" class="form-control input-sm">
                                <c:forEach var="fac" items="${openBookFacilities}">

                                    <c:choose>
                                        <c:when test="${fac.facilityId == facility.facilityId}">
                                            <option value="${fac.facilityId}" content="${fac.facilityName}" selected> ${fac.facilityId} - ${fac.facilityName}</option>
                                        </c:when>
                                        <c:otherwise>
                                            <option value="${fac.facilityId}" content="${fac.facilityName}"> ${fac.facilityId} - ${fac.facilityName}</option>
                                        </c:otherwise>
                                    </c:choose>

                                </c:forEach>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button id="obSchedulingBtn" type="button" class="btn btn btn-success">Launch Open Book Scheduler</button>
            </div>
        </div>
    </div>
</div>