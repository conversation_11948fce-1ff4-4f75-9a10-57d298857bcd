/*
Copyright (c) 2003-2012, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/

.cke_skin_kama
{
	display: block;
}

/* Main editor only settings. */
span.cke_skin_kama
{
	-moz-border-radius: 5px;
	-webkit-border-radius: 5px;
	border-radius: 5px;
	border: 1px solid #D3D3D3;
	padding: 5px;
}

.cke_skin_kama span.cke_browser_webkit,
.cke_skin_kama span.cke_browser_gecko18
{
	display: block;
}

.cke_skin_kama .cke_wrapper
{
	-moz-border-radius: 5px;
	-webkit-border-radius: 5px;
	-webkit-touch-callout: none;
	border-radius: 5px;
	background-color: #d3d3d3;
	background-image: url(images/sprites.png);
	background-repeat: repeat-x;
	background-position: 0 -1950px;
	display: block;
	/* IE Quirks: editor chrome overflow horizontally without an explicit width. */
	_display: inline-block;
	padding: 5px;

	/*background-color: Red;*/
}

.cke_shared .cke_skin_kama .cke_wrapper
{
	padding-bottom: 0;
}

.cke_skin_kama .cke_browser_ie6 .cke_wrapper,
.cke_skin_kama .cke_browser_iequirks .cke_wrapper
{
	background-image: none;
}

.cke_skin_kama .cke_editor
{
	display: inline-table;
	width: 100%;
}

.cke_skin_kama .cke_browser_ie .cke_editor,
.cke_skin_kama .cke_browser_webkit .cke_editor
{
	display: table;	/* #6684, #8342 */
}

.cke_skin_kama .ltr .cke_browser_ie iframe
{
	margin-right: -10px;
}

.cke_skin_kama .rtl .cke_browser_ie iframe
{
	margin-left: -10px;
}

.cke_skin_kama .cke_browser_opera .cke_editor.cke_skin_kama .cke_resizer
{
	display: table;
}

.cke_skin_kama .cke_contents
{
	margin: 5px;
}

.cke_skin_kama .cke_hc .cke_contents
{
	border: 1px solid black;
}

.cke_skin_kama .cke_contents iframe
{
	background-color: #fff;
}

.cke_skin_kama .cke_focus
{
	outline: auto 5px -webkit-focus-ring-color;
}

.cke_skin_kama textarea.cke_source
{
	font-family: 'Courier New' , Monospace;
	font-size: small;
	background-color: #fff;
	white-space: pre;
}

.cke_skin_kama .cke_browser_iequirks textarea.cke_source
{
	/* For IE6+Quirks only */
	_white-space: normal;
}

.cke_skin_kama .cke_resizer
{
	width: 12px;
	height: 12px;
	margin-top: 9px;
	display: block;
	float: right;
	/* resizer.gif*/
	background-image: url(images/sprites.png);
	_background-image: url(images/sprites_ie6.png);
	background-position: 0 -1428px;
	background-repeat: no-repeat;
	cursor: se-resize;
}

/* Adobe AIR doesn't support *-resize cursor shape. */
.cke_skin_kama .cke_browser_air .cke_resizer,
.cke_skin_kama .cke_browser_air .cke_rtl .cke_resizer
{
	cursor: move;
}

.cke_skin_kama .cke_resizer_rtl
{
	cursor: sw-resize;
	/* resizer_rtl.gif*/
	background-position: 0 -1455px;
	float: left;
}

.cke_skin_kama .cke_resizer_horizontal,
.cke_skin_kama .cke_rtl .cke_resizer_horizontal
{
	cursor: e-resize;
}

.cke_skin_kama .cke_resizer_vertical,
.cke_skin_kama .cke_rtl .cke_resizer_vertical
{
	cursor: n-resize;
}

.cke_skin_kama .cke_maximized .cke_resizer
{
	display: none;
}

.cke_skin_kama .cke_browser_ie6 .cke_contents textarea,
.cke_skin_kama .cke_browser_ie7 .cke_contents textarea
{
	position: absolute;
}

.cke_skin_kama .cke_browser_ie.cke_browser_quirks .cke_contents iframe
{
	position: absolute;
	top: 0;
}

.cke_skin_kama .cke_browser_ie6 .cke_editor,
.cke_skin_kama .cke_browser_ie7 .cke_editor
{
	display: inline-block;
}

.cke_skin_kama .cke_browser_ie6 .cke_editor,
.cke_shared .cke_skin_kama .cke_browser_ie7 .cke_wrapper
{
	padding-bottom: 5px;
}

/* All voice labels are not displayed. */
.cke_skin_kama .cke_voice_label
{
	display: none;
}

.cke_skin_kama legend.cke_voice_label
{
	display: none;
}

.cke_skin_kama .cke_browser_ie legend.cke_voice_label
{
	position: absolute;
	display: block;
	width: 0;
	height: 0;
	overflow: hidden;
}
