<?xml version="1.0" encoding="UTF-8"?>
<persistence version="1.0" xmlns="http://java.sun.com/xml/ns/persistence"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xsi:schemaLocation="http://java.sun.com/xml/ns/persistence http://java.sun.com/xml/ns/persistence/persistence_1_0.xsd">
    <persistence-unit name="CALLCENTERSUPPORT">
        <provider>org.hibernate.ejb.HibernatePersistence</provider>

        <class>com.smilebrands.callcentersupport.domain.CallLog</class>
        <class>com.smilebrands.callcentersupport.domain.AppointmentCallLog</class>
        <class>com.smilebrands.callcentersupport.domain.CallLogFile</class>


        <exclude-unlisted-classes>true</exclude-unlisted-classes>
        <properties>
            <!-- 2nd level cache  -->
            <property name="hibernate.cache.use_second_level_cache" value="true"/>
            <property name="hibernate.cache.use_query_cache" value="false"/>
            <property name="hibernate.cache.use_minimal_puts" value="true"/>
            <property name="hibernate.cache.use_structured_entries" value="true"/>
            <property name="hibernate.generate_statistics" value="true"/>
        </properties>
    </persistence-unit>
</persistence>
