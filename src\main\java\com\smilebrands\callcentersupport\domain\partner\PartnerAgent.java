package com.smilebrands.callcentersupport.domain.partner;

import org.springframework.data.annotation.Id;

import java.util.Date;

/**
 * Created by phongpham on 4/6/15.
 */
public class PartnerAgent {
    @Id
    private Integer agentId;
    private String firstName;
    private String lastName;
    private String email;
    private Date activatedOn = new Date();
    private Integer activatedBy;
    private Date inactivatedOn;
    private Date inactivatedBy;

    public PartnerAgent(){

    }

    public PartnerAgent(Integer agentId, String firstName, String lastName, String email, Integer activatedBy){
        this.agentId = agentId;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.activatedBy = activatedBy;
    }

    public Integer getAgentId() {
        return agentId;
    }

    public void setAgentId(Integer agentId) {
        this.agentId = agentId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Date getActivatedOn() {
        return activatedOn;
    }

    public void setActivatedOn(Date activatedOn) {
        this.activatedOn = activatedOn;
    }

    public Integer getActivatedBy() {
        return activatedBy;
    }

    public void setActivatedBy(Integer activatedBy) {
        this.activatedBy = activatedBy;
    }

    public Date getInactivatedOn() {
        return inactivatedOn;
    }

    public void setInactivatedOn(Date inactivatedOn) {
        this.inactivatedOn = inactivatedOn;
    }

    public Date getInactivatedBy() {
        return inactivatedBy;
    }

    public void setInactivatedBy(Date inactivatedBy) {
        this.inactivatedBy = inactivatedBy;
    }
}
