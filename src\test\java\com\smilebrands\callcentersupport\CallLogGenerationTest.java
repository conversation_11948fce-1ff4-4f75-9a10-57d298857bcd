package com.smilebrands.callcentersupport;

import com.smilebrands.callcentersupport.domain.*;
import com.smilebrands.callcentersupport.domain.helper.InclusionFileNameFilter;
import com.smilebrands.callcentersupport.repo.JdbcRepository;
import com.smilebrands.callcentersupport.repo.MongoRepository;
import com.smilebrands.callcentersupport.repo.jpa.CallLogFileRepository;
import com.smilebrands.callcentersupport.repo.jpa.CallLogRepository;
import com.smilebrands.callcentersupport.service.CallLogService;
import com.smilebrands.callcentersupport.service.CallService;
import com.smilebrands.callcentersupport.util.CTI_FFMPEGLocator;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import it.sauronsoftware.jave.AudioAttributes;
import it.sauronsoftware.jave.Encoder;
import it.sauronsoftware.jave.EncoderException;
import it.sauronsoftware.jave.EncodingAttributes;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.filefilter.FileFileFilter;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.test.annotation.Rollback;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FilenameFilter;
import java.net.URL;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.regex.Pattern;

/**
 * User: Marlin Clark
 * Date: 8/13/14
 */
public class CallLogGenerationTest extends BaseTest {

    @Autowired
    private MongoOperations mongoOperations;

    @Autowired
    private CallLogRepository callLogRepository;

    @Autowired
    private CallLogFileRepository callLogFileRepository;

    @Autowired
    private CallLogService callLogService;
    @Autowired
    private CallService callService;

    @Autowired
    protected JdbcTemplate jdbcTemplate;
    @Autowired
    protected JdbcRepository jdbcRepository;

    @Test
    public void replicate() throws CloneNotSupportedException {
        List<CallLog> logs = new ArrayList<CallLog>();
        List<AppointmentCallLog> appts = new ArrayList<AppointmentCallLog>();

        Date dt = new Date();
        List<AgentCall> calls = mongoOperations.find(new Query(Criteria.where(("callRegisteredDate")).is("08-15-2014")), AgentCall.class);
        logger.debug(calls.size() + " Agent calls have been captured.");
        int count = 0;
        int i = 0;

        for (AgentCall call : calls) {
            int facilities = call.getLoadedFacilities().size();
            int agents = call.getLoadedEmployees().size();
            int patients = call.getPatients().size();
            i++;

            logger.debug("(" + i + "/" + calls.size() + ") Agent Call [" + call.getUuid() + "] has " + facilities + " loaded Facilities, " + agents + " Agents and " + patients + " patients.");

            List<CallLog> callLogs = callLogService.populateCallLogForAgentCall(call, null, null, null, false);
            count += callLogs.size();
            logger.debug("Agent Call [" + call.getUuid() + "] has " + callLogs.size() + " call logs created.");

//            for (LoadedEmployee employee: call.getLoadedEmployees()) {
//                CallLog log = new CallLog();
//                log.setAgentType(employee.getScreenType());
//                log.setEmployeeNumber(employee.getEmployeeNumber());
//                log.setReasonCode(employee.getReasonCode());
//                log.setResolutionCode(employee.getResolutionCode());
//                log.setFacilityViewCount(facilities);
//
//                logs.add(log);
//
//                if (patients > 0) {
//                    logger.debug("This call log has " + patients + " patients.");
//
//                    int patAgentCount = uniqueEmployees(call.getPatients());
//                    if (patAgentCount > 1) {
//
//                        for (Patient pat : call.getPatients()) {
//                            if (pat.getEmpEntered().equals(log.getEmployeeNumber())) {
//                                CallLog _log = log.copy();
//                                logs.add(_log);
//
//                                AppointmentCallLog appt = new AppointmentCallLog();
//                                appt.setAppointmentDateTime(pat.getNextAppointmentDateTime());
//                                appt.setCreateDateTime(pat.getCreateDateTime());
//                                appt.setCreateEmployeeNumber(pat.getEmpEntered());
//
//                                appts.add(appt);
//                            }
//                        }
//
//                    } else {
//                        Patient pat = call.getPatients().get(0);
//                        log.setTempPatientId(pat.getTempPatientId());
//                        log.setFacilityId(pat.getFacilityId());
//
//                        AppointmentCallLog appt = new AppointmentCallLog();
//                        appt.setAppointmentDateTime(pat.getNextAppointmentDateTime());
//                        appt.setCreateDateTime(pat.getCreateDateTime());
//                        appt.setCreateEmployeeNumber(pat.getEmpEntered());
//
//                        appts.add(appt);
//                    }
//                }
//            }
        }

        logger.debug(calls.size() + " Agent calls have been captured and converted into " + count + " CallLog records and " + appts.size() + " Appt Log records.");
        logger.debug("time taken to execute: {}", (new Date().getTime() - dt.getTime()));

    }

    @Test
    public void testClone() throws Exception {
        CallLog log = new CallLog();
        logger.debug(log.toString());
        logger.debug(log.copy().toString());
    }

    private int uniqueEmployees(List<Patient> patients) {
        Set<Integer> emps = new HashSet<Integer>();
        for (Patient p : patients ) {
            emps.add(p.getEmpEntered());
        }

        return emps.size();
    }

    private CallLog copyLog(CallLog log) throws CloneNotSupportedException {
        return log.copy();
    }

    @Test
    public void testGenerateCallLogFromAgentCall(){
        AgentCall agentCall = callService.findAgentCall("b4620f5c-a084-425a-aedf-b6dde81f9d03");
        List<CallLog> callLogs = callLogService.populateCallLogForAgentCall(agentCall, 102029, null, null, true);
        logger.debug("call logs for 102029: " + callLogs.size());
        callLogs = callLogService.populateCallLogForAgentCall(agentCall, 104371, null, null, true);
        logger.debug("call logs for 104371: " + callLogs.size());
    }

    @Test
    public void testPutBackPatientToAgentCallMongo(){
        String query = "select distinct uuid, employee_number from agent_call_log c join appt_call_log a on a.call_log_id=c.call_log_id where a.appt_log_status='UNVERIFIED' order by c.UUID";
        List<Map<String, Object>> list = jdbcTemplate.query(query, new Object[]{}, new RowMapper<Map<String, Object>>() {
            @Override
            public Map<String, Object> mapRow(ResultSet rs, int rowNum) throws SQLException {
                Map<String, Object> record = new HashMap<String, Object>();
                record.put("UUID", rs.getString("UUID"));
//                record.put("CALL_LOG_ID", rs.getLong("CALL_LOG_ID"));
                record.put("EMPLOYEE_NUMBER", rs.getInt("EMPLOYEE_NUMBER"));
                return record;
            }
        });
        logger.debug("list: {}", list);
        logger.debug("list.size: {}", list.size());
        String queryAppointment = "select * from agent_call_log c join appt_call_log a on a.call_log_id=c.call_log_id where a.appt_log_status='UNVERIFIED' and c.uuid = ? and c.employee_number = ? order by APPT_LOG_ID";
        int count = 0;
        for(Map<String,Object> map : list){
            Integer employeeNumber = (Integer) map.get("EMPLOYEE_NUMBER");
            AgentCall agentCall = callService.findAgentCall((String)map.get("UUID"));
            if(agentCall != null && agentCall.getPatients().size() == 0){
                LoadedEmployee loadedEmployee = null;
                for(LoadedEmployee employee : agentCall.getLoadedEmployees()){
                    if(employee.getEmployeeNumber().equals(employeeNumber) && employee.getScreenType().equalsIgnoreCase("psr")){
                        loadedEmployee = employee;
                        break;
                    }
                }
                if(loadedEmployee != null && loadedEmployee.getCallLogId() != null){
                    List<Map<String, Object>> appointments = jdbcTemplate.query(queryAppointment, new Object[]{agentCall.getUuid(), loadedEmployee.getEmployeeNumber()}, new RowMapper<Map<String, Object>>() {
                        @Override
                        public Map<String, Object> mapRow(ResultSet rs, int rowNum) throws SQLException {
                            Map<String, Object>  record = new HashMap<String, Object>();
                            record.put("APPOINTMENT_CALL_LOG_ID", rs.getLong("APPT_LOG_ID"));
                            record.put("FACILITY_ID", rs.getInt("FACILITY_ID"));
                            record.put("PATIENT_ID", rs.getLong("TEMP_PATIENT_ID"));
                            record.put("APPT_DATE", rs.getTimestamp("APPT_DATE"));
                            return record;
                        }
                    });

                    if(agentCall.getUuid().equalsIgnoreCase("ae9aec98-82a9-4ab1-a4a9-0a86eba81ca4")){
                        logger.debug("\t\tappointments : {}", appointments);
                        logger.debug("appointments size: {}", appointments.size());
                    }
                    List<Patient> patients = new ArrayList<Patient>();
                    for(int i=0; i<appointments.size(); i++){
                        Map<String, Object> patientMap = appointments.get(i);
                        Patient patient = new Patient();
                        patient.setAppointmentCallLogId((Long)patientMap.get("APPOINTMENT_CALL_LOG_ID"));
                        patient.setPatientId((Long)patientMap.get("PATIENT_ID"));
                        patient.setFacilityId((Integer)patientMap.get("FACILITY_ID"));
                        patient.setNextAppointmentDateTime((Date) patientMap.get("APPT_DATE"));
                        patient.setNextAppointmentDate(VelocityUtils.getDateAsString(patient.getNextAppointmentDateTime(), null));
                        patient.setNextAppointmentTime(VelocityUtils.getDateAsString(patient.getNextAppointmentDateTime(), "HHmmss"));
                        patient.setEmpEntered(loadedEmployee.getEmployeeNumber());
                        patient.setCreateDateTime(loadedEmployee.getUnloadedDateTime());
                        patient.setCreateDate(VelocityUtils.getDateAsString(patient.getCreateDateTime(), null));
                        patient.setCreateTime(VelocityUtils.getDateAsString(patient.getCreateDateTime(), "HHmmss"));
                        patient.setSequence(i+1);
                        patients.add(patient);
                    }
                    agentCall.getPatients().addAll(patients);
                    if(patients.size() > 0){
                        logger.debug("update agent call[" + agentCall.getUuid() + "] with " + patients.size() + " appointments");
                        mongoOperations.save(agentCall);
                    }
                }
            }
        }
    }

    @Test
    public void testCheckExistingPatient(){
        Long phoneNumber = 9495054654L;
//        Long phoneNumber = 9495054564L;
        Integer facilityId = 13070;
//        boolean result = callService.hasPriorQsiAppointment(phoneNumber, facilityId);
//        logger.debug("existing: {}", result);
        int result2 = jdbcRepository.hasPriorQsiAppointment(phoneNumber, facilityId);
        logger.debug("existing(2): {}", result2);

    }

    @Test
    public void testReplicaSet(){
        Long dnis = 9495810090L;
        Long ani = 9495054654L;
        String sessionId = "abc123";
        int failCount = 0;
        for(int i=0; i<100; i++){
            Date start = new Date();
            boolean hasFail = false;
            InboundCall inboundCall = callService.registerCall(dnis, ani, sessionId);
            logger.debug("time taken to generate uuid[" + inboundCall.getUuid() + "] is " + (new Date().getTime() - start.getTime()));
            try{
                callService.registerCallEvent(inboundCall.getUuid(), "No PSR");
            }catch (Exception ex){
                logger.debug("fail to register No PSR event: {}", ex);
                hasFail = true;
            }
            try {
                callService.registerCallEvent(inboundCall.getUuid(), "Transfer to Office");
            }catch (Exception ex){
                logger.debug("fail to register Transfer to Office: {}", ex);
                hasFail = true;
            }
            if(hasFail){
                failCount++;
            }
            mongoOperations.remove(inboundCall);
        }
        logger.debug("Number of fail: {}", failCount);
    }


    @Test
    public void testGetAgentReport(){
        List<LinkedHashMap<String,String>> result = callLogService.getCallLogDetailReport("01-Jan-2015", "28-Jan-2015", null, null, null);
        int lineNo = 1;
        for(LinkedHashMap<String, String> record : result){
            logger.debug("\n\n" + lineNo + "/");
            for(String key : record.keySet()){
                logger.debug(key + ":::" + record.get(key));
            }
        }
    }

    @Test
    public void testReadFiles(){
        InclusionFileNameFilter filter = new InclusionFileNameFilter("xls");
        File file = new File("/opt/callcenter");
        String[] list = file.list(filter);
        logger.debug("list size for xls filter: {}", list.length);
        for(String str : list){
            logger.debug("{}", str);
        }
        filter = new InclusionFileNameFilter("csv");
        file = new File("/opt/callcenter");
        list = file.list(filter);
        logger.debug("\nlist size for csv filter: {}", list.length);
        for(String str : list){
            logger.debug("{}", str);
        }
        filter = new InclusionFileNameFilter("csv,xls");
        file = new File("/opt/callcenter");
        list = file.list(filter);
        logger.debug("\nlist size for csv,xls filter: {}", list.length);
        for(String str : list){
            logger.debug("{}", str);
        }
        filter = new InclusionFileNameFilter("wav,mp3");
        file = new File("/Pentaho_Extract/RDI/2015/4/7/new");
        list = file.list(filter);
        logger.debug("\nlist size for wav,mp3 filter: {}", list.length);
        for(String str : list){
            logger.debug("{}", str);
        }
        File[] files = file.listFiles();
        logger.debug("file list size: {}", files.length);
    }

    @Test
    public void testLinkFile(){
        callService.linkAudioFilesWithCallLog("RDI");
    }

    @Test
    public void testFindCallLogByUUIDAndEmployeeNumber(){
        List<CallLog> callLogs = callLogRepository.findByUuidAndEmployeeNumber("07972250-ac4f-4177-96c3-781b42cb6e41", 102029);
        logger.debug("list size: {}", callLogs.size());
    }

    @Test
    public void testFindCallLogFileByEmployeeNumberAndUuidAndFileType(){
        List<CallLogFile> logFiles = callLogFileRepository.findByEmployeeNumberAndUuidAndFileType(102029, "07972250-ac4f-4177-96c3-781b42cb6e41", "audio");
        logger.debug("list size: {}", logFiles.size());
    }

    @Test
    public void testJave(){
        try {
//            URL url = this.getClass().getResource("/ffmpeg_mac_64");

            File sourceFolder = new File("/Pentaho_Extract/RDI/2015/5/8");
            File[] files = sourceFolder.listFiles(new InclusionFileNameFilter("wav"));
            int count = 0;
            CTI_FFMPEGLocator cti = new CTI_FFMPEGLocator();
            Encoder encoder = new Encoder(cti);
            for(int i = 0; i<files.length; i++){
                try {

//                    String[] encodes = encoder.getAudioEncoders();
//                    System.out.println("encodes length: " + encodes.length);
//                    for(int i=0; i<encodes.length; i++){
//                        System.out.println(encodes[i]);
//                    }

                    File file = files[i];
                    int extIdx = file.getName().lastIndexOf(".wav");
                    String newFileName = file.getName().substring(0, extIdx);
                    File target = new File(file.getParent() + File.separator + newFileName + ".mp3");
                    logger.debug("file name: {}", target.getAbsolutePath());
                    AudioAttributes audio = new AudioAttributes();
//            audio.setCodec("libmp3lame");
//            audio.setBitRate(new Integer(128000));
//            audio.setChannels(new Integer(2));
//            audio.setSamplingRate(new Integer(44100));
                    EncodingAttributes attributes = new EncodingAttributes();
                    attributes.setFormat("mp3");
                    attributes.setAudioAttributes(audio);
                    encoder.encode(file, target, attributes);
                    count++;
                }catch(Exception ex){
                    Pattern SUCCESS_PATTERN = Pattern.compile(
                            "^\\s*video\\:\\S+\\s+audio\\:\\S+\\s+subtitle\\:\\S+\\s+other streams\\:\\S+\\s+global headers\\:\\S+.*$",
                            Pattern.CASE_INSENSITIVE);
                    boolean isSucceed = SUCCESS_PATTERN.matcher(ex.getMessage()).matches();
                    if(!isSucceed) {
                        ex.printStackTrace();
                    }else{
                        count++;
                    }
                }
            }
            logger.debug("successfully convert {} files to mp3", count);

        }catch(Exception ex){
            ex.printStackTrace();
        }
    }

    @Test
    public void testUpdateWavToMp3ForCallLog(){
        File sourceFolder = new File("/Pentaho_Extract/RDI/2015/5/8");
        File[] files = sourceFolder.listFiles(new InclusionFileNameFilter("mp3"));
        int count = 0;
        int inactiveCnt = 0;
        for(int i = 0; i<files.length; i++){
            File f = files[i];
            String fileName = f.getName();
            String[] tmp = fileName.split("_");
            if(tmp.length >= 2){
                String uuid = tmp[0];
                Integer employeeNumber = Integer.valueOf(tmp[1]);
                List<CallLogFile> audioFiles = callLogFileRepository.findByEmployeeNumberAndUuidAndFileType(employeeNumber, uuid, "audio");

                for(CallLogFile af : audioFiles){
                    if(af.getActive()){
                        af.setActive(false);
                        af.setInactivatedBy(102029);
                        af.setInactivatedOn(new Date());
                        callLogFileRepository.save(af);
                        logger.debug("about to inactivate file: {}", af.getFilePath());
                        inactiveCnt++;
                    }
                }
                CallLogFile logFile = new CallLogFile();
                logFile.setEmployeeNumber(employeeNumber);
                logFile.setUuid(uuid);
                logFile.setFilePath(f.getPath());
                logFile.setFileType("audio");
                logFile.setSource("RDI");
                logger.debug("about to save file: {}", logFile.getFilePath());
                count++;
                callLogFileRepository.save(logFile);
            }
        }
        logger.debug("saved {} new files", count);
        logger.debug("inactivated {} files", inactiveCnt);
    }

    @Test
    @Rollback(false)
    public void testProcessScreenPopHealthCheck(){
        callService.processScreenPopHealthCheck(10);
    }

    @Test
    public void testIsOldCallLog(){
        String uuid = "e9f408a2-474a-41a6-85f7-0e26a550e6b3";
        boolean result = callService.isOldScreenPop(null, uuid, 0);
        logger.debug(uuid + " is old for screen pop: {}", result);
        result = callService.isOldScreenPop(null, uuid, 1);
        logger.debug(uuid + " is old for call log management: {}", result);

        uuid = "4c99516b-513a-413c-9da0-3665a3a53907";
        result = callService.isOldScreenPop(null, uuid, 0);
        logger.debug(uuid + " is old for screen pop: {}", result);
        result = callService.isOldScreenPop(null, uuid, 1);
        logger.debug(uuid + " is old for call log management: {}", result);

        uuid = "842dec6a-c9e0-496b-b73d-b2bc887a5577";
        result = callService.isOldScreenPop(null, uuid, 0);
        logger.debug(uuid + " is old for screen pop: {}", result);
        result = callService.isOldScreenPop(null, uuid, 1);
        logger.debug(uuid + " is old for call log management: {}", result);
    }

    @Test
    public void testGetCallCenterEmployees(){

        List<CallCenterEmployee> list = jdbcRepository.getCallCenterEmployee(true);
        logger.debug("list size: {}", list.size());
        list = jdbcRepository.getCallCenterEmployee(false);
        logger.debug("list size: {}", list.size());

//        Integer employeeNumber = 110164;
        Integer employeeNumber = 118603;
        CallCenterEmployee cce = jdbcRepository.getCallCenterEmployeeByNumber(employeeNumber, true);
        if(cce != null){
            logger.debug("CCE is " + cce.getEmployeeFirstName() + " " + cce.getEmployeeLastName());
        }else{
            logger.debug("Cannot find record for [" + employeeNumber + "]");
        }
        cce = jdbcRepository.getCallCenterEmployeeByNumber(employeeNumber, false);
        if(cce != null){
            logger.debug("CCE is " + cce.getEmployeeFirstName() + " " + cce.getEmployeeLastName());
        }else{
            logger.debug("Cannot find record for [" + employeeNumber + "]");
        }
        cce = jdbcRepository.getCallCenterEmployeeFromEmployee(employeeNumber, true);
        if(cce != null){
            logger.debug("CCE is " + cce.getEmployeeFirstName() + " " + cce.getEmployeeLastName() + ":::" + cce.getEmailAddress());
        }else{
            logger.debug("Cannot find record for [" + employeeNumber + "]");
        }

    }

    @Test
    public void testFindAgentCallLogsQuery() {
        List<V_CallLog> logs = jdbcRepository.getAgentCallLog(118397, new Date());
        logger.debug(logs.size() + " call logs found for this agent.");

        logs = jdbcRepository.getAgentCallLog(null, new Date());
        logger.debug(logs.size() + " call logs found <null> this agent.");
    }

}
