package com.smilebrands.callcentersupport.domain;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * Created by phongpham on 10/1/14.
 */
@Document
public class CallCenterEmployee {

    @Id
    private Long empSupervisorId;
    private Integer employeeNumber;
    private String employeeName;
    private String employeeFirstName;
    private String employeeLastName;
    private Integer supervisorNumber;
    private String supervisorName;
    private String state;
    private String callCenter;
    private String emailAddress;
    private boolean isActive;
    private boolean isSupervisor;
    private Date inactiveDateTime;

    public Long getEmpSupervisorId() {
        return empSupervisorId;
    }

    public void setEmpSupervisorId(Long empSupervisorId) {
        this.empSupervisorId = empSupervisorId;
    }

    public Integer getEmployeeNumber() {
        return employeeNumber;
    }

    public void setEmployeeNumber(Integer employeeNumber) {
        this.employeeNumber = employeeNumber;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getEmployeeFirstName() {
        return employeeFirstName;
    }

    public void setEmployeeFirstName(String employeeFirstName) {
        this.employeeFirstName = employeeFirstName;
    }

    public String getEmployeeLastName() {
        return employeeLastName;
    }

    public void setEmployeeLastName(String employeeLastName) {
        this.employeeLastName = employeeLastName;
    }

    public Integer getSupervisorNumber() {
        return supervisorNumber;
    }

    public void setSupervisorNumber(Integer supervisorNumber) {
        this.supervisorNumber = supervisorNumber;
    }

    public String getSupervisorName() {
        return supervisorName;
    }

    public void setSupervisorName(String supervisorName) {
        this.supervisorName = supervisorName;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean isActive) {
        this.isActive = isActive;
    }

    public boolean isSupervisor() {
        return isSupervisor;
    }

    public void setSupervisor(boolean isSupervisor) {
        this.isSupervisor = isSupervisor;
    }

    public Date getInactiveDateTime() {
        return inactiveDateTime;
    }

    public void setInactiveDateTime(Date inactiveDateTime) {
        this.inactiveDateTime = inactiveDateTime;
    }

    public String getCallCenter() {
        return callCenter;
    }

    public void setCallCenter(String callCenter) {
        this.callCenter = callCenter;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String toString() {
        return employeeNumber + " --> " + employeeName;
    }
}
