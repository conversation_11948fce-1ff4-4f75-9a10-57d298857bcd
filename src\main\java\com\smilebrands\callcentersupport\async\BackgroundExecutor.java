package com.smilebrands.callcentersupport.async;

import com.smilebrands.callcentersupport.domain.ActionLog;
import com.smilebrands.callcentersupport.repo.MongoRepository;
import com.smilebrands.callcentersupport.service.CallService;
import com.smilebrands.callcentersupport.service.ConfigurationService;
import com.smilebrands.callcentersupport.util.DateTimeUtils;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.apache.commons.lang.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.UUID;

/**
 * Created by phongpham on 8/6/14.
 */
@Component
public class BackgroundExecutor {

    final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    ConfigurationService configurationService;

    @Autowired
    MongoRepository mongoRepository;

    @Autowired
    CallService callService;

    @Async
    public void rebuildMongoEnvironmentAsync(){
        logger.debug("rebuild mongo environment async");
        configurationService.rebuildMongoEnvironment();
    }

    @Async
    public void runMapReduceSummary() {
        logger.debug("build inbound call summary.");
        mongoRepository.inboundCallSummaryMapReduce(new Date());
    }

    @Async
    public void runUpdateFacilityRequest(Integer facilityId){
        logger.debug("run update facility request");
        configurationService.processUpdateFacilityRequest(facilityId);
    }

    @Async
    public void logAction(String uuid, String actionType, String message){
//        logger.debug("about to log for UUID[" + uuid + "] with action[" + actionType + "] and message[" + message + "]");
        mongoRepository.addLogAction(uuid, actionType, message);

    }

    @Async
    public void doLinkFileToCallLog(String source){
        callService.linkAudioFilesWithCallLog(source);
    }

    @Async
    public void checkScreenPopOnline(String uuid, Integer employeeNumber, String screenType, String popDateTime){
        callService.doScreenPopHealthCheck(uuid, employeeNumber, screenType, popDateTime, null);
    }
}
