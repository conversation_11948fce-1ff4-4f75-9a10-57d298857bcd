package com.smilebrands.callcentersupport.repo;

import com.smilebrands.callcentersupport.domain.*;
import com.smilebrands.callcentersupport.domain.rowmapper.CallCenterEmployeeRowMapper;
import com.smilebrands.callcentersupport.util.DateTimeUtils;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * User: <PERSON><PERSON>
 * Date: 6/25/14
 */
@Repository
public class JdbcRepository {

    final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    protected JdbcTemplate jdbcTemplate;

    @Autowired
    protected MongoRepository mongoRepository;

    @Autowired
    @Qualifier("postgresTemplate")
    protected JdbcTemplate postgresTemplate;

    @Autowired
    @Qualifier("jdbcTemplateWith3sTimeout")
    protected JdbcTemplate jdbcTemplateWith3sTimeout;

    @Autowired
    @Qualifier("GET_FACILITY_COLLECTION")
    protected String GET_FACILITY_COLLECTION;

    @Autowired
    @Qualifier("GET_FACILITIES_BY_ZIPCODE")
    protected String GET_FACILITIES_BY_ZIPCODE;

    @Autowired
    @Qualifier("GET_OFFICE_MANAGER")
    protected String GET_OFFICE_MANAGER;

    @Autowired
    @Qualifier("GET_OFFICE_MANAGEMENT")
    protected String GET_OFFICE_MANAGEMENT;

    @Autowired
    @Qualifier("GET_OFFICE_PROVIDERS")
    protected String GET_OFFICE_PROVIDERS;

    @Autowired
    @Qualifier("GET_FACILITY_PROVIDER_ATTRIBUTES")
    protected String GET_FACILITY_PROVIDER_ATTRIBUTES;

    @Autowired
    @Qualifier("GET_PROVIDER_INSURANCE")
    protected String GET_PROVIDER_INSURANCE;

    @Autowired
    @Qualifier("GET_EMPLOYEE_NAME")
    protected  String GET_EMPLOYEE_NAME;

    @Autowired
    @Qualifier("GET_EMPLOYEE_INFO_BY_NUMBER")
    protected String GET_EMPLOYEE_INFO_BY_NUMBER;

    @Autowired
    @Qualifier("GET_OFFICE_ATTRIBUTES")
    protected String GET_OFFICE_ATTRIBUTES;

    @Autowired
    @Qualifier("QUEUE_EMAIL_CONFIRM")
    protected String QUEUE_EMAIL_CONFIRM;

    @Autowired
    @Qualifier("GET_QSI_APPOINTMENT_PHONE")
    protected String GET_QSI_APPOINTMENT_PHONE;

    @Autowired
    @Qualifier("GET_QSI_PRIOR_APPT")
    protected String GET_QSI_PRIOR_APPT;

    @Autowired
    @Qualifier("GET_LIBERTY_APPOINTMENT_PHONE")
    protected String GET_LIBERTY_APPOINTMENT_PHONE;

    @Autowired
    @Qualifier("GET_LIBERTY_PRIOR_APPT")
    protected String GET_LIBERTY_PRIOR_APPT;

    @Autowired(required = false)
    @Qualifier("GET_AGENT_CALL_LOG")
    private String GET_AGENT_CALL_LOG;

    @Autowired
    @Qualifier("GET_CALL_RESOLUTION_CODE")
    private String GET_CALL_RESOLUTION_CODE;

    @Autowired
    @Qualifier("GET_ZIPCODE_FACILITIES")
    private String GET_ZIPCODE_FACILITIES;

    @Autowired
    @Qualifier("GET_CALL_LOG_DETAIL_REPORT")
    private String GET_CALL_LOG_DETAIL_REPORT;

    @Autowired
    @Qualifier("GET_CALL_LOG_DETAIL_REPORT_BY_LATEST_APPT_DATE")
    private String GET_CALL_LOG_DETAIL_REPORT_BY_LATEST_APPT_DATE;

    @Autowired
    @Qualifier("GET_SUPERVISED_AGENT")
    private String GET_SUPERVISED_AGENT;

    @Autowired
    @Qualifier("CHECK_IF_APPT_CALL_LOG_EXIST")
    private String CHECK_IF_APPT_CALL_LOG_EXIST;

    @Autowired
    @Qualifier("GET_TEAM_HIERARCHY")
    private String GET_TEAM_HIERARCHY;

    @Autowired
    @Qualifier("GET_CALL_CENTER_EMPLOYEE")
    private String GET_CALL_CENTER_EMPLOYEE;

    @Autowired
    @Qualifier("GET_CALL_CENTER_TEAM_LEAD")
    private String GET_CALL_CENTER_TEAM_LEAD;

    @Autowired
    @Qualifier("GET_CALL_CENTER_EMPLOYEE_BY_NUMBER")
    private String GET_CALL_CENTER_EMPLOYEE_BY_NUMBER;

    @Autowired
    @Qualifier("INACTIVATE_CALL_CENTER_EMPLOYEE")
    private String INACTIVATE_CALL_CENTER_EMPLOYEE;

    @Autowired
    @Qualifier("INACTIVATE_ACTIVE_CALL_CENTER_EMPLOYEE")
    private String INACTIVATE_ACTIVE_CALL_CENTER_EMPLOYEE;

    @Autowired
    @Qualifier("INSERT_CENTER_EMPLOYEE_BY_NUMBER")
    private String INSERT_CENTER_EMPLOYEE_BY_NUMBER;

    @Autowired
    @Qualifier("GET_CALL_LOG_EDITED")
    private String GET_CALL_LOG_EDITED;

    @Autowired
    @Qualifier("CDW_FACILITY_SUPPORT")
    private String cdwFacilitySupport;

    @Autowired
    @Qualifier("CDW_CISCO_AGENT_CALL_BY_DATE")
    private String cdwCiscoAgentCallByDate;

    @Autowired
    @Qualifier("CDW_SPAM_PHONE_NUMBERS")
    private String cdwSpamPhoneNumbers;

    @Autowired
    @Qualifier("GET_EMAIL_FOR_REPORT")
    private String GET_EMAIL_FOR_REPORT;

    @Autowired
    @Qualifier("GET_OPEN_BOOK_SUPPORTED_FACILITES")
    private String GET_OPEN_BOOK_SUPPORTED_FACILITES;

    @Autowired
    @Qualifier("GET_QSI_PATIENT_FACILITY")
    private String GET_QSI_PATIENT_FACILITY;

    @Autowired
    @Qualifier("GET_LIBERTY_PATIENT_FACILITY")
    private String GET_LIBERTY_PATIENT_FACILITY;

    /**
     * Populate CallResolutionCode table
     */
    public List<Map<String, Object>> buildCallResolutionCodeTable() {
        return jdbcTemplate.queryForList(GET_CALL_RESOLUTION_CODE);
    }

    /**
     * Populate the Zip Code to Facilities associations
     */
    public List<Map<String, Object>> getZipCodeFacilityRecords() {
        return jdbcTemplate.queryForList(GET_ZIPCODE_FACILITIES);
    }

    /**
     * Search QSI Appointments table for appointments by Patient Phone Number
     */
    public List<Appointment> getQsiPatientAppointments(Long phoneNumber, Integer facilityId, String dob) {
        String query = GET_QSI_APPOINTMENT_PHONE;
        List<Object> temp = new ArrayList<Object>();
        temp.add(phoneNumberToQSIFormat(phoneNumber));
        temp.add(phoneNumberToQSIFormat(phoneNumber));
        temp.add(phoneNumberToQSIFormat(phoneNumber));
        Object[] params = new Object[]{};
        if(dob != null){
            query = query.replace("'##DOB_CONDITION##'", "AND TO_CHAR(PAT.DATE_OF_BIRTH,'MMddYYYY') = ?");
            temp.add(dob);
        }else{
            query = query.replace("'##DOB_CONDITION##'", "");
        }

        if(facilityId != null && !facilityId.equals(0)){
            query = query.replace("'##FACILITY_CONDITION##'", "AND FAC.FACILITY_ID = ?");
            temp.add(facilityId);
        }else{
            query = query.replace("'##FACILITY_CONDITION##'", "");

        }
        params = temp.toArray();

        List<Appointment> appointments = jdbcTemplate.query(query,
                params,
                new RowMapper() {
                    @Override
                    public Object mapRow(ResultSet resultSet, int i) throws SQLException {
                        Appointment appointment = new Appointment();
                        appointment.setAppointmentDateTime(resultSet.getTimestamp("APPT_DATE"));

                        Facility facility = new Facility();
                        facility.setFacilityId(resultSet.getInt("FACILITY_ID"));
                        facility.setName(resultSet.getString("FACILITY_NAME"));
                        appointment.setFacility(facility);

                        Patient patient = new Patient();
                        patient.setPatientFirstName(resultSet.getString("NAME_FIRST"));
                        patient.setPatientLastName(resultSet.getString("NAME_LAST"));
                        patient.setDateOfBirth(resultSet.getDate("DATE_OF_BIRTH"));
                        patient.setPatientGender(resultSet.getString("GENDER"));
                        patient.setFacilityId(resultSet.getInt("FACILITY_ID"));
                        patient.setClinicId(resultSet.getInt("CLINIC_ID"));
                        patient.setPatientId(resultSet.getLong("PATIENT_ID"));
                        patient.setNextAppointmentDateTime(resultSet.getTimestamp("APPT_DATE"));
                        patient.setLastAppointmentDate(resultSet.getTimestamp("DATE_LAST_VISIT"));
                        appointment.setPatient(patient);

                        return appointment;
                    }
                });
        return appointments;
    }

    /**
     * Search Liberty Appointments table for appointments by Patient Phone Number
     */
    public List<Appointment> getLibertyPatientAppointments(Long phoneNumber, Integer facilityId, String dob) {
        String query = GET_LIBERTY_APPOINTMENT_PHONE;
        List<Object> temp = new ArrayList<Object>();
        temp.add(phoneNumber);
        temp.add(phoneNumber);
        temp.add(phoneNumber);
        if(dob != null){
            query = query.replace("'##DOB_CONDITION##'", "AND TO_CHAR(P.DATE_OF_BIRTH,'MMddYYYY') = ?");
            temp.add(dob);
        }else{
            query = query.replace("'##DOB_CONDITION##'", "");
        }
        if(facilityId != null && !facilityId.equals(0)){
            query = query.replace("'##FACILITY_CONDITION##'", "AND FAC.FACILITY_ID = ?");
            temp.add(facilityId);
        }else{
            query = query.replace("'##FACILITY_CONDITION##'", "");

        }
        Object[] params = temp.toArray();
        List<Appointment> appointments = jdbcTemplate.query(query, params,
                new RowMapper() {
                    @Override
                    public Object mapRow(ResultSet resultSet, int i) throws SQLException {
                        Appointment appointment = new Appointment();
                        appointment.setAppointmentDateTime(resultSet.getTimestamp("START_DATETIME"));

                        Facility facility = new Facility();
                        facility.setFacilityId(resultSet.getInt("FACILITY_ID"));
                        facility.setName(resultSet.getString("FACILITY_NAME"));
                        appointment.setFacility(facility);

                        Patient patient = new Patient();
                        patient.setPatientFirstName(resultSet.getString("FIRST_NAME"));
                        patient.setPatientLastName(resultSet.getString("LAST_NAME"));
                        patient.setDateOfBirth(resultSet.getDate("DATE_OF_BIRTH"));
                        patient.setPatientGender(resultSet.getString("GENDER"));
                        patient.setFacilityId(resultSet.getInt("FACILITY_ID"));
                        patient.setClinicId(resultSet.getInt("QSI_CLINIC_ID"));
                        patient.setPatientId(resultSet.getLong("PATIENT_ID"));
                        patient.setNextAppointmentDateTime(resultSet.getTimestamp("START_DATETIME"));
                        patient.setLastAppointmentDate(resultSet.getTimestamp("DATE_LAST_VISIT"));
                        appointment.setPatient(patient);
                        return appointment;
                    }
                });
        return appointments;
    }

    /**
     *  Search for a prior QSI appointment within the past year.
     *  Return values:  1: YES
     *                  0: NO
     *                  2: TIMEOUT
     */
    public int hasPriorQsiAppointment(Long phoneNumber, Integer facilityId) {
        try {
            String phone = phoneNumberToQSIFormat(phoneNumber);

            List<Map<String, Object>> x = jdbcTemplateWith3sTimeout.queryForList(GET_QSI_PRIOR_APPT, new Object[]{phone, phone, phone, facilityId});

            return x.size() > 0 ? 1 : 0;

        } catch (Exception x) {
            String message = "QSI Query Timeout caught search for ANI [" + phoneNumber + "] in Facility [" + facilityId + "]!";
            logger.warn(message);
            return 2;
        } finally {

        }
    }

    /**
     *  Search for a prior Liberty appointment within the past year.
     *  Return values:  1: YES
     *                  0: NO
     *                  2: TIMEOUT
     */
    public int hasPriorLibertyAppointment(Long phoneNumber, Integer facilityId) {
        try {

            List<Map<String, Object>> x = jdbcTemplateWith3sTimeout.queryForList(GET_LIBERTY_PRIOR_APPT, new Object[]{phoneNumber, phoneNumber, phoneNumber, facilityId});


            return x.size() > 0 ? 1 : 0;

        } catch (Exception x) {
            String message = "Liberty Query Timeout caught search for ANI [" + phoneNumber + "] in Facility [" + facilityId + "]!";
            logger.warn(message);
            return 2;
        } finally {

        }
    }

    private String phoneNumberToQSIFormat(Long phoneNumber) {
        String s = String.valueOf(phoneNumber);
        if (s.length() == 10) {
            return "(" + s.substring(0,3) + ")" + s.substring(3,6) + "-" + s.substring(6);
        }

        return null;
    }

    public List<V_CallLog> getAgentCallLog(Integer agentId, Date callDate){
        String query = GET_AGENT_CALL_LOG;
        List<Object> params = new ArrayList<Object>();
        Calendar cal = Calendar.getInstance();
        cal.setTime(callDate);
        //set time to be midnight
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH), 0, 0,0);
        //set time offset to be Pacific time since it would be the latest for call centers.
        cal.setTime(DateTimeUtils.getGMTTimeFromLocationOffset(cal.getTime(), -8));
        String callDateStart = VelocityUtils.getDateAsString(cal.getTime(), "yyyy-MM-dd HH:mm:ss");
        //set time to be 1 second before midnight of a day after
        cal.add(Calendar.DAY_OF_MONTH, 1);
        cal.add(Calendar.SECOND, -1);
        String callDateEnd = VelocityUtils.getDateAsString(cal.getTime(), "yyyy-MM-dd HH:mm:ss");
        logger.debug("callDateStart: {}", callDateStart);
        logger.debug("callDateEnd: {}", callDateEnd);
        params.add(callDateStart);
        params.add(callDateEnd);
        if (agentId != null && !agentId.equals(0)) {
            logger.debug("Request for GET AGENT CALL LOG by Agent ID [" + agentId + "]");
            String agentQuery = " AND (v.EMPLOYEE_NUMBER IN ( SELECT EMPLOYEE_NUMBER FROM CALL_CENTER_EMPLOYEE cce WHERE cce.IS_ACTIVE = 1 AND cce.SUPERVISOR_EMPLOYEE_NUMBER = ? ))"
                            + " UNION "
                            + " SELECT v.*, emp.FIRST_NAME || ' ' || emp.LAST_NAME as EMPLOYEE_NAME , cf.CALL_LOG_FILE_ID, cf.FILE_PATH "
                            + " FROM V_AGENT_CALL_LOG v "
                            + " LEFT JOIN EMPLOYEE emp ON emp.EMPLOYEE_NUMBER = v.EMPLOYEE_NUMBER "
                            + " LEFT JOIN CALL_LOG_FILE cf ON cf.UUID = v.UUID AND cf.EMPLOYEE_NUMBER = v.EMPLOYEE_NUMBER AND cf.IS_ACTIVE = 1 "
                            + " WHERE TO_CHAR(CALL_TIME, 'YYYY-MM-DD HH24:MI:SS') BETWEEN ? AND ? AND  v.EMPLOYEE_NUMBER = ? ";
            query = query.replace("'##DIRECT_SUPERVISOR##'", agentQuery);
            params.add(agentId);
            params.add(callDateStart);
            params.add(callDateEnd);
            params.add(agentId);

        } else {
            query = query.replace("'##DIRECT_SUPERVISOR##'", "");
        }

        logger.debug("Final SQL [" + query + "].");

        List<V_CallLog> list = jdbcTemplate.query(query, params.toArray(), new RowMapper<V_CallLog>() {
            @Override
            public V_CallLog mapRow(ResultSet rs, int rowNum) throws SQLException {
                V_CallLog vCallLog = new V_CallLog();
                CallLog agentCallLog = new CallLog();
                agentCallLog.setCallLogId(rs.getLong("CALL_LOG_ID"));
                agentCallLog.setUuid(rs.getString("UUID"));
                agentCallLog.setSessionId(rs.getString("SESSION_ID"));
                agentCallLog.setCallCenterName(rs.getString("CALL_CENTER"));
                agentCallLog.setEmployeeNumber(rs.getInt("EMPLOYEE_NUMBER"));
                agentCallLog.setEmployeeName(rs.getString("EMPLOYEE_NAME"));
                agentCallLog.setDirectSupervisor(rs.getInt("DIRECT_SUPERVISOR"));
                agentCallLog.setSupervisor(rs.getInt("SUPERVISOR"));
                agentCallLog.setFacilityId(rs.getInt("FACILITY_ID"));
                agentCallLog.setTempPatientId(rs.getLong("TEMP_PATIENT_ID"));
                agentCallLog.setPatientId(rs.getLong("PATIENT_ID"));
                agentCallLog.setQsiUniqueId(rs.getLong("UNIQUE_ID"));
                agentCallLog.setCallDateTime(rs.getTimestamp("CALL_CENTER_TIME"));
                agentCallLog.setCallDuration(rs.getDouble("CALL_DURATION"));
                agentCallLog.setAgentType(rs.getString("AGENT_TYPE"));
                agentCallLog.setPhoneType(rs.getString("PHONE_TYPE"));
                agentCallLog.setReasonCode(rs.getString("REASON_CODE"));
                agentCallLog.setResolutionCode(rs.getString("RESOLUTION_CODE"));
                agentCallLog.setLogStatus(rs.getString("CALL_LOG_STATUS"));
                agentCallLog.setEmailCaptured(rs.getBoolean("EMAIL_CAPTURED"));
                agentCallLog.setInsuranceWaiting(rs.getBoolean("WAITING_ON_INSURANCE"));
                agentCallLog.setAllowCredit(rs.getBoolean("ALLOW_CREDIT"));

                vCallLog.setOriginal(rs.getBoolean("IS_ORIGINAL"));

                Long appointmentCallLogId = rs.getLong("LAST_APPT_LOG_ID");
                if(appointmentCallLogId != null){
                    AppointmentCallLog appointmentCallLog = new AppointmentCallLog();
                    appointmentCallLog.setAppointmentCallLogId(appointmentCallLogId);
                    appointmentCallLog.setAppointmentDateTime(rs.getTimestamp("APPT_DATE"));
                    appointmentCallLog.setAppointmentStatus(rs.getString("APPT_LOG_STATUS"));
                    appointmentCallLog.setAppointmentValue(rs.getBigDecimal("APPT_VALUE"));
                    appointmentCallLog.setIsOrtho(rs.getBoolean("IS_ORTHO"));
                    vCallLog.setAppointmentCallLog(appointmentCallLog);
                }
                vCallLog.setAgentCallLog(agentCallLog);
                Long callLogFileId = rs.getLong("CALL_LOG_FILE_ID");
                if(callLogFileId != null){
                    CallLogFile clf = new CallLogFile();
                    clf.setCallLogFileId(callLogFileId);
                    clf.setFilePath(rs.getString("FILE_PATH"));
                    vCallLog.setCallLogFile(clf);
                }
                return vCallLog;
            }
        });
        return list;
    }

    public List<LinkedHashMap<String, String>> getCallLogDetailReport(String startDate, String endDate, List<Integer> agentIds, String dateType, String reportCriteria){
        String query = dateType != null && dateType.equalsIgnoreCase("latest")
                        ? GET_CALL_LOG_DETAIL_REPORT_BY_LATEST_APPT_DATE : GET_CALL_LOG_DETAIL_REPORT;
        List<Object> params = new ArrayList<Object>();
        params.add(startDate);
        params.add(endDate);
        String agentQuery = "";
        if(agentIds != null && agentIds.size() > 0){
            agentQuery += " AND V.EMPLOYEE_NUMBER IN (";
            String delimiter = "";
            for(Integer id : agentIds){
                agentQuery += delimiter + id;
                delimiter = ",";
            }
            agentQuery += ")";
        }
        query = query.replace("'##AGENT_QUERY##'", agentQuery);
        if(reportCriteria == null){
            reportCriteria = "";
        }else if(reportCriteria.indexOf("and") != 0 || reportCriteria.indexOf("AND") != 0){
            reportCriteria = " AND " + reportCriteria;
        }
        if(dateType != null){
            if(dateType.equalsIgnoreCase("appt")){
                query = query.replace("'##DATE_QUERY##'", " TRUNC(V.APPT_DATE) BETWEEN ? AND ?");
                query = query.replace("'##REPORT_QUERY##'", reportCriteria);
                query = query.replace("'##ORDER_BY##'", " ORDER BY V.CALL_CENTER, V.APPT_DATE");
            }else if(dateType.equalsIgnoreCase("latest")){
//                query = query.replace("'##DATE_QUERY##'", " TRUNC(V.LATEST_APPT_DATE) BETWEEN ? AND ?");
                query = query.replace("'##ORDER_BY##'", " ORDER BY V.CALL_CENTER, V.LATEST_APPT_DATE");
            }else{
                query = query.replace("'##DATE_QUERY##'", " TRUNC(V.CALL_CENTER_TIME) BETWEEN ? AND ?");
                query = query.replace("'##REPORT_QUERY##'", reportCriteria);
                query = query.replace("'##ORDER_BY##'", " ORDER BY V.CALL_CENTER_TIME");
            }

        }else{
            query = query.replace("'##DATE_QUERY##'", " TRUNC(V.CALL_CENTER_TIME) BETWEEN ? AND ?");
            query = query.replace("'##REPORT_QUERY##'", reportCriteria);
            query = query.replace("'##ORDER_BY##'", " ORDER BY V.CALL_CENTER_TIME");
        }
        List<LinkedHashMap<String, String>> result = jdbcTemplate.query(query, params.toArray(), new RowMapper<LinkedHashMap<String, String>>() {
            @Override
            public LinkedHashMap<String, String> mapRow(ResultSet rs, int rowNum) throws SQLException {
                LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
                map.put("Call Log ID", rs.getLong("CALL_LOG_ID") + "");
                map.put("Session ID", rs.getString("SESSION_ID"));
                map.put("Call Center", rs.getString("CALL_CENTER"));
                map.put("Call Start Time", rs.getString("CALL_START_TIME"));
                map.put("Call End Time", rs.getString("CALL_END_TIME"));
                map.put("Agent Name", rs.getString("AGENT_NAME"));
                map.put("Agent Type", rs.getString("AGENT_TYPE"));
                map.put("Agent Emp #", rs.getInt("EMPLOYEE_NUMBER") + "");
                map.put("Facility ID", rs.getInt("FACILITY_ID") + "");
                map.put("Facility Name", rs.getString("FACILITY_NAME"));
                map.put("CC Supported", rs.getString("SUPPORTED"));
                map.put("Clinic ID", rs.getInt("CLINIC_ID") + "");
                map.put("Patient ID", rs.getLong("PATIENT_ID") + "");
                map.put("Temp Patient ID", rs.getLong("TEMP_PATIENT_ID") + "");
                map.put("Patient Name", rs.getString("PATIENT_NAME"));
                map.put("Address", rs.getString("PATIENT_ADDRESS"));
                map.put("City/State/Zip", rs.getString("CIT_ST_ZIP"));
                map.put("Email Captured", rs.getString("EMAIL_CAPTURED"));
                map.put("Email Address", rs.getString("EMAIL_ADDRESS"));
                map.put("Waiting On Insurance", rs.getString("WAITING_ON_INSURANCE"));
                map.put("Phone 1", rs.getString("PHONE_NUMBER1"));
                map.put("Phone 2", rs.getString("PHONE_NUMBER2"));
                map.put("Date of Birth", VelocityUtils.getDateAsString(rs.getDate("DATE_OF_BIRTH"), "MM/dd/yyyy"));
                map.put("Gender", rs.getString("GENDER"));
                map.put("Language", rs.getString("PATIENT_LANGUAGE"));
                map.put("Plan Type", rs.getString("PlAN_TYPE"));
                map.put("Plan Group", rs.getString("PLAN_GROUP"));
                map.put("Referral Source", rs.getString("REFERRAL_SOURCE"));
                map.put("Phone Type", rs.getString("PHONE_TYPE"));
                map.put("Reason Code", rs.getString("REASON_CODE"));
                map.put("Resolution Code", rs.getString("RESOLUTION_CODE"));
                map.put("Call Log Status", rs.getString("CALL_LOG_STATUS"));
                map.put("Ring Time (sec)", rs.getInt("RING_TIME") + "");
                map.put("Talk Time (sec)", rs.getInt("TALK_TIME") + "");
                map.put("Hold Time (sec)", rs.getInt("HOLD_TIME") + "");
                map.put("Work Time (sec)", rs.getInt("WORK_TIME") + "");
                map.put("Appt Facility ID", rs.getInt("APPT_FACILITY_ID")+ "");
                map.put("Appt Provider ID", rs.getInt("APPT_PROVIDER_ID") + "");
                map.put("Appt Type", rs.getString("APPT_TYPE"));
                map.put("Appt Log Status", rs.getString("APPT_LOG_STATUS"));
                map.put("Previous Appt Log Status", rs.getString("PREVIOUS_APPT_LOG_STATUS"));
                map.put("Appt Date", VelocityUtils.getDateAsString(rs.getTimestamp("APPT_DATE"), "MM/dd/yyyy hh:mm:ss a"));
                map.put("Latest Appt Date", VelocityUtils.getDateAsString(rs.getTimestamp("LATEST_APPT_DATE"), "MM/dd/yyyy hh:mm:ss a"));
                map.put("Is Original", rs.getString("IS_ORIGINAL"));
                map.put("SLA", rs.getString("SLA"));
                map.put("Is Ortho", rs.getString("IS_ORTHO"));
                map.put("Allow Credit", rs.getString("ALLOW_CREDIT"));
                map.put("Was Verified", rs.getString("WAS_VERIFIED"));
                return map;
            }
        });
        return result;
    }

    public List<CallCenterEmployee> getSupervisedAgents(Integer supervisorId){
        return jdbcTemplate.query(GET_SUPERVISED_AGENT, new Object[]{supervisorId}, new RowMapper<CallCenterEmployee>() {
            @Override
            public CallCenterEmployee mapRow(ResultSet rs, int rowNum) throws SQLException {
                CallCenterEmployee cce = new CallCenterEmployee();
                cce.setEmployeeNumber(rs.getInt("EMPLOYEE_NUMBER"));
                cce.setEmployeeName(rs.getString("EMPLOYEE_NAME"));
                cce.setSupervisorNumber(rs.getInt("SUPERVISOR_EMPLOYEE_NUMBER"));
                cce.setSupervisorName(rs.getString("SUPERVISOR_EMPLOYEE_NAME"));
                cce.setCallCenter(rs.getString("CALL_CENTER"));
                return cce;
            }
        });
    }

    public boolean checkIfApptCallLogExist(Long tempPatientId, Date appointmentDate, Integer facilityId, Long appointmentLogId){
        String query = CHECK_IF_APPT_CALL_LOG_EXIST;
        List<Object> params = new ArrayList<Object>();
        params.add(tempPatientId);
        params.add(facilityId);
        params.add(VelocityUtils.getDateAsString(appointmentDate,"MM-dd-yyyy HH:mm"));
        if(appointmentLogId != null){
            String apptQuery = " AND LAST_APPT_LOG_ID != ?";
            apptQuery += " AND EXISTS (SELECT null FROM V_AGENT_CALL_LOG v2 WHERE v2.LAST_APPT_LOG_ID != v.LAST_APPT_LOG_ID";
            apptQuery += " AND v2.TEMP_PATIENT_ID = v.TEMP_PATIENT_ID AND v2.FACILITY_ID = v.FACILITY_ID AND v2.APPT_DATE = v.APPT_DATE";
            apptQuery += " AND nvl(v2.IS_ORIGINAL, -1) = nvl(v.IS_ORIGINAL, -1))";
            query = query.replace("'##APPT_LOG_ID##'", apptQuery);
            params.add(appointmentLogId);
        }else{
            query = query.replace("'##APPT_LOG_ID##'", "");
        }
        List<Map<String, Object>> list = jdbcTemplate.queryForList(query, params.toArray());
        return list.size() > 0;
    }

    public String getEmployeeName(Integer employeeNumber, int formatType){
        List<Map<String, Object>> list = jdbcTemplate.queryForList(GET_EMPLOYEE_NAME, new Object[]{employeeNumber});
        String result = "";
        if(list.size() > 0){
            String firstName = (String)list.get(0).get("FIRST_NAME");
            String lastName = (String)list.get(0).get("LAST_NAME");
            if(formatType == 0){    //FULL NAME
                result = firstName + " " + lastName;
            }else if(formatType == 1){  //FN + INITIAL OF LN
                result = VelocityUtils.getAbbreviationForAgentName(firstName, lastName);
            }
        }
        return result;
    }

    public List<Map<String, Object>> getTeamHierarchy(){
        List<Map<String, Object>> team = jdbcTemplate.query(GET_TEAM_HIERARCHY, new RowMapper<Map<String, Object>>() {
            @Override
            public Map<String, Object> mapRow(ResultSet rs, int rowNum) throws SQLException {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("EMP_SUPERVISOR_ID", rs.getInt("EMP_SUPERVISOR_ID"));
                map.put("EMPLOYEE_NUMBER", rs.getInt("EMPLOYEE_NUMBER"));
                map.put("EMPLOYEE_NAME", rs.getString("EMPLOYEE_NAME"));
                map.put("SUPERVISOR_EMPLOYEE_NUMBER", rs.getInt("SUPERVISOR_EMPLOYEE_NUMBER"));
                map.put("SUPERVISOR_EMPLOYEE_NAME", rs.getString("SUPERVISOR_EMPLOYEE_NAME"));
                return map;
            }
        });
        return team;
    }

    public List<CallCenterEmployee> getCallCenterResource(boolean supervisor){
        String query = supervisor ? GET_CALL_CENTER_TEAM_LEAD : GET_CALL_CENTER_EMPLOYEE;
        List<CallCenterEmployee> resource = jdbcTemplate.query(query, new RowMapper<CallCenterEmployee>() {
            @Override
            public CallCenterEmployee mapRow(ResultSet rs, int rowNum) throws SQLException {
                CallCenterEmployee cce = new CallCenterEmployee();
                cce.setEmployeeNumber(rs.getInt("EMPLOYEE_NUMBER"));
                cce.setEmployeeName(rs.getString("EMPLOYEE_NAME"));
                cce.setState(rs.getString("STATE"));
                cce.setCallCenter(rs.getString("CALL_CENTER"));
                return cce;
            }
        });
        return resource;
    }

    public CallCenterEmployee getCallCenterEmployeeFromEmployee(Integer employeeNumber, boolean needTimeout){
        JdbcTemplate template = needTimeout ? jdbcTemplateWith3sTimeout : jdbcTemplate;
        List<CallCenterEmployee> list = template.query(GET_EMPLOYEE_INFO_BY_NUMBER, new Object[]{employeeNumber}, new CallCenterEmployeeRowMapper());
        if(list.size() > 0){
            return list.get(0);
        }else{
            return null;
        }
    }

    public CallCenterEmployee getCallCenterEmployeeByNumber(Integer employeeNumber, boolean needTimeout){
        CallCenterEmployee cce = mongoRepository.findByEmployeeNumber(employeeNumber);
        if(cce != null ){
            logger.debug("Return call center employee[" + employeeNumber + "] from mongo");
            return cce;
        }
        JdbcTemplate template = needTimeout ? jdbcTemplateWith3sTimeout : jdbcTemplate;
        List<CallCenterEmployee> list = template.query(GET_CALL_CENTER_EMPLOYEE_BY_NUMBER, new Object[]{employeeNumber}, new CallCenterEmployeeRowMapper());
        if(list.size() > 0){
            cce = list.get(0);
            mongoRepository.persistCallCenterEmployee(cce);
            logger.debug("Return call center employee[" + employeeNumber + "] from oracle");
            return cce;
        }else{
            return null;
        }
    }

    public List<CallCenterEmployee> getCallCenterEmployee(boolean needTimeout){
        JdbcTemplate template = needTimeout ? jdbcTemplateWith3sTimeout : jdbcTemplate;
        List<CallCenterEmployee> list = template.query(GET_CALL_CENTER_EMPLOYEE_BY_NUMBER, new Object[]{null}, new CallCenterEmployeeRowMapper());
        return list;
    }

    public int updateCallCenterEmployee(Integer newSupervisorNumber, Integer employeeNumber){
        int result = 0;
        boolean hasChange = true;
        CallCenterEmployee existing = getCallCenterEmployeeByNumber(employeeNumber, false);

        if (existing != null) {
            Integer supervisorNumber = existing.getSupervisorNumber();
            logger.debug("Updating existing : " + existing.toString() + " Supervisor : " + supervisorNumber + " to " + newSupervisorNumber);

            if (supervisorNumber != null && !supervisorNumber.equals(newSupervisorNumber)) {
                hasChange = jdbcTemplate.update(INACTIVATE_CALL_CENTER_EMPLOYEE, new Object[]{existing.getEmpSupervisorId()}) > 0;
                logger.debug(INACTIVATE_CALL_CENTER_EMPLOYEE + " --> " + existing.getEmpSupervisorId() + " produced : " + hasChange);

            } else {
                hasChange = false;
                result = 1;
            }
        }

        logger.debug("Has Change ? " + hasChange);
        if (hasChange) {
            result = jdbcTemplate.update(INSERT_CENTER_EMPLOYEE_BY_NUMBER, new Object[]{employeeNumber, newSupervisorNumber});
            if (existing == null) {
                existing = getCallCenterEmployeeByNumber(employeeNumber, false);
            }
            try{
                existing.setSupervisorNumber(newSupervisorNumber);
                mongoRepository.persistCallCenterEmployee(existing);
            }catch (Exception ex){
                ex.printStackTrace();
                logger.debug("Fail to save to mongodb employeeNumber[" + employeeNumber + "] and supervisor[" + newSupervisorNumber + "]");
            }
        }
        return result;
    }

    public boolean removeCallCenterEmployeeAssociation(Integer employeeNumber, boolean forceInactivation) {
        logger.debug("Request to Remove a new Call Center Employee Association for Employee Number [" + employeeNumber + "].  Force --> " + forceInactivation);

        boolean result = true;
        CallCenterEmployee existing = getCallCenterEmployeeByNumber(employeeNumber, false);

        if (existing != null || forceInactivation) {
            logger.debug("Inactivating all active Call Center Employee Associations for Employee Number [" + employeeNumber + "]");
            result = jdbcTemplate.update(INACTIVATE_ACTIVE_CALL_CENTER_EMPLOYEE, new Object[]{ employeeNumber }) > 0;

            try{
                existing.setActive(false);
                mongoRepository.persistCallCenterEmployee(existing);
            }catch (Exception ex){
                ex.printStackTrace();
                logger.debug("Fail to update MongoDB Employee Number [" + employeeNumber + "].");
            }

        } else {
            logger.warn("Employee Number [" + employeeNumber + "] was not found!  No Call Center Employee records in Oracle were inactivated.");
            result = false;
        }

        return result;
    }

    public boolean addCallCenterEmployeeAssociation(Integer employeeNumber, Integer supervisorEmployeeNumber, boolean forceActivation) {
        logger.debug("Request to Add a new Call Center Employee Association for Employee Number [" + employeeNumber
                    + "] and Supervisor Employee Number [" + supervisorEmployeeNumber + "].  Force --> " + forceActivation);

        boolean result = true;
        CallCenterEmployee existing = getCallCenterEmployeeByNumber(employeeNumber, false);

        if (forceActivation) {
            removeCallCenterEmployeeAssociation(employeeNumber, false);
        }

        if (existing != null || forceActivation) {
            jdbcTemplate.update(INSERT_CENTER_EMPLOYEE_BY_NUMBER, new Object[]{employeeNumber, supervisorEmployeeNumber});
            CallCenterEmployee _existing = getCallCenterEmployeeByNumber(employeeNumber, false);

            if (existing == null || ! existing.getEmpSupervisorId().equals(_existing.getEmpSupervisorId())) {
                try {

                    mongoRepository.persistCallCenterEmployee(_existing);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    logger.debug("Fail to save to mongodb employeeNumber[" + employeeNumber + "] and supervisor[" + supervisorEmployeeNumber + "]");
                }
            } else {
                logger.error("Something has gone wrong!  A new Oracle CCE record was added however the old record's ID is being returned. These values should not equal.");
                throw new RuntimeException("Something has gone wrong!  A new Oracle CCE record was added however the old record's ID is being returned. These values should not equal.");
            }
        }


        return result;
    }


    public List<AppointmentCallLog> getEditedAppointmentCallLog(Date dt){
        List<AppointmentCallLog> list = jdbcTemplate.query(GET_CALL_LOG_EDITED, new Object[]{VelocityUtils.getDateAsString(dt, "MM-dd-yyyy")}, new RowMapper<AppointmentCallLog>() {
            @Override
            public AppointmentCallLog mapRow(ResultSet rs, int rowNum) throws SQLException {
                AppointmentCallLog appt = new AppointmentCallLog();
                appt.setAppointmentCallLogId(rs.getLong("APPT_LOG_ID"));
                appt.setCallLogId(rs.getLong("CALL_LOG_ID"));
                appt.setAppointmentDateTime(rs.getTimestamp("APPT_DATE"));
                appt.setAppointmentStatus(rs.getString("APPT_LOG_STATUS"));
                return appt;
            }
        });
        return list;
    }

    public List<Map<String, Object>> getFacilitySupportValues() {
        return postgresTemplate.queryForList(cdwFacilitySupport);
    }

    public List getSpamPhoneNumbers() {
        return postgresTemplate.query(cdwSpamPhoneNumbers, new RowMapper<PhoneNumberRoutingRules>() {
            @Override
            public PhoneNumberRoutingRules mapRow(ResultSet rs, int rowNum) throws SQLException {
                PhoneNumberRoutingRules rule = new PhoneNumberRoutingRules();
                rule.setPhoneNumber(rs.getLong("phone_number"));
                rule.setCreateDateTime(rs.getDate("date_added"));
                rule.setDescription(rs.getString("description"));
                rule.setSpam( (rs.getInt("is_spam") == 1 ? true : false) );
                rule.setRedirectCsr( rs.getInt("redirect_to_csr") == 1 ? true : false) ;
                rule.setRedirectQueueName(rs.getString("redirect_queue"));

                return rule;
            }
        });
    }

    public List<Map<String, Object>> getFacilitySupportValues(Integer facilityId) {
        return postgresTemplate.queryForList(cdwFacilitySupport + " where facility_id = " + facilityId);
    }

    public List<Map<String, Object>> getCiscoAgentCallByDate(List<Integer> agentIds, Date dt){
        String query = cdwCiscoAgentCallByDate;
        String dateStr = "'" + VelocityUtils.getDateAsString(dt, "dd-MMM-yyyy") + "'";
        logger.debug("dateStr: {}", dateStr);
        String arrayStr = "";
        for(int i=0; i<agentIds.size(); i++){
            arrayStr += "'" + agentIds.get(i) + "'";
            if(i < agentIds.size() - 1){
                arrayStr += ",";
            }
        }
        query = query.replace("#DATE#", dateStr);
        query = query.replace("#AGENT_IDS#", arrayStr);
        return postgresTemplate.queryForList(query);
    }

    public List<String> getEmailByReport(String reportType){
        String query = GET_EMAIL_FOR_REPORT;
        return jdbcTemplate.query(query, new Object[]{reportType}, new RowMapper<String>() {
            @Override
            public String mapRow(ResultSet rs, int rowNum) throws SQLException {
                return rs.getString("EMAIL_ADDRESS");
            }
        });
    }

    public List<Map> getOpenBookSupportedFacilities(){
        return jdbcTemplate.query(GET_OPEN_BOOK_SUPPORTED_FACILITES, new RowMapper<Map>() {
            @Override
            public Map mapRow(ResultSet rs, int rowNum) throws SQLException {
                Map m = new HashMap();
                m.put("facilityId", rs.getString("FACILITY_ID"));
                m.put("facilityName", rs.getString("FACILITY_NAME"));

                return m;
            }
        });
    }

    public List<Map> getQsiFacilitiesByAni(Long phoneNumber){
        return jdbcTemplate.query(GET_QSI_PATIENT_FACILITY, new String[]{ phoneNumberToQSIFormat(phoneNumber) }, new RowMapper<Map>() {
            @Override
            public Map mapRow(ResultSet rs, int rowNum) throws SQLException {
                Map m = new HashMap();
                m.put("facilityId", rs.getInt("FACILITY_ID"));
                m.put("facilityName", rs.getString("FACILITY_NAME"));

                return m;
            }
        });
    }

    public List<Map> getLibertyFacilitiesByAni(Long phoneNumber){
        return jdbcTemplate.query(GET_LIBERTY_PATIENT_FACILITY, new Long[]{ phoneNumber }, new RowMapper<Map>() {
            @Override
            public Map mapRow(ResultSet rs, int rowNum) throws SQLException {
                Map m = new HashMap();
                m.put("facilityId", rs.getInt("FACILITY_ID"));
                m.put("facilityName", rs.getString("FACILITY_NAME"));

                return m;
            }
        });
    }
}
