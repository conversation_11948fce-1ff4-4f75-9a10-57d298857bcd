package com.smilebrands.callcentersupport.service;

import com.smilebrands.callcentersupport.domain.*;
import com.smilebrands.callcentersupport.domain.Facility;
import com.smilebrands.callcentersupport.domain.constants.HourType;
import com.smilebrands.callcentersupport.domain.helper.InclusionFileNameFilter;
import com.smilebrands.callcentersupport.domain.partner.PartnerConfig;
import com.smilebrands.callcentersupport.domain.report.InboundCallSummary;
import com.smilebrands.callcentersupport.domain.report.InboundCallSummaryId;
import com.smilebrands.callcentersupport.domain.report.InboundCallSummaryValue;
import com.smilebrands.callcentersupport.repo.CallRepository;
import com.smilebrands.callcentersupport.repo.JdbcRepository;
import com.smilebrands.callcentersupport.repo.MongoRepository;
import com.smilebrands.callcentersupport.repo.jpa.CallLogFileRepository;
import com.smilebrands.callcentersupport.repo.jpa.CallLogRepository;
import com.smilebrands.callcentersupport.util.BaseJsonConverter;
import com.smilebrands.callcentersupport.util.CTI_FFMPEGLocator;
import com.smilebrands.callcentersupport.util.DateTimeUtils;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import it.sauronsoftware.jave.AudioAttributes;
import it.sauronsoftware.jave.Encoder;
import it.sauronsoftware.jave.EncoderException;
import it.sauronsoftware.jave.EncodingAttributes;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

/**
 * User: Marlin Clark
 * Date: 6/26/14
 */
@Service
public class CallServiceImpl extends BaseService implements CallService {

    private final static Format dateFormatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mmaa");

    private final static List<String> resolutionToTriggerEmail = new ArrayList<String>(){{
        add("BUSY");
        add("VM-MACHINE");
        add("VM-PERSON");
        add("EMAIL");
        add("NO-ANS");
        add("WEB-WRONG-NUMBER");
    }};

    @Autowired
    protected MongoRepository mongoRepository;

    @Autowired
    protected CallRepository callRepository;

    @Autowired
    protected CallLogRepository callLogRepository;

    @Autowired
    protected CallLogFileRepository callLogFileRepository;

    @Autowired
    protected CommunicationService communicationService;

    @Autowired
    protected WebRequestService webRequestService;

    @Autowired
    protected CallLogService callLogService;

    @Autowired
    protected JdbcRepository jdbcRepository;

    @Override
    public Facility findFacilityByFacilityId(Integer facilityId) {
        return facilityId != null
                        ? mongoRepository.findFacilityDetail(facilityId)
                        : null;
    }

    @Override
    public Campaign findCampaign(Long dnis) {
        return mongoRepository.findCampaign(dnis);
    }

    @Override
    public List<Facility> findFacilitiesByZipCode(String zipCode) {
        return mongoRepository.findFacilitiesByZipCode(zipCode, 7);
    }

    @Override
    public String generateUUID(){
        InboundCall check = null;
        String uuid = null;
        do{
            uuid = UUID.randomUUID().toString();
            check = mongoRepository.getInboundCall(uuid);   //MAKE SURE THE UUID IS NOT USED YET
        }while(check != null);
        return uuid;
    }

    @Override
    public InboundCall registerCall(Long dnis, Long ani, String sessionId) {
        InboundCall call = new InboundCall();

        call.setUuid(generateUUID());

        call.setDnis(dnis);
        call.setAni(ani);
        call.setSessionId(sessionId);

        // -- Eval the ANI to determine if this call requires special routing.
        if (call.getAni() != null) {
            PhoneNumberRoutingRules rule = mongoRepository.getPhoneNumberRoutingRules(call.getAni());
            if (rule != null && rule.getIsActive()) {
                call.setAniRoutingRuleDetected(true);
                call.setAniRoutingRuleQueueName(rule.getRedirectQueueName());

                if (rule.getSpam()) {
                    call.setAniRoutingIsSpam(true);
                }

                logger.warn("ROUTING RULE -- Inbound Call with ANI [" + call.getAni() + "] has an active Route Rule using Queue [" + rule.getRedirectQueueName() + "]");
            }
        }

        PhoneNumber number = mongoRepository.findPhoneNumber(dnis);
        if (number != null && number.isActive()) {
            call.setPhoneType(number.getPhoneType());

            if (number.getPhoneType().equalsIgnoreCase("TF")) {
                /*Npa npa = mongoRepository.getNpaByAni(ani);

                if (npa != null) {
                    call.setState(npa.getState());
                    call.setTimeZone(npa.getTimeZone());

                    if (npa.getZipCodes().size() > 0) {
                        call.setZipCode(npa.getZipCodes().iterator().next());
                        List<Facility> facilities = mongoRepository.findFacilitiesByZipCode(call.getZipCode(), 1);

                        if (facilities.size() == 1) {
                            call.setTargetFacilityId(facilities.get(0).getFacilityId());
                            PhoneNumber officeNumber = mongoRepository.findPhoneNumberByFacility(call.getTargetFacilityId(), "ML");

                            if (officeNumber != null) {
                                number = officeNumber;
                            }
                        }
                    }
                }
                call.setWireless(npa == null ? false : npa.isMobile());*/

            } else if (number.getPhoneType().equalsIgnoreCase("ML")) {
                PhoneNumber officeNumber = mongoRepository.findPhoneNumber(ani);

                if (officeNumber != null) {
                    call.setOfficeToOffice(officeNumber.getPhoneType().equalsIgnoreCase("ML"));

                } else if (ani != null && ani.toString().length() == 10) {
                    Facility facility = mongoRepository.findFacilityDetail(number.getFacilityId());

                    if (facility != null) {
                        int existingPatient = 0;
                        Date start = new Date();
                        long duration = 0;

                        if (facility.isLibertySupported()) {
                            existingPatient = jdbcRepository.hasPriorLibertyAppointment(ani, facility.getFacilityId());
                            duration = new Date().getTime() - start.getTime();

                        } else {
                            existingPatient = jdbcRepository.hasPriorQsiAppointment(ani, facility.getFacilityId());
                            duration = new Date().getTime() - start.getTime();
                        }

                        logger.debug("duration: {}", duration);
                        call.setExistingPatient(existingPatient == 1);

                        // -- add in Facility Call Center support values.
                        call.setCsrPriority(facility.getCsrPriority());
                        call.setPsrPriority(facility.getPsrPriority());
                        call.setCsrSupported(facility.isCsrSupported());
                        call.setPsrSupported(facility.isPsrSupported());

                        // -- add in Facility Is Open calc using the Facility Hours.
                        Integer dayOfWeek = Calendar.getInstance().get(Calendar.DAY_OF_WEEK);
                        switch (dayOfWeek) {
                            case 0:
                                if (facility.getSundayHour() != null && facility.getSundayHour().equalsIgnoreCase("CLOSED")) { call.setOpen(false); }
                                break;
                            case 1:
                                if (facility.getMondayHour() != null && facility.getMondayHour().equalsIgnoreCase("CLOSED")) { call.setOpen(false); }
                                break;
                            case 2:
                                if (facility.getTuesdayHour() != null && facility.getTuesdayHour().equalsIgnoreCase("CLOSED")) { call.setOpen(false); }
                                break;
                            case 3:
                                if (facility.getWednesdayHour() != null && facility.getWednesdayHour().equalsIgnoreCase("CLOSED")) { call.setOpen(false); }
                                break;
                            case 4:
                                if (facility.getThursdayHour() != null && facility.getThursdayHour().equalsIgnoreCase("CLOSED")) { call.setOpen(false); }
                                break;
                            case 5:
                                if (facility.getFridayHour() != null && facility.getFridayHour().equalsIgnoreCase("CLOSED")) { call.setOpen(false); }
                                break;
                            case 6:
                                if (facility.getSaturdayHour() != null && facility.getSaturdayHour().equalsIgnoreCase("CLOSED")) { call.setOpen(false); }
                                break;
                        }

                        // -- register ANI Recognition Event
                        if (existingPatient == 1) {
                            InboundCallEvent event = new InboundCallEvent();
                            event.setEventDateTime(new Date());
                            event.setEventId("ANI Recognition");

                            call.getInboundCallEvents().add(event);

                        } else if (existingPatient == 2) { //QUERY TIME OUT
                            InboundCallEvent event = new InboundCallEvent();
                            event.setEventDateTime(new Date());
                            event.setEventId("Query Timeout");

                            call.getInboundCallEvents().add(event);
                        }
                    }
                }

            } else if (number.isSearchOnAni()) {
                // -- set a custom menu number press option
                call.setMainMenuNumberPress(91);
                call.getMainMenuNumberPresses().add(91);

                // -- add the language linked to the Phone Number.
                if (number.getLanguage() != null) {
                    call.setLanguage(number.getLanguage());
                }

                // -- update the Phone Number to be a Facility Number as if the Caller dialed the number directly.
                PhoneNumber aniNumber = mongoRepository.findPhoneNumber(ani);
                if (aniNumber != null) {
                    number = aniNumber;
                    number.setPhoneType("TX");
                    number.setBrandName("Smile Brands");

                } else {
                    logger.warn("I found a configured DID Phone Number but the ANI is not a valid Facility Phone Number!");
                }
            }

            call.setPhoneNumber(number);
            if (number != null && !number.getPhoneType().equalsIgnoreCase("TF")) {
                call.setTargetFacilityId(number.getFacilityId());

                if (call.getTargetFacilityId() != null && !call.getTargetFacilityId().equals(0)) {
                    Facility facility = mongoRepository.findFacilityDetail(call.getTargetFacilityId());

                    if (facility != null) {
                        call.setTtsFacility(facility.getTTSFacility());
                        call.setTtsFacilityHour(facility.getTTSFacilityHour(call.getLanguage(), true));
                    }
                }
            }
            call.setMatched(call.getTargetFacilityId() != null && !call.getTargetFacilityId().equals(0));
        }

        Date loadedDate = new Date();
        if (call.getPhoneNumber() != null && call.getPhoneNumber().getCallCenterName() != null
                && call.getPhoneNumber().getCallCenterName().trim().length() > 0) {

            CallCenter callCenter = mongoRepository.getCallCenterByName(call.getPhoneNumber().getCallCenterName());
            if (callCenter != null) {
                loadedDate = DateTimeUtils.getLoadedTimeByLocationOffset(callCenter.getTimezoneOffset());
            }
        }

        call.setCreateDateTime(loadedDate);
        call.setCreateDate(VelocityUtils.getDateAsString(loadedDate, null));
        call.setCreateTime(VelocityUtils.getDateAsString(loadedDate, "HHmmss"));
        mongoRepository.registerInboundCall(call);

        // -- Check to see if this is a follow up call with a Prior Agent record and that record is less than 14 days old.
        if (call.getUuid() != null && call.getAni() != null) {
            AgentCall returningCall = findAgentCallByAni(call.getUuid(), call.getAni());

            if (returningCall != null && returningCall.getLoadedEmployees().size() > 0) {
                call.setPriorAgentDate(returningCall.getLoadedEmployees().get(0).getLoadedDateTime());
                call.setPriorAgentId(returningCall.getLoadedEmployees().get(0).getEmployeeNumber());

                // -- override the psr / csr priority values based on this event
            }
        }

        return call;
    }

    @Override
    public InboundCall copyInboundCall(String oldSessionId, String newSessionId) {
        InboundCall oldInboundCall = mongoRepository.getInboundCallBySessionId(oldSessionId);
        InboundCall inboundCall = null;
        if(oldInboundCall != null){
            inboundCall = oldInboundCall;
            inboundCall.setCopiedFromUUID(oldInboundCall.getUuid());
            inboundCall.setUuid(generateUUID());
            inboundCall.setSessionId(newSessionId);

            Date loadedDate = new Date();
            if(inboundCall.getPhoneNumber() != null && inboundCall.getPhoneNumber().getCallCenterName() != null
                    && inboundCall.getPhoneNumber().getCallCenterName().trim().length() > 0){
                CallCenter callCenter = mongoRepository.getCallCenterByName(inboundCall.getPhoneNumber().getCallCenterName());
                if(callCenter != null){
                    loadedDate = DateTimeUtils.getLoadedTimeByLocationOffset(callCenter.getTimezoneOffset());
                }
            }
            inboundCall.setCreateDateTime(loadedDate);
            inboundCall.setCreateDate(VelocityUtils.getDateAsString(loadedDate, null));
            inboundCall.setCreateTime(VelocityUtils.getDateAsString(loadedDate, "HHmmss"));

            InboundCallEvent event = new InboundCallEvent();
            event.setEventDateTime(new Date());
            event.setEventId("Copy Call");
            inboundCall.getInboundCallEvents().add(event);

            mongoRepository.registerInboundCall(inboundCall);
        }
        return inboundCall;
    }

    @Override
    public void registerCallEvent(String uuid, String eventId) {
        mongoRepository.addInboundCallEvent(uuid, eventId);
    }

    @Override
    public void registerCallLanguage(String uuid, String language) {
        mongoRepository.addInboundCallLanguage(uuid, language);
    }

    @Override
    public void registerCallMainMenuNumberPress(String uuid, Integer mainMenuNumberPress) {
        mongoRepository.addInboundCallMainMenuNumberPress(uuid, mainMenuNumberPress);
    }

    @Override
    public void registerSessionId(String uuid, String sessionId) {
        mongoRepository.addInboundCallSessionId(uuid, sessionId);
    }

    @Override
    public InboundCall findInboundCall(String uuid) {
        return mongoRepository.getInboundCall(uuid);
    }

    @Override
    public InboundCall findInboundCallBySessionId(String sessionId) {
        return mongoRepository.getInboundCallBySessionId(sessionId);
    }

    @Override
    public void updateInboundCall(InboundCall call) {
        mongoRepository.updateInboundCall(call);
    }

    @Override
    public void updateInboundCallScreenPopResult(String uuid, Integer agentId, boolean result) {
        InboundCall call = mongoRepository.getInboundCall(uuid);

        for (ScreenPopUrl pop : call.getScreenPopUrls()) {
            if (pop.getAgentEmployeeNumber().equals(agentId)) {
                pop.setLoaded(result);
            }
        }

        updateInboundCall(call);
    }

    @Override
    public PhoneNumber findPhoneNumberByFacilityId(Integer facilityId) {
        return mongoRepository.findPhoneNumberByFacility(facilityId);
    }

    @Override
    public PhoneNumber findPhoneNumberByFacilityId(Integer facilityId, String uuid) {
        InboundCall inboundCall = mongoRepository.getInboundCall(uuid);
        if(inboundCall != null){
            PhoneNumber number = mongoRepository.findPhoneNumber(inboundCall.getDnis());
            if(number != null && number.getFacilityId() != null && number.getFacilityId().equals(facilityId)){
                return inboundCall.getPhoneNumber();
            }
        }
        return mongoRepository.findPhoneNumberByFacility(facilityId);
    }

    @Override
    public PhoneNumber findPhoneNumberByFacilityAndPhoneType(Integer facilityId, String phoneType) {
        return mongoRepository.findPhoneNumberByFacility(facilityId, phoneType);
    }

    @Override
    public AgentCall startAgentCall(String uuid, Integer employeeNumber, Integer facilityId, String screenType, String sessionId) {

        String callCenterName = getCallCenterName(employeeNumber, uuid, screenType, false);
        Date loadedDate = getLoadedTimeByLocationOffset(callCenterName);

        AgentCall agentCall = new AgentCall();
        agentCall.setUuid(uuid);

        if(screenType.equalsIgnoreCase("wr")){
            agentCall.setCallRegisteredDate(VelocityUtils.getDateAsString(loadedDate, null));
            agentCall.setCallRegisteredTime(VelocityUtils.getDateAsString(loadedDate, "HHmmss"));
        }else{
            InboundCall call = mongoRepository.getInboundCall(uuid);
            agentCall.setSessionId(call.getSessionId());
            agentCall.setDnis(call.getDnis());
            agentCall.setPhoneType(call.getPhoneType());
            agentCall.setAni(call.getAni());
            agentCall.setCallRegisteredDate(VelocityUtils.getDateAsString(call.getCreateDateTime(), null));
            agentCall.setCallRegisteredTime(VelocityUtils.getDateAsString(call.getCreateDateTime(), "HHmmss"));
        }

        agentCall.setCallCenterName(callCenterName);

        agentCall.setCurrentFacilityId(facilityId);
        agentCall.setEmployeeNumber(employeeNumber);
        agentCall.setScreenPopOpenDateTime(loadedDate);
        agentCall.setScreenType(screenType);


        agentCall.getLoadedFacilities().add(new LoadedFacility(facilityId == null ? 0 : facilityId, new Date()));


        LoadedEmployee loadedEmployee = new LoadedEmployee(employeeNumber, loadedDate, screenType);
        loadedEmployee.setSequence(agentCall.getLoadedEmployees().size() + 1);
        loadedEmployee.setSessionId(sessionId);
        if(loadedEmployee.getLoadedDate() == null){
            communicationService.sendNotificationViaEmail(CommunicationServiceImpl.DEVELOPER_EMAIL, "Loaded employee has no loaded date/time for agent[" + employeeNumber + "] with UUID[" + uuid + "].", "Missing loaded date/time for Agent_Call_Log");
        }
        agentCall.getLoadedEmployees().add(loadedEmployee);

        return mongoRepository.registeredAgentCall(agentCall);
    }

    @Override
    public AgentCall findAgentCall(String uuid) {
        return mongoRepository.getAgentCall(uuid);
    }

    @Override
    public AgentCall findAgentCallByAni(String uuid, Long ani) {
        return mongoRepository.searchAgentCallByAni(uuid, ani);
    }

    @Override
    public AgentCall updateAgentCallWithFacility(String uuid, Integer facilityId) {
        AgentCall agentCall = mongoRepository.getAgentCall(uuid);

        agentCall.setCurrentFacilityId(facilityId);
        agentCall.getLoadedFacilities().add(new LoadedFacility(facilityId == null ? 0 : facilityId, new Date()));

        return mongoRepository.updateAgentCall(agentCall);
    }

    @Override
    public AgentCall updateAgentCallWithEmployeeNumber(String uuid, Integer employeeNumber, String screenType) {
        AgentCall agentCall = mongoRepository.getAgentCall(uuid);
        updateAgentCallWithScreenType(uuid, screenType);
        if(agentCall.getEmployeeNumber() == null || !agentCall.getEmployeeNumber().equals(employeeNumber)){
            agentCall.getAgentEmployeeNumbers().add(agentCall.getEmployeeNumber());
            agentCall.setEmployeeNumber(employeeNumber);
        }

        String callCenterName = getCallCenterName(employeeNumber, uuid, screenType, false);

        Date loadedDate = getLoadedTimeByLocationOffset(callCenterName);
        LoadedEmployee loadedEmployee = new LoadedEmployee(employeeNumber, loadedDate, screenType);
        loadedEmployee.setSequence(agentCall.getLoadedEmployees().size() + 1);
        agentCall.getLoadedEmployees().add(loadedEmployee);
        mongoRepository.updateAgentCall(agentCall);
        return agentCall;

    }

    @Override
    public String getCallCenterName(Integer employeeNumber, String uuid, String screenType, boolean byFacility){
        String callCenterName = null;

        if(byFacility){
            InboundCall call = findInboundCall(uuid);
            if(call != null){
                if(call.getPhoneNumber() != null){
                    callCenterName = call.getPhoneNumber().getCallCenterName();
                }else{
                    PhoneNumber phoneNumber = mongoRepository.findPhoneNumber(call.getDnis());
                    if(phoneNumber != null){
                        callCenterName = phoneNumber.getCallCenterName();
                    }else if(call.getTargetFacilityId() != null && call.getTargetFacilityId() != 0){
                        phoneNumber = mongoRepository.findPhoneNumberByFacility(call.getTargetFacilityId(), "ML");
                        if(phoneNumber != null){
                            callCenterName = phoneNumber.getCallCenterName();
                        }
                    }
                }
            }
        }
        if((callCenterName == null || callCenterName.trim().length() == 0) && screenType != null && screenType.equalsIgnoreCase("wr")){
            WebRequest wr = mongoRepository.findWebRequest(uuid);
            if(wr != null){
                PhoneNumber phoneNumber = mongoRepository.findPhoneNumberByFacility(wr.getOffice(), "ML");
                if(phoneNumber != null){
                    callCenterName = phoneNumber.getCallCenterName();
                }
            }
        }
        if((callCenterName == null || callCenterName.trim().length() == 0) && !byFacility){
            CallCenterEmployee cce = jdbcRepository.getCallCenterEmployeeByNumber(employeeNumber, true);
            if(cce != null && cce.getCallCenter() != null){
                callCenterName = cce.getCallCenter();
            }
        }

        return callCenterName == null || callCenterName.trim().length() == 0 ? "Irvine" : callCenterName;
    }

    @Override
    public CallCenter getCallCenterByName(String callCenterName) {
        if(callCenterName == null || callCenterName.trim().length() == 0){
            callCenterName = "Irvine";
        }
        return mongoRepository.getCallCenterByName(callCenterName);
    }

    @Override
    public void updateLoadedEmployeeSession(String uuid, String sessionId, Integer employeeNumber, String screenType) {
        if(sessionId != null && sessionId.trim().length() > 0){
            AgentCall agentCall = mongoRepository.getAgentCall(uuid);
            if(agentCall != null){
                LoadedEmployee loadedEmployee = agentCall.findLoadedEmployee(employeeNumber, screenType);
                if(loadedEmployee != null){
                    loadedEmployee.setSessionId(sessionId);
                }else{
                    agentCall.setSessionId(sessionId);
                }
                mongoRepository.registeredAgentCall(agentCall);
            }
        }
    }

    @Override
    public PartnerConfig findPartnerConfigById(String partnerId) {
        return mongoRepository.findPartnerById(partnerId);
    }

    private final static String FILE_PATH = "/Pentaho_Extract/RDI";
    @Override
    /**
     *
     *
     */
    public void linkAudioFilesWithCallLog(String source) {
        String location = FILE_PATH + "/" + VelocityUtils.getFolderByDateFormat();
        String newLocation = FILE_PATH + "/new/";
        String retrievedLocation = location + "/";
        File newFile = new File(newLocation);
        File retrievedFolder = new File(retrievedLocation);
        if(!retrievedFolder.exists()){
            retrievedFolder.mkdirs();
        }
        InclusionFileNameFilter filter = new InclusionFileNameFilter("wav,mp3");
        File[] files = newFile.listFiles(filter);
        int total = files.length;
        int failConvert = 0;
        int convert = 0;
        int link = 0;
        for(File file : files){
            try{
                String fileName = file.getName();
                String uuid = fileName;
                while(uuid.indexOf(".") != -1){
                    uuid = uuid.substring(0, uuid.indexOf("."));
                }
                Integer employeeNumber = null;
                if(uuid.indexOf("_") != -1){
                    String[] tmp = uuid.split("_");
                    if(tmp.length >= 2){
                        uuid = tmp[0];
                        try{
                            employeeNumber = Integer.valueOf(tmp[1]);
                        }catch (NumberFormatException ex){
                            logger.debug("Fail to get employee number for file[{}]", file.getName());
                            employeeNumber = null;
                        }
                    }
                }
                if(employeeNumber == null){
                    logger.debug("Fail to retrieve employee number for file name[{}]", file.getName());
                    continue;
                }
                Date start = new Date();
                List<CallLog> callLogs = callLogRepository.findByUuidAndEmployeeNumber(uuid, employeeNumber);
                logger.debug("time taken to find call logs: {}", (new Date().getTime() - start.getTime()));
                if(callLogs != null && callLogs.size() > 0){
                    String ext = FilenameUtils.getExtension(fileName);
                    String fileType = getFileType(ext);
                    File retrievedFile = new File(retrievedLocation + fileName);
                    IOUtils.copy(new FileInputStream(file), new FileOutputStream(retrievedFile));

                    if(ext.equalsIgnoreCase("wav")){
                        String mp3FileName = convertWavToMp3(retrievedFile.getAbsolutePath());
                        if(mp3FileName != null){
                            fileName = mp3FileName;
                            convert++;
                        }else{
                            failConvert++;
                        }
                    }

                    List<CallLogFile> logFiles = callLogFileRepository.findByEmployeeNumberAndUuidAndFileType(employeeNumber, uuid, fileType);
                    for(CallLogFile lf : logFiles){
                        if(lf.getActive() && lf.getInactivatedOn() == null){
                            lf.setActive(false);
                            lf.setInactivatedOn(new Date());
                            callLogFileRepository.save(lf);
                        }
                    }
                    CallLogFile logFile = new CallLogFile();
                    logFile.setEmployeeNumber(employeeNumber);
                    logFile.setUuid(uuid);
                    logFile.setFilePath(retrievedLocation + fileName);
                    logFile.setFileType(fileType);
                    logFile.setSource(source);
                    callLogFileRepository.save(logFile);
                    link++;
                    file.delete();
                }
            }catch(Exception ex){
                ex.printStackTrace();
                String message = "Fail to link file [" + (file != null ? file.getName() : "") + "] with exception[" + ex.getMessage() + "].";
                communicationService.sendNotificationViaEmail("<EMAIL>", message,"Link file to call log exception");
            }
        }
        String summary = "Got " + total + " files transferred from RDI.";
        summary += "\nSuccessfully linked " + link + " files to call logs.";
        summary += "\nConverted " + convert + " files to mp3 with " + failConvert + " fail.";
        communicationService.sendNotificationViaEmail("<EMAIL>", summary, "Summary for linking audio files with call logs.");

    }

    @Override
    public String convertWavToMp3(String filePath){
        String result = null;
        File target = null;
        try {
            CTI_FFMPEGLocator cti = new CTI_FFMPEGLocator();
            Encoder encoder = new Encoder(cti);
            File file = new File(filePath);
            int extIdx = file.getName().lastIndexOf(".wav");
            String newFileName = file.getName().substring(0, extIdx);
            target = new File(file.getParent() + File.separator + newFileName + ".mp3");
            logger.debug("file name: {}", target.getAbsolutePath());
            AudioAttributes audio = new AudioAttributes();
//            audio.setCodec("libmp3lame");
//            audio.setBitRate(new Integer(128000));
//            audio.setChannels(new Integer(2));
//            audio.setSamplingRate(new Integer(44100));
            EncodingAttributes attributes = new EncodingAttributes();
            attributes.setFormat("mp3");
            attributes.setAudioAttributes(audio);
            encoder.encode(file, target, attributes);

            result = target.getName();

        }catch(EncoderException ex){
            logger.debug("message: {}",ex.getMessage());
            boolean isSucceed = false;
            if(ex.getMessage() != null){
                Pattern SUCCESS_PATTERN = Pattern.compile(
                        "^\\s*video\\:\\S+\\s+audio\\:\\S+\\s+subtitle\\:\\S+\\s+other streams\\:\\S+\\s+global headers\\:\\S+.*$",
                        Pattern.CASE_INSENSITIVE);
                isSucceed = SUCCESS_PATTERN.matcher(ex.getMessage()).matches();
            }

            if(!isSucceed) {
                ex.printStackTrace();
                result = null;
            }else if(target != null){
                result = target.getName();
            }
        }
        return result;
    }

    @Override
    public ScreenPopHealthCheck doScreenPopHealthCheck(String uuid, Integer employeeNumber, String screenType, String popDateTime, String unloadDateTime) {
        String id = uuid + "+" + employeeNumber + "+" + screenType;
        String current = VelocityUtils.getDateAsString(new Date(), ScreenPopHealthCheck.FORMAT_DATE_TIME);
        ScreenPopHealthCheck record = mongoRepository.getScreenPopHealthCheck(id);
        if(record != null){
            if(unloadDateTime != null && unloadDateTime.trim().length() > 0){
                record.setUnloadDateTime(unloadDateTime);
            }else{
                record.setCheckDateTime(current);
//                record.setUnloadDateTime(null);
                record.setNumberOfCheck(record.getNumberOfCheck()+1);
            }
            mongoRepository.persistScreenPopHealthCheck(record);

        }else if(unloadDateTime == null){
            record = new ScreenPopHealthCheck();
            record.setId(id);
            record.setUuid(uuid);
            record.setEmployeeNumber(employeeNumber);
            record.setScreenType(screenType);
            record.setPopDateTime(popDateTime);
            record.setCheckDateTime(current);
            record.setNumberOfCheck(1);
            mongoRepository.persistScreenPopHealthCheck(record);
        }
        return null;
    }

    @Override
    public void processScreenPopHealthCheck(int limit) {
        List<ScreenPopHealthCheck> records = mongoRepository.getScreenPopHealthCheckToClose(limit);
        AgentCall agentCall = null;
        int agentCallCount  = 0;
        for(ScreenPopHealthCheck record : records){
            try{
                agentCall = mongoRepository.getAgentCall(record.getUuid());
                try{
                    if(agentCall != null){
                        callLogService.doPopulateCallLogForAgentCall(agentCall, record.getEmployeeNumber(), agentCall.getCurrentFacilityId(), record.getScreenType(), true, "MONGO-HEALTH-CHECK");
                        agentCallCount++;
                    }
                    record.setProcessedDateTime(VelocityUtils.getDateAsString(new Date(), ScreenPopHealthCheck.FORMAT_DATE_TIME));
                    record.setProcessedBy(ScheduleServiceImpl.NODE_NAME);
                    mongoRepository.persistScreenPopHealthCheck(record);
                }catch(Exception ex){
                    logger.debug("Fail to process screen pop health check for UUID[" + record.getUuid() + "]");
                    ex.printStackTrace();
                }
            }catch(Exception ex){
                ex.printStackTrace();
                record.setPendingProcessedBy(null);
                record.setPendingProcessedDateTime(null);
                mongoRepository.persistScreenPopHealthCheck(record);
            }
        }

        logger.debug("M2O :: Found [" + records.size() + "] unprocessed InboundCall collection records - [" + agentCallCount + "] contained an Agent Call Log.");
    }

    @Override
    public List<Map<String, Object>> getCiscoAgentCall(boolean canEditCallLog, List<CallCenterEmployee> employees, Date dt) {
        if(employees.size() == 0 && canEditCallLog){
            employees = jdbcRepository.getCallCenterResource(false);
        }
        List<Integer> ids = new ArrayList<Integer>();
        for(CallCenterEmployee cce : employees){
            ids.add(cce.getEmployeeNumber());
        }
        return ids.size() > 0 ? jdbcRepository.getCiscoAgentCallByDate(ids, dt) : new ArrayList<Map<String, Object>>();
    }

    @Override
    public boolean isOldScreenPop(Date callDateTime, String uuid, int checkType) {
        boolean result = false;
        AgentCall agentCall = null;
        if(callDateTime == null){
            agentCall = mongoRepository.getAgentCall(uuid);
            String callDateTimeStr = agentCall != null ? (agentCall.getCallRegisteredDate() + " " + agentCall.getCallRegisteredTime()) : null;
            callDateTime = callDateTimeStr != null ? VelocityUtils.parseDate(callDateTimeStr, "MM-dd-yyyy HHmmss") : null;
        }

        if(callDateTime != null){
            Calendar checkDateTime = Calendar.getInstance();
            if(checkType == 0){             //CHECK FOR SCREEN POP
                checkDateTime.add(Calendar.HOUR_OF_DAY, -4);                    //4 HOURS
                if(agentCall != null){
                    CallCenter callCenter = mongoRepository.getCallCenterByName(agentCall.getCallCenterName());
                    checkDateTime.setTime(DateTimeUtils.getTimeByLocationOffset(checkDateTime.getTime(), callCenter.getTimezoneOffset()));
                }

            }else if(checkType == 1){       //CHECK FOR CALL LOG MANAGEMENT
                checkDateTime.set(Calendar.DAY_OF_MONTH, 1);
                checkDateTime.add(Calendar.MONTH, -2);                          //2 MONTHS
                checkDateTime.set(Calendar.HOUR_OF_DAY, 0);
                checkDateTime.set(Calendar.MINUTE, 0);
                checkDateTime.set(Calendar.SECOND, 0);
            }
            result = callDateTime.compareTo(checkDateTime.getTime()) < 0;
        }
        return result;
    }

    @Override
    public boolean checkIfCallbackEnable(String callCenterName) {
        boolean result = false;
        CallCenter callCenter = mongoRepository.getCallCenterByName(callCenterName);
        if(callCenter != null){
            Date currentTime = DateTimeUtils.getLoadedTimeByLocationOffset(callCenter.getTimezoneOffset());
            Calendar today = Calendar.getInstance();
            today.setTime(currentTime);
            BusinessHour hour = callCenter.getBusinessHour(HourType.CB,today.get(Calendar.DAY_OF_WEEK));
            if(hour != null){
                logger.debug("Validate current hour[" + today.getTime() + "] with [" + hour.getFromHour() + ":" + hour.getFromMinute() + "-" + hour.getToHour() + ":" + hour.getToMinute() + "]");
                int currentHour = today.get(Calendar.HOUR_OF_DAY);
                int currentMinute = today.get(Calendar.MINUTE);
                if((currentHour > hour.getFromHour()
                        || (currentHour == hour.getFromHour() && currentMinute >= hour.getFromMinute()))
                    && (currentHour < hour.getToHour()
                        || (currentHour == hour.getToHour() && currentMinute <= hour.getToMinute()))
                ){
                    result = true;
                }
            }
        }
        return result;
    }

    @Override
    public CallLogFile findCallLogFileById(Long fileId) {
        return callLogFileRepository.findOne(fileId);
    }

    private String getFileType(String fileExt){
        String result = "";
        if(fileExt != null){
            fileExt = fileExt.toLowerCase();
            if(fileExt.equalsIgnoreCase("wav") || fileExt.equalsIgnoreCase("mp3")){
                result = "audio";
            }
        }
        return result;
    }

    private Date getLoadedTimeByLocationOffset(String callCenterName){
        Date loadedDate = new Date();
        if(callCenterName != null && callCenterName.trim().length() > 0){
            CallCenter callCenter = mongoRepository.getCallCenterByName(callCenterName);
            if(callCenter != null){
                loadedDate = DateTimeUtils.getLoadedTimeByLocationOffset(callCenter.getTimezoneOffset());
            }
        }
        return loadedDate;
    }

    @Override
    public AgentCall updateAgentCallWithScreenType(String uuid, String screenType) {
        AgentCall agentCall = mongoRepository.getAgentCall(uuid);
        if(agentCall.getScreenType() == null || !agentCall.getScreenType().equals(screenType)){
            agentCall.setScreenType(screenType);
            return mongoRepository.updateAgentCall(agentCall);
        }else{
            return agentCall;
        }
    }

    @Override
    public AgentCall updateAgentCallWithPatientDetails(String uuid, Long patientId, String firstName, String lastName, String emailAddress,
                                                       String appointmentTime, String language,boolean emailImmediately, String sourceSystem) {

        AgentCall agentCall = mongoRepository.getAgentCall(uuid);

        // -- check if this is an already known patient.
        Patient p = findExistingPatient(agentCall, sourceSystem, patientId);
        if (p == null) {
            p = new Patient();
            p.setSourceSystem(sourceSystem);
            p.setPatientFirstName(firstName);
            p.setPatientLastName(lastName);

            if (sourceSystem.equalsIgnoreCase("QSI")) {
                p.setTempPatientId(patientId);
            } else {
                p.setPatientId(patientId);
            }

            agentCall.getPatients().add(p);
        }

        if (emailAddress != null) {
            p.setEmailAddress(emailAddress);
            p.setEmailCaptureDateTime(new Date());
            p.setEmailImmediately(emailImmediately);

            if (emailImmediately) {
                boolean rez = queueConfirmationEmail(agentCall.getCurrentFacilityId(), patientId, firstName, lastName, emailAddress, appointmentTime, language);
                p.setEmailQueueDateTime(new Date());
            }
        }

        return mongoRepository.updateAgentCall(agentCall);
    }

    @Override
    public AgentCall updateAgentCallWithResolutionCode(String uuid, String resolutionCode, String reasonCode) {
        AgentCall agentCall = mongoRepository.getAgentCall(uuid);

        return mongoRepository.updateAgentCall(agentCall);
    }

    @Override
    public AgentCall updateAgentCallWithUnloadEvent(String uuid) {
        AgentCall agentCall = mongoRepository.getAgentCall(uuid);
        agentCall.setScreenPopCloseDateTime(new Date());

        return mongoRepository.updateAgentCall(agentCall);
    }

    @Override
    public AgentCall updateAgentCallWithUnloadEvent(String uuid, Integer employeeNumber, String screenType, Date unloadDateTime) {
        AgentCall agentCall = findAgentCall(uuid);
        LoadedEmployee loadedEmployee = findLoadedEmployee(agentCall, employeeNumber, screenType);
        if(loadedEmployee == null){
            String callCenterName = getCallCenterName(employeeNumber, uuid, screenType, false);
            Date loadedDate = getLoadedTimeByLocationOffset(callCenterName);
            loadedEmployee = new LoadedEmployee(employeeNumber, loadedDate, screenType);
            loadedEmployee.setSequence(agentCall.getLoadedEmployees().size() + 1);
            agentCall.getLoadedEmployees().add(loadedEmployee);
        }
        loadedEmployee.setUnloadedDate(VelocityUtils.getDateAsString(unloadDateTime, null));
        loadedEmployee.setUnloadedTime(VelocityUtils.getDateAsString(unloadDateTime, "HHmmss"));
        ScreenPopTime screenPopTime = mongoRepository.getScreenPopTime(uuid, employeeNumber, screenType);
        if(screenPopTime != null){
            screenPopTime.setUnloadDateTime(VelocityUtils.getDateAsString(unloadDateTime, "yyyy-MM-dd'T'HH:mm:ss"));
            mongoRepository.persistScreenPopTime(screenPopTime);
        }
        agentCall = mongoRepository.updateAgentCall(agentCall);
        if(loadedEmployee.getScreenType() != null && loadedEmployee.getScreenType().equalsIgnoreCase("wr")){
            String resolution = loadedEmployee.getResolutionCode();
            if(resolution != null && resolutionToTriggerEmail.indexOf(resolution) != -1){
                webRequestService.sendEmailToNonContactWebRequest(uuid, loadedEmployee.getEmployeeNumber());
            }
        }
        return agentCall;
    }

    @Override
    public AgentCall updateAgentCallWithUnloadEventAndResolution(String uuid, String resolutionCode, String reasonCode) {
        AgentCall agentCall = mongoRepository.getAgentCall(uuid);

        agentCall.setScreenPopCloseDateTime(new Date());

        return mongoRepository.updateAgentCall(agentCall);
    }

    @Override
    public AgentCall updateAgentCallWithPatientPhone(String uuid, Long phoneNumber) {
        AgentCall agentCall = mongoRepository.getAgentCall(uuid);
        agentCall.setCallerPhoneNumber(phoneNumber);

        return mongoRepository.updateAgentCall(agentCall);
    }

    @Override
    public AgentCall updateAgentCall(AgentCall agentCall, boolean modifyExisting) {
        if(!modifyExisting){
            mongoRepository.updateAgentCall(agentCall);
            return agentCall;
        }
        AgentCall existing = findAgentCall(agentCall.getUuid());
        InboundCall inboundCall = findInboundCall(agentCall.getUuid());
        Facility facility = findFacilityByFacilityId(agentCall.getCurrentFacilityId());
        Date current = new Date();

        //AGENT CALL MUST HAVE 1 LOADED EMPLOYEE WHO OPENS THE SCREEN POP
        LoadedEmployee employee = agentCall.getLoadedEmployees().size() > 0 ? agentCall.getLoadedEmployees().get(0) : null;
        List<Patient> patientList = new ArrayList<Patient>();
        if(existing != null){
            existing.setAni(agentCall.getAni());
            if(employee != null){
                LoadedEmployee existingLoadedEmployee = findLoadedEmployee(existing, employee.getEmployeeNumber(), employee.getScreenType());
                if(existingLoadedEmployee != null){
                    existingLoadedEmployee.setReasonCode(employee.getReasonCode());
//                    existingLoadedEmployee.setReasonDateTime(employee.getReasonDateTime());
                    existingLoadedEmployee.setReasonDate(VelocityUtils.getDateAsString(employee.getReasonDateTime(), null));
                    existingLoadedEmployee.setReasonTime(VelocityUtils.getDateAsString(employee.getReasonDateTime(), "HHmmss"));
                    existingLoadedEmployee.setResolutionCode(employee.getResolutionCode());
//                    existingLoadedEmployee.setResolutionDateTime(employee.getResolutionDateTime());
                    existingLoadedEmployee.setResolutionDate(VelocityUtils.getDateAsString(employee.getResolutionDateTime(), null));
                    existingLoadedEmployee.setResolutionTime(VelocityUtils.getDateAsString(employee.getResolutionDateTime(), "HHmmss"));
                }else{
                    employee.setSequence(existing.getLoadedEmployees().size() + 1);
                    employee.setResolutionDate(VelocityUtils.getDateAsString(employee.getResolutionDateTime(), null));
                    employee.setResolutionTime(VelocityUtils.getDateAsString(employee.getResolutionDateTime(), "HHmmss"));
                    employee.setReasonDate(VelocityUtils.getDateAsString(employee.getReasonDateTime(), null));
                    employee.setReasonTime(VelocityUtils.getDateAsString(employee.getReasonDateTime(), "HHmmss"));
                    existing.getLoadedEmployees().add(employee);
                }
            }
            for(Patient p : agentCall.getPatients()){
                p.setEmailCaptureDateTime(new Date());
                if(p.isEmailImmediately()){
                    Patient lookupPatient = callRepository.findPatientByIdAndFacility(p.getPatientId(), facility.getFacilityId(), facility.isLibertySupported() ? "LIBERTY" : "QSI");
                    if(lookupPatient != null){
                        boolean rez = queueConfirmationEmail(agentCall.getCurrentFacilityId(),
                                p.getPatientId(),
                                lookupPatient.getPatientFirstName(),
                                lookupPatient.getPatientLastName(),
                                p.getEmailAddress(),
                                p.getNextAppointmentDateTime() != null ? dateFormatter.format(p.getNextAppointmentDateTime()) : "",
                                inboundCall.getLanguage());
                        p.setEmailQueueDateTime(new Date());
                    }
                }
                if(p.getFacilityId() != null && !p.getFacilityId().equals(0)){
                    Facility patientFacility = findFacilityByFacilityId(p.getFacilityId());
                    p.setSourceSystem(patientFacility.isLibertySupported() ? "LIBERTY" : "QSI");
                    Patient existingPatient = existing.findExistingPatient(p.getPatientId(), p.getFacilityId(), p.getSequence(), p.getEmpEntered());
                    if(existingPatient != null){
                        existingPatient.setNextAppointmentDateTime(p.getNextAppointmentDateTime());
                        existingPatient.setNextAppointmentDate(VelocityUtils.getDateAsString(p.getNextAppointmentDateTime(), null));
                        existingPatient.setNextAppointmentTime(VelocityUtils.getDateAsString(p.getNextAppointmentDateTime(), "HHmm"));
                        existingPatient.setEmailCaptured(p.isEmailCaptured());
                        existingPatient.setInsuranceWaiting(p.isInsuranceWaiting());
                        existingPatient.setIsOrtho(p.isOrtho());
                        existingPatient.setScreenType(p.getScreenType());
                        existingPatient.setUpdateDate(VelocityUtils.getDateAsString(current, null));
                        existingPatient.setUpdateTime(VelocityUtils.getDateAsString(current, "HHmmss"));
                        patientList.add(existingPatient);
                    }else{
                        p.setNextAppointmentDate(VelocityUtils.getDateAsString(p.getNextAppointmentDateTime(), null));
                        p.setNextAppointmentTime(VelocityUtils.getDateAsString(p.getNextAppointmentDateTime(), "HHmm"));
                        p.setCreateDate(VelocityUtils.getDateAsString(current, null));
                        p.setCreateTime(VelocityUtils.getDateAsString(current, "HHmmss"));
                        patientList.add(p);
                    }
                }
            }
            if(employee != null){
                for(int i=0; i<existing.getPatients().size(); i++){
                    Patient existingPatient = existing.getPatients().get(i);
                    if(existingPatient.getEmpEntered().equals(employee.getEmployeeNumber())
                            && (existingPatient.getScreenType() == null ||
                            (existingPatient.getScreenType() != null && existingPatient.getScreenType().equalsIgnoreCase(employee.getScreenType())))){
                        existing.getPatients().remove(i);
                        i--;
                    }
                }
            }

            existing.getPatients().addAll(patientList);
            existing.setScreenType(agentCall.getScreenType());

            if(existing.getEmployeeNumber() == null || !existing.getEmployeeNumber().equals(agentCall.getEmployeeNumber())){
                existing.getAgentEmployeeNumbers().add(existing.getEmployeeNumber());
                existing.setEmployeeNumber(agentCall.getEmployeeNumber());
            }
            if(existing.getScreenPopCloseDateTime() == null){
                existing.setScreenPopCloseDateTime(agentCall.getScreenPopCloseDateTime());
            }
            return mongoRepository.updateAgentCall(existing);
        }else{
            return mongoRepository.updateAgentCall(agentCall);
        }
    }

    @Override
    public List<AgentCall> searchAgentCall(String date, List<Integer> employeeNumbers) {
        Date dt = new Date();
        List<AgentCall> agentCalls = mongoRepository.searchAgentCalls(date, employeeNumbers, 0);
        List<AgentCall> result = new ArrayList<AgentCall>();
        if(employeeNumbers != null && employeeNumbers.size() > 0){
            Set<Integer> employeeSet = new HashSet<Integer>();
            employeeSet.addAll(employeeNumbers);
            for(AgentCall agentCall : agentCalls){
                for(int i=0; i<agentCall.getLoadedEmployees().size(); i++){
                    if(!employeeSet.contains(agentCall.getLoadedEmployees().get(i).getEmployeeNumber())){
                        agentCall.getLoadedEmployees().remove(i);
                        i--;
                    }
                }
                for(int i=0; i<agentCall.getPatients().size(); i++){
                    Patient p = agentCall.getPatients().get(i);
                    if(!employeeSet.contains(p.getEmpEntered())){
                        agentCall.getPatients().remove(i);
                        i--;
                    }
                }
                if(agentCall.getLoadedEmployees().size() > 0){
                    result.add(agentCall);
                }
            }
        }else{
            result.addAll(agentCalls);
        }
        return result;
    }

    @Override
    public boolean queueConfirmationEmail(Integer facilityId, Long patientId, String firstName, String lastName, String emailAddress, String appointmentTime, String language) {
        return callRepository.queueConfirmationEmail(facilityId, patientId, firstName, lastName, emailAddress, appointmentTime, language);
    }

    @Override
    public Appointment findAppointmentByPatientPhoneNumber(Long phoneNumber, Integer facilityId, String dob) {
        if(phoneNumber == null || phoneNumber.toString().length() != 10){
            return null;
        }
        if(facilityId == null || facilityId.equals(0)){
            Appointment appointment = callRepository.findLibertyEventByPatientPhoneNumber(phoneNumber, facilityId, dob);
            if(appointment == null){
                appointment = callRepository.findAppointmentByPatientPhoneNumber(phoneNumber, facilityId, dob);
            }
            return appointment;
        }else{
            Facility facility = mongoRepository.findFacilityDetail(facilityId);
            if(facility != null && facility.isLibertySupported()){
                return callRepository.findLibertyEventByPatientPhoneNumber(phoneNumber, facilityId, dob);
            }else{
                return callRepository.findAppointmentByPatientPhoneNumber(phoneNumber, facilityId, dob);
            }
        }
    }

    @Override
    public List<Appointment> findAppointmentsByPatientPhoneNumber(Long phoneNumber, Integer facilityId, String dob) {
        if(facilityId == null || facilityId.equals(0)){
            List<Appointment> appointments = callRepository.findLibertyEventsByPatientPhoneNumber(phoneNumber, facilityId, dob);
            if(appointments.size() == 0){
                appointments = callRepository.findAppointmentsByPatientPhoneNumber(phoneNumber, facilityId, dob);
            }
            return appointments;
        }else{
            Facility facility = mongoRepository.findFacilityDetail(facilityId);
            if(facility != null && facility.isLibertySupported()){
                return callRepository.findLibertyEventsByPatientPhoneNumber(phoneNumber, facilityId, dob);
            }else{
                return callRepository.findAppointmentsByPatientPhoneNumber(phoneNumber, facilityId, dob);
            }
        }
    }

    @Override
    public List<Appointment> findAppointments(Long patientId, Long phoneNumber, Integer facilityId) {
        List<Appointment> appointments = callRepository.findAppointmentsByPatientPhoneNumber(phoneNumber, facilityId, null);
        if(patientId != null && !patientId.equals(0L)){
            for(int i=0; i<appointments.size(); i++){
                Appointment appointment = appointments.get(i);
                if(!appointment.getPatient().getPatientId().equals(patientId)){
                    appointments.remove(i);
                    i--;
                }
            }
        }
        return appointments;
    }

    @Override
    public Patient findPatientByIdAndFacility(Long patientId, Integer facilityId, String sourceSystem) {
        return callRepository.findPatientByIdAndFacility(patientId, facilityId, sourceSystem);
    }

    @Override
    public PhoneNumber findFacilityByAni(Long phoneNumber) {
        List<Map> facilities = jdbcRepository.getQsiFacilitiesByAni(phoneNumber);
        if (facilities.size() == 0) {
            facilities = jdbcRepository.getLibertyFacilitiesByAni(phoneNumber);
        }

        if (facilities.size() == 0) {
            return null;
        } else {
            Map<String, Object> facility = facilities.get(0);
            Integer facilityId = (Integer) facility.get("facilityId");

            logger.debug("Found Facility ID [" + facilityId + "] when searching by Phone Number [" + phoneNumber + "]");
            return findPhoneNumberByFacilityAndPhoneType(facilityId, "ML");
        }
    }

    private Patient findExistingPatient(AgentCall call, String sourceSource, Long patientId) {
        for (Patient p : call.getPatients()) {

            if (sourceSource.equalsIgnoreCase("QSI")) {
                if (p.getTempPatientId().equals(patientId)) {
                    return p;
                }
            } else {
                if (p.getPatientId().equals(patientId)) {
                    return p;
                }
            }
        }

        return null;
    }

    @Override
    public LoadedEmployee findLoadedEmployee(AgentCall call, Integer employeeNumber, String screenType){
        LoadedEmployee result = null;
        for(LoadedEmployee employee : call.getLoadedEmployees()){
            if(employee.getEmployeeNumber().equals(employeeNumber) && employee.getScreenType().equalsIgnoreCase(screenType)){
                result = employee;
                break;
            }
        }
        return result;
    }

    @Override
    public String getStateFromAni(Long ani) {
        String areaCode = ani.toString().substring(0, 3);
        logger.debug("Searching for State using Area Code[" + areaCode + "] from ANI[" + ani + "].");

        String url = "http://www.allareacodes.com/api/1.0/api.json?npa=" + areaCode + "&tracking_email=<EMAIL>&tracking_url=http://www.google.com";
        RestTemplate restTemplate = new RestTemplate();

        String json = restTemplate.getForObject(url, String.class).toString();
        Map map = BaseJsonConverter.convertToMap(json);
        String state = "XX";
        if (map.get("area_codes") != null) {
            ArrayList<Map> entries = (ArrayList) map.get("area_codes");
            if (entries.size() > 0) {
                state = entries.get(0).get("state").toString();
            }
        }

        return state;
    }

    @Override
    public String translate(String message, String language) {
        try {
            String url = "https://www.googleapis.com/language/translate/v2?key=AIzaSyCDZfLXdYX1NCX0nxPoPQ0HhdWRR4isS7U&source=en&target=" + language + "&q=" + URLEncoder.encode(message, "UTF-8");
            RestTemplate restTemplate = new RestTemplate();

            String json = restTemplate.getForObject(url, String.class).toString();
            logger.debug("Google replied with [" + json + "];");

            Map map = BaseJsonConverter.convertToMap(json);
            if (map.get("data") != null) {
                Map data = (Map) map.get("data");
                if (data.get("translations") != null) {
                    List translations = (List) data.get("translations");
                    if (translations.size() > 0) {
                        Map translationText = (Map) translations.get(0);
                        logger.debug(translations.toString());
                        message = URLDecoder.decode(translationText.get("translatedText").toString(), "UTF-8");
                    }
                }
            }

            return message;

        } catch (Exception ex) {
            ex.printStackTrace();
            logger.error("Unable to translate message[" + message + "] to language[" + language + "] because[" + ex.getMessage() + "]!");
            return message;
        }
    }

    @Override
    public List<Object> getDistinctFieldFromCollection(String collectionName, String fieldName) {
        return mongoRepository.getDistinctField(collectionName, fieldName);
    }

    @Override
    public List<InboundCallSummary> getInboundCallSummary(String callCenterName, String phoneType, String menuPress, Date processDate) {
        List<InboundCallSummary> list = mongoRepository.getInboundCallSummary(callCenterName, phoneType, menuPress, processDate);
        if(list.size() > 0 && (callCenterName == null || callCenterName.trim().length() == 0)){
            Map<String, InboundCallSummaryValue> map = new HashMap<String, InboundCallSummaryValue>();
            List<InboundCallSummary> combinedSummaryList = new ArrayList<InboundCallSummary>();
            for(InboundCallSummary summary : list){
                String key = summary.getSummaryId().getPhoneType() + "-" + summary.getSummaryId().getMenuPress();
                InboundCallSummaryValue combinedSummaryValue = map.get(key);
                if(combinedSummaryValue == null){
                    InboundCallSummaryId summaryId = new InboundCallSummaryId("Combined", summary.getSummaryId().getPhoneType(), summary.getSummaryId().getMenuPress(), null);
                    InboundCallSummaryValue value = new InboundCallSummaryValue(summary.getValue().getCallCount(),
                            summary.getValue().getSupportedCount(),
                            summary.getValue().getNewPatCount(), summary.getValue().getExtPatCount(),
                            summary.getValue().getNoPSRCount(), summary.getValue().getNoCSRCount(),
                            summary.getValue().getTrnOffCount(), summary.getValue().getOffOffCount(),
                            summary.getValue().getPsrPopCount(), summary.getValue().getCsrPopCount(),
                            summary.getValue().getCsrPsrCount(), summary.getValue().getAniRecCount(),
                            summary.getValue().getQueryTimeoutCount(), summary.getValue().getSearchNotFoundCount(),
                            summary.getValue().getTransferToRDI(), summary.getValue().getReceivedByRDI(),
                            summary.getValue().getRequestCallback(), summary.getValue().getHandledCallback());
                    InboundCallSummary combinedSummary = new InboundCallSummary(summaryId, value);
                    combinedSummary.setCombined(true);
                    combinedSummaryList.add(combinedSummary);
                    map.put(key, value);
                }else{
                    combinedSummaryValue.sumValue(summary.getValue());
                }
            }
            list.addAll(combinedSummaryList);
        }
        return list;
    }

    @Override
    public Map<String, Object> getInboundCallSummaryReport(String callCenterName, String phoneType, String menuPress, String eventCount) {
        List<InboundCallSummary> list = mongoRepository.getInboundCallSummary(callCenterName, phoneType, menuPress, null);
        LinkedHashMap<String, Object> data = new LinkedHashMap<String, Object>();
        boolean doPhoneTypeReport = phoneType != null;
        boolean doMenuPressReport = menuPress != null;
        List<String> chartKeys = new ArrayList<String>();
        for(InboundCallSummary summary : list){
            String dateKey = VelocityUtils.getDateAsString(summary.getSummaryId().getProcessDate(), "yyyy-MM-dd");
            Long value = (Long)data.get(dateKey);
            if(value == null){
                value = 0L;
                if(data.keySet().size() > 60){  //LOAD 60-DAY of DATA
                    continue;
                }
            }
//            if(doMenuPressReport && !doPhoneTypeReport){
//                phoneType = summary.getSummaryId().getPhoneType();
//                if(chartKeys.indexOf(phoneType) == -1){
//                    chartKeys.add(phoneType);
//                }
//                value.put(phoneType, value.get(phoneType) != null
//                        ? ((Long)value.get(phoneType)) + summary.getValue().getCallCount()
//                        : summary.getValue().getCallCount());
//            }else if(doPhoneTypeReport && !doMenuPressReport){
//                menuPress = summary.getSummaryId().getMenuPress();
//                if(chartKeys.indexOf(menuPress.toString()) == -1){
//                    chartKeys.add(menuPress.toString());
//                }
//                value.put(menuPress.toString(), value.get(menuPress.toString()) != null
//                        ? ((Long)value.get(menuPress.toString())) + summary.getValue().getCallCount()
//                        : summary.getValue().getCallCount());
//                data.put(dateKey, value);
//            }
            if(eventCount == null){
                value += summary.getValue().getCallCount();
            }else{
                value += summary.getValue().getEventCount(eventCount);
            }

            data.put(dateKey, value);
        }
        List<String> dateKeys = new ArrayList<String>();
        dateKeys.addAll(data.keySet());
        Collections.sort(dateKeys);
        Collections.sort(chartKeys);
        Map<String, Object> result = new HashMap<String, Object>();
//        LinkedHashMap<String, Object> dataMap = new LinkedHashMap<String, Object>();
//        for(String chartKey : chartKeys){
//            List<Long> counts = new ArrayList<Long>();
//            for(String dateKey : dateKeys){
//                Long count = data.get(dateKey) != null ? (Long)((Map)data.get(dateKey)).get(chartKey) : null;
//                counts.add(count != null ? count : 0L);
//            }
//            dataMap.put(chartKey, counts);
//        }
        result.put("data", data);
        result.put("keys", chartKeys);
        result.put("labels", dateKeys);
        return result;
    }

    @Override
    public void persistScreenPopTime(String uuid, Integer employeeNumber, String screenType, String popDateTime, String openDateTime) {
        ScreenPopTime screenPopTime = mongoRepository.getScreenPopTime(uuid, employeeNumber, screenType);
        if(screenPopTime == null){

            screenPopTime = new ScreenPopTime();
            screenPopTime.setUuid(uuid);
            screenPopTime.setEmployeeNumber(employeeNumber);
            screenPopTime.setScreenType(screenType);
            screenPopTime.setPopDateTime(popDateTime);

            InboundCall inboundCall = mongoRepository.getInboundCall(uuid);
            if(inboundCall != null){
                screenPopTime.setDnis(inboundCall.getDnis());
                screenPopTime.setAni(inboundCall.getAni());
                if(inboundCall.getPhoneNumber() != null){
                    screenPopTime.setCallCenter(inboundCall.getPhoneNumber().getCallCenterName());
                }
                screenPopTime.setPhoneType(inboundCall.getPhoneType());
                screenPopTime.setCallDate(VelocityUtils.getDateAsString(inboundCall.getCreateDateTime(), "yyyy-MM-dd"));
            }

            screenPopTime.setId(screenPopTime.generateId());
        }
        screenPopTime.getOpenDateTime().add(openDateTime);
        mongoRepository.persistScreenPopTime(screenPopTime);
    }

    @Override
    public List<ScreenPopTime> getScreenPops(String startDate, String endDate) {
        List<ScreenPopTime> list = mongoRepository.getScreenPopTimeByDateRange(startDate, endDate);
        Map<Integer,String> employeeMap = new HashMap<Integer, String>();
        for(ScreenPopTime spt : list){
            String employeeName = employeeMap.get(spt.getEmployeeNumber());
            if(employeeName == null){
                employeeName = jdbcRepository.getEmployeeName(spt.getEmployeeNumber(), 0);
                employeeName = spt.getEmployeeNumber() + (employeeName != null && employeeName.trim().length() > 0 ? (" - " + employeeName) : "");
                employeeMap.put(spt.getEmployeeNumber(), employeeName);
            }
            spt.setEmployeeName(employeeName);
        }
        return list;
    }

    @Override
    public List<Provider> getProviderInsurance(Integer facilityId) {
        List<Provider> result = null;
        Facility facility = mongoRepository.findFacilityDetail(facilityId);
        if(facility != null){
            result = new ArrayList<Provider>();
            List<Employee> providers = facility.getEmployees().get("Dentists");
            for(Employee emp : providers){
                Provider provider = (Provider)emp;
                if(provider.getProviderInsurances() != null && provider.getProviderInsurances().size() > 0){
                    result.add(provider);
                }
            }
        }
        return result;
    }

    @Override
    public List<Map> getOpenBookSupportedFacilities() {
        return jdbcRepository.getOpenBookSupportedFacilities();
    }
}
