package com.smilebrands.callcentersupport.domain;

import com.smilebrands.callcentersupport.domain.helper.DateIsoDeSerializer;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.springframework.data.annotation.Transient;

import java.io.Serializable;
import java.util.*;

/**
 * Created by phongpham on 7/18/14.
 */
public class LoadedEmployee implements Serializable {

    private Integer employeeNumber;
    private Integer sequence;
    private String loadedDate;
    private String loadedTime;
    private String unloadedDate;
    private String unloadedTime;
    private String reasonCode;
    private String reasonDate;
    private String reasonTime;
    private String resolutionCode;
    private String resolutionDate;
    private String resolutionTime;
    private String screenType;
    private String sessionId;

    private String partnerId;
    private String partnerSessionId;
    private Long partnerStartDateTime;
    private String partnerTimezone;
    private Integer partnerRingTime;
    private Integer partnerTalkTime;
    private Integer partnerHoldTime;
    private Integer partnerWorkTime;
    private Long partnerCalledNumber;
    private Integer partnerTeamId;
    private Integer partnerMetServiceLevel;

    private Long callLogId;

    private List<Map<String, Object>> smsResult;

    @Transient
    private Date reasonDateTime;
    @Transient
    private Date resolutionDateTime;
    @Transient
    private Date loadedDateTime;
    @Transient
    private Date unloadedDateTime;


    public LoadedEmployee(){}

    public LoadedEmployee(Integer employeeNumber, Date loadedDateTime, String screenType){
        this.employeeNumber = employeeNumber;
        this.screenType = screenType;
        loadedDateTime = loadedDateTime != null ? loadedDateTime : new Date();
        this.loadedDate = VelocityUtils.getDateAsString(loadedDateTime, null);
        this.loadedTime = VelocityUtils.getDateAsString(loadedDateTime, "HHmmss");
    }

    public Integer getEmployeeNumber() {
        return employeeNumber;
    }

    public void setEmployeeNumber(Integer employeeNumber) {
        this.employeeNumber = employeeNumber;
    }

    public String getLoadedDate() {
        return loadedDate;
    }

    public void setLoadedDate(String loadedDate) {
        this.loadedDate = loadedDate;
    }

    public String getLoadedTime() {
        return loadedTime;
    }

    public void setLoadedTime(String loadedTime) {
        this.loadedTime = loadedTime;
    }

    public String getUnloadedDate() {
        return unloadedDate;
    }

    public void setUnloadedDate(String unloadedDate) {
        this.unloadedDate = unloadedDate;
    }

    public String getUnloadedTime() {
        return unloadedTime;
    }

    public void setUnloadedTime(String unloadedTime) {
        this.unloadedTime = unloadedTime;
    }

    public String getScreenType() {
        return screenType;
    }

    public void setScreenType(String screenType) {
        this.screenType = screenType;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Long getCallLogId() {
        return callLogId;
    }

    public void setCallLogId(Long callLogId) {
        this.callLogId = callLogId;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public String getReasonCode() {
        return reasonCode;
    }

    public void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }

    public String getReasonDate() {
        return reasonDate;
    }

    public void setReasonDate(String reasonDate) {
        this.reasonDate = reasonDate;
    }

    public String getReasonTime() {
        return reasonTime;
    }

    public void setReasonTime(String reasonTime) {
        this.reasonTime = reasonTime;
    }

    public String getResolutionCode() {
        return resolutionCode;
    }

    public void setResolutionCode(String resolutionCode) {
        this.resolutionCode = resolutionCode;
    }

    public String getResolutionDate() {
        return resolutionDate;
    }

    public void setResolutionDate(String resolutionDate) {
        this.resolutionDate = resolutionDate;
    }

    public String getResolutionTime() {
        return resolutionTime;
    }

    public void setResolutionTime(String resolutionTime) {
        this.resolutionTime = resolutionTime;
    }

    public Date getReasonDateTime() {
        return reasonDateTime;
    }

    @JsonDeserialize(using = DateIsoDeSerializer.class)
    public void setReasonDateTime(Date reasonDateTime) {
        this.reasonDateTime = reasonDateTime;
    }

    public Date getResolutionDateTime() {
        return resolutionDateTime;
    }

    @JsonDeserialize(using = DateIsoDeSerializer.class)
    public void setResolutionDateTime(Date resolutionDateTime) {
        this.resolutionDateTime = resolutionDateTime;
    }

    public Date getLoadedDateTime() {
        Date result = loadedDateTime;
        if(result == null){
            String timeFormat = this.loadedTime != null ? (this.loadedTime.length() == 6 ? "HHmmss" : (this.loadedTime.length() == 4 ? "HHmm" : null)) : null;
            if(timeFormat != null){
                result = VelocityUtils.parseDate(this.loadedDate + " " + this.loadedTime, "MM-dd-yyyy " + timeFormat);
            }
        }
        return result;
    }

    public void setLoadedDateTime(Date loadedDateTime) {
        this.loadedDateTime = loadedDateTime;
    }

    public Date getUnloadedDateTime() {
        return unloadedDateTime;
    }

    public void setUnloadedDateTime(Date unloadedDateTime) {
        this.unloadedDateTime = unloadedDateTime;
    }

    public String getPartnerSessionId() {
        return partnerSessionId;
    }

    public void setPartnerSessionId(String partnerSessionId) {
        this.partnerSessionId = partnerSessionId;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    public Long getPartnerStartDateTime() {
        return partnerStartDateTime;
    }

    public void setPartnerStartDateTime(Long partnerStartDateTime) {
        this.partnerStartDateTime = partnerStartDateTime;
    }

    public String getPartnerTimezone() {
        return partnerTimezone;
    }

    public void setPartnerTimezone(String partnerTimezone) {
        this.partnerTimezone = partnerTimezone;
    }

    public Integer getPartnerRingTime() {
        return partnerRingTime;
    }

    public void setPartnerRingTime(Integer partnerRingTime) {
        this.partnerRingTime = partnerRingTime;
    }

    public Integer getPartnerTalkTime() {
        return partnerTalkTime;
    }

    public void setPartnerTalkTime(Integer partnerTalkTime) {
        this.partnerTalkTime = partnerTalkTime;
    }

    public Integer getPartnerHoldTime() {
        return partnerHoldTime;
    }

    public void setPartnerHoldTime(Integer partnerHoldTime) {
        this.partnerHoldTime = partnerHoldTime;
    }

    public Integer getPartnerWorkTime() {
        return partnerWorkTime;
    }

    public void setPartnerWorkTime(Integer partnerWorkTime) {
        this.partnerWorkTime = partnerWorkTime;
    }

    public Long getPartnerCalledNumber() {
        return partnerCalledNumber;
    }

    public void setPartnerCalledNumber(Long partnerCalledNumber) {
        this.partnerCalledNumber = partnerCalledNumber;
    }

    public Integer getPartnerTeamId() {
        return partnerTeamId;
    }

    public void setPartnerTeamId(Integer partnerTeamId) {
        this.partnerTeamId = partnerTeamId;
    }

    public Integer getPartnerMetServiceLevel() {
        return partnerMetServiceLevel;
    }

    public void setPartnerMetServiceLevel(Integer partnerMetServiceLevel) {
        this.partnerMetServiceLevel = partnerMetServiceLevel;
    }

    public void captureSMSEvent(String to, String msg, String sids, Integer facilityId){
        if(smsResult == null){
            smsResult = new ArrayList<Map<String, Object>>();
        }
        Map<String, Object> smsEvent = new HashMap<String, Object>();
        smsEvent.put("to", to);
        smsEvent.put("facilityId", facilityId);
        smsEvent.put("msg", msg);
        smsEvent.put("sids", sids);
        smsEvent.put("createdOn", new Date());
        smsResult.add(smsEvent);
    }
}
