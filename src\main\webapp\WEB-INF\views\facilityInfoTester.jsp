<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd" >
<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<%@ taglib uri="../customTag.tld" prefix="custom" %>

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Smile Brands Call Center - Facility Tester</title>
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/bootstrap.min.css" >
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/style/redmond/jquery-ui-1.8.20.custom.css" />
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/call-template.css" >

</head>
<body>

    <div class="container">
        <br/>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h1 class="panel-title gray">Facility Screen Pop Tester</h1>
            </div>
            <div class="panel-body">
                <div class="row filter-cmp">
                    <div class="col-md-1">Filter By:</div>
                    <div class="col-md-3">
                        <select filterOptionFor="facilityList" style="width:100%;">
                            <option value="facilityId">Facility ID</option>
                            <option value="name">Facility Name</option>
                            <option value="enDNIS">DNIS</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <input type="text" filterInputFor="facilityList" style="width:90%;"/>
                        <span class="glyphicon glyphicon-remove pointer" removeFilterFor="facilityList"/>
                    </div>
                </div>
                <table class="table table-striped" id="facilityList">
                    <thead>
                        <tr>
                            <th sortField="facilityId" class="pointer">Facility ID<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="name" class="pointer">Facility Name<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="enDNIS" class="pointer">English Screen Pop<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="spDNIS" class="pointer">Spanish Screen Pop<span class="glyphicon padding-left-5"></span></th>
                        </tr>
                    </thead>
                    <tbody>
                        <c:forEach var="facility" items="${facilityList}">
                            <c:set var = "dnis" value = "${facility.phoneNumber}"/>
                            <c:set var = "libertySupported" value = "${facility.libertySupported}"/>
                            <c:set var = "url" value = "<a href='../load-screen-pop/psr/abc123/102029/en/2/${facility.facilityId}' target='_blank'>${dnis}</a>"/>
                            <c:set var = "urlSP" value = "<a href='../load-screen-pop/psr/abc123/102029/sp/2/${facility.facilityId}' target='_blank'>${dnis}</a>"/>
                            <c:set var = "urlFW" value = "<a href='../inboundCall?dnis=${dnis}&libertySupported=${libertySupported}' target='_blank'>${dnis}</a>"/>

                            <tr>
                                <td name="facilityId" value="${facility.facilityId}">${facility.facilityId}</td>
                                <td name="name" value="${facility.name}">${facility.name}</td>
                                <td name="enDNIS" value="${dnis}">${url}</td>
                                <td name="spDNIS" value="${dnis}">${urlSP}</td>
                            </tr>

                        </c:forEach>
                    </tbody>
                </table>

            </div>
        </div>
    </div>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-1.7.2.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/sbi-script.js"></script>
</body>
</html>