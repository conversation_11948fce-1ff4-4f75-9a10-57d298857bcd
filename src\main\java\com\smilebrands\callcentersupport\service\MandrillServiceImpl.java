package com.smilebrands.callcentersupport.service;

import com.smilebrands.callcentersupport.domain.mandrill.SendRequest;
import org.codehaus.jackson.map.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by phongpham on 6/2/15.
 */
@Service
public class MandrillServiceImpl implements MandrillService {

    final static Logger logger = LoggerFactory.getLogger(MandrillServiceImpl.class);

    //@Autowired
    //private String mandrillApiUrl;
    private String mandrillApiUrl = "https://mandrillapp.com/api/1.0/messages/send.json";

    @Override
    public List<LinkedHashMap> sendNotificationEmail(SendRequest sendRequest) {
        ObjectMapper mapper = new ObjectMapper();
        String json = null;

        // Convert SendRequest object to json string
        try {
            json = mapper.writeValueAsString(sendRequest);
        } catch(IOException e) {
            logger.error("Unable to convert SendRequest object to json string");
        }
        return sendNotificationEmail(json);
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<LinkedHashMap> sendNotificationEmail(String json) {
        List<LinkedHashMap> sendResponses = new ArrayList<LinkedHashMap>();
        HttpURLConnection conn = null;

        try {
            // Connect and authenticate to Mandrill server
            URL url = new URL(mandrillApiUrl);
            conn = (HttpURLConnection) url.openConnection();
            conn.setDoOutput(true);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "application/json");

            // Submit SendRequest as json string
            OutputStream os = conn.getOutputStream();
            os.write(json.getBytes("UTF-8"));
            os.flush();

            if (conn.getResponseCode() != HttpURLConnection.HTTP_OK) {
                logger.error("Failed to send the following email to Mandrill:\n" + json);
                //throw new RuntimeException("Failed with HTTP error code : " + conn.getResponseCode());
                logger.error("Response code [ " + conn.getResponseCode() + "] received when sending email: \n" + json);
                return sendResponses;
            }

            // Read output into StringBuilder object
            BufferedReader br = new BufferedReader(new InputStreamReader((conn.getInputStream())));
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }

            logger.debug("sendNotificationEmail response: " + sb.toString());

            // Convert json string to List of SendResponse objects
            ObjectMapper mapper = new ObjectMapper();
            sendResponses = mapper.readValue(sb.toString(), ArrayList.class);
        } catch (IOException e) {
            logger.warn("Exception sending notification email: " + e.getMessage());
        } finally {
            if(conn != null) conn.disconnect();
        }
        return sendResponses;
    }

}