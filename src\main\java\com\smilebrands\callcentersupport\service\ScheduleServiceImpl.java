package com.smilebrands.callcentersupport.service;

import com.smilebrands.callcentersupport.domain.*;
import com.smilebrands.callcentersupport.domain.constants.AgentType;
import com.smilebrands.callcentersupport.domain.constants.Language;
import com.smilebrands.callcentersupport.domain.constants.PreferredRecallTime;
import com.smilebrands.callcentersupport.domain.constants.SkillType;
import com.smilebrands.callcentersupport.domain.schedule.Task;
import com.smilebrands.callcentersupport.repo.JdbcRepository;
import com.smilebrands.callcentersupport.repo.MongoRepository;
import com.smilebrands.callcentersupport.util.DateTimeUtils;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.codehaus.jackson.map.DeserializationConfig;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.Inet4Address;
import java.net.URL;
import java.util.*;

/**
 * Created by phongpham on 11/6/14.
 */
@Service
public class ScheduleServiceImpl extends BaseService implements ScheduleService {

    public static String NODE_NAME = "";
    private final static String ACQUIRED_ON_FORMAT = "yyyy/MM/dd HH:mm:ss";
    private static boolean addShutdownHook = true;

    public static List<Task> taskList;

    @Autowired
    @Qualifier("REDIRECT_URL")
    private String redirectUrl;

    @Autowired
    @Qualifier("CISCO_URL")
    private String ciscoUrl;

    static {
        try {
            NODE_NAME = Inet4Address.getLocalHost().getHostName().trim();

        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex);
        }
    }

    @PostConstruct
    public void init(){
        if(addShutdownHook){
            releaseAllTask();
            System.out.println("Add shutdown hook in ScheduleServiceImpl");
            addShutdownHook = false;
            Runtime.getRuntime().addShutdownHook(new Thread(){
                public void run(){
                    System.out.println("Release all claimed tasks before shutdown");
                    releaseAllTask();
                }
            });
            if(taskList == null || taskList.size() == 0){
                taskList = mongoRepository.getAllTask();
            }
        }
    }

    public String getAcquiredByInfo(){
        String result = NODE_NAME;

        StackTraceElement[] elements = Thread.currentThread().getStackTrace();
        String currentClassName = this.getClass().getName();
        String methodName = "";
        for(StackTraceElement ste : elements){
            if(ste != null && ste.getClassName().equals(currentClassName)){
                methodName = ste.getMethodName();
                break;
            }
        }
        result += ":" + currentClassName + ":" + methodName;
        return result;
    }


    @Autowired
    private MongoRepository mongoRepository;

    @Autowired
    private CommunicationService communicationService;

    @Autowired
    CallLogService callLogService;

    @Autowired
    CallService callService;

    @Autowired
    private JdbcRepository jdbcRepository;



    @Override
    public Task claimOpenTask(String taskName) {
        Task task = mongoRepository.claimOpenTask(getAcquiredByInfo(), VelocityUtils.getDateAsString(new Date(), ACQUIRED_ON_FORMAT), taskName);
        logger.debug("claim task[" + task + "] at[" + new Date() + "]");
        return task;
    }

    @Override
    public Task claimOpenTask(Long taskId) {
        Task task = mongoRepository.claimOpenTask(getAcquiredByInfo(), VelocityUtils.getDateAsString(new Date(), ACQUIRED_ON_FORMAT), taskId);
        logger.debug("claim task[" + task + "] at[" + new Date() + "]");
        return task;
    }

    @Override
    public boolean processTask(Task task) {
        if(task != null){
            if(task.getTaskName().equalsIgnoreCase("processWebRequest")){
                processWebRequest(task.getMongoConditions(), task.getMongoLimit());
            }else if(task.getTaskName().equalsIgnoreCase("checkLongWebRequest")){
                checkLongQueuedWebRequest(task.getMongoConditions(), task.getMongoLimit());
            }else if(task.getTaskName().equalsIgnoreCase("generateCallLogReport")){
                generateCallLogReport(task);
            }
        }
        return false;
    }

    @Override
    @Async
    public void processTaskAsync(Task task) {
        try{
            if(task.getTaskName().equalsIgnoreCase("processScreenPopHealthCheck")){
                callService.processScreenPopHealthCheck(task.getMongoLimit());
            }else if(task.getTaskName().equalsIgnoreCase("generateCallLogReport")){
                generateCallLogReport(task);
            }else{
                logger.debug("process task[" + task.getTaskName() + "] async");
                try {
                    Thread.sleep(15*1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                logger.debug("finish task[" + task.getTaskName() + "]");
            }
        }catch(Exception ex){
            ex.printStackTrace();
        }
        finally {
            releaseTask(task);
        }
    }

    @Override
    public void releaseTask(Task task) {
        if(task != null){
            mongoRepository.releaseTask(task);
            logger.debug("release task[" + task + "] at[" + new Date() + "]");
        }
    }

    @Override
    public void releaseAllTask() {
        List<Task> claimedTasks = mongoRepository.getAllClaimedTask(NODE_NAME);
        for(Task task : claimedTasks){
            mongoRepository.releaseTask(task);
        }
    }

    @Override
    public void processWebRequest(Map<String, Object> mongoOptions, int mongoLimit) {
        try {
            mongoLimit = (mongoLimit == -1) ? 25 : mongoLimit;
            List<WebRequest> webRequests = mongoRepository.getWebRequestToProcess(mongoOptions, mongoLimit);

            String current = VelocityUtils.getDateAsString(new Date(), "yyyy-MM-dd HH:mm:ss");

            Date tomorrow = DateTimeUtils.getBusinessDateByIncrement(1);
            String tomorrowStr = VelocityUtils.getDateAsString(tomorrow,"yyyy-MM-dd 06:00:00");

            //Date nextWeek = DateTimeUtils.getBusinessDateByIncrement(7);
            //String nextWeekStr = VelocityUtils.getDateAsString(nextWeek,"yyyy-MM-dd 06:00:00");

            //Calendar noAgentsReady = Calendar.getInstance();
            //noAgentsReady.add(Calendar.MINUTE, 30);
            //String noAgentsReadyStr = VelocityUtils.getDateAsString(noAgentsReady.getTime(), "yyyy-MM-dd HH:mm:ss");

            logger.debug("WRP :: Beginning to process " + webRequests.size() + " web requests.");

            int agentsReady     = 0;
            int submitToCisco   = 0;
            List<Integer> noAgentFrom = new ArrayList<Integer>();

            for (WebRequest webRequest : webRequests) {
                logger.debug("WRP :: Request from Phone: [" + webRequest.getPhone() + "] UUID: [" + webRequest.getUuid() + "] has Process Count {}", webRequest.getNumberOfProcess());
                boolean processWebRequest = false;

                // -- REMOVED APPOINTMENT DATE PREFERENCE ORDERING PER MARKETING - CALL CENTER
                // -- FORCE EVERYTHING TO PROCESS AS ORDERED BY PENDING WEB REQUEST QUERY -> CREATE DATE TIME
                // -- MHC - FEB 13 2021

                /*PreferredRecallTime preferredRecallTime = webRequest.getPreferredRecallTime() != null
                                                            ? PreferredRecallTime.getByOrder(webRequest.getPreferredRecallTime())
                                                            : PreferredRecallTime.ASAP;*/

                /*if (preferredRecallTime == PreferredRecallTime.ASAP
                        || (webRequest.getNextProcessDateTime() != null && webRequest.getNextProcessDateTime().compareTo(current) <= 0)) {*/

                    Integer facilityId = webRequest.getOffice();
                    Facility facility = mongoRepository.findFacilityDetail(facilityId);

                    if (facility != null) {
                        Date currentTime = DateTimeUtils.getLoadedTimeByLocationOffset(facility.getTimeOffset());
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(currentTime);

                        if (cal.get(Calendar.HOUR_OF_DAY) >= 20) {
                            // DO NOT PROCESS IF OFFICE HOUR IS CURRENTLY GREATER OR EQUAL TO 8PM
                            webRequest.setNextProcessDateTime(tomorrowStr);
                            logger.debug("WRP :: Time is {} - Setting next ProcessedDateTime to {} ", Calendar.HOUR_OF_DAY, tomorrowStr);

                        } else if (cal.get(Calendar.HOUR_OF_DAY) < 5) {
                            logger.debug("WRP :: Time is {} - Waiting for the Call Center to open at 5 ", Calendar.HOUR_OF_DAY);

                        } else {

                            PhoneNumber phoneNumber = mongoRepository.findPhoneNumberByFacility(facilityId, "ML");
                            if (phoneNumber != null && phoneNumber.getCallCenterName() != null) {

                                CallCenter callCenter = mongoRepository.getCallCenterByName(phoneNumber.getCallCenterName());
                                int agentId = callCenter.getAgentId(AgentType.NA, SkillType.WR, Language.NA);
                                boolean isAgentReady = noAgentFrom.indexOf(agentId) == -1;

                                logger.debug("WRP :: Is Agent ID [" + agentId + "] ready? {}", isAgentReady);

                                if (callCenter != null && isAgentReady) {
                                    logger.debug("WRP :: Sending Web Request to Cisco Service.  UUID: {}", webRequest.getUuid());
                                    agentsReady = sendGet(webRequest, agentId);
                                    webRequest.setNumberOfProcess(webRequest.getNumberOfProcess() + 1);
                                    processWebRequest = true;
                                    submitToCisco++;
                                    logger.debug("WRP :: Cisco Service returned {} Agents Ready.", agentsReady);

                                    // -- This condition determines if the process should continue to send more.  If agentsReady > 0 -> yes
                                    // -- It will also mark the current request as unprocessed...
                                    if (agentsReady == -1) {
                                        logger.error("WRP :: Invalid Agents Read valid for Agent: {}", agentsReady);
                                        processWebRequest = false;
                                        noAgentFrom.add(agentId);

                                    } else if (agentsReady == 0) {
//                                        webRequest.setNextProcessDateTime(noAgentsReadyStr);
                                        processWebRequest = false;
                                        noAgentFrom.add(agentId);
                                        logger.debug("WRP :: 0 Agents Read response for Agent {}", agentId);
                                    }
                                } else {
                                    logger.warn("WRP :: Is Agent Ready is False for UUID {} Continuing ...", webRequest.getUuid());
                                }
                            } else {
                                logger.warn("WRP :: Web Request issue for UUID {}  Either Facility Phone or Call Center Name is <NULL>", webRequest.getUuid());
                            }
                        }
                    } else {
                        logger.warn("WRP :: Request received for a <NULL> Facility ID! UUID: {}", webRequest.getUuid());
                    }
                /*} else {
                    if (webRequest.getNextProcessDateTime() == null) {
                        if (preferredRecallTime == PreferredRecallTime.TOMORROW) {
                            webRequest.setNextProcessDateTime(tomorrowStr);
                        } else if (preferredRecallTime == PreferredRecallTime.NEXT_WEEK) {
                            webRequest.setNextProcessDateTime(nextWeekStr);
                        }
                    }
                }*/

                // -- If not process, remove processdBy values and release the request for the next cycle
                if (!processWebRequest) {
                    webRequest.setPendingProcessedBy(null);
                    webRequest.setPendingProcessedDateTime(null);
                    logger.debug("WRP :: Release web request from pending for UUID[" + webRequest.getUuid() + "]");
                    mongoRepository.saveWebRequest(webRequest);
                }
            }

            String end = VelocityUtils.getDateAsString(new Date(), "yyyy-MM-dd HH:mm:ss");
            logger.debug("WRP :: Process completed cycle sending [" + submitToCisco + "] of [" + webRequests.size() + "] items to Cisco.  Start DT: {} - End DT: {}", current, end);

        } catch (Exception e) {
            logger.error("WRP :: Error captured during process! Message: {}", e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void checkLongQueuedWebRequest(Map<String, Object> mongoOptions, int mongoLimit) {
        List<WebRequest> webRequests = mongoRepository.findQueuedWebRequest(mongoOptions);
        int count = 1;
        String msg = "";
        for (WebRequest wr : webRequests) {
//            logger.debug(count + "/ " + wr + "\nNumber of Process: " + wr.getNumberOfProcess());
            msg +=  count + "/ " + wr + "\nNumber of Process: " + wr.getNumberOfProcess();
            if(wr.getNextProcessDateTime() != null){
                msg += "\nNext processed on: " + wr.getNextProcessDateTime();
            }
            msg += "\n\n\n";
            count++;
        }
        if(msg != null && msg.trim().length() > 0){
            String subject = webRequests.size() + " pending web request(s) created more than 1 hour ago.";
            String recipient = "<EMAIL>";
            if (webRequests.size() >= 10) {
                recipient = "<EMAIL>," + recipient;
            }
            communicationService.sendNotificationViaEmailAsPlainText(recipient, msg, subject);
        }
    }

    @Override
    public void generateCallLogReport(Task task) {

        Calendar cal = Calendar.getInstance();
        if(task.getScheduledTimeZone() != null && task.getScheduledTimeZone().trim().length() > 0){
            Calendar temp = Calendar.getInstance(TimeZone.getTimeZone(task.getScheduledTimeZone()));
            cal.set(temp.get(Calendar.YEAR), temp.get(Calendar.MONTH), temp.get(Calendar.DAY_OF_MONTH), temp.get(Calendar.HOUR_OF_DAY), temp.get(Calendar.MINUTE));
        }
        String timeStr = VelocityUtils.getDateAsString(cal.getTime(), "HH:mm");
        logger.debug("timeStr: {}", timeStr);
        int idx = task.getScheduledTimes().indexOf(timeStr);
        if(task.getScheduledTimes() != null && idx != -1){
            logger.debug("running call log report at " + cal.getTime());
            String[] recipients = getRecipientsForReport(task);
            if(recipients != null && recipients.length > 0){
                String reportName = task.getDescription() != null ? task.getDescription() : "";
                String reportCriteria = null;
                try{
                    if(reportName.equalsIgnoreCase("CTI_Waiting_On_Insurance")){
                        reportCriteria = "WAS_VERIFIED = 'Yes' AND IS_ORIGINAL = 'Yes' AND WAITING_ON_INSURANCE = 'Yes'";
                        /*generateReport(recipients, cal, 0, 3, "dd-MMM-yyyy", reportCriteria, "appt", "Waiting on Insurance", new Integer[]{Calendar.SUNDAY});   //next 3 days of appointment date*/

                        /** Updated the WOI process to use yesterday's Call Date vs 3 Days prior to an Appointment Date (MHC:04/11/2019) */
                        generateReport(recipients, cal, -1, -1, "dd-MMM-yyyy", reportCriteria, "call", "Waiting on Insurance", new Integer[]{Calendar.SUNDAY}); //previous call date

                    }else if(reportName.equalsIgnoreCase("CTI_No_Show")){
                        reportCriteria = "WAS_VERIFIED = 'Yes' AND IS_ORIGINAL = 'Yes' AND APPT_LOG_STATUS = 'CLOSED'";
                        Integer[] excludeDays = new Integer[]{Calendar.SATURDAY, Calendar.SUNDAY};
                        Calendar calDesired = Calendar.getInstance();
                        calDesired.setTime(DateTimeUtils.adjustDateByOffset(cal.getTime(), -4, excludeDays));
                        String startStr = VelocityUtils.getDateAsString(calDesired.getTime(), "dd-MMM-yyyy");
                        String endStr = VelocityUtils.getDateAsString(calDesired.getTime(), "dd-MMM-yyyy");
                        if(calDesired.get(Calendar.DAY_OF_WEEK) == Calendar.FRIDAY){
                            calDesired.add(Calendar.DAY_OF_MONTH, 1);
                            endStr = VelocityUtils.getDateAsString(calDesired.getTime(), "dd-MMM-yyyy");
                        }
                        emailReport(startStr, endStr, "appt", recipients, reportCriteria, "No Show");
//                        generateReport(recipients, cal, -4, -1, "dd-MMM-yyyy", reportCriteria, "appt", "No Show", excludeDays); //last 4 days of appointment date
                    }else if(reportName.equalsIgnoreCase("CTI_Temp_Patient_Error")){
                        reportCriteria = "TEMP_PATIENT_ID < 1000000 AND TEMP_PATIENT_ID LIKE '9%' AND UPPER(NVL(WAS_VERIFIED, 'NO')) != 'YES'";
                        generateReport(recipients, cal, -1, -1, "dd-MMM-yyyy", reportCriteria, "call", "Temp Patient Error", new Integer[]{Calendar.SUNDAY}); //previous call date
                    }else if(reportName.equalsIgnoreCase("CTI_Booked_On_Holiday")){
                        reportCriteria = "WAS_VERIFIED = 'Yes' AND IS_ORIGINAL = 'Yes' AND (DASHBOARD.IS_HOLIDAY(APPT_DATE) = 1 OR TO_CHAR(APPT_DATE, 'Dy') = 'Sun')";
                        generateReport(recipients, cal, 0, 365, "dd-MMM-yyyy", reportCriteria, "appt", "Appointment on Holiday", null);
                    }else if(reportName.equalsIgnoreCase("CTI_SCA_Ortho_Error")){
                        reportCriteria = "WAS_VERIFIED = 'Yes' AND IS_ORIGINAL = 'Yes' AND IS_ORTHO = 'Yes' AND NOT EXISTS (SELECT null FROM FACILITY fac WHERE fac.FACILITY_ID = V.FACILITY_ID AND fac.QSI_ORTHO_CLINIC = 9)";
                        generateReport(recipients, cal, 0, 365, "dd-MMM-yyyy", reportCriteria, "appt", "SCA Ortho Error", null);
                    }else if(reportName.equalsIgnoreCase("CTI_Ortho_Type_Error")){
                        reportCriteria = "WAS_VERIFIED = 'Yes' AND IS_ORIGINAL = 'Yes' AND APPT_TYPE NOT IN ('CN', 'OC') AND APPT_PROVIDER_ID BETWEEN 2099 AND 2200";
                        generateReport(recipients, cal, -1, -1, "dd-MMM-yyyy", reportCriteria, "call", "Ortho Type Error", new Integer[]{Calendar.SUNDAY});   //previous call date
                    }else if(reportName.equalsIgnoreCase("CTI_Screenpop_Open")){
                        generateReportForScreenPop(recipients);
                    }else{
                        generateReportByDateTypes(recipients, cal, idx);
                    }
                }catch(Exception ex){
                    ex.printStackTrace();
                    mongoRepository.addLogAction(null, ActionLog.REPORT_ACTION, "Fail to generate report for [" + reportName + "] with exception: " + ex.getMessage());
                }

            }

        }
    }

    private void generateReportByDateTypes(String[] recipients, Calendar current, int idxHourOfDay){
        String startDate, endDate;
        String dateFormat = "dd-MMM-yyyy";
        boolean runPriorMonth = current.get(Calendar.DAY_OF_MONTH) == 1;
        if(idxHourOfDay == 0){
            current.add(Calendar.DAY_OF_MONTH, -1);
            endDate = VelocityUtils.getDateAsString(current.getTime(), dateFormat);
            current.set(Calendar.DAY_OF_MONTH, 1);
            startDate = VelocityUtils.getDateAsString(current.getTime(), dateFormat);
        }else{
            startDate = endDate = VelocityUtils.getDateAsString(current.getTime(), dateFormat);
        }
        logger.debug("startDate: " + startDate);
        logger.debug("endDate: " + endDate);

        emailReport(startDate, endDate, "latest", recipients, null, null);
        emailReport(startDate, endDate, "appt", recipients, null, null);
        emailReport(startDate, endDate, "call", recipients, null, null);
        if(idxHourOfDay == 0 && runPriorMonth){
            current.add(Calendar.MONTH, -1);
            current.set(Calendar.DAY_OF_MONTH, 1);
            startDate = VelocityUtils.getDateAsString(current.getTime(), dateFormat);
            current.set(Calendar.DAY_OF_MONTH, current.getActualMaximum(Calendar.DAY_OF_MONTH));
            endDate = VelocityUtils.getDateAsString(current.getTime(), dateFormat);

            emailReport(startDate, endDate, "latest", recipients, null, null);
            emailReport(startDate, endDate, "appt", recipients, null, null);
            emailReport(startDate, endDate, "call", recipients, null, null);
        }
    }

    private void generateReport(String[] recipients, Calendar current
                                        , int startOffset, int endOffset
                                        , String dateFormat, String reportCriteria
                                        , String dateType, String reportType, Integer[] excludeDays){
        String startDate, endDate;
        Date currentDate = current.getTime();

        current.setTime(DateTimeUtils.adjustDateByOffset(currentDate, startOffset, excludeDays));
        startDate = VelocityUtils.getDateAsString(current.getTime(), dateFormat);

        current.setTime(DateTimeUtils.adjustDateByOffset(currentDate, endOffset, excludeDays));
        endDate = VelocityUtils.getDateAsString(current.getTime(), dateFormat);
        emailReport(startDate, endDate, dateType, recipients, reportCriteria, reportType);
    }

    private void generateReportForScreenPop(String[] recipients){
        Calendar cal = Calendar.getInstance();
        Date start, end;
        String startStr, endStr;
        Integer[] sunday = new Integer[]{Calendar.SUNDAY};
        if(cal.get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY){
            start = DateTimeUtils.adjustDateByOffset(cal.getTime(), -2, sunday);
        }else{
            start = DateTimeUtils.adjustDateByOffset(cal.getTime(), -1, sunday);
        }
        end = DateTimeUtils.adjustDateByOffset(cal.getTime(), 0, sunday);

        startStr = VelocityUtils.getDateAsString(start, "yyyy-MM-dd");
        endStr = VelocityUtils.getDateAsString(end, "yyyy-MM-dd");

        List<ScreenPopTime> screenPops = callService.getScreenPops(startStr, endStr);
        List<LinkedHashMap<String, String>> mapList = new ArrayList<LinkedHashMap<String, String>>();
        for(ScreenPopTime spt : screenPops){
            LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
            map.put("UUID", spt.getUuid());
            map.put("ANI", spt.getAni() != null ? spt.getAni().toString() : "");
            map.put("Call Center", spt.getCallCenter());
            map.put("Employee", spt.getEmployeeName().toString());
            map.put("Screen Type", spt.getScreenType());
            map.put("Loaded Time", spt.getPopDateTime());
            map.put("Unloaded Time", spt.getUnloadDateTime() != null ? spt.getUnloadDateTime() : "");
            map.put("Duration", spt.getDuration());
            mapList.add(map);
        }
        String fileName = "Screen Pop Report from " + startStr + " to " + endStr;
        String filePath = VelocityUtils.convertListMapToCSV(mapList, fileName, false);
        if(filePath.trim().length() > 0){
            communicationService.sendNotificationViaEmail(recipients, null, null, fileName, filePath, true);
        }
    }


    public void emailReport(String startDate, String endDate, String dateType, String[] recipients, String reportCriteria, String reportType){
        String fileName = "Call Center Detail by ";
        if(dateType != null){
            if(dateType.equalsIgnoreCase("latest")){
                fileName += "Latest Appointment Date";
            }else if(dateType.equalsIgnoreCase("appt")){
                fileName += "Appointment Date";
            }else{
                fileName += "Call Date";
            }
            if(reportType != null && reportType.trim().length() > 0){
                fileName += " for " + reportType.trim();
            }
            fileName += " - [" + startDate + " to " + endDate + "]";
            logger.debug("building report for " + fileName);
            List<LinkedHashMap<String, String>> mapList = callLogService.getCallLogDetailReport(startDate, endDate, null, dateType, reportCriteria);
            String filePath = VelocityUtils.convertListMapToCSV(mapList, fileName, false);
            if(filePath.trim().length() > 0){
                communicationService.sendNotificationViaEmail(recipients, null, null, fileName, filePath, true);
            }
        }

    }

    private String[] getRecipientsForReport(Task task){
        String reportName = task != null && task.getDescription() != null ? task.getDescription() : "CALL LOG REPORT";
        List<String> emails = jdbcRepository.getEmailByReport(reportName);
        int count = 0;
        String[] recipients = null;
        if(emails != null && emails.size() > 0){
            recipients = new String[emails.size()];
            for(String email : emails){
                if(email != null){
                    if(email.indexOf("@") == -1){
                        email = email + "@smilebrands.com";
                    }
                    recipients[count] = email;
                    count++;
                }
            }
        }
        return recipients;
    }

//    @PreDestroy
//    public void beforeDestroy(){
//        System.out.println("Before destroy ScheduleServiceImpl");
//        releaseAllTask();
//    }

    //HTTP GET request
    private int sendGet(WebRequest webRequest, int grabAgentId) throws Exception {
        int agentsReady = 0;
        try{
            Date start = new Date();
            String redirect = redirectUrl + webRequest.getUuid() + "?";
            String url = ciscoUrl + "?destPhoneNo=" + webRequest.getPhone();
            url += "&grabAgent=" + grabAgentId + "&uuid=" + webRequest.getUuid() + "&redirectUrl=" + redirect;
            mongoRepository.addLogAction(webRequest.getUuid(), ActionLog.WEB_REQUEST, "About to call cisco web service @[" + url + "].");

            logger.debug("WRP :: [" + webRequest.getUuid() + "] Cisco Web Request URL -> {}", url);

            URL obj = new URL(url);
            HttpURLConnection con = (HttpURLConnection) obj.openConnection();

            // optional default is GET
            con.setRequestMethod("GET");

            // add request header
            // con.setRequestProperty("User-Agent", "Mozilla/5.0");

            int responseCode = con.getResponseCode();
            //System.out.println("\nSending 'GET' request to URL : " + url);
            //System.out.println("Response Code : " + responseCode);

            BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
            String inputLine;
            StringBuffer response = new StringBuffer();

            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();

            //System.out.println("WRP :: [" + webRequest.getUuid() + "] Cisco RAW Response" + response.toString());
            ResponseWrapper responseWrapper = (ResponseWrapper)convertNoPath(response.toString(), ResponseWrapper.class);
            logger.debug("WRP :: Cisco Response Code: {} Body: {}", responseCode, response.toString());

            if (responseWrapper.getSuccess()) {
                Map<String, Object> data = (Map)responseWrapper.getData();
                agentsReady = data.get("agentsReady") != null ? (Integer)data.get("agentsReady") : 0;

                if (data.get("agentsReady") == null) {
                    logger.warn("WRP :: [" + webRequest.getUuid() + "] Cisco return a <NULL> agentsReady value when an Integer was excepted!");
                }
            }

            Date end = new Date();
            long seconds = (end.getTime()-start.getTime())/1000;
            logger.warn("WRP :: [" + webRequest.getUuid() + "] Cisco Request/Response duration was {} seconds.", seconds);

        }catch (Exception ex){
            ex.printStackTrace();
            agentsReady = -1;
        }
        return agentsReady;
    }

    public static Object convertNoPath(String json, Class clazz) {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationConfig.Feature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        try {
            return mapper.readValue(json, clazz);

        } catch (Exception e) {
            e.printStackTrace();

            throw new RuntimeException("Unable to convert Json [" + json + "] to Class [" + clazz.getName() + "]<br/> Msg: " + e.getMessage()+ "!");
        }
    }

}
