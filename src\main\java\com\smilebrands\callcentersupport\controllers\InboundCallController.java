package com.smilebrands.callcentersupport.controllers;

import com.smilebrands.callcentersupport.domain.*;
import com.smilebrands.callcentersupport.service.CallService;
import com.smilebrands.callcentersupport.service.CallbackService;
import com.smilebrands.callcentersupport.service.CommunicationService;
import com.smilebrands.callcentersupport.service.ConfigurationService;
import com.smilebrands.callcentersupport.util.BaseJsonConverter;
import com.smilebrands.callcentersupport.util.DateTimeUtils;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import org.springframework.web.util.UriComponents;

import javax.servlet.http.HttpServletRequest;
import java.beans.Statement;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * User: Marlin Clark
 * Date: 6/24/14
 */
@Controller
public class InboundCallController extends AbstractController {

    @Autowired
    private CallService callService;

    @Autowired
    private CommunicationService communicationService;

    @Autowired
    private ConfigurationService configurationService;

    @Autowired
    private CallbackService callbackService;

    @Autowired
    @Qualifier("SCREEN_POP_URL")
    private String baseScreenPopUrl;

    private final static Long PLANO_CALL_CENTER_NUMBER = 9725431691L;
    private final static Integer PLANO_FACILITY_ID = 83000;
    private final static Long IRVINE_CALL_CENTER_NUMBER = 7148690907L;
    private final static Integer IRVINE_FACILITY_ID = 82000;

    private final static SimpleDateFormat MDY_DATEFORMAT = new SimpleDateFormat("MMddyyyy");
    private final static SimpleDateFormat DMY_DATEFORMAT = new SimpleDateFormat("ddMMyyyy");

    /*
     * Service used to register an inbound call by Cisco.  A  UUID is produced and associated to the dialed
     * phone number and Cisco Session.
     */
    @ResponseBody
    @RequestMapping(value="register-call.xml", method= RequestMethod.GET, produces = "application/xml")
    public IvrResponseWrapper registerInboundCallXml(@RequestParam Long dnis,
                                                     @RequestParam String ani,
                                                     @RequestParam String sessionId) {

        IvrResponseWrapper wrapper = new IvrResponseWrapper();
        Long aniLong = null;
        try {
            aniLong = Long.valueOf(ani);
        } catch (Exception ex) {
            logger.debug("ANI [" + ani + "] is not a number!");
        }

        InboundCall inboundCall  = callService.registerCall(dnis, aniLong, sessionId);

        if (inboundCall.getPhoneNumber() != null) {
            inboundCall.setAllowCallback(callService.checkIfCallbackEnable(inboundCall.getPhoneNumber().getCallCenterName()));
        }

        wrapper.setInboundCall(inboundCall);
        wrapper.setSuccess(inboundCall != null);
        wrapper.setCount(inboundCall != null ? 1 : 0);

        logger.debug("Inbound Call captured DNIS[" + dnis + "] ANI[" + ani + "] Session ID[" + sessionId
                + "] created UUID[" + wrapper.getInboundCall().getUuid() + "].");
        logAction(wrapper.getInboundCall().getUuid(), ActionLog.INBOUND_CALL_ACTION, "Inbound Call captured DNIS[" + dnis + "] ANI[" + ani + "] Session ID[" + sessionId
                + "] created UUID[" + wrapper.getInboundCall().getUuid() + "].");

        return wrapper;
    }

    @ResponseBody
    @RequestMapping(value = "copy-call.xml", method = RequestMethod.GET, produces = "application/xml")
    public IvrResponseWrapper copyInboundCallXml(@RequestParam String oldSessionId, @RequestParam String newSessionId){
        IvrResponseWrapper wrapper = new IvrResponseWrapper();
        InboundCall inboundCall = callService.copyInboundCall(oldSessionId, newSessionId);
        wrapper.setInboundCall(inboundCall);
        wrapper.setSuccess(inboundCall != null);
        wrapper.setCount(inboundCall != null ? 1 : 0);
        if(inboundCall != null){
            logAction(inboundCall.getUuid(), ActionLog.INBOUND_CALL_ACTION, "Successfully copy InboundCall from session ID[" + oldSessionId + "] to session ID[" + newSessionId + "] with new UUID[" + inboundCall.getUuid() + "].");
        }else{
            logAction(null, ActionLog.INBOUND_CALL_ACTION, "Fail to copy InboundCall from session ID[" + oldSessionId + "] to session ID[" + newSessionId + "].");
        }
        return wrapper;
    }

    @ResponseBody
    @RequestMapping(value = "look-up-uuid.xml", method = RequestMethod.GET, produces = "application/xml")
    public IvrResponseWrapper lookupUUID(@RequestParam String sessionId){
        IvrResponseWrapper wrapper = new IvrResponseWrapper();
        wrapper.setInboundCall(callService.findInboundCallBySessionId(sessionId));
        wrapper.setSuccess(wrapper.getInboundCall() != null);
        wrapper.setCount(wrapper.getInboundCall() != null ? 1: 0);
        logAction(wrapper.getInboundCall() != null ? wrapper.getInboundCall().getUuid() : null,
                ActionLog.INBOUND_CALL_ACTION,
                "Look up InboundCall by session ID[" + sessionId + "] with result UUID[" + (wrapper.getInboundCall() != null ? wrapper.getInboundCall().getUuid() : "") + "]");
        return wrapper;
    }

    /*
     * Service used to find the closest Facilities to the provided Zip Code.
     */
    @ResponseBody
    @RequestMapping(value="zipcode-search.xml", method= RequestMethod.GET, produces = "application/xml")
    public IvrResponseWrapper searchZipCodeToFacilities(@RequestParam String uuid,
                                                        @RequestParam String zipCode,
                                                        @RequestParam(required = false) Integer limit) {

        logger.debug("Neighbor Offices search using ZIP[" + zipCode + "] UUI[" + uuid + "].");
        logAction(uuid, ActionLog.INBOUND_CALL_ACTION, "Neighbor Offices search using ZIP[" + zipCode + "] UUI[" + uuid + "].");

        List<Facility> facilities = callService.findFacilitiesByZipCode(zipCode);

        InboundCall call = callService.findInboundCall(uuid);
        if(call != null){
            call.setZipCode(zipCode);
            callService.updateInboundCall(call);
        }

        IvrResponseWrapper wrapper = new IvrResponseWrapper();
        List<Facility> returnedOffices = new ArrayList<Facility>();
        if(limit == null || limit.equals(0)){
            returnedOffices.addAll(facilities);

        }else{
            for(int i=0; i<facilities.size() && i<limit; i++){
                Facility facility = facilities.get(i);
                PhoneNumber phoneNumber = callService.findPhoneNumberByFacilityId(facility.getFacilityId(), uuid);
                if(phoneNumber != null){
                    facility.setAssociatedPhoneNumber(phoneNumber);
                    facility.setPhoneNumber(null);
                    returnedOffices.add(facility);
                }
            }

            if(returnedOffices.size() == 0 && call.getTimeZone() != null){
                PhoneNumber callCenterNumber = null;
                Integer facilityId = 0;
                String timeZone = call.getTimeZone();
                if(timeZone.equalsIgnoreCase("central")
                        || timeZone.equalsIgnoreCase("east")
                        || timeZone.equalsIgnoreCase("atlantic")){
                    logger.debug("switch call center to Plano for " + timeZone);
                    facilityId = PLANO_FACILITY_ID;
                    callCenterNumber = configurationService.getPhoneNumber(PLANO_CALL_CENTER_NUMBER);
                }else if(timeZone.equalsIgnoreCase("hawaii")
                        || timeZone.equalsIgnoreCase("alaska")
                        || timeZone.equalsIgnoreCase("pacific")
                        || timeZone.equalsIgnoreCase("mountain")){
                    logger.debug("switch call center to Irvine for " + timeZone);
                    facilityId = IRVINE_FACILITY_ID;
                    callCenterNumber = configurationService.getPhoneNumber(IRVINE_CALL_CENTER_NUMBER);
                }

                if(callCenterNumber != null){
                    Facility facility = new Facility();
                    facility.setFacilityId(facilityId);
                    facility.setAssociatedPhoneNumber(callCenterNumber);
                    returnedOffices.add(facility);
                }
            }

            /**
             *  Update the Inbound Call with the first Phone Number we found since
             *  the IVR will transfer the call to that office.  This will allow the
             *  Screen Pop to show the target office and not the ANI identified office.
             */
            if (returnedOffices.size() > 0) {
                Facility f = returnedOffices.get(0);
                if (f.getAssociatedPhoneNumber() != null) {
                    call.getPreviousPhoneNumbers().add(call.getPhoneNumber());
                    call.setPhoneNumber(f.getAssociatedPhoneNumber());
                    call.setTargetFacilityId(f.getFacilityId());
                    callService.updateInboundCall(call);
                }
            }
        }
        wrapper.setSuccess(true);
        wrapper.setFacilities(returnedOffices);
        wrapper.setCount(returnedOffices.size());
        wrapper.setMessage("Complete");
        logAction(uuid, ActionLog.INBOUND_CALL_ACTION, "Result for searching neighboring office for UUID[" + uuid + "] with zipCode[" + zipCode + "] is " + wrapper.getCount());

        return wrapper;
    }

    /*
     * Service used to find the closest Call Center based on Area Code from the ANI.
     */
    @ResponseBody
    @RequestMapping(value="area-code-state.xml", method= RequestMethod.GET, produces = "application/xml")
    public IvrResponseWrapper searchAreaCodeToCallCenter(@RequestParam String uuid) {

        logger.debug("Search for Call Center for Area Code using UUID[" + uuid + "].");
        logAction(uuid, ActionLog.INBOUND_CALL_ACTION, "Search for Call Center for Area Code using UUID[" + uuid + "]");

        InboundCall call = callService.findInboundCall(uuid);

        IvrResponseWrapper wrapper = new IvrResponseWrapper();
        wrapper.setSuccess(true);
        wrapper.setMessage(callService.getStateFromAni(call.getAni()));
        wrapper.setCount(1);

        return wrapper;
    }

    /*
     *  Service used to record configured events associated to the UUID provided by the call registration service.
     */
    @ResponseStatus(HttpStatus.OK)
    @RequestMapping(value="register-call-session-id", method= RequestMethod.GET)
    public void registerCallSessionId(@RequestParam String uuid,
                                      @RequestParam String sessionId) {

        logger.debug("Update Call UUID[" + uuid + "] with Session ID[" + sessionId + "].");
        logAction(uuid, ActionLog.INBOUND_CALL_ACTION, "Update Call UUID[" + uuid + "] with Session ID[" + sessionId + "].");
        InboundCall call = callService.findInboundCall(uuid);
        call.setSessionId(sessionId);

        callService.updateInboundCall(call);
    }


    /*
     *  Service used to record configured events associated to the UUID provided by the call registration service.
     */
    @ResponseStatus(HttpStatus.OK)
    @RequestMapping(value="register-call-event", method= RequestMethod.GET)
    public void registerCallEvent(@RequestParam String uuid,
                                  @RequestParam String eventCode) {
        String event = InboundCall.CALL_EVENT_MAP.get(eventCode);
        logger.debug("Call Event captured UUID[" + uuid + "] & Event ID[" + eventCode + "] & Event Type [" + event +"].");
        logAction(uuid, ActionLog.INBOUND_CALL_ACTION, "Call Event captured UUID[" + uuid + "] & Event ID[" + eventCode + "] & Event Type [" + event +"].");
        callService.registerCallEvent(uuid, event == null ? eventCode : event);
    }

    /**
     *  Service used to access Facility Details.
     */
    @ResponseBody
    @RequestMapping(value="facility-detail.xml", method= RequestMethod.GET, produces = "application/xml")
    public IvrResponseWrapper searchFacility(String uuid) {

        logger.debug("Facility Detail using UUI[" + uuid + "].");
        logAction(uuid, ActionLog.INBOUND_CALL_ACTION, "Facility Detail using UUI[" + uuid + "].");
        IvrResponseWrapper wrapper = new IvrResponseWrapper();

        InboundCall call = callService.findInboundCall(uuid);
        if (call != null) {
            Facility facility = callService.findFacilityByFacilityId(call.getTargetFacilityId());

            wrapper.setSuccess(true);
            wrapper.setFacility(facility);
            wrapper.setCount(1);
            wrapper.setMessage("Complete");

        } else {
            wrapper.setSuccess(false);
            wrapper.setMessage("Missing Inbound Call!");

        }

        return wrapper;
    }

    /**
     *  Service used to text an address link to target Facility
     */
    @ResponseBody
    @RequestMapping(value="text-facility-map.xml", method= RequestMethod.GET, produces = "application/xml")
    public IvrResponseWrapper textFacilityMapLink(String uuid, Long mobileNumber) {
        logger.debug("Request to send SMS message to UUID[" + uuid + "] & Mobile Number[" + mobileNumber + "].");
        logAction(uuid, ActionLog.INBOUND_CALL_ACTION, "Request to send SMS message to UUID[" + uuid + "] & Mobile Number[" + mobileNumber + "].");

        IvrResponseWrapper wrapper = new IvrResponseWrapper();

        InboundCall call = callService.findInboundCall(uuid);
        if(call == null){
            wrapper.setSuccess(false);
            wrapper.setMessage("Inbound Call Not Found.");
        }else if(call.getPhoneNumber() == null || call.getPhoneNumber().getFacilityId() == null){
            wrapper.setSuccess(false);
            wrapper.setMessage("Facility Not Found");
        }else{
            Facility facility = callService.findFacilityByFacilityId(call.getPhoneNumber().getFacilityId());
            if(facility == null){
                wrapper.setSuccess(false);
                wrapper.setMessage("Facility Not Found");
            }else{

                String sids = communicationService.sendSMS(mobileNumber.toString(), "Facility Map Link will be in here.", facility.getFacilityId(), null);
                wrapper.setSuccess(sids != null && sids.length() > 0);
            }
        }

        return wrapper;
    }

    /*
     *  Service used to find an upcoming patient appointment by the phone number provided.
     */
    @ResponseBody
    @RequestMapping(value="appointment-search.xml", method= RequestMethod.GET, produces = "application/xml")
    public IvrResponseWrapper searchAppointmentByPhone(@RequestParam Long appointmentPhone,
                                                       @RequestParam String dateOfBirth,
                                                       @RequestParam String uuid) {

        logger.debug("Search Appointment using Phone[" + appointmentPhone + "] with UUID[" + uuid + "] and dob as [" + dateOfBirth + "].");
        logAction(uuid, ActionLog.INBOUND_CALL_ACTION, "Search Appointment using Phone[" + appointmentPhone + "] with UUID[" + uuid + "] and dob as [" + dateOfBirth + "].");
        IvrResponseWrapper wrapper = new IvrResponseWrapper();

        // -- grab the existing InboundCall entity
        InboundCall call = callService.findInboundCall(uuid);
        //DATE OF BIRTH IS REQUIRED AND HAS TO BE FORMATTED AS MMDDYYYY
        boolean isInvalidDOB = dateOfBirth == null || dateOfBirth.trim().length() != 8;
        if(!isInvalidDOB){
            try{
                if(Long.valueOf(dateOfBirth) != null){
                    isInvalidDOB = false;
                }

                //BECAUSE IVR PROMPTS USER TO ENTER 2-DIGIT DAY 2-DIGIT MONTH and 4-DIGIT YEAR -- ROLLBACK TO MMDDYYYY ON 09-10-2014
                try {
                    Date dob = MDY_DATEFORMAT.parse(dateOfBirth.toString());
                    if(dob == null){
                        isInvalidDOB = true;
                    }
                } catch (ParseException e) {

                }
            }catch (NumberFormatException ex){
                isInvalidDOB = true;
            }
        }
        if(isInvalidDOB){
            wrapper.setSuccess(false);
            wrapper.setMessage("No Appointment Found.");
            logger.debug("Invalid data of birth");
            return wrapper;
        }

        // -- searches for an existing appointment and returns success (matched) along with the Phone Number
        Date dt = new Date();
        Integer facilityId = null;
        if(call.getPhoneType() != null && call.getPhoneType().equalsIgnoreCase("ML")){
            facilityId = call.getPhoneNumber().getFacilityId();
        }
        logger.debug("Search Appointment by Phone Number [" + appointmentPhone + "] with Facility ID [" + facilityId + "] with DOB [" + dateOfBirth.trim() + "]");
        Appointment appointment = callService.findAppointmentByPatientPhoneNumber(appointmentPhone, facilityId, dateOfBirth.trim());
        logger.debug("Time taken to search " + (new Date().getTime() - dt.getTime()));
        if (appointment != null) {
            appointment.setFacility(callService.findFacilityByFacilityId(appointment.getFacility().getFacilityId()));
            call.setTtsAppointment(appointment.getTTSAppointment(call.getLanguage()));
            if(appointment.getFacility() != null){
                call.setTtsFacility(appointment.getFacility().getTTSFacility());
                call.setTtsFacilityHour(appointment.getFacility().getTTSFacilityHour(call.getLanguage(), true));
            }
            logger.debug("Found an Appointment Date[" + appointment.getAppointmentDateTime() + "] using Patient Phone[" + appointmentPhone + "].");

            PhoneNumber number = callService.findPhoneNumberByFacilityId(appointment.getFacility().getFacilityId(), uuid);
            logger.debug("Found Facility Phone using Facility ID[" + appointment.getFacility().getFacilityId() + "].");

            if (number != null) {

                // -- update the InboundCall with the Appointment Facility's Main Number.
                if(number.getPhoneNumber() != null
                        && (call.getPhoneNumber() == null || !number.getPhoneNumber().equals(call.getPhoneNumber().getPhoneNumber()))){
                    call.getPreviousPhoneNumbers().add(call.getPhoneNumber());
                    call.setPhoneNumber(number);
                    call.setTargetFacilityId(number.getFacilityId());
                    call.setMatched(number.getFacilityId() != null && !number.getFacilityId().equals(0));
                    Facility facility = callService.findFacilityByFacilityId(number.getFacilityId());
                    if(facility != null){
                        call.setTtsFacility(facility.getTTSFacility());
                        call.setTtsFacilityHour(facility.getTTSFacilityHour(call.getLanguage(), true));
                    }
                }
                appointment.getPatient().setPhoneNumber(appointmentPhone);

                call.setPatient(appointment.getPatient());

                callService.updateInboundCall(call);

                wrapper.setSuccess(true);
                wrapper.setMessage("Appointment Found");
                wrapper.setInboundCall(call);

            } else {
                wrapper.setSuccess(false);
                wrapper.setMessage("No Facility Phone Number Found.");
            }

        } else {
            InboundCallEvent event = new InboundCallEvent();
            event.setEventDateTime(new Date());
            event.setEventId("Search not Found");
            call.getInboundCallEvents().add(event);
            callService.updateInboundCall(call);

            wrapper.setSuccess(false);
            wrapper.setMessage("No Appointment Found.");

        }

        return wrapper;
    }

    /*
     *  Service used to find current patient facility by the phone number provided and last visit.
     *
     *  Returns an InboundCall -> PhoneNumber
     */
    @ResponseBody
    @RequestMapping(value="ani-search.xml", method= RequestMethod.GET, produces = "application/xml")
    public IvrResponseWrapper searchFacilityByPhoneNumber(@RequestParam Long phoneNumber,
                                                          @RequestParam String uuid) {

        logger.debug("Search Facility using Phone[" + phoneNumber + "] with UUID[" + uuid + "].");
        logAction(uuid, ActionLog.INBOUND_CALL_ACTION, "Search Facility using Phone[" + phoneNumber + "] with UUID[" + uuid + "].");
        IvrResponseWrapper wrapper = new IvrResponseWrapper();

        // -- grab the existing InboundCall entity
        InboundCall call = callService.findInboundCall(uuid);

        Date dt = new Date();
        PhoneNumber number = callService.findFacilityByAni(phoneNumber);
        logger.debug("Time taken to search " + (new Date().getTime() - dt.getTime()));

        if (number != null) {
            // -- update the InboundCall with the Appointment Facility's Main Number.
            if(number.getPhoneNumber() != null
                    && (call.getPhoneNumber() == null || !number.getPhoneNumber().equals(call.getPhoneNumber().getPhoneNumber()))){
                call.getPreviousPhoneNumbers().add(call.getPhoneNumber());
                call.setPhoneNumber(number);
                call.setTargetFacilityId(number.getFacilityId());
                call.setMatched(number.getFacilityId() != null && !number.getFacilityId().equals(0));
                Facility facility = callService.findFacilityByFacilityId(number.getFacilityId());
                if(facility != null){
                    call.setTtsFacility(facility.getTTSFacility());
                    call.setTtsFacilityHour(facility.getTTSFacilityHour(call.getLanguage(), true));
                }
            }

            wrapper.setSuccess(true);
            wrapper.setMessage("Patient Found");
            wrapper.setInboundCall(call);

        } else {
            wrapper.setSuccess(false);
            wrapper.setMessage("No Patient Phone Number Found.");
        }

        return wrapper;
    }

    @ResponseBody
    @RequestMapping(value="psr/generate-url-with-session-id.xml", method= RequestMethod.GET, produces = "application/xml")
    public IvrResponseWrapper generatePsrScreenPopUrlWithSessionId(@RequestParam String sessionId,
                                                                   @RequestParam Integer agentId,
                                                                   HttpServletRequest request) {
        InboundCall inboundCall = callService.findInboundCallBySessionId(sessionId);
        logAction(null, ActionLog.INBOUND_CALL_ACTION, "Request to psr/generate-url-with-session-id.xml with Session ID[" + sessionId + "] and agentId[" + agentId + "]");
        if(inboundCall != null){
            return generatePsrScreenPopUrl(inboundCall.getUuid(), sessionId, agentId, request);
        }else{
            IvrResponseWrapper wrapper = new IvrResponseWrapper();
            wrapper.setSuccess(false);
            wrapper.setMessage("Inbound call not found for session ID[" + sessionId + "]");
            logAction(null, ActionLog.INBOUND_CALL_ACTION, "Inbound call not found for session ID[" + sessionId + "] @ request psr/generate-url-with-session-id");
            return wrapper;
        }

    }

    /**
     *  Service used to generate a PSR Screen Pop URL for the IVR to use.
     */
    @ResponseBody
    @RequestMapping(value="psr/generate-url.xml", method= RequestMethod.GET, produces = "application/xml")
    public IvrResponseWrapper generatePsrScreenPopUrl(@RequestParam String uuid,
                                                      @RequestParam (required = false) String sessionId,
                                                      @RequestParam Integer agentId,
                                                      HttpServletRequest request) {

        IvrResponseWrapper wrapper = new IvrResponseWrapper();
        wrapper.setSuccess(true);

        // -- find the existing InboundCall entity
        InboundCall call = callService.findInboundCall(uuid);
        if(call == null && sessionId != null){
            call = callService.findInboundCallBySessionId(sessionId);
            if(call == null){
                call = generateMissingInboundCall(agentId, sessionId, "psr");
            }
            uuid = call.getUuid();
        }
        logger.debug("Request to generate psr url for UUID[" + uuid + "] with agent ID[" + agentId + "] and session ID[" + sessionId + "]");
        logAction(uuid, ActionLog.INBOUND_CALL_ACTION, "Request to generate psr url for UUID[" + uuid + "] with agent ID[" + agentId + "] and session ID[" + sessionId + "]");
        Integer mainMenuPress = call.getMainMenuNumberPress();
        if(!call.getMainMenuNumberPressOverride().equals(-1)){
            mainMenuPress = call.getMainMenuNumberPressOverride();
        }

        // -- register the URL generation
        UriComponents ucb = ServletUriComponentsBuilder.fromContextPath(request)
                .path("/load-screen-pop/")
                .path("psr/")
                .path(uuid + "/")
                .path(agentId + "/")
                .path(call.getLanguage() + "/")
                .path(mainMenuPress + "/")
                .path((call.getTargetFacilityId() == null ? 0 : call.getTargetFacilityId()) + "")
                .build();

        String url = ucb.toUriString();
        logger.debug(url);

        ScreenPopUrl pop = new ScreenPopUrl();
        pop.setUrl(url);
        pop.setAgentEmployeeNumber(agentId);
        pop.setGenerationDate(new Date());

        call.getScreenPopUrls().add(pop);
        callService.updateInboundCall(call);

        wrapper.setMessage(url);

        logger.debug("Generate PSR url [" + url + "] with UUID [" + uuid + "]");
        logAction(uuid, ActionLog.INBOUND_CALL_ACTION, "Generate PSR url [" + url + "] with UUID [" + uuid + "]");

        return wrapper;
    }

    /**
     *  Service used to register the result of the URL.
     */
    @ResponseStatus(HttpStatus.OK)
    @RequestMapping(value="psr/url-response.xml", method= RequestMethod.GET)
    public void registerPsrScreenPopResult(@RequestParam String uuid, @RequestParam Integer agentId, boolean connected){
        logger.debug("Register PSR Screen Pop Result with UUID[" + uuid + "], agentId [" + agentId + "] and connected [" + connected + "].");
        logAction(uuid, ActionLog.INBOUND_CALL_ACTION, "Register PSR Screen Pop Result with UUID[" + uuid + "], agentId [" + agentId + "] and connected [" + connected + "].");
        callService.updateInboundCallScreenPopResult(uuid, agentId, connected);
    }

    @ResponseBody
    @RequestMapping(value="csr/generate-url-with-session-id.xml", method= RequestMethod.GET, produces = "application/xml")
    public IvrResponseWrapper generateCsrScreenPopUrlWithSessionId(@RequestParam String sessionId,
                                                                   @RequestParam Integer agentId,
                                                                   HttpServletRequest request) {
        InboundCall inboundCall = callService.findInboundCallBySessionId(sessionId);
        logAction(null, ActionLog.INBOUND_CALL_ACTION, "Request to csr/generate-url-with-session-id.xml with Session ID[" + sessionId + "] and agentId[" + agentId + "]");
        if(inboundCall != null){
            return generateCsrScreenPopUrl(inboundCall.getUuid(), sessionId, agentId, request);
        }else{
            IvrResponseWrapper wrapper = new IvrResponseWrapper();
            wrapper.setSuccess(false);
            wrapper.setMessage("Inbound call not found for session ID[" + sessionId + "]");
            logAction(null, ActionLog.INBOUND_CALL_ACTION, "Inbound call not found for session ID[" + sessionId + "] @ request csr/generate-url-with-session-id");
            return wrapper;
        }
    }

    /**
     *  Service used to generate a CSR Screen Pop URL for the IVR to use.
     */
    @ResponseBody
    @RequestMapping(value="csr/generate-url.xml", method= RequestMethod.GET, produces = "application/xml")
    public IvrResponseWrapper generateCsrScreenPopUrl(@RequestParam String uuid,
                                                      @RequestParam (required = false) String sessionId,
                                                      @RequestParam Integer agentId,
                                                      HttpServletRequest request) {

        logger.debug("Request to generate csr url for UUID[" + uuid + "] with agent ID[" + agentId + "] and session ID[" + sessionId + "]");
        logAction(uuid, ActionLog.INBOUND_CALL_ACTION, "Request to generate csr url for UUID[" + uuid + "] with agent ID[" + agentId + "] and session ID[" + sessionId + "]");

        IvrResponseWrapper wrapper = new IvrResponseWrapper();
        wrapper.setSuccess(true);

        // -- find the existing InboundCall entity
        InboundCall call = callService.findInboundCall(uuid);
        if(call == null){
            call = callService.findInboundCallBySessionId(sessionId);
            if(call == null){
                call = generateMissingInboundCall(agentId, sessionId, "csr");
            }
            uuid = call.getUuid();
        }

        // -- register the URL generation
        UriComponents ucb = ServletUriComponentsBuilder.fromContextPath(request)
                .path("/load-screen-pop/")
                .path("csr/")
                .path(uuid + "/")
                .path(agentId + "/")
                .path(call.getLanguage() + "/")
                .path(call.getMainMenuNumberPress() + "")
                .build();

        String url = ucb.toUriString();
        logger.debug(url);

        ScreenPopUrl pop = new ScreenPopUrl();
        pop.setUrl(url);
        pop.setAgentEmployeeNumber(agentId);
        pop.setGenerationDate(new Date());

        call.getScreenPopUrls().add(pop);
        callService.updateInboundCall(call);

        wrapper.setMessage(url);

        logger.debug("Generate CSR url as [" + url + "] with UUID [" + uuid + "].");
        logAction(uuid, ActionLog.INBOUND_CALL_ACTION, "Generate CSR url as [" + url + "] with UUID [" + uuid + "].");

        return wrapper;
    }

    private InboundCall generateMissingInboundCall(Integer agentId, String sessionId, String agentType){
        String callCenterName = callService.getCallCenterName(agentId, null, agentType, false);
        CallCenter callCenter = callService.getCallCenterByName(callCenterName);
        InboundCall call = callService.registerCall(callCenter.getCallbackPhoneNumber(), null, sessionId);
        call.setPhoneType("NA");
        Date loadedDate = DateTimeUtils.getLoadedTimeByLocationOffset(callCenter.getTimezoneOffset());
        call.setCreateDateTime(loadedDate);
        call.setCreateDate(VelocityUtils.getDateAsString(loadedDate, null));
        call.setCreateTime(VelocityUtils.getDateAsString(loadedDate, "HHmmss"));
        return call;
    }

    /**
     *  Service used to register the result of the URL.
     */
    @ResponseStatus(HttpStatus.OK)
    @RequestMapping(value="csr/url-response.xml", method= RequestMethod.GET)
    public void registerCsrScreenPopResult(String uuid, Integer agentId, boolean connected){
        logger.debug("Register CSR Screen Pop Result with UUID[" + uuid + "], agentId [" + agentId + "] and connected [" + connected + "].");
        logAction(uuid, ActionLog.INBOUND_CALL_ACTION, "Register CSR Screen Pop Result with UUID[" + uuid + "], agentId [" + agentId + "] and connected [" + connected + "].");
        callService.updateInboundCallScreenPopResult(uuid, agentId, connected);
    }

    @ResponseStatus(HttpStatus.OK)
    @RequestMapping(value = "register-call-language", method = RequestMethod.GET)
    public void updateLanguage(@RequestParam String uuid, @RequestParam String language){
        logger.debug("Register Call Language [" + language + "] with UUID[" + uuid + "].");
        logAction(uuid, ActionLog.INBOUND_CALL_ACTION, "Register Call Language [" + language + "] with UUID[" + uuid + "].");
        callService.registerCallLanguage(uuid, language);
    }

    @ResponseStatus(HttpStatus.OK)
    @RequestMapping(value = "register-call-main-menu-press", method = RequestMethod.GET)
    public void registerCallMainMenuNumberPress(@RequestParam String uuid, @RequestParam Integer mainMenuNumberPress){
        logger.debug("Register Main Menu Number Press [" + mainMenuNumberPress + "] with UUID[" + uuid + "].");
        logAction(uuid, ActionLog.INBOUND_CALL_ACTION, "Register Main Menu Number Press [" + mainMenuNumberPress + "] with UUID[" + uuid + "].");
        callService.registerCallMainMenuNumberPress(uuid, mainMenuNumberPress);
    }

    /*
     * Service used to find the closest Facilities to the provided Zip Code with a specific return convention of
     * facility1, facility2, etc. to support Cisco's limitation with XML.
     */
    @ResponseBody
    @RequestMapping(value="cisco-zipcode-search.xml", method= RequestMethod.GET, produces = "application/xml")
    public CiscoSearchResponseWrapper facilitiesZipCodeSearch(@RequestParam String uuid, @RequestParam String zipCode) throws Exception {

        logger.debug("Neighbor Offices search using ZIP[" + zipCode + "] with UUID[" + uuid + "].");
        List<Facility> facilities = callService.findFacilitiesByZipCode(zipCode);
        InboundCall inboundCall = callService.findInboundCall(uuid);
        String language = inboundCall != null ? inboundCall.getLanguage() : null;
        boolean isSp = language != null && language.equalsIgnoreCase("sp");
        int presentSize = (facilities.size() > 6 ? 6 : facilities.size());
        String message = isSp   ? "Encontramos " + presentSize + " oficinas cercanas"
                : "We have found " + presentSize
                + " office" + (facilities.size() > 1 ? "s" : "") + " close to your location";

        CiscoSearchResponseWrapper wrapper = new CiscoSearchResponseWrapper();
        for (int x=1, y=0; y < facilities.size(); x++, y++) {
            Facility f = facilities.get(y);
            PhoneNumber phoneNumber = callService.findPhoneNumberByFacilityAndPhoneType(f.getFacilityId(), "ML");
            if (phoneNumber != null) {
                f.setAssociatedPhoneNumber(phoneNumber);
                f.setPhoneNumber(null);
            }

            new Statement(f, "setCounter", new Object[]{ x }).execute();
            new Statement(f, "setLanguage", new Object[]{ language }).execute();
            new Statement(wrapper, "setFacility" + x, new Object[]{ f }).execute();

            if (x==6) { break; }
        }

        wrapper.setSuccess(true);
        wrapper.setCount((facilities.size() > 6 ? 6 : facilities.size()));
        wrapper.setNextGroupMessage(isSp
                ? "Si no ha ubicado la oficina correcta, porfavor oprima estrella para las siguientes oficinas."
                : "If you have not found the right office please press star for the next set of offices.");
        wrapper.setMessage(message);

        return wrapper;
    }


    @ResponseStatus(HttpStatus.OK)
    @RequestMapping(value = "register-toll-free-facility.xml", method = RequestMethod.GET)
    public void registerTollFreeFacilitySelection(@RequestParam String uuid, @RequestParam Integer facilityId) {
        InboundCall inboundCall = callService.findInboundCall(uuid);
        logAction(uuid, ActionLog.INBOUND_CALL_ACTION, "About to register toll free call[" + uuid + "] with facility[" + facilityId + "]");
        if (inboundCall != null) {
            inboundCall.setTargetFacilityId(facilityId);
            callService.updateInboundCall(inboundCall);
        }
    }

    @ResponseBody
    @RequestMapping(value = "register-callback-request.xml", method = RequestMethod.GET, produces = "application/xml")
    public IvrResponseWrapper registerCallback(@RequestParam String uuid,
                                               @RequestParam String sessionId,
                                               @RequestParam String agentType,
                                               @RequestParam String callbackNumber,
                                               @RequestParam Long dnis,
                                               @RequestParam String callCenter){
        IvrResponseWrapper wrapper = new IvrResponseWrapper();
        Long aniNum = null;
        try{
            aniNum = Long.valueOf(callbackNumber);
        }catch (NumberFormatException ex){
            ex.printStackTrace();
        }

        InboundCall inboundCall = callService.findInboundCall(uuid);
        if(inboundCall == null && sessionId != null){
            inboundCall = callService.findInboundCallBySessionId(sessionId);
        }
        if(inboundCall == null){
            inboundCall = callService.registerCall(dnis, aniNum, sessionId);
        }

        CallbackRequest cb = callbackService.registerCallbackRequest(inboundCall != null ? inboundCall.getUuid() : callService.generateUUID()
                                                                , aniNum, agentType, callCenter);
        logAction(cb.getUuid(), ActionLog.INBOUND_CALL_ACTION, "Register callback request with callbackNumber[" + callbackNumber + "]");
        callService.registerCallEvent(uuid, InboundCall.CALL_EVENT_MAP.get("108"));

        wrapper.setInboundCall(inboundCall);
        wrapper.setCallbackRequest(cb);
        wrapper.setSuccess(cb != null);
        return wrapper;
    }

    @ResponseBody
    @RequestMapping(value = "register-callback-number.xml", method = RequestMethod.GET, produces = "application/xml")
    public IvrResponseWrapper registerCallbackNumber(@RequestParam String uuid,
                                                    @RequestParam String sessionId,
                                                    @RequestParam String callbackNumber,
                                                    @RequestParam(required = false) String callCenter,
                                                    @RequestParam(required = false) String language){
        IvrResponseWrapper wrapper = new IvrResponseWrapper();
        Long aniNum = null;
        try{
            aniNum = Long.valueOf(callbackNumber);
        }catch (NumberFormatException ex){
            ex.printStackTrace();
        }

        CallbackRequest cb = callbackService.registerCallbackNumber(uuid, aniNum);
        wrapper.setCallbackRequest(cb);

        return wrapper;
    }

    @ResponseBody
    @RequestMapping(value = "initiate-callback.xml", method = RequestMethod.GET, produces = "application/xml")
    public IvrResponseWrapper initiateCallBack(@RequestParam String uuid,
                                               @RequestParam String newSessionId,
                                               @RequestParam Integer agentId,
                                               HttpServletRequest request){
        IvrResponseWrapper wrapper = new IvrResponseWrapper();
        InboundCall oldInboundCall = callService.findInboundCall(uuid);
        CallbackRequest cb = callbackService.findCallbackRequestById(oldInboundCall != null ? oldInboundCall.getUuid() : null);
        String msg = "Initiate callback with UUID[" + uuid + "], newSessionId[" + newSessionId + "], agentId[" + agentId + "]";
        msg += " and callback record[" + (cb != null ? "found" : "not found") + "]";
        logAction(uuid, ActionLog.CALL_BACK, msg);
        InboundCall inboundCall = null;
//        String newUUID = null;

        String agentType = cb != null ? cb.getAgentType() : "csr";
//        if(cb != null){
//            inboundCall = callService.registerCall(oldInboundCall != null ? oldInboundCall.getDnis() : 0L, cb.getPhoneNumber(), newSessionId);
//            newUUID = inboundCall.getUuid();
//            agentType = cb.getAgentType();
//        }else{
//            inboundCall = generateMissingInboundCall(agentId, newSessionId, agentType);
//            newUUID = inboundCall.getUuid();
//        }
        if(oldInboundCall != null){
            callService.registerSessionId(oldInboundCall.getUuid(), newSessionId);
            callService.registerCallEvent(oldInboundCall.getUuid(), InboundCall.CALL_EVENT_MAP.get("109"));
        }else{
            inboundCall = generateMissingInboundCall(agentId, newSessionId, agentType);
            callService.registerCallEvent(inboundCall.getUuid(), InboundCall.CALL_EVENT_MAP.get("109"));

            uuid = inboundCall.getUuid();
        }
        if(cb != null){
            cb.setHandledByAgent(agentId);
            cb.setSessionId(newSessionId);
            cb.setProcessedDateTime(VelocityUtils.getDateAsString(new Date(), "yyyy-MM-dd HHmmss"));
            callbackService.persistCallbackRequest(cb);
            wrapper.setCallbackRequest(cb);
        }
        if("psr".equalsIgnoreCase(agentType)){
            return generatePsrScreenPopUrl(uuid, newSessionId, agentId, request);
        }else if("csr".equalsIgnoreCase(agentType)){
            return generateCsrScreenPopUrl(uuid, newSessionId, agentId, request);
        }

        return wrapper;
    }

    @ResponseBody
    @RequestMapping(value="health/check.xml", method= RequestMethod.GET, produces = "application/xml")
    public IvrResponseWrapper mongoDBHealthCheck(HttpServletRequest request) {
        IvrResponseWrapper wrapper = new IvrResponseWrapper();
        Integer opts[] = new Integer[]{ 12010, 51010, 10020, 20030, 32203 };

        try {
            int fid = opts[new Random().nextInt(opts.length)];
            Facility facility = callService.findFacilityByFacilityId(fid);
            wrapper.setSuccess(true);
            wrapper.setMessage("Facility query to MongoDB for Facility ID[" + fid + "] succeeded.");

        } catch (Exception ex) {
            wrapper.setSuccess(false);
            wrapper.setMessage("Facility query to MongoDB failed!");
        }

        return wrapper;
    }
}
