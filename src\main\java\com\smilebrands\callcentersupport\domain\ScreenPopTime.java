package com.smilebrands.callcentersupport.domain;

import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by phongpham on 8/26/14.
 */
@Document
public class ScreenPopTime {

    @Id
    private Long id;
    private String uuid;
    private Long dnis;
    private Long ani;
    private String callCenter;
    private String phoneType;
    private String callDate;
    private Integer employeeNumber;
    private String screenType;
    private String popDateTime;
    private String unloadDateTime;
    private List<String> openDateTime = new ArrayList<String>();

    private String employeeName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Long getDnis() {
        return dnis;
    }

    public void setDnis(Long dnis) {
        this.dnis = dnis;
    }

    public Long getAni() {
        return ani;
    }

    public void setAni(Long ani) {
        this.ani = ani;
    }

    public String getCallCenter() {
        return callCenter;
    }

    public void setCallCenter(String callCenter) {
        this.callCenter = callCenter;
    }

    public String getPhoneType() {
        return phoneType;
    }

    public void setPhoneType(String phoneType) {
        this.phoneType = phoneType;
    }

    public String getCallDate() {
        return callDate;
    }

    public void setCallDate(String callDate) {
        this.callDate = callDate;
    }

    public Integer getEmployeeNumber() {
        return employeeNumber;
    }

    public void setEmployeeNumber(Integer employeeNumber) {
        this.employeeNumber = employeeNumber;
    }

    public String getScreenType() {
        return screenType;
    }

    public void setScreenType(String screenType) {
        this.screenType = screenType;
    }

    public String getPopDateTime() {
        return popDateTime;
    }

    public void setPopDateTime(String popDateTime) {
        this.popDateTime = popDateTime;
    }

    public String getUnloadDateTime() {
        return unloadDateTime;
    }

    public void setUnloadDateTime(String unloadDateTime) {
        this.unloadDateTime = unloadDateTime;
    }

    public List<String> getOpenDateTime() {
        return openDateTime;
    }

    public void setOpenDateTime(List<String> openDateTime) {
        this.openDateTime = openDateTime;
    }

    @Transient
    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getDuration(){
        String result = "";
        if(this.unloadDateTime != null && this.unloadDateTime.trim().length() > 0){
            Date start = VelocityUtils.parseDate(this.popDateTime, "yyyy-MM-dd'T'HH:mm:ss");
            Date end = VelocityUtils.parseDate(this.unloadDateTime, "yyyy-MM-dd'T'HH:mm:ss");
            long duration = end.getTime() - start.getTime();
            int diffSeconds = (int)duration / 1000 % 60;
            int diffMinutes = (int)duration / (60 * 1000) % 60;
            int diffHours = (int)duration / (60 * 60 * 1000);
            result = diffHours + " hr " + diffMinutes + " min " + diffSeconds + " sec";
        }
        return result;
    }

    public Long generateId(){
        int hashValue = 0;
        hashValue += this.uuid != null ? this.uuid.hashCode() : 0;
        hashValue += this.dnis != null ? this.dnis.hashCode() : 0;
        hashValue += this.ani != null ? this.ani.hashCode() : 0;
        hashValue += this.callCenter != null ? this.callCenter.hashCode() : 0;
        hashValue += this.phoneType != null ? this.phoneType.hashCode() : 0;
        hashValue += this.callDate != null ? this.callDate.hashCode() : 0;
        hashValue += this.employeeNumber != null ? this.employeeNumber.hashCode() : 0;
        hashValue += this.screenType != null ? this.screenType.hashCode() : 0;
        hashValue += this.popDateTime != null ? this.popDateTime.hashCode() : 0;
        hashValue += new Date().hashCode();
        return new Long(hashValue);
    }
}
