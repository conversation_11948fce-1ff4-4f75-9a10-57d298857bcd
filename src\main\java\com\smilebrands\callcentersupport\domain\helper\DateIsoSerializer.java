package com.smilebrands.callcentersupport.domain.helper;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.DateSerializer;
import org.codehaus.jackson.JsonGenerator;
import org.codehaus.jackson.JsonProcessingException;
import org.codehaus.jackson.map.JsonSerializer;
import org.codehaus.jackson.map.SerializerProvider;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.ISODateTimeFormat;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Used to serialize Java.util.Date
 *
 * <AUTHOR>
 * Date: 6/27/11
 */
@Component
public class DateIsoSerializer extends DateSerializer {
    public DateIsoSerializer(){
        super(false, new SimpleDateFormat("MM-dd-yyyy HH:mm:ss"));
    }


}
