package com.smilebrands.callcentersupport.domain;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

/**
 * Date: 2/2/12
 * Time: 1:27 PM
 */
@Document
public class Employee {

    @Id
    private Integer employeeNumber;
    private String firstName;
    private String lastName;
    private String title;
    private String prefix;
    private String email;
    private boolean active = true;

    private String gender;

    private List<Employee> supervisedEmployees;

    private Integer enteredEmployeeNumber;
    private String enteredDateTime;
    private Integer updatedEmployeeNumber;
    private String updatedDateTime;

    public Employee() {}

    public Employee(String firstName, String lastName, String title) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.title = title;
        this.prefix = prefix;
        this.email = email;
    }

    public Employee(String firstName, String lastName, String title, String email) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.title = title;
        this.email = email;
        this.prefix = prefix;
    }

    public Employee(Integer employeeNumber, String firstName, String lastName, String title){
        this.employeeNumber = employeeNumber;
        this.firstName = firstName;
        this.lastName = lastName;
        this.title = title;
    }

    public Integer getEmployeeNumber() {
        return employeeNumber;
    }

    public void setEmployeeNumber(Integer employeeNumber) {
        this.employeeNumber = employeeNumber;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public List<Employee> getSupervisedEmployees() {
        return supervisedEmployees;
    }

    public void setSupervisedEmployees(List<Employee> supervisedEmployees) {
        this.supervisedEmployees = supervisedEmployees;
    }

    public Integer getEnteredEmployeeNumber() {
        return enteredEmployeeNumber;
    }

    public void setEnteredEmployeeNumber(Integer enteredEmployeeNumber) {
        this.enteredEmployeeNumber = enteredEmployeeNumber;
    }

    public String getEnteredDateTime() {
        return enteredDateTime;
    }

    public void setEnteredDateTime(String enteredDateTime) {
        this.enteredDateTime = enteredDateTime;
    }

    public Integer getUpdatedEmployeeNumber() {
        return updatedEmployeeNumber;
    }

    public void setUpdatedEmployeeNumber(Integer updatedEmployeeNumber) {
        this.updatedEmployeeNumber = updatedEmployeeNumber;
    }

    public String getUpdatedDateTime() {
        return updatedDateTime;
    }

    public void setUpdatedDateTime(String updatedDateTime) {
        this.updatedDateTime = updatedDateTime;
    }
}
