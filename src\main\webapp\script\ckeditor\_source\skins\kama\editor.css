/*
Copyright (c) 2003-2012, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/

@import url("reset.css");
@import url("mainui.css");
@import url("panel.css");
@import url("toolbar.css");
@import url("menu.css");
@import url("richcombo.css");
@import url("elementspath.css");
@import url("icons.css");
@import url("presets.css");

/* Restore the container visibility */
html .cke_skin_kama
{
	visibility: inherit;
}

html.cke_skin_kama_container
{
	visibility: visible;
}
