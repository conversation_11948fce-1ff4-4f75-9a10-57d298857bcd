<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<div class="modal fade" id="smsDialog" tabindex="-1" role="dialog" aria-labelledby="smsDialogModalLabel" aria-hidden="true" style="top:100px;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title" id="smsDialogModalLabel">Send SMS</h4>
            </div>
            <div class="modal-body" style="padding-bottom:0px;">
                <form class="form-horizontal call-info-form" role="form">
                    <div class="form-group">
                        <label for="smsInput" class="col-sm-3 control-label">Phone Number
                            <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="Phone Number" data-placement="right">*</span>
                        </label>
                        <div class="col-sm-9">
                            <input id="smsInput" type="text" class="form-control input-sm required" placeholder="Phone Number" data-type="number" length=10 />
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="smsTemplateInput" class="col-sm-3 control-label">SMS Template</label>
                        <div class="col-sm-9">
                            <select id="smsTemplateInput" class="form-control input-sm">
                                <c:forEach var="template" items="${templateList}">
                                    <c:if test="${template.communicationType == 'SMS'}">
                                        <option value="${template.templateId}" content="${template.content}"> ${template.templateName}</option>
                                    </c:if>
                                </c:forEach>
                            </select>
                        </div>
                    </div>
                    <hr/>
                    <div class="form-group">
                        <label for="smsPreview" class="col-sm-3 control-label">Preview
                            <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="Message" data-placement="right">*</span>
                        </label>
                        <div class="col-sm-9">
                            <textarea id="smsPreview" class="form-control input-sm required" rows="4" readonly style="width: 100%;"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button id="sendSMSBtn" type="button" class="btn btn-primary">Send</button>
            </div>
        </div>
    </div>
</div>