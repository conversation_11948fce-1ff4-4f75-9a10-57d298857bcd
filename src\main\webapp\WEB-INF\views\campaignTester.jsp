<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd" >
<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<%@ taglib uri="../customTag.tld" prefix="custom" %>

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Smile Brands Call Center - Campaign Tester</title>
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/bootstrap.min.css" >
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/style/redmond/jquery-ui-1.8.20.custom.css" />

</head>
<body>

    <div class="container">
        <br/>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h1 class="panel-title gray">Campaign Screen Pop Tester</h1>
            </div>
            <div class="panel-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Campaign Name</th>
                            <th>Call Center</th>
                            <th>Screen Pop</th>
                        </tr>
                    </thead>
                    <tbody>
                        <c:forEach var="campaign" items="${campaignList}">
                            <c:set var="dnis" value="${campaign.campaignPhoneNumber}"/>
                            <c:set var="url" value="<a href='../campaigninfo?dnis=${dnis}&uniqueCallId=abc123&employeeNumber=100551' target='_blank'>${dnis}</a>"/>

                            <tr>
                                <td>${campaign.campaignName}</td>
                                <td>${campaign.callCenter}</td>
                                <td>${url}</td>
                            </tr>

                        </c:forEach>
                    </tbody>
                </table>

            </div>
        </div>
    </div>

</div>
</body>
</html>