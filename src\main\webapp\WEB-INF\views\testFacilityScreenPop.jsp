<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd" >
<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<%@ taglib uri="../customTag.tld" prefix="custom" %>

<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">

    <title>Call Center Screen Pop</title>

    <!-- Bootstrap core CSS -->
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/bootstrap.min.css" >

    <!-- Custom styles for this template -->
    <link href="${pageContext.request.contextPath}/bootstrap/css/call-template.css" rel="stylesheet">


  </head>

  <body>
        <input type="text" style="display:none;" id="targetFacilityId" value="${facility.facilityId}"/>
        <input type="text" style="display:none;" id="uuid" value="${uniqueCallId}"/>
        <input type="text" style="display:none;" id="employeeNumber" value="${employeeNumber}"/>
        <input type="text" style="display:none;" id="language" value="${language}"/>
        <input type="text" style="display:none;" id="numberPress" value="${numberPress}"/>

        <div class="modal fade" id="facilitySearchByZipCode" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                        <h4 class="modal-title" id="myModalLabel">Search Office by Zip Code</h4>
                    </div>
                    <div class="modal-body">
                        <div class="input-group">
                            <span class="input-group-addon">Enter zip code:</span>
                            <input id="fsbzInput" type="text" class="form-control" placeholder="5-digit zip code">
                        </div>
                        <div id="display_list" class="list-group" style="padding-top:20px;">

                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" id="">Set Office</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">

            <!-- Main  -->
            <div class="well">
              <h1>${brandName}</h1>
            </div>


            <div class="alert alert-info">
                <h2>${facility.name}
                    <button id="changeOfficeBtn" type="button" class="btn btn-default btn-sm sbi-tooltip" data-toggle="tooltip" data-placement="right" title="Change Office">
                      <span class="glyphicon glyphicon-edit"></span>
                    </button>
                </h2>

            </div>

            <div class="bs-callout bs-callout-info">
                <h4>Changing the collapsed mobile navbar breakpoint</h4>
                <p>The navbar collapses into its vertical mobile view when the viewport is narrower than <code>@grid-float-breakpoint</code>, and expands into its horizontal non-mobile view when the viewport is at least <code>@grid-float-breakpoint</code> in width. Adjust this variable in the Less source to control when the navbar collapses/expands. The default value is <code>768px</code> (the smallest "small" or "tablet" screen).</p>
            </div>


            <div class="row">
            <div class="col-sm-4">
              <div class="panel panel-default">
                <div class="panel-heading">
                  <h3 class="panel-title">Panel title</h3>
                </div>
                <div class="panel-body">
                  Panel content
                </div>
              </div>

            </div><!-- /.col-sm-4 -->
            <div class="col-sm-4">
              <div class="panel panel-success">
                <div class="panel-heading">
                  <h3 class="panel-title">Panel title</h3>
                </div>
                <div class="panel-body">
                  Panel content
                </div>
              </div>

            </div><!-- /.col-sm-4 -->
            <div class="col-sm-4">
              <div class="panel panel-warning">
                <div class="panel-heading">
                  <h3 class="panel-title">Panel title</h3>
                </div>
                <div class="panel-body">
                  Panel content
                </div>
              </div>

            </div><!-- /.col-sm-4 -->
          </div>



          <div class="starter-template">
            <h1>Bootstrap starter template</h1>
            <p class="lead">Use this document as a way to quickly start any new project.<br> All you get is this text and a mostly barebones HTML document.</p>
          </div>

    </div><!-- /.container -->


    <!-- Bootstrap core JavaScript
    ================================================== -->
    <!-- Placed at the end of the document so the pages load faster -->
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-1.7.2.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-ui-1.8.20.custom.min.js"></script>
    <script src="${pageContext.request.contextPath}/bootstrap/js/bootstrap.js"></script>

    <script type="text/javascript" src="${pageContext.request.contextPath}/script/screenPopActions.js"></script>
  </body>
</html>