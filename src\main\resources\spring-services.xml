<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
          http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">


    <!-- Base URL for the Screen Pop -->
    <bean id="SCREEN_POP_URL" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>http://leghorn.bnd.corp:9898/</value>
        </constructor-arg>
    </bean>

    <!--Redirect URL for WEB REQUEST -->
    <bean id="REDIRECT_URL" class="java.lang.String">
        <constructor-arg type="java.lang.String" ref="redirectUrl">
        </constructor-arg>
    </bean>

    <!-- CISCO URL for WEB REQUEST -->
    <bean id="CISCO_URL" class="java.lang.String">
        <constructor-arg type="java.lang.String" ref="ciscoUrl">
        </constructor-arg>
    </bean>
    <bean id="CALLBACK_CISCO_URL" class="java.lang.String">
        <constructor-arg type="java.lang.String" ref="callBackCiscoUrl">
        </constructor-arg>
    </bean>

    <!-- Web Controllers -->
    <context:component-scan base-package="com.smilebrands.callcentersupport.service"/>
    <context:component-scan base-package="com.smilebrands.callcentersupport.repo"/>

    <bean id="httpUtils" class="com.smilebrands.callcentersupport.util.HttpUtils"/>
    <bean id="ctiScheduler" class="com.smilebrands.callcentersupport.scheduler.CTIScheduler"/>

    <!--<bean id="listenerBean" class="com.smilebrands.callcentersupport.util.ApplicationListenerBean"/>-->

    <!-- Spring JDBC Template -->
    <bean id="jdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
        <property name="dataSource" ref="dataSource" />
    </bean>

    <bean id="jdbcTemplateWith3sTimeout" class="org.springframework.jdbc.core.JdbcTemplate">
        <property name="dataSource" ref="dataSource" />
        <property name="queryTimeout" value="3"/>
    </bean>

    <!-- Postgres JDBC Template -->
    <bean id="postgresTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
        <property name="dataSource" ref="postgresDataSource" />
    </bean>

    <!--<bean id="facilityInfoService" class="com.smilebrands.callcentersupport.service.old.FacilityInfoServiceImpl" />
    <bean id="callDetailService" class="com.smilebrands.callcentersupport.service.old.CallDetailServiceImpl" />
    <bean id="xmlBuilder" class="com.smilebrands.callcentersupport.groovy.XmlBuilder" />-->

    <!-- ====================  Apache Velocity Engine ==================== -->
    <bean id="velocityEngine" class="org.springframework.ui.velocity.VelocityEngineFactoryBean">
        <property name="resourceLoaderPath">
            <value>/WEB-INF/templates/</value>
        </property>
    </bean>

</beans>