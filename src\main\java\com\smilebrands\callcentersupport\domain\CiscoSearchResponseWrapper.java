package com.smilebrands.callcentersupport.domain;

import javax.xml.bind.annotation.XmlRootElement;

/**
 * User: <PERSON><PERSON>
 * Date: 1/22/15
 */
@XmlRootElement
public class CiscoSearchResponseWrapper {

    private Boolean success;
    private String message;
    private String nextGroupMessage;
    private int count;
    private Facility facility1;
    private Facility facility2;
    private Facility facility3;
    private Facility facility4;
    private Facility facility5;
    private Facility facility6;

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public Facility getFacility1() {
        return facility1;
    }

    public void setFacility1(Facility facility1) {
        this.facility1 = facility1;
    }

    public Facility getFacility2() {
        return facility2;
    }

    public void setFacility2(Facility facility2) {
        this.facility2 = facility2;
    }

    public Facility getFacility3() {
        return facility3;
    }

    public void setFacility3(Facility facility3) {
        this.facility3 = facility3;
    }

    public Facility getFacility4() {
        return facility4;
    }

    public void setFacility4(Facility facility4) {
        this.facility4 = facility4;
    }

    public Facility getFacility5() {
        return facility5;
    }

    public void setFacility5(Facility facility5) {
        this.facility5 = facility5;
    }

    public Facility getFacility6() {
        return facility6;
    }

    public void setFacility6(Facility facility6) {
        this.facility6 = facility6;
    }

    public String getNextGroupMessage() {
        return nextGroupMessage;
    }

    public void setNextGroupMessage(String nextGroupMessage) {
        this.nextGroupMessage = nextGroupMessage;
    }
}
