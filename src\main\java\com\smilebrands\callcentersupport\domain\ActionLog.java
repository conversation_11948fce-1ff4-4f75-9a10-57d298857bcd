package com.smilebrands.callcentersupport.domain;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by phongpham on 8/29/14.
 */
@Document
public class ActionLog {

    public final static String INBOUND_CALL_ACTION = "inboundCall";
    public final static String SCREEN_POP_ACTION = "screenPop";
    public final static String CONFIGURATION_ACTION = "configuration";
    public final static String REPORT_ACTION = "report";
    public final static String SQL_ACTION = "sql";
    public final static String WEB_REQUEST = "webRequest";
    public final static String CALL_BACK = "callBack";

    @Id
    private String id;
    private String uuid;
    private String actionType = "inboundCall";
    private List<String> logs = new ArrayList<String>();
    private String createDate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public List<String> getLogs() {
        return logs;
    }

    public void setLogs(List<String> logs) {
        this.logs = logs;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public void setId(){
        this.id = this.uuid + "_" + this.actionType;
    }
}
