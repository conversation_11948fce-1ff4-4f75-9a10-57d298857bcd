package com.smilebrands.callcenter.web.error;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.eclipse.jetty.http.HttpStatus;
import org.eclipse.jetty.server.Request;
import org.eclipse.jetty.server.handler.ErrorHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Custom error handler for Jetty to better handle incomplete requests
 * and provide more detailed logging.
 */
public class CustomErrorHandler extends ErrorHandler {
    private static final Logger logger = LoggerFactory.getLogger(CustomErrorHandler.class);

    @Override
    public void handle(String target, Request baseRequest, HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {
        String method = request.getMethod();
        String uri = request.getRequestURI();
        int status = response.getStatus();
        String message = "Unknown"; // HttpServletResponse doesn't have getMessage()

        // Log detailed information about the error
        logger.error("Error handling request: " + method + " " + uri + " - Status: " + status + " " + message);

        // Log additional request details that might help diagnose the issue
        logger.error("Request details: Remote IP: " + request.getRemoteAddr() +
                ", Content-Type: " + request.getContentType() +
                ", Content-Length: " + request.getContentLength() +
                ", User-Agent: " + request.getHeader("User-Agent"));

        // Handle incomplete requests specially
        // Since we can't check the message directly, we'll handle all 500 errors
        // and let our filter do more specific handling
        if (status == HttpStatus.INTERNAL_SERVER_ERROR_500) {
            logger.warn("Detected possible incomplete request. This may be due to a client disconnect or network issue.");

            // Check if this is an export request - if so, don't interfere with the response
            String requestURI = request.getRequestURI();
            if (requestURI != null && (requestURI.contains("/export/") || requestURI.contains("/stream-file/"))) {
                logger.info("Skipping error handling for export/stream request: " + requestURI);
                // Let the original handler deal with it
                super.handle(target, baseRequest, request, response);
                return;
            }

            // Check if response is already committed (output stream or writer already used)
            if (response.isCommitted()) {
                logger.warn("Response already committed, cannot write error page");
                baseRequest.setHandled(true);
                return;
            }

            response.setStatus(HttpStatus.BAD_REQUEST_400);
            response.setContentType("text/html;charset=utf-8");

            // Use getOutputStream() consistently to avoid conflicts with export functionality
            try {
                // Always use output stream to avoid getWriter/getOutputStream conflicts
                response.getOutputStream().write(buildErrorPageHtml().getBytes("UTF-8"));
                response.getOutputStream().flush();
                logger.debug("Successfully wrote error page using output stream");
            } catch (IllegalStateException e) {
                // This happens when there's a stream conflict
                logger.warn("Cannot write error page - stream conflict: " + e.getMessage());
                // Don't try to write anything else, just log and continue
            } catch (IOException e) {
                logger.error("IO error writing error page: " + e.getMessage());
            }

            baseRequest.setHandled(true);
            return;
        }

        // For other errors, use the default error handler
        super.handle(target, baseRequest, request, response);
    }

    /**
     * Builds the HTML content for the error page
     * @return HTML string for the incomplete request error page
     */
    private String buildErrorPageHtml() {
        return "<html><head><title>Incomplete Request</title></head>" +
               "<body><h2>Incomplete Request</h2>" +
               "<p>The server received an incomplete request. This may be due to:</p>" +
               "<ul>" +
               "<li>Client disconnection during request transmission</li>" +
               "<li>Network issues causing partial data transmission</li>" +
               "<li>Proxy or gateway timeout</li>" +
               "</ul>" +
               "<p>Please try again or contact support if the issue persists.</p>" +
               "</body></html>";
    }
}
