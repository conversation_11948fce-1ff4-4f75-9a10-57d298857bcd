package com.smilebrands.callcentersupport.domain.report;

/**
 * Created by phong<PERSON>m on 8/13/14.
 */
public class InboundCallSummaryValue {

    private Long callCount = 0L;
    private Long supportedCount = 0L;
    private Long newPatCount = 0L;
    private Long extPatCount = 0L;
    private Long noPSRCount = 0L;
    private Long noCSRCount = 0L;
    private Long trnOffCount = 0L;
    private Long offOffCount = 0L;
    private Long psrPopCount = 0L;
    private Long csrPopCount = 0L;
    private Long aniRecCount = 0L;
    private Long csrPsrCount = 0L;
    private Long queryTimeoutCount = 0L;
    private Long searchNotFoundCount = 0L;
    private Long transferToRDI = 0L;
    private Long receivedByRDI = 0L;
    private Long requestCallback = 0L;
    private Long handledCallback = 0L;

    public InboundCallSummaryValue(){

    }

    public InboundCallSummaryValue(Long callCount,
                                   Long supportedCount,
                                   Long newPatCount, Long extPatCount,
                                   Long noPSRCount, Long noCSRCount,
                                   Long trnOffCount, Long offOffCount,
                                   Long psrPopCount, Long csrPopCount,
                                   Long csrPsrCount, Long aniRecCount,
                                   Long queryTimeoutCount, Long searchNotFoundCount,
                                   Long transferToRDI, Long receivedByRDI,
                                   Long requestCallback, Long handledCallback){
        this.callCount = callCount;
        this.supportedCount = supportedCount;
        this.newPatCount = newPatCount;
        this.extPatCount = extPatCount;
        this.noPSRCount = noPSRCount;
        this.noCSRCount = noCSRCount;
        this.trnOffCount = trnOffCount;
        this.offOffCount = offOffCount;
        this.psrPopCount = psrPopCount;
        this.csrPopCount = csrPopCount;
        this.csrPsrCount = csrPsrCount;
        this.aniRecCount = aniRecCount;
        this.queryTimeoutCount = queryTimeoutCount;
        this.searchNotFoundCount = searchNotFoundCount;
        this.transferToRDI = transferToRDI;
        this.receivedByRDI = receivedByRDI;
        this.requestCallback = requestCallback;
        this.handledCallback = handledCallback;
    }

    public Long getCallCount() {
        return callCount;
    }

    public void setCallCount(Long callCount) {
        this.callCount = callCount;
    }

    public Long getSupportedCount() {
        return supportedCount;
    }

    public void setSupportedCount(Long supportedCount) {
        this.supportedCount = supportedCount;
    }

    public Long getNewPatCount() {
        return newPatCount;
    }

    public void setNewPatCount(Long newPatCount) {
        this.newPatCount = newPatCount;
    }

    public Long getExtPatCount() {
        return extPatCount;
    }

    public void setExtPatCount(Long extPatCount) {
        this.extPatCount = extPatCount;
    }

    public Long getNoPSRCount() {
        return noPSRCount;
    }

    public void setNoPSRCount(Long noPSRCount) {
        this.noPSRCount = noPSRCount;
    }

    public Long getNoCSRCount() {
        return noCSRCount;
    }

    public void setNoCSRCount(Long noCSRCount) {
        this.noCSRCount = noCSRCount;
    }

    public Long getTrnOffCount() {
        return trnOffCount;
    }

    public void setTrnOffCount(Long trnOffCount) {
        this.trnOffCount = trnOffCount;
    }

    public Long getOffOffCount() {
        return offOffCount;
    }

    public void setOffOffCount(Long offOffCount) {
        this.offOffCount = offOffCount;
    }

    public Long getPsrPopCount() {
        return psrPopCount;
    }

    public void setPsrPopCount(Long psrPopCount) {
        this.psrPopCount = psrPopCount;
    }

    public Long getCsrPopCount() {
        return csrPopCount;
    }

    public void setCsrPopCount(Long csrPopCount) {
        this.csrPopCount = csrPopCount;
    }

    public Long getAniRecCount() {
        return aniRecCount;
    }

    public void setAniRecCount(Long aniRecCount) {
        this.aniRecCount = aniRecCount;
    }

    public Long getCsrPsrCount() {
        return csrPsrCount;
    }

    public void setCsrPsrCount(Long csrPsrCount) {
        this.csrPsrCount = csrPsrCount;
    }

    public Long getQueryTimeoutCount() {
        return queryTimeoutCount;
    }

    public void setQueryTimeoutCount(Long queryTimeoutCount) {
        this.queryTimeoutCount = queryTimeoutCount;
    }

    public Long getSearchNotFoundCount() {
        return searchNotFoundCount;
    }

    public void setSearchNotFoundCount(Long searchNotFoundCount) {
        this.searchNotFoundCount = searchNotFoundCount;
    }

    public Long getTransferToRDI() {
        return transferToRDI;
    }

    public void setTransferToRDI(Long transferToRDI) {
        this.transferToRDI = transferToRDI;
    }

    public Long getReceivedByRDI() {
        return receivedByRDI;
    }

    public void setReceivedByRDI(Long receivedByRDI) {
        this.receivedByRDI = receivedByRDI;
    }

    public Long getRequestCallback() {
        return requestCallback;
    }

    public void setRequestCallback(Long requestCallback) {
        this.requestCallback = requestCallback;
    }

    public Long getHandledCallback() {
        return handledCallback;
    }

    public void setHandledCallback(Long handledCallback) {
        this.handledCallback = handledCallback;
    }

    public void sumValue(InboundCallSummaryValue summaryValue){
        if(summaryValue != null){
            this.callCount += summaryValue.getCallCount();
            this.supportedCount += summaryValue.getSupportedCount();
            this.newPatCount += summaryValue.getNewPatCount();
            this.extPatCount += summaryValue.getExtPatCount();
            this.noPSRCount += summaryValue.getNoPSRCount();
            this.noCSRCount += summaryValue.getNoCSRCount();
            this.trnOffCount += summaryValue.getTrnOffCount();
            this.offOffCount += summaryValue.getOffOffCount();
            this.psrPopCount += summaryValue.getPsrPopCount();
            this.csrPopCount += summaryValue.getCsrPopCount();
            this.aniRecCount += summaryValue.getAniRecCount();
            this.csrPsrCount += summaryValue.getCsrPsrCount();
            this.queryTimeoutCount += summaryValue.getQueryTimeoutCount();
            this.searchNotFoundCount += summaryValue.getSearchNotFoundCount();
            this.transferToRDI += summaryValue.getTransferToRDI();
            this.receivedByRDI += summaryValue.getReceivedByRDI();
            this.requestCallback += summaryValue.getRequestCallback();
            this.handledCallback += summaryValue.getHandledCallback();
        }
    }

    public Long getEventCount(String eventName){
        if(eventName != null){
            if(eventName.equalsIgnoreCase("Call Center Supported")){
                return this.supportedCount;
            }else if(eventName.equalsIgnoreCase("New Patient")){
                return this.newPatCount;
            }else if(eventName.equalsIgnoreCase("Existing Patient")){
                return this.extPatCount;
            }else if(eventName.equalsIgnoreCase("No PSR")){
                return this.noPSRCount;
            }else if(eventName.equalsIgnoreCase("No CSR")){
                return this.noCSRCount;
            }else if(eventName.equalsIgnoreCase("Transfer to Office")){
                return this.trnOffCount;
            }else if(eventName.equalsIgnoreCase("Office to Office")){
                return this.offOffCount;
            }else if(eventName.equalsIgnoreCase("PSR Pop")){
                return this.psrPopCount;
            }else if(eventName.equalsIgnoreCase("CSR Pop")){
                return this.csrPopCount;
            }else if(eventName.equalsIgnoreCase("ANI Recognition")){
                return this.aniRecCount;
            }else if(eventName.equalsIgnoreCase("CSR - PSR")){
                return this.csrPsrCount;
            }else if(eventName.equalsIgnoreCase("Query Timeout")){
                return this.queryTimeoutCount;
            }else if(eventName.equalsIgnoreCase("Search Not Found")){
                return this.searchNotFoundCount;
            }else if(eventName.equalsIgnoreCase("Request to call back")){
                return this.requestCallback;
            }else if(eventName.equalsIgnoreCase("Handle call back request")){
                return this.handledCallback;
            }else{
                return 0L;
            }
        }else{
            return 0L;
        }

    }
}
