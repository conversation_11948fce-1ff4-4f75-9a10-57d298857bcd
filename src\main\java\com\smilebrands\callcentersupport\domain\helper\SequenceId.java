package com.smilebrands.callcentersupport.domain.helper;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Created by phongpham on 11/6/14.
 */
@Document(collection = "customSequence")
public class SequenceId {

    @Id
    private String sequenceName;
    private Long sequenceValue;

    public String getSequenceName() {
        return sequenceName;
    }

    public void setSequenceName(String sequenceName) {
        this.sequenceName = sequenceName;
    }

    public Long getSequenceValue() {
        return sequenceValue;
    }

    public void setSequenceValue(Long sequenceValue) {
        this.sequenceValue = sequenceValue;
    }
}
