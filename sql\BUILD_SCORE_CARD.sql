select agent_login_id, coalesce(agent_type, 'n/a') as agent_type, call_date
, sum(cnt) as cnt, sum(handle_time) as handle_time, sum(hold_time) as hold_time
, booked, showed, not_lunch_time, not_ready_time, grade
from (
select
v.agentloginid as agent_login_id, v.sessionid
, (case when cl.agent_type is not null then cl.agent_type
    when cl.agent_type is null and csq.csqname in ('IRV_CSR_ENG', 'IRV_CSR_SPA', 'PLA_CSR_ENG', 'PLA_CSR_SPA') then 'csr'
    when cl.agent_type is null and csq.csqname in ('Web_Irvine_CSQ','Web_Plano_CSQ') then 'wr'
    else 'psr' end) as agent_type, (v.startdatetime at time zone 'UTC')::date as call_date
, count(*) as cnt
, sum(talktime + holdtime + worktime) as handle_time
, sum(holdtime) as hold_time
, coalesce(cl.booked, 0) as booked, coalesce(cl.showed, 0) as showed
,coalesce( asd.not_lunch_time, 0) as not_lunch_time, coalesce(asd.not_ready_time, 0) as not_ready_time
, qa.grade

from cisco.v_agentconnectiondetail v
left join cisco.cis_contactqueuedetail cqd on cqd.sessionid = v.sessionid and cqd.sessionseqnum = v.sessionseqnum and cqd.nodeid = v.nodeid
left join cisco.cis_contactservicequeue csq on csq.recordid = cqd.targetid and csq.queuetype = cqd.targettype
left join (
    select employee_number, ag.agent_type, session_id
        ,(call_date_time at time zone 'UTC')::date as call_date
        , 0 as booked
        , 0 as showed
    from cisco.cti_agent_call_log ag
        left join cisco.cti_appt_call_log apt on apt.appt_log_id = ag.last_appt_log_id
    group by employee_number, ag.agent_type, call_date, session_id
) as cl on cl.session_id = v.sessionid and cl.employee_number::varchar = v.agentloginid
left join (
    select agentloginid, (eventdatetime at time zone 'UTC')::date as eventdate
        , sum(case when reasoncode != 14 then stateduration end) as not_lunch_time
        , sum(case when eventtype = 2 and reasoncode not in (14, 32762) then stateduration end) as not_ready_time
    from cisco.cis_agentstatedetail
    where extract('hour' from eventdatetime at time zone 'UTC') between 6 and 21
    group by agentloginid, eventdate
) as asd on asd.agentloginid = v.agentloginid and asd.eventdate = (v.startdatetime at time zone 'UTC')::date
left join(
    select employee_number, call_date::date as call_date, round(avg(grade * 5), 1) as grade from cisco.agent_qa_score
        group by employee_number, call_date::date
) qa on qa.employee_number::varchar = v.agentloginid and qa.call_date = (v.startdatetime at time zone 'UTC')::date

where 1=1

and (startdatetime at time zone 'UTC')::date >= to_char(now()-interval '3 months', '1-mon-yyyy')::date
--and (startdatetime at time zone 'UTC')::date between '1-Jul-2015' and '31-Jul-2015' and v.agentloginid = '114268'
and (
(cl.agent_type is null
	and csq.csqname in ('IRV_Campaign', 'IRV_ENG_CSQ', 'IRV_Marketing_CSQ', 'IRV_Marketing_SPA_CSQ', 'IRV_Missed_Appts', 'IRV_Non_CC_Sitedown', 'IRV_Site_DWN_Non_CC', 'IRV_Site_Trans_ENG', 'IRV_Site_Trans_Sp', 'IRV_SPA_CSQ', 'PLA_Campaign', 'PLA_ENG_CSQ', 'PLA_Marketing_CSQ', 'PLA_Marketing_SPA_CSQ', 'PLA_Missed_Appts', 'PLA_Non_CC_Sitedown', 'PLA_Site_DWN_Non_CC', 'PLA_Site_Trans_ENG', 'PLA_Site_Trans_Sp', 'PLA_SPA_CSQ', 'Shared_ENG_CSQ', 'Shared_SPA_CSQ', 'IRV_CSR_ENG', 'IRV_CSR_SPA', 'PLA_CSR_ENG', 'PLA_CSR_SPA','Web_Irvine_CSQ','Web_Plano_CSQ')
)
or (cl.agent_type = 'psr' and csq.csqname in ('IRV_Campaign', 'IRV_ENG_CSQ', 'IRV_Marketing_CSQ', 'IRV_Marketing_SPA_CSQ', 'IRV_Missed_Appts', 'IRV_Non_CC_Sitedown', 'IRV_Site_DWN_Non_CC', 'IRV_Site_Trans_ENG', 'IRV_Site_Trans_Sp', 'IRV_SPA_CSQ', 'PLA_Campaign', 'PLA_ENG_CSQ', 'PLA_Marketing_CSQ', 'PLA_Marketing_SPA_CSQ', 'PLA_Missed_Appts', 'PLA_Non_CC_Sitedown', 'PLA_Site_DWN_Non_CC', 'PLA_Site_Trans_ENG', 'PLA_Site_Trans_Sp', 'PLA_SPA_CSQ', 'Shared_ENG_CSQ', 'Shared_SPA_CSQ'))
or (cl.agent_type = 'csr' and csq.csqname in ('IRV_CSR_ENG', 'IRV_CSR_SPA', 'PLA_CSR_ENG', 'PLA_CSR_SPA'))
or (cl.agent_type = 'wr' and csq.csqname in ('Web_Irvine_CSQ','Web_Plano_CSQ'))
)
and talktime > 0

group by v.agentloginid
, (v.startdatetime at time zone 'UTC')::date
, cl.booked, cl.showed, cl.agent_type, v.sessionid
, asd.not_lunch_time, asd.not_ready_time
, qa.grade
, csq.csqname
) tmp
group by agent_login_id, agent_type, call_date
, booked, showed, not_lunch_time, not_ready_time, grade
order by call_date