package com.smilebrands.callcentersupport.service;

import java.util.*;

import com.smilebrands.callcentersupport.repo.FacilityEmailRepository;
import com.smilebrands.callcentersupport.repo.JdbcRepository;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.smilebrands.callcentersupport.domain.*;
import com.smilebrands.callcentersupport.repo.MongoRepository;
import org.springframework.ui.ModelMap;

/**
 * User: <PERSON><PERSON>
 * Date: 11/04/14
 */
@Service
public class WebRequestServiceImpl extends BaseService implements WebRequestService {

    @Autowired
    protected MongoRepository mongoRepository;

    @Autowired
    protected CommunicationService communicationService;

    @Autowired(required = false)
    @Qualifier("devMode")
    private Boolean devMode = true;

    @Autowired
    protected FacilityEmailRepository facilityEmailRepository;

    @Autowired
    protected JdbcRepository jdbcRepository;

    private static String EMAIL_TEMPLATE = "appointment_template_3.0.vm";

    private static String TO_EMAIL_TEST = "<EMAIL>";

    @Override
    public WebRequest saveWebRequest(WebRequest webRequest, boolean submitRequest) {
        if(submitRequest){
            WebRequest existing = mongoRepository.findPendingWebRequestByPhoneNUmber(webRequest.getPhone());
            if(existing == null){
                webRequest.setUuid(UUID.randomUUID().toString());
                webRequest.setCreateDateTime(new Date());
            }else{
                webRequest.setUuid(existing.getUuid());
                webRequest.setNumberOfRequest(existing.getNumberOfRequest() + 1);
                webRequest.setUpdateDateTime(new Date());
                webRequest.setCreateDateTime(existing.getCreateDateTime());
            }
        }
        mongoRepository.saveWebRequest(webRequest);

        return webRequest;
    }

    @Override
    public WebRequest findWebRequest(String uuid) {
        return mongoRepository.findWebRequest(uuid);
    }

    @Override
    public void updateWebRequestScreenResult(String uuid, Integer agentId, boolean result) {
        WebRequest wr = mongoRepository.findWebRequest(uuid);

        for (ScreenPopUrl pop : wr.getScreenPopUrls()) {
            if (pop.getAgentEmployeeNumber().equals(agentId)) {
                pop.setLoaded(result);
                break;
            }
        }

        mongoRepository.saveWebRequest(wr);
    }

    /**
     * If the facility is supported by a call center and it's a new patient, then save it without a processed date.
     * Then it will be picked by the job that will show it in a screen pop in the call center.
     * Otherwise, send the web request to managers in an email.
     */
    @Override
    public WebRequest saveThenSendEmailOrScreenPopWebRequest(WebRequest wr) {
        WebRequest result = null;

        if (mongoRepository.findWebRequestByPhoneNUmberAndEmail(wr.getPhone(), wr.getEmailAddress()) != null) {
            logger.warn("Same request has been submitted recently. Will not save it and will not send email.");

        } else {
            PhoneNumber phoneNumber = mongoRepository.findPhoneNumberByFacility(wr.getOffice(), "ML");

            String logMessage = "The Web Request [{}] ";

            if ((phoneNumber != null && phoneNumber.isCallCenterSupported() && wr.getIsNewPatient()) ||
                (phoneNumber != null && !phoneNumber.isCallCenterSupported() && wr.getIsNewPatient() && wr.getUtmCampaign() != null) )  {

                logger.debug("WR:: Request UUID [" + result.getUuid() + "] is Call Center Supported ["
                        + phoneNumber.isCallCenterSupported() + "], is a New Patient [" + wr.getIsNewPatient()
                        + "] with UTM Campaign Values [" + (wr.getUtmCampaign() == null ? "<null>" : wr.getUtmCampaign()) + "]");
                wr.setResolution("Processed by a call center");
                result = this.saveWebRequest(wr, true);
                logMessage += "has been saved to MongoDB and will be processed by a call center.";

            } else {

                wr.setProcessedDateTime(VelocityUtils.getDateAsString(new Date(), "yyyy-MM-dd HH:mm:ss"));
                wr.setResolution("Sent to office managers via email.");
                result = this.saveWebRequest(wr, true);

                this.sendAppointmentRequestThroughEmail(wr, phoneNumber != null && phoneNumber.isCallCenterSupported(), null);
                logMessage += "has been saved to MongoDB and sent to managers via email.";
            }

//             if (phoneNumber == null
//                || (phoneNumber != null && !(phoneNumber.isCallCenterSupported() && wr.getIsNewPatient()))
//                || (phoneNumber != null && !(wr.getIsNewPatient() && wr.getUtmCampaign() != null)) ) {
//
//                wr.setProcessedDateTime(VelocityUtils.getDateAsString(new Date(), "yyyy-MM-dd HH:mm:ss"));
//                wr.setResolution("Sent to office managers via email.");
//                result = this.saveWebRequest(wr, true);
//
//                this.sendAppointmentRequestThroughEmail(wr, phoneNumber != null && phoneNumber.isCallCenterSupported(), null);
//                logMessage += "has been saved to MongoDB and sent to managers via email.";
//
//            } else {
//                wr.setResolution("Processed by a call center");
//                result = this.saveWebRequest(wr, true);
//                logMessage += "has been saved to MongoDB and will be processed by a call center.";
//
//                 /*// -- ADDED BLOCK TO SEND NEW REQUEST TO CALL CENTER TO PROCESS BY EMAIL VS SCREEN POP -- //
//                 wr.setProcessedDateTime(VelocityUtils.getDateAsString(new Date(), "yyyy-MM-dd HH:mm:ss"));
//                 wr.setResolution("Sent to Call Center via email.");
//                 result = this.saveWebRequest(wr, true);
//
//                 this.sendAppointmentRequestThroughEmail(wr, phoneNumber != null && phoneNumber.isCallCenterSupported(), "<EMAIL>");
//                 logMessage += "has been saved to MongoDB and sent to Call Center via email.";
//
//                 logger.warn("Inbound Web Request typically handled by Screen Pop has been emailed to a Call Center distribution email -> <EMAIL>");*/
//            }

            logger.debug(logMessage, result.getUuid());
        }

        return result;
    }

    @Override
    public void sendEmailToNonContactWebRequest(String uuid, Integer employeeNumber) {
        WebRequest wr = mongoRepository.updateRequestForEmail(uuid, true, null, null);
        Facility facility = wr != null ? mongoRepository.findFacilityDetail(wr.getOffice()) : null;
        AgentCall agentCall = mongoRepository.getAgentCall(uuid);
        if (facility != null && agentCall != null && wr.getEmailAddress() != null && wr.getEmailAddress().trim().length() > 0) {
            ModelMap model = new ModelMap();
            Calendar cal = Calendar.getInstance();
            PhoneNumber phoneNumber = mongoRepository.findPhoneNumberByFacility(facility.getFacilityId(), "ML");
            CallCenter callCenter = mongoRepository.getCallCenterByName(phoneNumber != null ? phoneNumber.getCallCenterName() : "Irvine");
            Long callbackNumber = callCenter != null ? callCenter.getCallbackPhoneNumber() : null;
            if(callbackNumber == null){
                return;
            }
            String callbackNumberStr = VelocityUtils.phoneFormatter(callbackNumber.toString());
            for (LoadedEmployee le : agentCall.getLoadedEmployees()) {

                if (le.getScreenType() != null && le.getScreenType().equalsIgnoreCase("wr")
                        && (employeeNumber == null || employeeNumber.equals(le.getEmployeeNumber()))) {

                    CallCenterEmployee cce = getCallCenterEmployee(le.getEmployeeNumber());
                    String from = cce != null && cce.getEmailAddress() != null && cce.getEmailAddress().trim().length() > 0
                            ? cce.getEmailAddress().trim() : "developer";

                    if (from.indexOf("@") != -1) {
                        from = from.substring(0, from.indexOf("@"));
                    }

                    from += "@" + getEmailDomainByBrand(facility.getBrandName());
                    String senderName = cce != null && cce.getEmployeeFirstName() != null && cce.getEmployeeFirstName().trim().length() > 0
                                        ? VelocityUtils.getAbbreviationForAgentName(cce.getEmployeeFirstName().trim(), cce.getEmployeeLastName())
                                        : jdbcRepository.getEmployeeName(le.getEmployeeNumber(), 1);

                    if (cal.get(Calendar.HOUR_OF_DAY) <= 12) {
                        model.put("greetingTime", "Good morning");
                    } else {
                        model.put("greetingTime", "Good afternoon");
                    }
                    model.put("greetingName", wr.getFirstName() + " " + wr.getLastName());
                    model.put("brandName", facility.getBrandName());
                    model.put("callbackNumber", callbackNumberStr);
                    model.put("callCenterHours", callCenter.getDisplayHours());
                    model.put("timezone", callCenter.getTimezoneShort());
                    model.put("senderName", senderName);

                    String fromName = facility.getBrandName();
                    if (fromName == null || fromName.trim().length() == 0) {
                        fromName = "Monarch/Castle/Bright Now! Dental";
                    }
                    fromName = fromName.trim();
                    String subject = "Your appointment request for " + fromName;

                    String confirmation = communicationService.sendNotification(from, fromName, wr.getEmailAddress(), model, "webrequest-email.vm", subject, wr.getUuid());
                    logger.debug("email confirmation: {}", confirmation);
                    mongoRepository.updateRequestForEmail(uuid, false, confirmation, from);
                    mongoRepository.addLogAction(uuid, ActionLog.WEB_REQUEST, "Send email to [" + wr.getEmailAddress() + "] with confirmation[" + confirmation + "]");
                    break;
                }
            }
        }
    }

    @Override
    public boolean isValidRequest(WebRequest wr) {
        boolean result = wr != null;
        if(wr != null){
            result = result && wr.getPhone() != null && wr.getPhone() > 999999999;  //phone number has to be 10-digit number
        }
        return result;
    }

    private CallCenterEmployee getCallCenterEmployee(Integer employeeNumber){
        CallCenterEmployee cce = jdbcRepository.getCallCenterEmployeeByNumber(employeeNumber, false);
        if(cce == null){
            cce = jdbcRepository.getCallCenterEmployeeFromEmployee(employeeNumber, false);
        }
        return cce;
    }

    private String getEmailDomainByBrand(String brand){
        String result = "brightnow.com";
        if(brand != null){
            if(brand.toLowerCase().contains("monarch")){
                result = "monarchdental.com";
            }else if(brand.toLowerCase().contains("castle")){
                result = "castledental.com";
            }
        }
        return result;
    }

    private void sendAppointmentRequestThroughEmail(WebRequest wr, boolean callCenterSupported, String emailAddress) {
        Integer facilityId = wr.getOffice();

        // Email message model variables
        Map model = new HashMap();

        Date appDate = VelocityUtils.parseDate(wr.getAppointmentDate(), "yyyy-MM-dd");

        // Web request Info
        model.put("createDatetime", VelocityUtils.getDateAsString(new Date(), "EEEE MM-dd-yyyy HH:mm:ss a"));

        // Patient Info
        model.put("firstName", wr.getFirstName());
        model.put("lastName", wr.getLastName());
        model.put("phone", wr.getPhone() != null ? VelocityUtils.phoneFormatter(wr.getPhone().toString()) : "");
        model.put("phoneType", wr.getPhoneType());
        model.put("emailAddress", wr.getEmailAddress());
        model.put("zipCode", wr.getZipCode());

        // Appointment Info
        model.put("reason", wr.getReasonForAppointment());
        model.put("day", VelocityUtils.getDateAsString(appDate, "EEEE MM-dd-yyyy"));
        model.put("time", wr.getPreferredTime());
        model.put("newPatient", (wr.getIsNewPatient() ? "Yes" : "No"));
        model.put("comment", wr.getComment());

        // Facility Info
        model.put("facilityId", facilityId);
        model.put("facilityName", wr.getOfficeName());
        model.put("facilityAddress", wr.getOfficeAddress());

        // Find facility call center web appointment emails.
        List<String> callCenterEmails = facilityEmailRepository.getCallCenterEmail(facilityId.longValue());

        // Find facility manager emails and web appointment emails.
        List <String> managerEmails = facilityEmailRepository.getApptEmail(facilityId.longValue());

        // This is who we'll email ...
        List <String> emails = null;

        List <EmailSuccess> es = new ArrayList();
        boolean success = true;

        /**
         *  If this is a new patient appointment request and the office has ccEmails configured, send the ccEmails collection
         *  which is the supporting call center.
         *
         *  If this is not a new patient appointment request, send to the managerEmails collection.
         *
         */
        if (callCenterSupported && wr.getIsNewPatient() && callCenterEmails.size() > 0) {
            logger.info("Appt request for a new patient in a Call Center supported office.");
            emails = callCenterEmails;
        } else {
            emails = managerEmails;
        }

        if (emails == null || emails.size() == 0) {
            emails = new ArrayList<String>();
            emails.add(facilityId + "-<EMAIL>");
        }

        // -- IF FORCE EMAIL ADDRESS IS SET, USE IT REGARDLESS OF OTHER CONFIGURED VALUES!!
        if (emailAddress != null) {
            emails = new ArrayList<String>();
            emails.add(emailAddress);
        }

        String recipient = "";

        // Send email to managers (Plain Text)
        for (String mgrEmail : emails) {
            boolean rez = false;
            if (devMode) {
                logger.debug("Test Mode");
                communicationService.sendNotificationViaEmail(wr.getNoReplyEmailAddress(), TO_EMAIL_TEST, model, EMAIL_TEMPLATE, "Appointment Request - " + wr.getWebSiteHostName());

                recipient = TO_EMAIL_TEST;
                rez = true;
            } else {
                rez = communicationService.sendNotificationViaEmail(wr.getNoReplyEmailAddress(), mgrEmail, model, EMAIL_TEMPLATE, "Appointment Request - " + wr.getWebSiteHostName());

                recipient += mgrEmail + ", ";
            }
            logger.info("Appointment Email sent to:  " + mgrEmail + " was successful: " + rez);

            if (success && !rez) {
                logger.warn("An invalid Email [" + mgrEmail + "] has been found!  Sending the <NAME_EMAIL>");
                success = false;
            }

            EmailSuccess _es = new EmailSuccess();
            _es.setEmail(mgrEmail);
            _es.setSuccess(rez);
            es.add(_es);
        }
        wr.setRecipient(recipient);

        mongoRepository.saveWebRequest(wr);

        if (!success) {
            logger.info("Sent a failure email containing " + es.size() + " entries.");
        }
    }
}
