package com.smilebrands.callcentersupport.domain;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

/**
 * User: <PERSON><PERSON>
 * Date: 11/17/16
 */
@Document
public class PhoneNumberRoutingRules implements Serializable {

    @Id
    private Long phoneNumber;
    private String description;
    private Boolean spam;
    private Boolean redirectCsr;
    private String redirectQueueName;
    private Boolean isActive = true;

    private Date createDateTime;
    private Integer createdEmployeeNumber;

    private Date updatedDateTime;
    private Integer updatedEmployeeNumber;

    public Long getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(Long phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getSpam() {
        return spam;
    }

    public void setSpam(Boolean spam) {
        this.spam = spam;
    }

    public Boolean getRedirectCsr() {
        return redirectCsr;
    }

    public void setRedirectCsr(Boolean redirectCsr) {
        this.redirectCsr = redirectCsr;
    }

    public String getRedirectQueueName() {
        return redirectQueueName;
    }

    public void setRedirectQueueName(String redirectQueueName) {
        this.redirectQueueName = redirectQueueName;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Date getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(Date createDateTime) {
        this.createDateTime = createDateTime;
    }

    public Integer getCreatedEmployeeNumber() {
        return createdEmployeeNumber;
    }

    public void setCreatedEmployeeNumber(Integer createdEmployeeNumber) {
        this.createdEmployeeNumber = createdEmployeeNumber;
    }

    public Date getUpdatedDateTime() {
        return updatedDateTime;
    }

    public void setUpdatedDateTime(Date updatedDateTime) {
        this.updatedDateTime = updatedDateTime;
    }

    public Integer getUpdatedEmployeeNumber() {
        return updatedEmployeeNumber;
    }

    public void setUpdatedEmployeeNumber(Integer updatedEmployeeNumber) {
        this.updatedEmployeeNumber = updatedEmployeeNumber;
    }
}
