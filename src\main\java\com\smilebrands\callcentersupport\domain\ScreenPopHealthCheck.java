package com.smilebrands.callcentersupport.domain;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Created by phongpham on 9/11/15.
 */
@Document
public class ScreenPopHealthCheck {

    public final static String FORMAT_DATE_TIME = "yyyy-MM-dd'T'HH:mm:ss";

    @Id
    private String id;
    private String uuid;
    private Integer employeeNumber;
    private String screenType;
    private String popDateTime;
    private String unloadDateTime;
    private String checkDateTime;
    private Integer numberOfCheck = 0;

    private String pendingProcessedDateTime;
    private String pendingProcessedBy;
    private String processedDateTime;
    private String processedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getEmployeeNumber() {
        return employeeNumber;
    }

    public void setEmployeeNumber(Integer employeeNumber) {
        this.employeeNumber = employeeNumber;
    }

    public String getScreenType() {
        return screenType;
    }

    public void setScreenType(String screenType) {
        this.screenType = screenType;
    }

    public String getPopDateTime() {
        return popDateTime;
    }

    public void setPopDateTime(String popDateTime) {
        this.popDateTime = popDateTime;
    }

    public String getUnloadDateTime() {
        return unloadDateTime;
    }

    public void setUnloadDateTime(String unloadDateTime) {
        this.unloadDateTime = unloadDateTime;
    }

    public String getCheckDateTime() {
        return checkDateTime;
    }

    public void setCheckDateTime(String checkDateTime) {
        this.checkDateTime = checkDateTime;
    }

    public Integer getNumberOfCheck() {
        return numberOfCheck;
    }

    public void setNumberOfCheck(Integer numberOfCheck) {
        this.numberOfCheck = numberOfCheck;
    }

    public String getPendingProcessedDateTime() {
        return pendingProcessedDateTime;
    }

    public void setPendingProcessedDateTime(String pendingProcessedDateTime) {
        this.pendingProcessedDateTime = pendingProcessedDateTime;
    }

    public String getPendingProcessedBy() {
        return pendingProcessedBy;
    }

    public void setPendingProcessedBy(String pendingProcessedBy) {
        this.pendingProcessedBy = pendingProcessedBy;
    }

    public String getProcessedDateTime() {
        return processedDateTime;
    }

    public void setProcessedDateTime(String processedDateTime) {
        this.processedDateTime = processedDateTime;
    }

    public String getProcessedBy() {
        return processedBy;
    }

    public void setProcessedBy(String processedBy) {
        this.processedBy = processedBy;
    }
}
