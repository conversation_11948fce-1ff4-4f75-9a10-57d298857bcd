<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd" >
<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<%@ taglib uri="../customTag.tld" prefix="custom" %>

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Smile Brands Call Center - Reason - Resolution List</title>
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/bootstrap.min.css" >
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/style/redmond/jquery-ui-1.8.20.custom.css" />

    <!-- Custom styles for this template -->
    <link href="${pageContext.request.contextPath}/bootstrap/css/call-template.css" rel="stylesheet">
</head>
<body>
    <div class="modal fade" id="reasonResolutionDetail" tabindex="-1" role="dialog" aria-labelledby="reasonResolutionModalLabel" aria-hidden="true" style="top:100px;">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 class="modal-title" id="reasonResolutionModalLabel">Update Reason/Resolution</h4>
                </div>
                <div class="modal-body" style="padding-bottom:0px;">
                    <form class="form-horizontal call-info-form" role="form">
                        <div class="form-group">
                            <label for="codeInput" class="col-sm-4 control-label">ID
                                <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="ID" data-placement="right">*</span>
                            </label>
                            <div class="col-sm-7">
                                <input id="codeInput" type="text" class="form-control input-sm required" placeholder="Enter ID" dataField="code" style="display:none;"/>
                                <div style="padding-top:5px;">
                                    <span id="codeLbl" style="font-size:14px;"></span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="descriptionInput" class="col-sm-4 control-label">Description
                                <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="Description" data-placement="right">*</span>
                            </label>
                            <div class="col-sm-7">
                                <input id="descriptionInput" type="text" class="form-control input-sm required" placeholder="Enter Descriptions" dataField="description"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="displayOrderInput" class="col-sm-4 control-label">Display Order
                                <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="Display Order" data-placement="right">*</span>
                            </label>
                            <div class="col-sm-4">
                                <input id="displayOrderInput" type="text" class="form-control input-sm required" data-type="number" placeholder="Enter Display Order" dataField="displayOrder"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="screenTypeInput" class="col-sm-4 control-label">Screen Type
                            </label>
                            <div class="col-sm-4">
                                <select id="screenTypeInput" class="form-control input-sm" dataField="screenType">
                                    <option>ALL</option>
                                    <option>PSR</option>
                                    <option>CSR</option>
                                    <option>WR</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="isActiveInput" class="col-sm-4 control-label">Is Active?
                            </label>
                            <div class="col-sm-7">
                                <input id="isActiveInput" type="checkbox" class="input-sm" dataField="isActive" style="width:50px;" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="isReasonInput" class="col-sm-4 control-label">Is Reason Code?
                            </label>
                            <div class="col-sm-7">
                                <input id="isReasonInput" type="checkbox" class="input-sm" dataField="isReason" style="width:50px;" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="isResolutionInput" class="col-sm-4 control-label">Is Resolution Code?
                            </label>
                            <div class="col-sm-7">
                                <input id="isResolutionInput" type="checkbox" class="input-sm" dataField="isResolution" style="width:50px;" />
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button id="saveReasonResolutionDetailBtn" type="button" class="btn btn-primary">Save</button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <br/>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h1 class="panel-title gray">Reason / Resolution List
                    <span class="glyphicon glyphicon-plus fr pointer add-code" data-toggle="tooltip" title="Add New"></span>
                </h1>
            </div>
            <div class="panel-body">
                <div class="row filter-cmp">
                    <div class="col-md-1">Filter By:</div>
                    <div class="col-md-3">
                        <select filterOptionFor="reasonResolutionList" style="width:100%;">
                            <option value="code">ID</option>
                            <option value="description">Description</option>
                            <option value="displayOrder">Display Order</option>
                            <option value="screenType">Screen Type</option>
                            <option value="isActive">Is Active?</option>
                            <option value="isReason">Is Reason?</option>
                            <option value="isResolution">Is Resolution?</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <input type="text" filterInputFor="reasonResolutionList" style="width:90%;"/>
                        <span class="glyphicon glyphicon-remove pointer" removeFilterFor="reasonResolutionList"/>
                    </div>
                </div>
                <table id="reasonResolutionList" class="table table-striped">
                    <thead>
                        <tr>
                            <th sortField="code" class="pointer">ID<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="description" class="pointer">Description<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="displayOrder" class="pointer">Display Order<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="screenType" class="pointer">Screen Type<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="isActive" class="pointer">Active<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="isReason" class="pointer">Use as Reason<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="isResolution" class="pointer">Use as Resolution<span class="glyphicon padding-left-5"></span></th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <c:forEach var="code" items="${reasonResolutionList}">

                            <tr>
                                <td name="code" value="${code.code}">${code.code}</td>
                                <td name="description" value="${code.description}">${code.description}</td>
                                <td name="displayOrder" value="${code.displayOrder}">${code.displayOrder}</td>
                                <td name="screenType" value="${code.screenType}">${code.screenType}</td>
                                <td name="isActive" value="${code.isActive}">${code.isActive}</td>
                                <td name="isReason" value="${code.isReason}">${code.isReason}</td>
                                <td name="isResolution" value="${code.isResolution}">${code.isResolution}</td>
                                <td><span class="glyphicon glyphicon-pencil pointer edit-code" data-toggle="tooltip" title="Edit"></span></td>
                            </tr>

                        </c:forEach>
                    </tbody>
                </table>

            </div>
        </div>
    </div>

    <!-- Placed at the end of the document so the pages load faster -->
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-1.7.2.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-ui-1.8.20.custom.min.js"></script>
    <script src="${pageContext.request.contextPath}/bootstrap/js/bootstrap.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/configurationActions.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/sbi-script.js"></script>
</body>
</html>