package com.smilebrands.callcentersupport;

import com.mongodb.Mongo;
import com.mongodb.MongoClient;
import com.mongodb.WriteConcern;
import com.smilebrands.callcentersupport.domain.*;
import com.smilebrands.callcentersupport.domain.constants.*;
import com.smilebrands.callcentersupport.domain.partner.PartnerAgent;
import com.smilebrands.callcentersupport.domain.partner.PartnerConfig;
import com.smilebrands.callcentersupport.domain.report.InboundCallSummary;
import com.smilebrands.callcentersupport.repo.JdbcRepository;
import com.smilebrands.callcentersupport.repo.jpa.CallLogRepository;
import com.smilebrands.callcentersupport.repo.FacilityRepository;
import com.smilebrands.callcentersupport.repo.MongoRepository;
import com.smilebrands.callcentersupport.service.CallService;
import com.smilebrands.callcentersupport.util.DateTimeUtils;
import com.smilebrands.callcentersupport.util.TranslateUtil;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.sql.*;
import java.text.DateFormatSymbols;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;

/**
 * User: Marlin Clark
 * Date: 7/8/14
 */
public class ConversionTest extends BaseTest {

    @Autowired
    protected JdbcTemplate jdbcTemplate;

    @Autowired
    protected FacilityRepository facilityRepository;

    @Autowired
    protected MongoRepository mongoRepository;

    @Autowired
    protected CallLogRepository callLogRepository;

    @Autowired
    protected JdbcRepository jdbcRepository;

    @Autowired
    private MongoOperations mongoOperations;

    @Autowired
    private CallService callService;

    @Autowired
    @Qualifier("CONVR_CALL_CENTER_LIST")
    protected String CONVR_CALL_CENTER_LIST;
    @Autowired
    @Qualifier("CONVR_CAMPAIGN_LIMITED")
    protected String CONVR_CAMPAIGN_LIMITED;
    @Autowired
    @Qualifier("CONVR_CAMPAIGN")
    protected String CONVR_CAMPAIGN;

    @Test
    public void loadCallCenterSupportPhoneNumbers() {
        List<Map<String, Object>> numbers = jdbcTemplate.queryForList(CONVR_CALL_CENTER_LIST);

        for (Map<String, Object> record : numbers) {
            PhoneNumber number = new PhoneNumber();

            number.setPhoneNumber(Long.valueOf(record.get("LOOK_UP_DNIS").toString()));

            if (record.get("TRANSFER_NUMBER") != null)
                number.setTransferNumber(Long.valueOf(record.get("TRANSFER_NUMBER").toString()));

            if (record.get("FAC_ATTR_VALUE") != null){
                String brandName = record.get("FAC_ATTR_VALUE").toString().trim();
                number.setBrandName(brandName);
                String vmRoute = brandName.toLowerCase();
                vmRoute = vmRoute.replace("dental", "");
                vmRoute = vmRoute.replace("!", "");
                vmRoute = vmRoute.replace(" ", "");
                vmRoute = vmRoute.trim();
                if(!vmRoute.equals("brightnow") && !vmRoute.equals("monarch")
                    && !vmRoute.equals("castle") && !vmRoute.equals("newport")){
                    vmRoute = "smilebrands";
                }
                number.setVoiceMailRoute(vmRoute);
            }


            number.setFacilityId(Integer.valueOf(record.get("FACILITY_ID").toString()));

            number.setIvrPromptFile("hello.mp3");
            number.setPhoneType("ML");

            number.setCallCenterSupported(record.get("CALL_CENTER_SUPPORTED").toString().equals("Y"));
            number.setCallCenterName(record.get("CALL_CENTER").toString());
            number.setCallCenterQueueName(record.get("CALL_CENTER_QUEUE").toString());

            logger.debug(number.toString());

            mongoOperations.save(number);
        }
    }

    @Test
    public void loadCampaigns() {
        List<Map<String, Object>> numbers = jdbcTemplate.queryForList(CONVR_CAMPAIGN);
        numbers.addAll(jdbcTemplate.queryForList(CONVR_CAMPAIGN_LIMITED));

        for (Map<String, Object> record : numbers) {
            PhoneNumber number = new PhoneNumber();
            number.setPhoneNumber(Long.valueOf(record.get("CAMPAIGN_LOOK_UP_DNIS").toString()));
            number.setBrandName("Smile Brands");
            number.setCampaignName(record.get("CAMPAIGN_NAME").toString());
            number.setCampaignLimited(record.get("LIMITED_SUPPORT").toString().equalsIgnoreCase("Y"));
            number.setSearchOnAni(false);
            number.setPhoneType("CM");
            number.setLanguage("en");
            number.setCallCenterName(record.get("CALL_CENTER").toString());

            mongoOperations.save(number);

        }

        /*List<Map<String, Object>> numbers = jdbcTemplate.queryForList(CONVR_CAMPAIGN);

        for (Map<String, Object> record : numbers) {
            Campaign c = new Campaign();

            c.setCallCenter(record.get("CALL_CENTER").toString());
            c.setCampaignPhoneNumber(Long.valueOf(record.get("CAMPAIGN_LOOK_UP_DNIS").toString()));
            c.setCampaignName(record.get("CAMPAIGN_NAME").toString());
            c.setLimitedSupport(false);

            logger.debug(c.toString());
            mongoOperations.save(c);

        }*/

        /*Map<Campaign,List<Facility>> campFacs = new HashMap<Campaign, List<Facility>>();
        numbers = jdbcTemplate.queryForList(CONVR_CAMPAIGN_LIMITED);
        Set<String> campaignFacilityKeys = new HashSet<String>();

        for (Map<String, Object> record : numbers) {
            Campaign c = new Campaign();

            c.setCallCenter(record.get("CALL_CENTER").toString());
            c.setCampaignPhoneNumber(Long.valueOf(record.get("CAMPAIGN_LOOK_UP_DNIS").toString()));
            c.setCampaignName(record.get("CAMPAIGN_NAME").toString());
            c.setLimitedSupport(true);

            logger.debug(c.toString());

            if (! campFacs.containsKey(c)) {
                campFacs.put(c, new ArrayList<Facility>());
            }

            Facility f = new Facility();
            f.setFacilityId(Integer.valueOf(record.get("FACILITY_ID").toString()));
            f.setName(record.get("FACILITY_NAME").toString());

            if(campaignFacilityKeys.contains(c.getCampaignPhoneNumber() + "-" + f.getFacilityId())){
                continue;
            }else{
                campaignFacilityKeys.add(c.getCampaignPhoneNumber() + "-" + f.getFacilityId());
            }


            if (record.get("ADDRESS") != null)
                f.setAddress(record.get("ADDRESS").toString());

            if (record.get("CITY") != null)
                f.setCity(record.get("CITY").toString());

            if (record.get("STATE") != null)
                f.setState(record.get("STATE").toString());

            if (record.get("ZIPCODE") != null)
                f.setZip(record.get("ZIPCODE").toString());

            campFacs.get(c).add(f);
        }

        logger.debug(campFacs.size() + " Limited Campaigns.");

        for (Campaign c : campFacs.keySet()) {
            c.setFacilities(campFacs.get(c));
            mongoOperations.save(c);

            logger.info("Limited Campaign " + c.getCampaignName() + " contains " + c.getFacilities().size() + " facilities");
        }*/

    }

    @Test
    public void testBuildFacility(){
        List<Facility> facilities = facilityRepository.findAllFacilities(true);
        logger.debug(facilities.size() + " Facilities have been found!");

        for(Facility facility : facilities){
//            if(facility.getFacilityId() == 10300 || facility.getFacilityId() == 10230){

                logger.debug("Processing Facility [" + facility.getFacilityId() + "]");
                Facility existing = mongoRepository.findFacilityDetail(facility.getFacilityId());
                if(existing != null){
                    facility.setMessage(existing.getMessage());
                }
                PhoneNumber phoneNumber = mongoRepository.findPhoneNumberByFacility(facility.getFacilityId(), "ML");
                if(phoneNumber != null){
                    facility.setPhoneNumber(phoneNumber.getPhoneNumber());
                }
                mongoRepository.persistFacilityDetail(facility);

//            }

        }
    }

    @Test
    public void testBuildSingleFacility() {
        Facility f = facilityRepository.findFacilityByFacilityId(10300);
        logger.debug(f.toString());
    }

    @Test
    public void enumConstant(){
        int id = 26493;
        AttributeId ai = AttributeId.getById(id, "FACILITY");
        logger.debug("{}", ai);
    }

    @Test
    public void funWithPatientFullName(){
        Patient patient = new Patient();
        patient.setPatientLastName("PANGLE");
        patient.setPatientFirstName("KEVIN");
        logger.debug("{}", patient.getPatientFullName());
    }

    @Test
    public void funWithPatientDOBFormat(){
        Calendar cal = Calendar.getInstance();
        Patient patient = new Patient();
        logger.debug("null: {}", patient.getDateOfBirthAsDigit());
        patient.setDateOfBirth(cal.getTime());
        logger.debug("today: {}", patient.getDateOfBirthAsDigit());
        cal.set(1984, Calendar.MAY, 20);
        patient.setDateOfBirth(cal.getTime());
        logger.debug("May 20, 1984: {}", patient.getDateOfBirthAsDigit());
        cal.set(1988, Calendar.DECEMBER, 3);
        patient.setDateOfBirth(cal.getTime());
        logger.debug("Dec 3, 1988: {}", patient.getDateOfBirthAsDigit());
    }

    @Test
    public void updateFacilityMessage(){
        try{

            MongoOperations mongoOps = new MongoTemplate(new Mongo("itchy.bnd.corp"), "facilities");
            List<Facility> facilities = mongoOps.findAll(Facility.class, "facilityInfo");
            Set<Integer> updatedFacilities = new HashSet<Integer>();
            for(Facility fac : facilities){
                if(fac.getMessage() != null && fac.getMessage().trim().length() > 0){
                    Integer facilityId = Integer.valueOf(fac.getFacilityId());
                    if(updatedFacilities.contains(facilityId)){
                        continue;
                    }
                    Facility newFac = facilityRepository.findFacilityByFacilityId(facilityId);
                    if(newFac != null){
                        newFac.setMessage(fac.getMessage());
                        mongoRepository.persistFacilityDetail(newFac);
                        updatedFacilities.add(facilityId);
                    }
                }

            }
            logger.debug("facilities size: {}", facilities.size());
            logger.debug("updated facilities: {}", updatedFacilities.size());
        }catch (Exception ex){
            ex.printStackTrace();
        }
    }

    @Test
    public void load800Numbers() throws Exception {
        CsvReader reader = new CsvReader(new FileReader(new File("/Users/<USER>/Desktop/800Numbers.csv")));
        List data = reader.readAll();

        for (int x = 0; x < data.size(); x++) {
            String elements[] = (String[]) data.get(x);

            PhoneNumber number = new PhoneNumber();
            number.setPhoneNumber(Long.valueOf(elements[0]));
            number.setBrandName(elements[1]);
            number.setPhoneType("TF");
            number.setLanguage("en");
            number.setCallCenterSupported(true);
            number.setCallCenterQueueName(elements[3]);
            number.setCallCenterName(elements[2]);

            logger.debug(number.toString());

            mongoOperations.save(number);

        }


    }


    @Test
    public void loadNPADataToPostgres() {
        CsvReader reader = null;
        try {
            reader = new CsvReader(new FileReader(new File("/Users/<USER>/Desktop/IT Projects/2014-5-DeluxeAreaCodeDatabase-csv/2014-5-DeluxeAreaCodeDatabase.csv")));

            List data = reader.readAll();
            logger.debug(data.size() + " CSV records.");

            String url = "*******************************************";
            Properties props = new Properties();
            props.setProperty("user","importer");
            props.setProperty("password","1mp0rt3r");
            Connection conn = DriverManager.getConnection(url, props);

            PreparedStatement ps = conn.prepareStatement("INSERT INTO imports.npa_nxx_2014_5(npa, nxx, npa_nxx, is_mobile, company, lat, lng, zip_code, city, state, status, time_zone) VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

            final int batchSize = 1000;
            int count = 0;

            for (int x = 1; x < data.size(); x++) {
                String elements[] = (String[]) data.get(x);

                ps.setObject(1, Integer.parseInt(elements[0]));
                ps.setObject(2, Integer.parseInt(elements[1]));
                ps.setObject(3, Integer.parseInt(elements[0] + "" + Integer.parseInt(elements[1])));
                ps.setObject(4, elements[12].equalsIgnoreCase("W") ? 1 : 0);
                ps.setObject(5, elements[24]);
                ps.setObject(6, Double.parseDouble(elements[5]));
                ps.setObject(7, Double.parseDouble(elements[6]));
                ps.setObject(8, elements[14]);
                ps.setObject(9, elements[8]);
                ps.setObject(10, elements[7]);
                ps.setObject(11, elements[28]);

                String tz = elements[10];
                if (tz.equalsIgnoreCase("4")) { ps.setObject(12, "atlantic"); }
                if (tz.equalsIgnoreCase("5")) { ps.setObject(12, "eastern"); }
                if (tz.equalsIgnoreCase("6")) { ps.setObject(12, "central"); }
                if (tz.equalsIgnoreCase("7")) { ps.setObject(12, "mountain"); }
                if (tz.equalsIgnoreCase("8")) {
                    ps.setObject(12, "pacific"); }
                if (tz.equalsIgnoreCase("9")) { ps.setObject(12, "alaska"); }
                if (tz.equalsIgnoreCase("10")) { ps.setObject(12, "hawaii"); }

                ps.addBatch();

                if (++count % batchSize == 0) {
                    ps.executeBatch();
                }

            }

            ps.executeBatch();
            ps.close();
            conn.close();

        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
            e.getNextException().printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @Test
    public void loadNPAData() throws Exception {
        CsvReader reader = new CsvReader(new FileReader(new File("/Users/<USER>/Desktop/IT Projects/2014-5-DeluxeAreaCodeDatabase-csv/2014-5-DeluxeAreaCodeDatabase.csv")));
        List data = reader.readAll();
        logger.debug(data.size() + " CSV records.");

        Map<Integer, Npa> ns = new HashMap<Integer, Npa>();

        for (int x = 1; x < data.size(); x++) {
            String elements[] = (String[]) data.get(x);

            Integer key = Integer.parseInt(elements[26]);
            String zip = elements[14];

            Npa n = new Npa();
            if (ns.containsKey(key)) {
                n = ns.get(key);
                n.getZipCodes().add(zip);

            } else {
                n.setNpa(Integer.parseInt(elements[0]));
                n.setNxx(Integer.parseInt(elements[1]));
                n.setMobile(elements[12].equalsIgnoreCase("W"));
                n.getZipCodes().add(zip);
                n.setCity(elements[8]);
                n.setState(elements[7]);
                n.setCompany(elements[24]);
                n.setStatus(elements[28]);

                String tz = elements[10];
                if (tz.equalsIgnoreCase("4")) { n.setTimeZone("atlantic"); }
                if (tz.equalsIgnoreCase("5")) { n.setTimeZone("eastern"); }
                if (tz.equalsIgnoreCase("6")) { n.setTimeZone("central"); }
                if (tz.equalsIgnoreCase("7")) { n.setTimeZone("mountain"); }
                if (tz.equalsIgnoreCase("8")) { n.setTimeZone("pacific"); }
                if (tz.equalsIgnoreCase("9")) { n.setTimeZone("alaska"); }
                if (tz.equalsIgnoreCase("10")) { n.setTimeZone("hawaii"); }

                ns.put(key, n);
            }

        }

        logger.debug(ns.size() + " NPAs from " + data.size() + " records.");
        for (Integer n : ns.keySet()) {
            Npa npa = ns.get(n);
            mongoOperations.save(npa);
        }

    }

    @Test
    public void testLoadCallCenter(){
        CallCenter irvine = new CallCenter();
        irvine.setCallCenterName("Irvine");
        irvine.setFacilityId(82000);
        irvine.setTimezone("Pacific");
        irvine.setTimezoneShort("PST");
        irvine.setTimezoneOffset(-8);
        irvine.getAgents().add(new CallCenterAgent(AgentType.NA, SkillType.WR, Language.NA, 6489));
        irvine.setCallbackPhoneNumber(8448021153L);
        irvine.getCallCenterHours().add("Monday to Thursday 7:00am-6:30pm");
        irvine.getCallCenterHours().add("Friday 7:00am-6:00pm");
        irvine.getCallCenterHours().add("Saturday 7:30am-4:00pm");
        irvine.getCallCenterBusinessHours().add(new BusinessHour(HourType.CB, Calendar.MONDAY, 7, 0, 18, 30));
        irvine.getCallCenterBusinessHours().add(new BusinessHour(HourType.CB, Calendar.TUESDAY, 7, 0, 18, 30));
        irvine.getCallCenterBusinessHours().add(new BusinessHour(HourType.CB, Calendar.WEDNESDAY, 7, 0, 18, 30));
        irvine.getCallCenterBusinessHours().add(new BusinessHour(HourType.CB, Calendar.THURSDAY, 7, 0, 18, 30));
        irvine.getCallCenterBusinessHours().add(new BusinessHour(HourType.CB, Calendar.FRIDAY, 7, 30, 18, 0));

        irvine.getAgents().add(new CallCenterAgent(AgentType.PSR, SkillType.CB, Language.ENG, 6571));
        irvine.getAgents().add(new CallCenterAgent(AgentType.PSR, SkillType.CB, Language.SPA, 6572));
        irvine.getAgents().add(new CallCenterAgent(AgentType.CSR, SkillType.CB, Language.ENG, 6573));
        irvine.getAgents().add(new CallCenterAgent(AgentType.CSR, SkillType.CB, Language.SPA, 6574));

        mongoOperations.save(irvine);
        CallCenter plano = new CallCenter();
        plano.setCallCenterName("Plano");
        plano.setFacilityId(83000);
        plano.setTimezone("Central");
        plano.setTimezoneShort("CST");
        plano.setTimezoneOffset(-6);
        plano.getAgents().add(new CallCenterAgent(AgentType.NA, SkillType.WR, Language.NA, 38489));
        plano.setCallbackPhoneNumber(8448021154L);
        plano.getCallCenterHours().add("Monday to Thursday 7:30am-7:30pm");
        plano.getCallCenterHours().add("Friday 7:30am-7:00pm");
        plano.getCallCenterHours().add("Saturday 8:00am-4:00pm");
        plano.getCallCenterBusinessHours().add(new BusinessHour(HourType.CB, Calendar.MONDAY, 7, 30, 17, 30));
        plano.getCallCenterBusinessHours().add(new BusinessHour(HourType.CB, Calendar.TUESDAY, 7, 30, 17, 30));
        plano.getCallCenterBusinessHours().add(new BusinessHour(HourType.CB, Calendar.WEDNESDAY, 7, 30, 17, 30));
        plano.getCallCenterBusinessHours().add(new BusinessHour(HourType.CB, Calendar.THURSDAY, 7, 30, 17, 30));
        plano.getCallCenterBusinessHours().add(new BusinessHour(HourType.CB, Calendar.FRIDAY, 7, 0, 18, 0));
        plano.getCallCenterBusinessHours().add(new BusinessHour(HourType.CB, Calendar.SATURDAY, 8, 0, 18, 0));

        plano.getAgents().add(new CallCenterAgent(AgentType.PSR, SkillType.CB, Language.ENG, 38661));
        plano.getAgents().add(new CallCenterAgent(AgentType.PSR, SkillType.CB, Language.SPA, 38662));
        plano.getAgents().add(new CallCenterAgent(AgentType.CSR, SkillType.CB, Language.ENG, 38663));
        plano.getAgents().add(new CallCenterAgent(AgentType.CSR, SkillType.CB, Language.SPA, 38664));

        mongoOperations.save(plano);
    }

    @Test
    public void testGetAgentForCallCenter(){
        CallCenter callCenter = mongoOperations.findById("Plano", CallCenter.class);
        logger.debug("callcenter: {}", callCenter.getCallCenterName());
        logger.debug("agent ID for Plano WR: {}", callCenter.getAgentId(AgentType.NA, SkillType.WR, Language.NA));
        logger.debug("agent ID for Plano CB CSR ENG: {}", callCenter.getAgentId(AgentType.CSR, SkillType.CB, Language.ENG));
        logger.debug("agent ID for Plano CB PSR SPA: {}", callCenter.getAgentId(AgentType.PSR, SkillType.CB, Language.SPA));
        logger.debug("agent ID for bogus: {}", callCenter.getAgentId(AgentType.CSR, SkillType.WR, Language.ENG));

        logger.debug("valueOf EN: {}", Language.valueOf("ENG"));
        logger.debug("valueOf en: {}", Language.getByValue("en"));
        logger.debug("valueOf null: {}", Language.getByValue(null));
        logger.debug("valueOf empty: {}", Language.getByValue("   "));
        logger.debug("valueOf bogus: {}", Language.getByValue("fgkfdjgdfg"));

        String queueName = "Irvine".substring(0, 3).toUpperCase() + "_CB_" + AgentType.PSR + "_" + Language.ENG ;
        logger.debug("queueName: {}", queueName);

    }

    @Test
    public void updatePhoneNumberWithActiveFlag(){
        List<PhoneNumber> phoneNumbers = mongoRepository.findAllPhoneNumbers();
        for(PhoneNumber phoneNumber : phoneNumbers){
            phoneNumber.setActive(true);
            mongoRepository.persistPhoneNumber(phoneNumber);
        }
    }

    @Test
    public void updatePhoneNumberWithIvrAndSupportedFlag(){
        List<PhoneNumber> phoneNumbers = mongoRepository.findAllPhoneNumbers();
        for(PhoneNumber phoneNumber : phoneNumbers){
            phoneNumber.setIvrName("MainIVR");
            if(phoneNumber.isCallCenterSupported()){
                phoneNumber.setCsrSupported(true);
                phoneNumber.setPsrSupported(true);
            }
            mongoRepository.persistPhoneNumber(phoneNumber);
        }
    }

    @Test
    public void testLookupPhoneNumberByFacilityId(){
        PhoneNumber number = mongoRepository.findPhoneNumberByFacility(10230);
        logger.debug("number found: {}", number != null);
        number.setActive(false);
        mongoRepository.persistPhoneNumber(number);
        PhoneNumber number2 = mongoRepository.findPhoneNumberByFacility(10230);
        logger.debug("number found: {}", number2 != null);
        number.setActive(true);
        mongoRepository.persistPhoneNumber(number);
    }

    @Test
    public void testGetFacilityManagement(){
        List<Employee> employees = facilityRepository.findFacilityManagement(10230);
        logger.debug("For Facility ID 10230:::" + employees.size());
        for(Employee emp : employees){
            logger.debug(emp.getFirstName() + " " + emp.getLastName() + ":::" + emp.getTitle() + ":::" + emp.getEmail());
        }
        employees = facilityRepository.findFacilityManagement(35050);
        logger.debug("For Facility ID 35050:::" + employees.size());
        for(Employee emp : employees){
            logger.debug(emp.getFirstName() + " " + emp.getLastName() + ":::" + emp.getTitle() + ":::" + emp.getEmail());
        }
        employees = facilityRepository.findFacilityManagement(10300);
        logger.debug("For Facility ID 10300:::" + employees.size());
        for(Employee emp : employees){
            logger.debug(emp.getFirstName() + " " + emp.getLastName() + ":::" + emp.getTitle() + ":::" + emp.getEmail());
        }
        employees = facilityRepository.findFacilityManagement(51040);
        logger.debug("For Facility ID 10300:::" + employees.size());
        for(Employee emp : employees){
            logger.debug(emp.getFirstName() + " " + emp.getLastName() + ":::" + emp.getTitle() + ":::" + emp.getEmail());
        }
    }

    @Test
    public void funWithDateFormat() throws Exception{
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        DateFormatSymbols symbols = new DateFormatSymbols();
        symbols.setAmPmStrings(new String[] { "am", "pm" });
        df.setDateFormatSymbols(symbols);

        Calendar cal = Calendar.getInstance();
        logger.debug("{}", df.format(cal.getTime()));
        cal.set(Calendar.HOUR_OF_DAY, 8);
        logger.debug("{}", df.format(cal.getTime()));

        String dob = "25041943";
        df = new SimpleDateFormat("ddMMyyyy");
        SimpleDateFormat sf = new SimpleDateFormat("MMddyyyy");
        Date in = df.parse(dob);
        logger.debug("parse " + dob + " to " + in);
        logger.debug("format " + dob + " to " + sf.format(in));

        sf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        logger.debug("2014-08-09T14:15:04: {}", sf.parse("2014-08-09T14:15:04"));
        logger.debug("2014-08-09T08:15:41: {}", sf.parse("2014-08-09T08:15:41"));

        cal = Calendar.getInstance();
        df = new SimpleDateFormat("yyyy-MM-dd hh:mm:ssa z");
        logger.debug("with timezone: {}", df.format(cal.getTime()));
        cal.set(2015, Calendar.JANUARY, 3);
        for(int i=0; i<12; i++){
            logger.debug("with timezone: {}", df.format(cal.getTime()));
            cal.add(Calendar.MONTH, 1);
        }
    }

    @Test
    public void convertDateFieldToStringAndInteger(){
        Date dt = new Date();
        List<InboundCall> inboundCalls = mongoOperations.findAll(InboundCall.class);
        String date = "2014-08-09";
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        for(InboundCall inboundCall : inboundCalls){
            boolean needToRemove = false;
            if((inboundCall.getAni() != null && (inboundCall.getAni().equals(9493315916L) || inboundCall.getAni().equals(9495054655L)))
                || inboundCall.getUuid().indexOf("test") == 0
                || (inboundCall.getCreateDateTime() == null
                    || df.format(inboundCall.getCreateDateTime()).compareTo(date) < 0)){
                logger.debug("about to remove test inbound call for ID [" + inboundCall.getUuid() + "] with ANI [" + inboundCall.getAni() + "]");
                mongoOperations.remove(inboundCall);
                needToRemove = true;
            }
            AgentCall call = mongoRepository.getAgentCall(inboundCall.getUuid());
            if(call == null){
//                logger.debug("Cannot find Agent Call with ID[" + inboundCall.getUuid() + "]");
                continue;
            }
            boolean goodToUpdate = true;
            if(needToRemove){
                logger.debug("about to remove test agent call [" + call.getUuid() + "] with ANI number [" + call.getAni() + "]");
                mongoOperations.remove(call);
                continue;
            }
            call.setCallRegisteredDate(VelocityUtils.getDateAsString(inboundCall.getCreateDateTime(), null));
            call.setCallRegisteredTime(VelocityUtils.getDateAsString(inboundCall.getCreateDateTime(), "HHmmss"));

            for(LoadedFacility facility : call.getLoadedFacilities()){
                facility.setLoadDate(VelocityUtils.getDateAsString(facility.getLoadDateTime(), null));
                facility.setLoadTime(VelocityUtils.getDateAsString(facility.getLoadDateTime(), "HHmmss"));
                facility.setLoadDateTime(null);
            }
            for(LoadedEmployee employee : call.getLoadedEmployees()){
                if(employee.getUnloadedDateTime() == null){
                    goodToUpdate = false;
                    break;
                }
                employee.setLoadedDate(VelocityUtils.getDateAsString(employee.getLoadedDateTime(), null));
                employee.setLoadedTime(VelocityUtils.getDateAsString(employee.getLoadedDateTime(), "HHmmss"));
                employee.setLoadedDateTime(null);

                employee.setUnloadedDate(VelocityUtils.getDateAsString(employee.getUnloadedDateTime(), null));
                employee.setUnloadedTime(VelocityUtils.getDateAsString(employee.getUnloadedDateTime(), "HHmmss"));
                employee.setUnloadedDateTime(null);

                employee.setResolutionDate(VelocityUtils.getDateAsString(employee.getResolutionDateTime(), null));
                employee.setResolutionTime(VelocityUtils.getDateAsString(employee.getResolutionDateTime(), "HHmmss"));
                employee.setResolutionDateTime(null);

                employee.setReasonDate(VelocityUtils.getDateAsString(employee.getReasonDateTime(), null));
                employee.setReasonTime(VelocityUtils.getDateAsString(employee.getReasonDateTime(), "HHmmss"));
                employee.setReasonDateTime(null);
            }
            for(Patient p : call.getPatients()){
                p.setCreateDate(VelocityUtils.getDateAsString(p.getCreateDateTime(), null));
                p.setCreateTime(VelocityUtils.getDateAsString(p.getCreateDateTime(), "HHmm"));
                p.setCreateDateTime(null);

                if(p.getUpdateDateTime() != null){
                    p.setUpdateDate(VelocityUtils.getDateAsString(p.getUpdateDateTime(), null));
                    p.setUpdateTime(VelocityUtils.getDateAsString(p.getUpdateDateTime(), "HHmm"));
                    p.setUpdateDateTime(null);
                }
                p.setNextAppointmentDate(VelocityUtils.getDateAsString(p.getNextAppointmentDateTime(), null));
                p.setNextAppointmentTime(VelocityUtils.getDateAsString(p.getNextAppointmentDateTime(), "HHmm"));
            }
            if(goodToUpdate){
                logger.debug("about to update agent call[" + call.getUuid() + "]");
                mongoRepository.updateAgentCall(call);
            }

        }
        logger.debug("time taken to process [" + inboundCalls.size() + "] inbound call(s): " + (new Date().getTime() - dt.getTime()));

    }

    @Test
    public void testCompareString(){
        logger.debug("compare 2014-08-11 with 2014-08-09: {}","2014-08-11".compareToIgnoreCase("2014-08-09"));
        logger.debug("compare 2014-08-09 with 2014-08-09: {}","2014-08-09".compareToIgnoreCase("2014-08-09"));
        logger.debug("compare 2014-07-22 with 2014-08-09: {}","2014-07-22".compareToIgnoreCase("2014-08-09"));
    }

    @Test
    public void updateReasonResolution(){
        List<CallResolutionCode> list = mongoRepository.getCallReasonResolutionsCodes();
        for(CallResolutionCode crrc : list){
            mongoRepository.persistCallResolutionCode(crrc);
        }
    }

    @Test
    public void testLoadInboundCall(){
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.set(2014, Calendar.AUGUST, 11, 0, 0, 0);
        cal2.set(2014, Calendar.AUGUST, 11, 23, 59, 59);

        Query query = new Query(Criteria.where("createDateTime").gte(cal1.getTime()).lt(cal2.getTime()));
        List<InboundCall> inboundCalls = mongoOperations.find(query, InboundCall.class);
        logger.debug("query: {}", query.toString());
        for(InboundCall call : inboundCalls){
            call.setCreateDate(VelocityUtils.getDateAsString(call.getCreateDateTime(), null));
            call.setCreateTime(VelocityUtils.getDateAsString(call.getCreateDateTime(), "HHmmss"));
            mongoOperations.save(call);
        }
        logger.debug("inboundCalls for " + cal1.getTime() + ": " + inboundCalls.size());
    }

    @Test
    public void testLoadInboundCallSummary(){
        List<InboundCallSummary> list = mongoOperations.findAll(InboundCallSummary.class);
        logger.debug("list size: {}", list.size());
        for(InboundCallSummary summary : list){
            logger.debug("ID: {}", summary.getSummaryId().getCallCenterName() + ":::" + summary.getSummaryId().getPhoneType());
            logger.debug("Value: {}", summary.getValue().getCallCount());
        }
        Criteria criteria = Criteria.where("_id.callCenterName").is("Irvine");
//        criteria.and("_id.phoneType").is("ML");
        Query query = new Query(criteria);
        logger.debug("query: {}", query.toString());
        list = mongoOperations.find(query, InboundCallSummary.class);
        logger.debug("list size with query: {}", list.size());
    }

    @Test
    public void checkFacilityNotHavingPhoneNumber(){
        List<Facility> facilities = mongoRepository.findAllFacility();
        List<Integer> facilityIds = new ArrayList<Integer>();
        for(Facility facility : facilities){
            PhoneNumber phoneNumber = mongoRepository.findPhoneNumberByFacility(facility.getFacilityId());
            if(phoneNumber == null){
                facilityIds.add(facility.getFacilityId());
            }
        }
        logger.debug("Facility missing phone number: {}", facilityIds);
        Set<Integer> facilitySet = new HashSet<Integer>();
        List<ZipFacility> zipFacilities = mongoOperations.findAll(ZipFacility.class);
        for(ZipFacility zf : zipFacilities){
            for(Facility facility : zf.getFacilities()){
                if(!facilitySet.contains(facility.getFacilityId())){
                    PhoneNumber phoneNumber = mongoRepository.findPhoneNumberByFacility(facility.getFacilityId());
                    if(phoneNumber == null){
                        facilitySet.add(facility.getFacilityId());
                    }
                }
            }
        }
        logger.debug("Facility (in ZipFacility) missing phone number (" + facilitySet.size() + "): {}", facilitySet);
    }

    @Test
    public void funWithTimezone(){
        Calendar cal = Calendar.getInstance();
        TimeZone timezone = cal.getTimeZone();
        logger.debug("cal: {}", cal.getTime());
        logger.debug("time zone: {}",timezone);
        logger.debug("time zone offset: {}", timezone.getOffset(Calendar.HOUR) / (1000 * 60 * 60));
        logger.debug("time zone raw offset: {}", timezone.getRawOffset() / (1000 * 60 * 60));

        cal = Calendar.getInstance();
        cal.set(2014, Calendar.DECEMBER, 1);
        timezone = cal.getTimeZone();
        logger.debug("cal: {}", cal.getTime());
        logger.debug("time zone: {}",timezone);
        logger.debug("time zone offset: {}", timezone.getOffset(Calendar.HOUR) / (1000 * 60 * 60));
        logger.debug("time zone raw offset: {}", timezone.getRawOffset() / (1000 * 60 * 60));
    }

    @Test
    public void testFindCallCenterByName(){
        CallCenter c = mongoRepository.getCallCenterByName("Irvine");
        logger.debug(c.getCallCenterName() + ":::" + c.getTimezoneOffset());
        c = mongoRepository.getCallCenterByName("Plano");
        logger.debug(c.getCallCenterName() + ":::" + c.getTimezoneOffset());
        c = mongoRepository.getCallCenterByName("Hello world");
        logger.debug("{}", c);
    }

    @Test
    public void testLoadTimeByLocationOffset(){
        Date d = DateTimeUtils.getLoadedTimeByLocationOffset(-8);
        logger.debug("current time for offset -8: {}", d);
        d = DateTimeUtils.getLoadedTimeByLocationOffset(-7);
        logger.debug("current time for offset -7: {}", d);
        d = DateTimeUtils.getLoadedTimeByLocationOffset(-6);
        logger.debug("current time for offset -6: {}", d);
        d = DateTimeUtils.getLoadedTimeByLocationOffset(-5);
        logger.debug("current time for offset -5: {}", d);
        d = DateTimeUtils.getLoadedTimeByLocationOffset(7);
        logger.debug("current time for offset 7: {}", d);
    }

    @Test
    public void testParseDateTime(){
        Date dt = VelocityUtils.parseDate("08-18-2014 091020", "MM-dd-yyyy HHmmss");
        logger.debug("dt: {}", dt);
    }

    @Test
    public void funWithList(){
        List<Integer> l1 = new ArrayList<Integer>();
        l1.add(1);
        l1.add(2);
        l1.add(3);
        List<Integer> l2 = new ArrayList<Integer>();
        l2.add(2);
        l2.add(3);
        l2.add(4);
        logger.debug("l1: {}", l1);
        logger.debug("l2: {}", l2);
        l1.removeAll(l2);
        logger.debug("l1.removeAll(l2): {}", l1);
        mongoOperations.findAndRemove(new Query(Criteria.where("uuid").is("9f15ba4c-9a42-49e6-ba33-fc0d12eb7886")), AgentCall.class);
        mongoOperations.findAndRemove(new Query(Criteria.where("uuid").is("9f15ba4c-9a42-49e6-ba33-fc0d12eb7886")), InboundCall.class);
    }

    @Test
    public void testUpdateLoadedTimeByOffset(){
        List<InboundCall> inboundCalls = mongoOperations.find(new Query(Criteria.where("createDate").is("08-14-2014")), InboundCall.class);
        int updateInboundCallCnt = 0;
        int updateInboundCallPlanoCnt = 0;
        int updateInboundCallIrvineCnt = 0;
        for(InboundCall inboundCall : inboundCalls){
            if(inboundCall.getCreateDateTime() != null
                && inboundCall.getPhoneNumber() != null && inboundCall.getPhoneNumber().getCallCenterName() != null){
                CallCenter callCenter = mongoRepository.getCallCenterByName(inboundCall.getPhoneNumber().getCallCenterName());
                if(callCenter != null){
                    boolean needToUpdate = false;
                    Date createDateTime = DateTimeUtils.getTimeByLocationOffset(inboundCall.getCreateDateTime(), callCenter.getTimezoneOffset());
                    String createTime = VelocityUtils.getDateAsString(createDateTime, "HHmmss");
                    String createDate = VelocityUtils.getDateAsString(createDateTime, null);
                    if(!createDate.equals(inboundCall.getCreateDate())){
                        needToUpdate = true;
                    }
                    if(!createTime.equals(inboundCall.getCreateTime())){
                        needToUpdate = true;
                    }
                    if(needToUpdate){
                        updateInboundCallCnt++;
                        if(callCenter.getCallCenterName().equalsIgnoreCase("Plano")){
                            updateInboundCallPlanoCnt++;
                        }else if(callCenter.getCallCenterName().equalsIgnoreCase("Irvine")){
                            updateInboundCallIrvineCnt++;
                        }
                        logger.debug("update create time for inbound call[" + inboundCall.getUuid() + "] " +
                                "from [" + inboundCall.getCreateDate() + " " + inboundCall.getCreateTime() + "]" +
                                " to [" + createDate + " " + createTime + "]");
                        inboundCall.setCreateTime(createTime);
                        inboundCall.setCreateDate(createDate);
                    }
                }
            }
        }
        logger.debug("Update " + updateInboundCallCnt + " inbound calls time " + ", Irvine has " + updateInboundCallIrvineCnt + " and Plano has " + updateInboundCallPlanoCnt);
    }

    @Test
    public void updateAgentCallWithCallCenterNameAndSessionId(){
        List<AgentCall> agentCalls = mongoOperations.findAll(AgentCall.class);
        for(AgentCall agentCall : agentCalls){
//            if(agentCall.getSessionId() == null){
                InboundCall inboundCall = mongoRepository.getInboundCall(agentCall.getUuid());
                if(inboundCall != null){
                    agentCall.setSessionId(inboundCall.getSessionId());
                    agentCall.setPhoneType(inboundCall.getPhoneType());
                    if(inboundCall.getPhoneNumber() != null){
                        agentCall.setCallCenterName(inboundCall.getPhoneNumber().getCallCenterName());
                    }
                    mongoRepository.updateAgentCall(agentCall);
                }
//            }
        }
    }

    @Test
    public void testCallLogRepo(){
        logger.debug("Call Log: {}",callLogRepository.findOne(1L));
    }

    @Test
    public void testGetCallLogDetailReport(){
        Date dt = new Date();
        List<Integer> agentIds = new ArrayList<Integer>();
        agentIds.add(113312);
        agentIds.add(109857);
        agentIds.add(115965);
        List<LinkedHashMap<String, String>> list = jdbcRepository.getCallLogDetailReport("01-Jan-2015", "28-Jan-2015", agentIds, null, null);
        logger.debug("\n\ntime taken to load list size " + list.size() + " is " + (new Date().getTime()-dt.getTime()) + "\n\n");

        list = jdbcRepository.getCallLogDetailReport("01-Jan-2015", "28-Jan-2015", agentIds, "appt", null);
        logger.debug("\n\ntime taken to load list size " + list.size() + " is " + (new Date().getTime()-dt.getTime()) + "\n\n");

        list = jdbcRepository.getCallLogDetailReport("01-Jan-2015", "28-Jan-2015", agentIds, "latest", null);
        logger.debug("\n\ntime taken to load list size " + list.size() + " is " + (new Date().getTime()-dt.getTime()) + "\n\n");
    }

    @Test
    public void testTranslate(){
        Calendar cal = Calendar.getInstance();
        cal.set(2014, Calendar.AUGUST, 25);
        for(int i=0; i<7; i++){
            cal.set(Calendar.DAY_OF_WEEK, cal.get(Calendar.DAY_OF_WEEK) + 1);
            logger.debug("translate " + cal.getDisplayName(Calendar.DAY_OF_WEEK, Calendar.LONG, Locale.US) + " to sp: {}", TranslateUtil.translateWeekDay(cal.get(Calendar.DAY_OF_WEEK), "sp"));
            logger.debug("translate " + cal.getDisplayName(Calendar.DAY_OF_WEEK, Calendar.LONG, Locale.US) + " to en: {}", TranslateUtil.translateWeekDay(cal.get(Calendar.DAY_OF_WEEK), "en"));
        }
        cal.set(2014, Calendar.JANUARY, 1);
        for(int i=0; i<12; i++){
            cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) + 1);
            logger.debug("translate " + cal.getDisplayName(Calendar.MONTH, Calendar.LONG, Locale.US) + " to sp: {}", TranslateUtil.translateMonth(cal.get(Calendar.MONTH), "sp"));
            logger.debug("translate " + cal.getDisplayName(Calendar.MONTH, Calendar.LONG, Locale.US) + " to en: {}", TranslateUtil.translateMonth(cal.get(Calendar.MONTH), "en"));
        }

        int number = 2014;
        logger.debug("translate " + number + " to sp: {}", TranslateUtil.translateNumber(number, "count", "SP"));
        number = 14;
        logger.debug("translate " + number + " to sp: {}", TranslateUtil.translateNumber(number, "count", "SP"));
        number = 54;
        logger.debug("translate " + number + " to sp: {}", TranslateUtil.translateNumber(number, "count", "SP"));
    }

    @Test
    public void funWithSubString(){
        String str = "2014";
        logger.debug("thousand: {}", Integer.valueOf(str.substring(0, str.length() - 3)));
        logger.debug("remainder: {}", Integer.valueOf(str.substring(str.length() - 3, str.length())));
    }

    @Test
    public void testGetGMTTime(){
        Calendar cal = Calendar.getInstance();
        logger.debug(cal.getTime() + " is converted to GMT from -8 as {}", DateTimeUtils.getGMTTimeFromLocationOffset(cal.getTime(), -8));
        logger.debug(cal.getTime() + " is converted to GMT from -6 as {}", DateTimeUtils.getGMTTimeFromLocationOffset(cal.getTime(), -6));
        logger.debug(cal.getTime() + " is converted to GMT from 7 as {}", DateTimeUtils.getGMTTimeFromLocationOffset(cal.getTime(), 7));

        cal.set(2014, Calendar.DECEMBER, 2);
        logger.debug(cal.getTime() + " is converted to GMT from -8 as {}", DateTimeUtils.getGMTTimeFromLocationOffset(cal.getTime(), -8));
        logger.debug(cal.getTime() + " is converted to GMT from -6 as {}", DateTimeUtils.getGMTTimeFromLocationOffset(cal.getTime(), -6));
        logger.debug(cal.getTime() + " is converted to GMT from 7 as {}", DateTimeUtils.getGMTTimeFromLocationOffset(cal.getTime(), 7));
    }

    @Test
    public void testCallTime(){
        AgentCall agentCall = mongoRepository.getAgentCall("44b02cd2-26e8-478a-8dad-fdcc37e2dabe");
//        AgentCall agentCall = mongoRepository.getAgentCall("88ec8f14-38ee-40d9-adf1-0c19d4111120");
        LoadedEmployee loadedEmployee = agentCall.findLoadedEmployee(116207,"csr");
        CallCenter callCenter = mongoRepository.getCallCenterByName(agentCall.getCallCenterName());
        logger.debug("offset: {}", callCenter.getTimezoneOffset());
        logger.debug("loaded date time: {}", loadedEmployee.getLoadedDateTime());
        logger.debug("loaded date time (GMT): {}", DateTimeUtils.getGMTTimeFromLocationOffset(loadedEmployee.getLoadedDateTime(), callCenter.getTimezoneOffset()));

    }

    @Test
    public void testSetSchedulerUrl(){
        String[] names = new String[] {"Irvine", "Plano"};
        for(String n : names){
            CallCenter callCenter = mongoRepository.getCallCenterByName(n);
            if(callCenter != null){
                callCenter.setLibertyUrl("http://liberty.bnd.corp/scheduler");
                callCenter.setOpenBookUrl("http://openbook.smilebrands.com/scheduling/ui/");
                mongoOperations.save(callCenter);
            }
        }
    }

    @Test
    public void testCopyData(){
        Mongo mongo = null;
        try{
            logger.debug("start copy data from itchy to marge");
            Date dt = new Date();
            int count = 0;
            mongo = new Mongo("marge.bnd.corp");
            WriteConcern writeConcern = new WriteConcern(1);
            mongo.setWriteConcern(writeConcern);
            MongoOperations mongoOps = new MongoTemplate(mongo, "callCenter");
            List<InboundCall> inboundCalls = mongoOperations.find(new Query(Criteria.where("createDate").is("08-25-2014")),InboundCall.class);
            for(InboundCall inboundCall : inboundCalls){
                count++;
                mongoOps.save(inboundCall);
                logger.debug("save " + count + " out of " + inboundCalls.size() + " from itchy to bart");
            }
            logger.debug("time taken to copy: {}", (new Date().getTime() - dt.getTime()));

        }catch (Exception ex){
            ex.printStackTrace();
        }
        finally {
            logger.debug("close connection");
            mongo.close();
        }
    }

    @Test
    public void testGetCopyData(){
        Mongo mongo = null;
        try{
            logger.debug("start copy data from itchy to bart");
            Date dt = new Date();
            int count = 0;
            mongo = new Mongo("marge.bnd.corp");
            MongoOperations mongoOps = new MongoTemplate(mongo, "callCenter");
            List<InboundCall> inboundCalls = mongoOps.find(new Query(Criteria.where("createDate").is("08-20-2014")),InboundCall.class);
            logger.debug("time taken to get " + inboundCalls.size() + " record(s): {}", (new Date().getTime() - dt.getTime()));
        }catch (Exception ex){
            ex.printStackTrace();
        }
    }

    @Test
    public void updateScreenPopTimeWithUnloadDateTime(){
        List<ScreenPopTime> screenPopTimes = mongoOperations.find(new Query(Criteria.where("callDate").is("09-17-2014")),ScreenPopTime.class);
        for(int i=0; i<screenPopTimes.size(); i++){
            ScreenPopTime spt = screenPopTimes.get(i);
            AgentCall agentCall = mongoRepository.getAgentCall(spt.getUuid());
            if(agentCall != null){
                LoadedEmployee employee = callService.findLoadedEmployee(agentCall, spt.getEmployeeNumber(), spt.getScreenType());
                if(employee != null && employee.getUnloadedDate() != null && employee.getUnloadedDate().trim().length() > 0
                        && employee.getUnloadedTime() != null && employee.getUnloadedTime().trim().length() > 0){
                    Date dt = VelocityUtils.parseDate(employee.getUnloadedDate() + " " + employee.getUnloadedTime(), "MM-dd-yyyy HHmmss");
                    spt.setUnloadDateTime(VelocityUtils.getDateAsString(dt, "yyyy-MM-dd'T'HH:mm:ss"));
                    logger.debug("update " + (i+1) + " of " + screenPopTimes.size());
                    mongoRepository.persistScreenPopTime(spt);
                }
            }
        }
    }

    @Test
    public void testLoadScreenPopTime(){
        String start = null;
        String end = null;
        List<ScreenPopTime> result = callService.getScreenPops(start, end);
        logger.debug("result for " + start + " - " + end + ": {}", result);
        start = "09-14-2014";
        result = callService.getScreenPops(start, end);
        logger.debug("result for " + start + " - " + end + ": {}", result);
        start = "09-13-2014";
        end = "09-12-2014";
        result = callService.getScreenPops(start, end);
        logger.debug("result for " + start + " - " + end + ": {}", result);
        start = "09-13-2014";
        end = "09-14-2014";
        result = callService.getScreenPops(start, end);
        logger.debug("result for " + start + " - " + end + ": {}", result);
    }

    @Test
    public void testGetProviderForFacility(){
        List<Employee> providers = facilityRepository.findFacilityProvider(10300);
        for(Employee provider : providers){
            logger.debug("{}", provider);
        }
    }
      
    @Test
    public void testGetDateAsString(){
        Calendar cal = Calendar.getInstance();
        cal.set(2014, Calendar.NOVEMBER, 11, 6, 8, 0);
        String dtStr = VelocityUtils.getDateAsString(cal.getTime(), "yyyy-MM-dd HH:mm:ss");
        logger.debug("yyyy-MM-dd HH:mm:ss format: {}", dtStr);
        logger.debug("compare with 2014-11-11 06:08:00: {}", dtStr.compareTo("2014-11-11 06:08:00"));
        logger.debug("compare with 2014-11-10 06:08:00: {}", dtStr.compareTo("2014-11-10 06:08:00"));
        logger.debug("compare with 2014-10-11 06:08:00: {}", dtStr.compareTo("2014-10-11 06:08:00"));
        logger.debug("compare with 2013-11-11 06:08:00: {}", dtStr.compareTo("2013-11-11 06:08:00"));
        logger.debug("compare with 2015-11-11 06:08:00: {}", dtStr.compareTo("2015-11-11 06:08:00"));
        logger.debug("compare with 2014-12-11 06:08:00: {}", dtStr.compareTo("2014-12-11 06:08:00"));
        logger.debug("compare with 2014-11-11 05:08:00: {}", dtStr.compareTo("2014-11-11 05:08:00"));
    }

    @Test
    public void testConvertDateFormatForScreenPopTime(){
        List<ScreenPopTime> list = mongoOperations.find(new Query(Criteria.where("callDate").exists(true)),ScreenPopTime.class);
        logger.debug("list size: {}", list.size());
        for(ScreenPopTime spt : list){
            Date dt = VelocityUtils.parseDate(spt.getCallDate(), "MM-dd-yyyy");
            if(dt != null){
                spt.setCallDate(VelocityUtils.getDateAsString(dt, "yyyy-MM-dd"));
                mongoRepository.persistScreenPopTime(spt);
            }
        }
    }

    @Test
    public void funWithStateTime(){
        State[] states = State.values();
        for(State state : states){
            logger.debug("current time in " + state.getStateName() + " is " + DateTimeUtils.getLoadedTimeByLocationOffset(state.getOffset()));
        }
    }

    @Test
    public void populatePartnerInfo(){
        PartnerConfig pc = new PartnerConfig();
        pc.setPartnerId("100");
        pc.setPartnerName("RDI");
        pc.setAgentStart(10001);
        pc.setAgentEnd(10025);
        pc.setCreatedOn(new Date());
        pc.setCreatedBy(102029);
        pc.getAgents().add(new PartnerAgent(10001, "Brandy", "Lyon", "<EMAIL>", 102029));
        pc.getAgents().add(new PartnerAgent(10002, "Chandra", "Copeland", "<EMAIL>", 102029));
        pc.getAgents().add(new PartnerAgent(10003, "Damon", "Snellgrove", "<EMAIL>", 102029));
        pc.getAgents().add(new PartnerAgent(10004, "Janet", "Kaiser", "<EMAIL>", 102029));
        pc.getAgents().add(new PartnerAgent(10005, "Karen", "Johnson", "<EMAIL>", 102029));
        pc.getAgents().add(new PartnerAgent(10006, "Mario", "Puttman", "<EMAIL>", 102029));
        pc.getAgents().add(new PartnerAgent(10007, "Michael", "Arnold", "<EMAIL>", 102029));
        pc.getAgents().add(new PartnerAgent(10008, "Nathan", "Triska", "<EMAIL>", 102029));
        pc.getAgents().add(new PartnerAgent(10009, "Jessica", "Hidalgo", "<EMAIL>", 102029));
        pc.getAgents().add(new PartnerAgent(100010, "Cody", "Best", "<EMAIL>", 102029));
        pc.getAgents().add(new PartnerAgent(100011, "Laura", "Dooley", "<EMAIL>", 102029));
        pc.getAgents().add(new PartnerAgent(100011, "James", "Cappa", "<EMAIL>", 102029));

        mongoOperations.save(pc);
    }

    @Test
    public void validatePhoneNumber(){
        String query = "select distinct f.FACILITY_ID, f.FACILITY_NAME, p.PHONE_NUMBER as phone_number, px.PHONE_NUMBER as fax_number ";
        query += "from FACILITY f ";
        query += "join QSI_CLINIC_EXTENSION qce on qce.FACILITY_ID = f.FACILITY_ID ";
        query += "join V_FACILITY_HIERARCHY fh on fh.FACILITY_ID = f.FACILITY_ID and fh.AREA_ID is not null and fh.MARKET_ID is not null ";
        query += "LEFT OUTER JOIN FACILITY_PHONE_NUMBER fp ON fp.FACILITY_ID = f.FACILITY_ID AND fp.PHONE_NUMBER_TYPE = 'VOICE' AND fp.UNLINK_DATETIME IS NULL ";
        query += "LEFT OUTER JOIN PHONE_NUMBER p ON fp.PHONE_NUMBER_ID = p.PHONE_NUMBER_ID ";
        query += "LEFT OUTER JOIN FACILITY_PHONE_NUMBER fxp ON fxp.FACILITY_ID = f.FACILITY_ID AND fxp.PHONE_NUMBER_TYPE = 'FAX' AND fxp.UNLINK_DATETIME IS NULL ";
        query += "LEFT OUTER JOIN PHONE_NUMBER px ON fxp.PHONE_NUMBER_ID = px.PHONE_NUMBER_ID ";
        query += "where f.INACTIVATION_DATETIME is null and f.CLOSED_DATE is null and f.FACILITY_TYPE_ID = 1 ";
        query += "order by f.facility_id";
        List<Map<String, Object>> list = jdbcTemplate.query(query, new RowMapper<Map<String, Object>>() {
            @Override
            public Map<String, Object> mapRow(ResultSet rs, int rowNum) throws SQLException {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("FACILITY_ID", rs.getInt("FACILITY_ID"));
                map.put("PHONE_NUMBER", rs.getLong("PHONE_NUMBER"));
                map.put("FAX_NUMBER", rs.getLong("FAX_NUMBER"));
                return map;
            }
        });
        List<LinkedHashMap<String, String>> mismatch = new ArrayList<LinkedHashMap<String, String>>();
        int count = 1;
        for(Map<String, Object> facility : list){
//            logger.debug(count + "/" + facility);

            Integer facilityId = (Integer)facility.get("FACILITY_ID");
            Long phoneNumber = (Long)facility.get("PHONE_NUMBER");
            Long faxNumber = (Long)facility.get("FAX_NUMBER");
            PhoneNumber phone = mongoRepository.findPhoneNumberByFacility(facilityId, "ML");
            if(phone != null && phoneNumber != null && !phone.getPhoneNumber().equals(phoneNumber)){
                logger.debug(count + "/" + facilityId + " does not have match phone: FINDA is [" + phoneNumber + "], screen pop is [" + phone.getPhoneNumber() + "]");
                LinkedHashMap<String, String> tmp = new LinkedHashMap<String, String>();
                tmp.put("Facility ID", facilityId.toString());
                tmp.put("FindA Number", phoneNumber.toString());
                tmp.put("ScreenPop Number", phone.getPhoneNumber().toString());
                phone = mongoRepository.findPhoneNumber(phoneNumber);
                boolean differentOffice = false;
                if(phone != null){
                    logger.debug("\t" + phoneNumber + " is associated with facility[" + phone.getFacilityId() + "] as type[" + phone.getPhoneType() + "] and active status[" + phone.isActive() + "] in mongo.");
                    if(!phone.getFacilityId().equals(facilityId)){
                        logger.debug("\t***Different facility.");
                        differentOffice = true;
                        tmp.put("Other Facility ID", phone.getFacilityId().toString());
                    }
                }
                if(differentOffice == false){
                    tmp.put("Other Facility ID", "");
                }
                List<PhoneNumber> phoneNumbers = mongoOperations.find(new Query(Criteria.where("facilityId").is(facilityId).and("isActive").is(true).and("phoneType").is("ML")), PhoneNumber.class);
                tmp.put("Number of active ML", phoneNumbers.size() + "");
                mismatch.add(tmp);
                count++;
            }
        }
        VelocityUtils.convertListMapToCSV(mismatch, "Mismatch phone number between FINDA and CTI", true);
    }

    @Test
    public void validateProviderWithCredentialing(){
        List<Facility> facilities = mongoRepository.findAllFacility();
        Set<Integer> missingFacilities = new HashSet<Integer>();
        for(Facility facility : facilities){
            List<Employee> dentists = facility.getEmployees().get("Dentists");
            int hasCredentialing = 0;
            for(Employee d : dentists){
                String title = d.getTitle() != null ? d.getTitle().toLowerCase() : "";
                if(title.indexOf("hygienist") == -1 && title.indexOf("house") == -1){
                    hasCredentialing++;
                }
            }
            if(hasCredentialing == 0){
                missingFacilities.add(facility.getFacilityId());
            }
        }
        logger.debug("missing facilities: {}", missingFacilities);
    }

    @Test
    public void testNeighborSpecialty(){
        Facility facility = callService.findFacilityByFacilityId(10680);
        List<Facility> neighboringOffices = facility.getNeighborFacilities();
        for(Facility f : neighboringOffices){
            Facility df = callService.findFacilityByFacilityId(f.getFacilityId());
            if(df != null){
                f.setEmployees(df.getEmployees());
            }
            logger.debug("Specialty Note for [" + f.getName() + "]: {}", f.getSpecialtySchedule());
        }
    }

    @Test
    public void testGetCiscoAgentCall(){
        List<Integer> agentIds = new ArrayList<Integer>();
        Calendar cal = Calendar.getInstance();
        cal.set(2015, Calendar.SEPTEMBER, 14);
//        agentIds.add(102104);
        List<Map<String, Object>> list = callService.getCiscoAgentCall(true, new ArrayList<CallCenterEmployee>(), cal.getTime());
        for(Map<String, Object> map : list){
            logger.debug("{}", map);
        }
    }

    @Test
    public void testToPopulatePriority(){
        List<Facility> facilities = mongoRepository.findAllFacility();
        Map<Integer, Set<Integer>> priorities = new HashMap<Integer, Set<Integer>>();
        Integer priority = 0;
        Integer threshHold = facilities.size() / 5;
        for(int i=0; i<facilities.size(); i++){
            if(i % threshHold == 0 && priority < 5){
                priority++;
            }
            Facility facility = facilities.get(i);
            if(priorities.get(priority) == null){
                priorities.put(priority, new HashSet<Integer>());
            }
            facility.setCsrPriority(priority);
            facility.setPsrPriority(priority);
            mongoRepository.persistFacilityDetail(facility);
            priorities.get(priority).add(facility.getFacilityId());
        }
        for(Integer key : priorities.keySet()){
            logger.debug("priority " + key + " has [" + priorities.get(key) + "]");
        }
    }

    @Test
    public void modifySpanishAddress(){
        Facility facility = mongoRepository.findFacilityDetail(12090);
        facility.setLanguage("sp");
        logger.debug("{}", facility.getShortAddress());

        facility = mongoRepository.findFacilityDetail(13100);
        facility.setLanguage("sp");
        logger.debug("{}", facility.getShortAddress());

        facility = mongoRepository.findFacilityDetail(10630);
        facility.setLanguage("sp");
        logger.debug("{}", facility.getShortAddress());
    }

    @Test
    public void modifyJsonString(){
//        String str = "{\\\"uuid\\\":\\\"e9f408a2-474a-41a6-85f7-0e26a550e6b3\\\",\\\"employeeNumber\\\":\\\"114981\\\",\\\"callCenter\\\":\\\"Plano\\\",\\\"screenType\\\":\\\"PSR\\\",\\\"callDateTime\\\":\\\"2015-03-25T08:00:00\\\",\\\"reason\\\":\\\"NP-CASH\\\",\\\"resolution\\\":\\\"CHANGED\\\",\\\"patients\\\":[{\\\"agentCallLogId\\\":\\\"925349\\\",\\\"apptCallLogId\\\":\\\"4679931\\\",\\\"patientId\\\":\\\"9001330\\\",\\\"facilityId\\\":\\\"20120\\\",\\\"nextAppointmentDateTime\\\":\\\"\\\",\\\"emailCaptured\\\":false,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":false},{\\\"agentCallLogId\\\":\\\"923695\\\",\\\"apptCallLogId\\\":\\\"4746303\\\",\\\"patientId\\\":\\\"9070630\\\",\\\"facilityId\\\":\\\"30240\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-09-15T16:00:00\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":false},{\\\"agentCallLogId\\\":\\\"981762\\\",\\\"apptCallLogId\\\":\\\"4791907\\\",\\\"patientId\\\":\\\"9790270\\\",\\\"facilityId\\\":\\\"35120\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-09-30T14:00:00\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":true},{\\\"agentCallLogId\\\":\\\"916252\\\",\\\"apptCallLogId\\\":\\\"4657662\\\",\\\"patientId\\\":\\\"9463640\\\",\\\"facilityId\\\":\\\"31240\\\",\\\"nextAppointmentDateTime\\\":\\\"\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":false},{\\\"agentCallLogId\\\":\\\"975077\\\",\\\"apptCallLogId\\\":\\\"4784346\\\",\\\"patientId\\\":\\\"9030750\\\",\\\"facilityId\\\":\\\"31080\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-09-24T13:30:00\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":false},{\\\"agentCallLogId\\\":\\\"927479\\\",\\\"apptCallLogId\\\":\\\"4672174\\\",\\\"patientId\\\":\\\"9064120\\\",\\\"facilityId\\\":\\\"30190\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-08-21T11:00:00\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":false},{\\\"agentCallLogId\\\":\\\"925277\\\",\\\"apptCallLogId\\\":\\\"4668448\\\",\\\"patientId\\\":\\\"9115520\\\",\\\"facilityId\\\":\\\"30530\\\",\\\"nextAppointmentDateTime\\\":\\\"\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":false},{\\\"agentCallLogId\\\":\\\"789550\\\",\\\"apptCallLogId\\\":\\\"4448989\\\",\\\"patientId\\\":\\\"9033580\\\",\\\"facilityId\\\":\\\"42090\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-07-14T15:30:00\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":true},{\\\"agentCallLogId\\\":\\\"703949\\\",\\\"apptCallLogId\\\":\\\"4245576\\\",\\\"patientId\\\":\\\"9008640\\\",\\\"facilityId\\\":\\\"10140\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-05-29T15:30:00\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":true},{\\\"agentCallLogId\\\":\\\"708743\\\",\\\"apptCallLogId\\\":\\\"4296169\\\",\\\"patientId\\\":\\\"9060210\\\",\\\"facilityId\\\":\\\"30190\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-06-08T14:00:00\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":false},{\\\"agentCallLogId\\\":\\\"708741\\\",\\\"apptCallLogId\\\":\\\"4296168\\\",\\\"patientId\\\":\\\"9060220\\\",\\\"facilityId\\\":\\\"30190\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-06-08T15:00:00\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":false},{\\\"agentCallLogId\\\":\\\"701514\\\",\\\"apptCallLogId\\\":\\\"4242677\\\",\\\"patientId\\\":\\\"9008640\\\",\\\"facilityId\\\":\\\"10140\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-05-29T15:30:00\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":true},{\\\"agentCallLogId\\\":\\\"971834\\\",\\\"apptCallLogId\\\":\\\"4778076\\\",\\\"patientId\\\":\\\"9026530\\\",\\\"facilityId\\\":\\\"31110\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-09-22T08:30:00\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":true},{\\\"agentCallLogId\\\":\\\"921288\\\",\\\"apptCallLogId\\\":\\\"4752697\\\",\\\"patientId\\\":\\\"9066190\\\",\\\"facilityId\\\":\\\"30220\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-09-17T10:30:00\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":false},{\\\"agentCallLogId\\\":\\\"929200\\\",\\\"apptCallLogId\\\":\\\"4725271\\\",\\\"patientId\\\":\\\"9777470\\\",\\\"facilityId\\\":\\\"35210\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-09-14T12:00:00\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":true},{\\\"agentCallLogId\\\":\\\"640480\\\",\\\"apptCallLogId\\\":\\\"4143199\\\",\\\"patientId\\\":\\\"9397550\\\",\\\"facilityId\\\":\\\"30180\\\",\\\"nextAppointmentDateTime\\\":\\\"\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":false},{\\\"agentCallLogId\\\":\\\"659349\\\",\\\"apptCallLogId\\\":\\\"4276392\\\",\\\"patientId\\\":\\\"9067840\\\",\\\"facilityId\\\":\\\"30590\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-06-05T09:00:00\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":false},{\\\"agentCallLogId\\\":\\\"636390\\\",\\\"apptCallLogId\\\":\\\"4121666\\\",\\\"patientId\\\":\\\"9703100\\\",\\\"facilityId\\\":\\\"35270\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-05-01T14:30:00\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":true},{\\\"agentCallLogId\\\":\\\"636842\\\",\\\"apptCallLogId\\\":\\\"4151019\\\",\\\"patientId\\\":\\\"9397550\\\",\\\"facilityId\\\":\\\"31300\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-05-08T09:00:00\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":true},{\\\"agentCallLogId\\\":\\\"549896\\\",\\\"apptCallLogId\\\":\\\"3998198\\\",\\\"patientId\\\":\\\"9046380\\\",\\\"facilityId\\\":\\\"30160\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-04-01T16:00:00\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":false},{\\\"agentCallLogId\\\":\\\"698401\\\",\\\"apptCallLogId\\\":\\\"4244316\\\",\\\"patientId\\\":\\\"9022190\\\",\\\"facilityId\\\":\\\"31160\\\",\\\"nextAppointmentDateTime\\\":\\\"\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":false},{\\\"agentCallLogId\\\":\\\"671833\\\",\\\"apptCallLogId\\\":\\\"4250605\\\",\\\"patientId\\\":\\\"9057140\\\",\\\"facilityId\\\":\\\"30140\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-06-01T15:00:00\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":true},{\\\"agentCallLogId\\\":\\\"711668\\\",\\\"apptCallLogId\\\":\\\"4299865\\\",\\\"patientId\\\":\\\"9051750\\\",\\\"facilityId\\\":\\\"34050\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-06-11T16:00:00\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":true},{\\\"agentCallLogId\\\":\\\"711667\\\",\\\"apptCallLogId\\\":\\\"4325179\\\",\\\"patientId\\\":\\\"9051760\\\",\\\"facilityId\\\":\\\"34050\\\",\\\"nextAppointmentDateTime\\\":\\\"\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":false},{\\\"agentCallLogId\\\":\\\"672383\\\",\\\"apptCallLogId\\\":\\\"4205593\\\",\\\"patientId\\\":\\\"9712120\\\",\\\"facilityId\\\":\\\"35030\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-05-18T10:00:00\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":false},{\\\"agentCallLogId\\\":\\\"679958\\\",\\\"apptCallLogId\\\":\\\"4291298\\\",\\\"patientId\\\":\\\"9063840\\\",\\\"facilityId\\\":\\\"30100\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-06-10T09:00:00\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"allowCredit\\\":true}]}";
//        String str = "{\\\"uuid\\\":\\\"e9f408a2-474a-41a6-85f7-0e26a550e6b3\\\",\\\"currentFacilityId\\\":\\\"30690\\\",\\\"ani\\\":\\\"**********\\\",\\\"patients\\\":[{\\\"sequence\\\":1,\\\"patientId\\\":\\\"9008250\\\",\\\"facilityId\\\":\\\"30690\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-11-10T17:00:00\\\",\\\"patientLinkDateTime\\\":\\\"2015-10-01T16:43:35\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":false,\\\"isOrtho\\\":false,\\\"empEntered\\\":\\\"114981\\\",\\\"screenType\\\":\\\"psr\\\"}],\\\"loadedEmployees\\\":[{\\\"employeeNumber\\\":\\\"114981\\\",\\\"screenType\\\":\\\"psr\\\",\\\"reasonCode\\\":\\\"NP-CASH\\\",\\\"reasonDateTime\\\":\\\"2015-10-01T16:43:35\\\",\\\"resolutionCode\\\":\\\"CHANGED\\\",\\\"resolutionDateTime\\\":\\\"2015-10-01T16:43:35\\\"}]}";
        String str = "{\\\"uuid\\\":\\\"5163ddd4-7c2b-485d-a2aa-9fc63fb27cb5\\\",\\\"currentFacilityId\\\":\\\"10210\\\",\\\"ani\\\":\\\"**********\\\",\\\"patients\\\":[{\\\"sequence\\\":1,\\\"patientId\\\":\\\"9013530\\\",\\\"facilityId\\\":\\\"10750\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-06-06T12:30:00\\\",\\\"patientLinkDateTime\\\":\\\"2015-05-18T11:57:04\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":true,\\\"isOrtho\\\":false,\\\"empEntered\\\":\\\"115303\\\",\\\"screenType\\\":\\\"psr\\\"},{\\\"sequence\\\":2,\\\"patientId\\\":\\\"9013540\\\",\\\"facilityId\\\":\\\"10750\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-06-06T14:00:00\\\",\\\"patientLinkDateTime\\\":\\\"2015-05-18T11:57:04\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":true,\\\"isOrtho\\\":false,\\\"empEntered\\\":\\\"115303\\\",\\\"screenType\\\":\\\"psr\\\"},{\\\"sequence\\\":3,\\\"patientId\\\":\\\"9753480\\\",\\\"facilityId\\\":\\\"10210\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-06-06T09:30:00\\\",\\\"patientLinkDateTime\\\":\\\"2015-05-18T11:57:04\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":true,\\\"isOrtho\\\":true,\\\"empEntered\\\":\\\"115303\\\",\\\"screenType\\\":\\\"psr\\\"},{\\\"sequence\\\":4,\\\"patientId\\\":\\\"9753490\\\",\\\"facilityId\\\":\\\"10210\\\",\\\"nextAppointmentDateTime\\\":\\\"2015-06-06T10:00:00\\\",\\\"patientLinkDateTime\\\":\\\"2015-05-18T11:57:04\\\",\\\"emailCaptured\\\":true,\\\"insuranceWaiting\\\":true,\\\"isOrtho\\\":true,\\\"empEntered\\\":\\\"115303\\\",\\\"screenType\\\":\\\"psr\\\"}],\\\"loadedEmployees\\\":[{\\\"employeeNumber\\\":\\\"115303\\\",\\\"screenType\\\":\\\"psr\\\",\\\"reasonCode\\\":\\\"NP-STATE\\\",\\\"reasonDateTime\\\":\\\"2015-05-18T11:57:04\\\",\\\"resolutionCode\\\":\\\"SCHEDULED\\\",\\\"resolutionDateTime\\\":\\\"2015-05-18T11:57:04\\\"}]}";
        str = str.replace("\\", "");
        logger.debug("str: {}", str);
    }

    @Test
    public void testGetCallbackReport(){
        Query query = new Query();
        query.with(new Sort(Sort.Direction.ASC, "requestDateTime"));
        List<CallbackRequest> callbackRequests = mongoOperations.find(query, CallbackRequest.class);
        String content = "UUID,Call Center,Original Session ID,Callback Session ID,DNIS,ANI,Call Time,Request Time,Handled Time,Handled by Agent";
        int cnt = 0;
        for(CallbackRequest callbackRequest : callbackRequests){
            cnt++;
            logger.debug(cnt + "/" + callbackRequest.getUuid() + ":::" + callbackRequest.getRequestDateTime());
            InboundCall inboundCall = mongoRepository.getInboundCall(callbackRequest.getUuid());
            AgentCall agentCall = mongoRepository.getAgentCall(callbackRequest.getUuid());
            content += "\n" + callbackRequest.getUuid() + ",";
            content += callbackRequest.getCallCenter() + ",";
            int offset = callbackRequest.getCallCenter() != null && callbackRequest.getCallCenter().equalsIgnoreCase("Plano") ? -6 : -8;
            if(inboundCall != null){
                if(inboundCall.getSessionIds().size() > 0){
                    content += inboundCall.getSessionIds().get(0) + ",";
                    content += inboundCall.getSessionId();
                }else{
                    content += inboundCall.getSessionId() + ",";
                }

                content += "," + inboundCall.getDnis() + "," + inboundCall.getAni();
                content += "," + inboundCall.getCreateDate() + " " + inboundCall.getCreateTime();
                String requestDateTime = "";
                String handledDateTime = "";
                for(InboundCallEvent inboundCallEvent : inboundCall.getInboundCallEvents()){
                    if(inboundCallEvent.getEventId().equalsIgnoreCase("Request to call back")){

                        requestDateTime = VelocityUtils.getDateAsString(DateTimeUtils.getTimeByLocationOffset(inboundCallEvent.getEventDateTime(), offset), "yyyy-MM-dd HH:mm:ss");
                    }
                    if(inboundCallEvent.getEventId().equalsIgnoreCase("Handle call back request")){
                        handledDateTime = VelocityUtils.getDateAsString(DateTimeUtils.getTimeByLocationOffset(inboundCallEvent.getEventDateTime(), offset), "yyyy-MM-dd HH:mm:ss");
                    }
                }
                content += "," + requestDateTime;
                content += "," + handledDateTime;
            }else{
                content += ",,,,,,";
            }
            if(agentCall != null){
                String handledAgents = "";
                for(LoadedEmployee loadedEmployee : agentCall.getLoadedEmployees()){
                    if(loadedEmployee.getEmployeeNumber() > 0){
                        handledAgents += loadedEmployee.getEmployeeNumber() + ":::" + loadedEmployee.getScreenType() + "<comma/>";
                    }
                }
                content += "," + handledAgents;
            }else{
                content += ",";
            }
        }
        logger.debug(content);
    }

    @Test
    public void testMigrateData(){
        Mongo itchy = new MongoClient("itchy.bnd.corp");
        MongoOperations itchyOps = new MongoTemplate(itchy, "callCenter");

//        List<Task> tasks = itchyOps.find(new Query(), Task.class);
//        for(Task t : tasks){
//            mongoOperations.save(t);
//        }
//        List<CallCenter> callCenters = itchyOps.find(new Query(), CallCenter.class);
//        for(CallCenter cc : callCenters){
//            mongoOperations.save(cc);
//        }
//        List<Facility> facilities = itchyOps.find(new Query(), Facility.class);
//        for(Facility facility : facilities){
//            mongoOperations.save(facility);
//        }
//        List<PhoneNumber> phoneNumbers = itchyOps.find(new Query(), PhoneNumber.class);
//        for(PhoneNumber pn : phoneNumbers){
//            mongoOperations.save(pn);
//        }
//        List<Npa> npas = itchyOps.find(new Query(), Npa.class);
//        for(Npa npa : npas){
//            mongoOperations.save(npa);
//        }
//        List<CallCenterEmployee> callCenterEmployees = itchyOps.find(new Query(), CallCenterEmployee.class);
//        for(CallCenterEmployee cce : callCenterEmployees){
//            mongoOperations.save(cce);
//        }
//        List<CallResolutionCode> callResolutionCodes = itchyOps.find(new Query(), CallResolutionCode.class);
//        for(CallResolutionCode ccr : callResolutionCodes){
//            mongoOperations.save(ccr);
//        }
//        List<ZipFacility> zipFacilityList = itchyOps.find(new Query(), ZipFacility.class);
//        for(ZipFacility zf : zipFacilityList){
//            mongoOperations.save(zf);
//        }
        List<AgentCall> agentCalls = itchyOps.find(new Query(), AgentCall.class);
        logger.debug("number of agent calls: {}", agentCalls.size());
        mongoOperations.insert(agentCalls, AgentCall.class);
    }

}
