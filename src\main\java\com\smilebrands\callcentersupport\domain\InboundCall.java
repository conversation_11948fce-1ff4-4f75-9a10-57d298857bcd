package com.smilebrands.callcentersupport.domain;

import com.smilebrands.callcentersupport.domain.constants.State;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.springframework.data.annotation.Id;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import java.io.Serializable;
import java.util.*;

/**
 * User: <PERSON><PERSON>
 * Date: 6/24/14
 */
@XmlRootElement
public class InboundCall implements Serializable {


    public final static Map<String, String> CALL_EVENT_MAP = new HashMap<String, String>();

    static {
        CALL_EVENT_MAP.put("101", "No PSR");
        CALL_EVENT_MAP.put("102", "No CSR");
        CALL_EVENT_MAP.put("103", "Transfer to Office");
        CALL_EVENT_MAP.put("104", "Timeout");
        CALL_EVENT_MAP.put("105", "New Patient");
        CALL_EVENT_MAP.put("106", "Existing Patient");
        CALL_EVENT_MAP.put("107", "Copy Call");
        CALL_EVENT_MAP.put("108", "Request to call back");
        CALL_EVENT_MAP.put("109", "Handle call back request");
        CALL_EVENT_MAP.put("201", "Transfer to RDI");
        CALL_EVENT_MAP.put("202", "Received by RDI");
    }

    @Id
    private String uuid;
    private Long dnis;
    private Long ani;
    private String phoneType;
    private String sessionId;
    private List<String> sessionIds = new ArrayList<String>();
    private Integer targetFacilityId;
    private String language = "en";
    private Integer mainMenuNumberPress = -1;
    private List<Integer> mainMenuNumberPresses = new ArrayList<Integer>();
    private Integer mainMenuNumberPressOverride = -1;
    private PhoneNumber phoneNumber;
    private Date createDateTime = new Date();
    private String createDate;
    private String createTime;
    private List<PhoneNumber> previousPhoneNumbers = new ArrayList<PhoneNumber>();
    private List<ScreenPopUrl> screenPopUrls = new ArrayList<ScreenPopUrl>();

    private Integer pressOption;
    private Patient patient;

    private Boolean aniRoutingRuleDetected;
    private Boolean aniRoutingIsSpam;
    private String aniRoutingRuleQueueName;

    private boolean psrSupported = true;
    private boolean csrSupported = true;
    private Integer psrPriority = 3;
    private Integer csrPriority = 3;
    private boolean isOpen = true;

    private Integer priorAgentId = 0;
    private Date priorAgentDate;

    private boolean matched = false;
    private boolean wireless = false;
    private boolean officeToOffice = false;
    private boolean existingPatient = false;
    private String state;
    private String zipCode;
    private String timeZone;
    private String ttsState;
    private String ttsAppointment;
    private String ttsFacility;
    private String ttsFacilityHour;

    private List<InboundCallEvent> inboundCallEvents = new ArrayList<InboundCallEvent>();

    private String copiedFromUUID;

    private boolean allowCallback = true;

    public InboundCall(){
        this.createDate = VelocityUtils.getDateAsString(this.createDateTime, null);
        this.createTime = VelocityUtils.getDateAsString(this.createDateTime, "HHmmss");
    }

    public Long getDnis() {
        return dnis;
    }

    public void setDnis(Long dnis) {
        this.dnis = dnis;
    }

    public Long getAni() {
        return ani;
    }

    public void setAni(Long ani) {
        this.ani = ani;
    }

    public String getPhoneType() {
        return phoneType;
    }

    public void setPhoneType(String phoneType) {
        this.phoneType = phoneType;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public List<String> getSessionIds() {
        return sessionIds;
    }

    public void setSessionIds(List<String> sessionIds) {
        this.sessionIds = sessionIds;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @XmlTransient
    public List<InboundCallEvent> getInboundCallEvents() {
        return inboundCallEvents;
    }

    public void setInboundCallEvents(List<InboundCallEvent> inboundCallEvents) {
        this.inboundCallEvents = inboundCallEvents;
    }

    public PhoneNumber getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(PhoneNumber phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public Integer getTargetFacilityId() {
        return targetFacilityId;
    }

    public void setTargetFacilityId(Integer targetFacilityId) {
        this.targetFacilityId = targetFacilityId;
    }

    @XmlTransient
    public List<ScreenPopUrl> getScreenPopUrls() {
        return screenPopUrls;
    }

    public void setScreenPopUrls(List<ScreenPopUrl> screenPopUrls) {
        this.screenPopUrls = screenPopUrls;
    }

    @XmlTransient
    public List<PhoneNumber> getPreviousPhoneNumbers() {
        return previousPhoneNumbers;
    }

    public void setPreviousPhoneNumbers(List<PhoneNumber> previousPhoneNumbers) {
        this.previousPhoneNumbers = previousPhoneNumbers;
    }

    public Integer getPressOption() {
        return pressOption;
    }

    public void setPressOption(Integer pressOption) {
        this.pressOption = pressOption;
    }

    public Patient getPatient() {
        return patient;
    }

    public void setPatient(Patient patient) {
        this.patient = patient;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public Integer getMainMenuNumberPress() {
        return mainMenuNumberPress;
    }

    public void setMainMenuNumberPress(Integer mainMenuNumberPress) {
        this.mainMenuNumberPress = mainMenuNumberPress;
    }

    public List<Integer> getMainMenuNumberPresses() {
        return mainMenuNumberPresses;
    }

    public void setMainMenuNumberPresses(List<Integer> mainMenuNumberPresses) {
        this.mainMenuNumberPresses = mainMenuNumberPresses;
    }

    public Integer getMainMenuNumberPressOverride() {
        return mainMenuNumberPressOverride;
    }

    public void setMainMenuNumberPressOverride(Integer mainMenuNumberPressOverride) {
        this.mainMenuNumberPressOverride = mainMenuNumberPressOverride;
    }

    public Date getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(Date createDateTime) {
        this.createDateTime = createDateTime;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public boolean isMatched() {
        return matched;
    }

    public void setMatched(boolean matched) {
        this.matched = matched;
    }

    public boolean isWireless() {
        return wireless;
    }

    public void setWireless(boolean wireless) {
        this.wireless = wireless;
    }

    public boolean isOfficeToOffice() {
        return officeToOffice;
    }

    public void setOfficeToOffice(boolean officeToOffice) {
        this.officeToOffice = officeToOffice;
    }

    public boolean isExistingPatient() {
        return existingPatient;
    }

    public void setExistingPatient(boolean existingPatient) {
        this.existingPatient = existingPatient;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public String getTtsState() {
        try{
            State stateEnum = State.valueOf(state);
            ttsState = stateEnum.getStateName();
        }catch (Exception ex){
            if(state != null){
                System.out.println("Fail to convert state [" + state + "] to STATE enum");
            }
            ttsState = state;
        }
        return ttsState;
    }

    public void setTtsState(String ttsState) {
        this.ttsState = ttsState;
    }

    public String getTtsAppointment() {
        return ttsAppointment;
    }

    public void setTtsAppointment(String ttsAppointment) {
        this.ttsAppointment = ttsAppointment;
    }

    public String getTtsFacility() {
        return ttsFacility;
    }

    public void setTtsFacility(String ttsFacility) {
        this.ttsFacility = ttsFacility;
    }

    public String getTtsFacilityHour() {
        return ttsFacilityHour;
    }

    public void setTtsFacilityHour(String ttsFacilityHour) {
        this.ttsFacilityHour = ttsFacilityHour;
    }

    public String getCopiedFromUUID() {
        return copiedFromUUID;
    }

    public void setCopiedFromUUID(String copiedFromUUID) {
        this.copiedFromUUID = copiedFromUUID;
    }

    public boolean isAllowCallback() {
        return allowCallback;
    }

    public void setAllowCallback(boolean allowCallback) {
        this.allowCallback = allowCallback;
    }

    public boolean isPsrSupported() {
        return psrSupported;
    }

    public void setPsrSupported(boolean psrSupported) {
        this.psrSupported = psrSupported;
    }

    public boolean isCsrSupported() {
        return csrSupported;
    }

    public void setCsrSupported(boolean csrSupported) {
        this.csrSupported = csrSupported;
    }

    public Integer getPsrPriority() {
        return psrPriority;
    }

    public void setPsrPriority(Integer psrPriority) {
        this.psrPriority = psrPriority;
    }

    public Integer getCsrPriority() {
        return csrPriority;
    }

    public void setCsrPriority(Integer csrPriority) {
        this.csrPriority = csrPriority;
    }

    public boolean isOpen() {
        return isOpen;
    }

    public void setOpen(boolean isOpen) {
        this.isOpen = isOpen;
    }

    public Integer getPriorAgentId() {
        return priorAgentId;
    }

    public void setPriorAgentId(Integer priorAgentId) {
        this.priorAgentId = priorAgentId;
    }

    public Date getPriorAgentDate() {
        return priorAgentDate;
    }

    public void setPriorAgentDate(Date priorAgentDate) {
        this.priorAgentDate = priorAgentDate;
    }


    public Boolean getAniRoutingRuleDetected() {
        return aniRoutingRuleDetected;
    }

    public void setAniRoutingRuleDetected(Boolean aniRoutingRuleDetected) {
        this.aniRoutingRuleDetected = aniRoutingRuleDetected;
    }

    public Boolean getAniRoutingIsSpam() {
        return aniRoutingIsSpam;
    }

    public void setAniRoutingIsSpam(Boolean aniRoutingIsSpam) {
        this.aniRoutingIsSpam = aniRoutingIsSpam;
    }

    public String getAniRoutingRuleQueueName() {
        return aniRoutingRuleQueueName;
    }

    public void setAniRoutingRuleQueueName(String aniRoutingRuleQueueName) {
        this.aniRoutingRuleQueueName = aniRoutingRuleQueueName;
    }
}
