package com.smilebrands.callcentersupport.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.smilebrands.callcentersupport.domain.helper.DateIsoSerializer;

import java.util.Date;

/**
 * Created by phongpham on 10/23/14.
 */
public class ProviderInsurance {

    private Integer providerId;
    private Integer officeId;
    private Integer facility;
    private String payerName;
    private String payerStatus;
    private Date startDate;
    private Date endDate;
    private Date statusDate;
    private String effectiveDate;    //THIS FIELD IS STRING IN DATABASE AND THE DATA IS NOT WELL-FORMATTED AS DATE

    public Integer getProviderId() {
        return providerId;
    }

    public void setProviderId(Integer providerId) {
        this.providerId = providerId;
    }

    public Integer getOfficeId() {
        return officeId;
    }

    public void setOfficeId(Integer officeId) {
        this.officeId = officeId;
    }

    public Integer getFacility() {
        return facility;
    }

    public void setFacility(Integer facility) {
        this.facility = facility;
    }

    public String getPayerName() {
        return payerName;
    }

    public void setPayerName(String payerName) {
        this.payerName = payerName;
    }

    public String getPayerStatus() {
        return payerStatus;
    }

    public void setPayerStatus(String payerStatus) {
        this.payerStatus = payerStatus;
    }

    @JsonSerialize(using= DateIsoSerializer.class)
    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @JsonSerialize(using= DateIsoSerializer.class)
    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @JsonSerialize(using= DateIsoSerializer.class)
    public Date getStatusDate() {
        return statusDate;
    }

    public void setStatusDate(Date statusDate) {
        this.statusDate = statusDate;
    }

    public String getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(String effectiveDate) {
        this.effectiveDate = effectiveDate;
    }
}
