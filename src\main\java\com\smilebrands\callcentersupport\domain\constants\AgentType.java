package com.smilebrands.callcentersupport.domain.constants;

/**
 * Created by phong<PERSON><PERSON> on 1/21/16.
 */
public enum  AgentType {
    CSR,
    PSR,
    NA
    ;

    public static AgentType getByValue(String v){
        AgentType result = AgentType.NA;
        if(v != null && v.trim().length() > 0){
            v = v.trim().toLowerCase();
            if(v.equals("csr")){
                result = AgentType.CSR;
            }else if(v.equals("psr")){
                result = AgentType.PSR;
            }
        }
        return result;
    }
}
