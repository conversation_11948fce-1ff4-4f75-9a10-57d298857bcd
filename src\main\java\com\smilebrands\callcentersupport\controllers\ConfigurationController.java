package com.smilebrands.callcentersupport.controllers;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.smilebrands.callcentersupport.async.BackgroundExecutor;
import com.smilebrands.callcentersupport.domain.ActionLog;
import com.smilebrands.callcentersupport.domain.CallCenterEmployee;
import com.smilebrands.callcentersupport.domain.CallResolutionCode;
import com.smilebrands.callcentersupport.domain.Campaign;
import com.smilebrands.callcentersupport.domain.Facility;
import com.smilebrands.callcentersupport.domain.PhoneNumber;
import com.smilebrands.callcentersupport.domain.PhoneNumberRoutingRules;
import com.smilebrands.callcentersupport.domain.ResponseWrapper;
import com.smilebrands.callcentersupport.repo.MongoRepositoryImpl;
import com.smilebrands.callcentersupport.service.CallService;
import com.smilebrands.callcentersupport.service.CommunicationService;
import com.smilebrands.callcentersupport.service.ConfigurationService;
import com.smilebrands.callcentersupport.util.BaseJsonConverter;


/**
 * User: Marlin Clark
 * Date: 6/25/14
 */
@Controller
public class ConfigurationController extends AbstractController {

    @Autowired
    private ConfigurationService configurationService;

    @Autowired
    private CallService callService;

    @Autowired
    private CommunicationService communicationService;

    @Autowired
    private BackgroundExecutor backgroundExecutor;


    /*
     *  Load Secure Page.
     */
    @RequestMapping(value="secure", method= RequestMethod.GET)
    public String loadSecureSpace(ModelMap model, HttpServletRequest request) {
        Calendar cal = Calendar.getInstance();
        model.addAttribute("currentYear", cal.get(Calendar.YEAR));
        model.addAttribute("currentMonth", cal.get(Calendar.MONTH) + 1);
        Integer employeeNumber = getEmployeeNumberFromCookie(request);
        boolean hasAgentList = false;
        if(employeeNumber != null){
            List<CallCenterEmployee> agentList = configurationService.getSupervisedAgents(employeeNumber);
            model.addAttribute("agentList", agentList);
            hasAgentList = agentList.size() > 1;
        }
        model.addAttribute("hasAgentList", hasAgentList);
        Cookie canUpdateMongoDB = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_UPDATE_MONGODB");
        if(canUpdateMongoDB != null && canUpdateMongoDB.getValue().equalsIgnoreCase("true")){
            model.addAttribute("canUpdateMongoDB", true);
        }
        Cookie canEditCode = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_EDIT_REASON_RESOLUTION");
        if(canEditCode != null && canEditCode.getValue().equalsIgnoreCase("true")){
            model.addAttribute("canUpdateReasonResolution", true);
        }
        Cookie canEditPhone = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_EDIT_PHONE_NUMBER");
        if(canEditPhone != null && canEditPhone.getValue().equalsIgnoreCase("true")){
            model.addAttribute("canEditPhoneNumber", true);
        }
        Cookie canEditFacility = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_EDIT_FACILITY");
        if(canEditFacility != null && canEditFacility.getValue().equalsIgnoreCase("true")){
            model.addAttribute("canEditFacility", true);
        }
        Cookie canUpdateTeam = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_EDIT_TEAM");
        if(canUpdateTeam != null && canUpdateTeam.getValue().equalsIgnoreCase("true")){
            model.addAttribute("canEditTeam", true);
            model.addAttribute("teamHierarchy", configurationService.getTeamHierarchy());
            model.addAttribute("supervisorList", configurationService.getCallCenterResource(true));
            model.addAttribute("callCenterEmployeeList", configurationService.getCallCenterResource(false));
        }
        Cookie canViewReport = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_ACCESS_CALL_CENTER_REPORT");
        if(canViewReport != null && canViewReport.getValue().equalsIgnoreCase("true")){
            model.addAttribute("canAccessReport", true);
        }

        return "secure";
    }

    @ResponseBody
    @RequestMapping(value="secure/mongo-sync", method= RequestMethod.GET, produces = "application/json")
    public ResponseWrapper rebuildMongo(ModelMap model, HttpServletRequest request) {
        Cookie canUpdateCookie = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_UPDATE_MONGODB");
        if(canUpdateCookie == null || !canUpdateCookie.getValue().equalsIgnoreCase("true")){
            return new ResponseWrapper(false, "Do not have permission to sync mongo DB", 0, null);
        }
        Integer employeeNumber = getEmployeeNumberFromCookie(request);
        logAction(null, ActionLog.CONFIGURATION_ACTION, "Request to sync mongo from employee[" + employeeNumber + "].");
        configurationService.rebuildMongoEnvironment();
        return new ResponseWrapper(true, "", 0, null);
    }

    @RequestMapping(value="trigger-internal-sync-mechanism/{token}", method= {RequestMethod.PUT, RequestMethod.GET})
    @ResponseStatus(HttpStatus.OK)
    public void triggerInternalTask(@PathVariable(value = "token") String token) {
        if(token != null && token.equals(MongoRepositoryImpl.SYNC_TOKEN)){
            backgroundExecutor.rebuildMongoEnvironmentAsync();
        }else{
            logger.debug("Fail to validate token {} to sync mongo!", token);
        }
    }

    @RequestMapping(value="trigger-internal-map-reduce/{token}", method= {RequestMethod.PUT, RequestMethod.GET})
    @ResponseStatus(HttpStatus.OK)
    public void triggerInternalMapReduce(@PathVariable(value = "token") String token) {
        if(token != null && token.equals(MongoRepositoryImpl.SYNC_TOKEN)){
            backgroundExecutor.runMapReduceSummary();
        }else{
            logger.debug("Fail to validate token {} to issue Map Reduce task!", token);
        }
    }

    @RequestMapping(value="trigger-internal-update-facility/{token}", method= {RequestMethod.PUT, RequestMethod.GET})
    @ResponseStatus(HttpStatus.OK)
    public void triggerUpdateFacility(@PathVariable(value = "token") String token, @RequestParam (required =  false) Integer facilityId) {
        if(token != null && token.equals(MongoRepositoryImpl.SYNC_TOKEN)){
            backgroundExecutor.runUpdateFacilityRequest(facilityId);
        }else{
            logger.debug("Fail to validate token {} to update facility!", token);
        }
    }

    @RequestMapping(value = "test-mp3-conversion", method = RequestMethod.GET)
    @ResponseStatus(HttpStatus.OK)
    public void test(){
        String mp3 = callService.convertWavToMp3("/Pentaho_Extract/RDI/2015/5/2/d819fd1c-8c82-4351-85d7-f634636443c0_10002_6022067856_2015-04-29.wav");
        logger.debug("mp3 file: {}", mp3);
    }

    /*
     * Search for Phone Number entity by phone number.
     */
    @ResponseBody
    @RequestMapping(value="secure/phone-number/{phoneNumber}", method= RequestMethod.GET, produces = "application/json")
    public ResponseWrapper getPhoneNumber(@PathVariable("phoneNumber") Long phoneNumber) {
        logger.debug("Search for Phone Number by phone number[" + phoneNumber + "].");
        PhoneNumber number = configurationService.getPhoneNumber(phoneNumber);
        String phoneType = number != null ? number.getPhoneType() : "";
        if(number != null
                && (phoneType.equalsIgnoreCase("ML") || phoneType.equalsIgnoreCase("CM"))
                && number.getFacilityId() != null){
            Facility facility = callService.findFacilityByFacilityId(number.getFacilityId());
            if(facility != null){
                number.setFacilityName(facility.getName() + " (" + facility.getFacilityId() + ")");
                number.setFacilityAddress(facility.getFullAddress());
            }
        }

        return new ResponseWrapper(true, "", number != null ? 1 : 0, number);
    }

    @ResponseBody
    @RequestMapping(value = "secure/phone-number/facility-check", method = RequestMethod.GET, produces = "application/json")
    public ResponseWrapper checkPhoneNumberByFacilityId(@RequestParam Integer facilityId){
        logger.debug("Search for Phone Number by facility ID [" + facilityId + "].");
        ResponseWrapper wrapper = new ResponseWrapper();
        PhoneNumber number = callService.findPhoneNumberByFacilityAndPhoneType(facilityId, "ML");
        wrapper.setCount(number != null ? 1 : 0);
        wrapper.setData(number);
        return wrapper;
    }

    @ResponseBody
    @RequestMapping(value = "secure/phone-number/inactivate/{phoneNumber}", method = RequestMethod.GET, produces = "application/json")
    public ResponseWrapper inactivatePhoneNumber(@PathVariable("phoneNumber") Long phoneNumber, @RequestParam Integer employeeNumber){
        logger.debug("Inactive Phone Number [" + phoneNumber + "] by employee [" + employeeNumber + "].");
        logAction(null, ActionLog.CONFIGURATION_ACTION, "Inactive Phone Number [" + phoneNumber + "] by employee [" + employeeNumber + "].");
        PhoneNumber number = configurationService.getPhoneNumber(phoneNumber);
        if(number != null){
            number.setActive(false);
            number.setInactivatedBy(employeeNumber);
            number.setInactivatedOn(new Date());
            number = configurationService.persistPhoneNumber(number);
            if(number.getFacilityId() != null && number.getFacilityId() > 0){
                backgroundExecutor.runUpdateFacilityRequest(number.getFacilityId());
            }
        }
        ResponseWrapper wrapper = new ResponseWrapper();
        wrapper.setCount(number != null ? 1 : 0);
        wrapper.setSuccess(number != null ? true : false);
        wrapper.setData(number);
        return wrapper;
    }

    /*
     * Return all Phone Number entities
     */
    @ResponseBody
    @RequestMapping(value="secure/phone-numbers", method= RequestMethod.GET, produces = "application/json")
    public ResponseWrapper getAllPhoneNumber() {
        logger.debug("Search for all Phone Numbers.");
        List<PhoneNumber> numbers = configurationService.getAllPhoneNumbers();

        return new ResponseWrapper(true, "", numbers.size(), numbers);
    }

    /*
     * Add / Update a Phone Number entity
     */
    @ResponseBody
    @RequestMapping(value="secure/phone-number-persist/{phoneNumber}", method= RequestMethod.POST, produces = "application/json")
    public ResponseWrapper savePhoneNumber(@RequestBody String json
                                            , @RequestParam String action
                                            , HttpServletRequest request) {
        logger.debug("Request to persist a Phone Numbers.");

        Cookie canUpdateCookie = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_EDIT_PHONE_NUMBER");
        if(canUpdateCookie == null || !canUpdateCookie.getValue().equalsIgnoreCase("true")){
            return new ResponseWrapper(false, "Do not have permission to update phone number", 0, null);
        }
        Integer employeeNumber = getEmployeeNumberFromCookie(request);

        PhoneNumber number = (PhoneNumber) convertJson(json, PhoneNumber.class);
        if(action.equalsIgnoreCase("add")){
            logAction(null, ActionLog.CONFIGURATION_ACTION, "Request to add phone number[" + number.getPhoneNumber() + "] from employee[" + employeeNumber + "]");
            PhoneNumber existingNumber = configurationService.getPhoneNumber(number.getPhoneNumber());
            if(existingNumber != null){
                return new ResponseWrapper(false, number.getPhoneNumber() + " already exists in the system.", 0, null);
            }else{
                number.setCreatedBy(employeeNumber);
                number.setCreatedOn(new Date());
                number = configurationService.persistPhoneNumber(number);
                if(number.getFacilityId() != null && number.getFacilityId() > 0){
                    backgroundExecutor.runUpdateFacilityRequest(number.getFacilityId());
                }
                return new ResponseWrapper(true, "Complete", 1, number);
            }
        }else if(action.equalsIgnoreCase("update")){
            number.setUpdatedBy(employeeNumber);
            number.setUpdatedOn(new Date());
            logAction(null, ActionLog.CONFIGURATION_ACTION, "Request to update phone number[" + number.getPhoneNumber() + "] from employee[" + employeeNumber + "]");
            number = configurationService.persistPhoneNumber(number);
            if(number.getFacilityId() != null && number.getFacilityId() > 0){
                backgroundExecutor.runUpdateFacilityRequest(number.getFacilityId());
            }
            return new ResponseWrapper(true, "Complete", 1, number);
        }else{
            return new ResponseWrapper(false, "Do not know what to do with action " + action, 0, null);
        }
    }

    @ResponseBody
    @RequestMapping(value = "secure/routing-rules", method = RequestMethod.GET, produces = "application/json")
    public ResponseWrapper getRoutingRules(){
        logger.debug("Search for all Phone Numbers.");
        List<PhoneNumberRoutingRules> numbers = configurationService.findAllRoutingRules();

        return new ResponseWrapper(true, "", numbers.size(), numbers);
    }

    /*
     * Add / Update a CalLResolutionCode entity
     */
    @ResponseBody
    @RequestMapping(value="secure/routing-rule", method= RequestMethod.POST, produces = "application/json")
    public ResponseWrapper saveRoutingRule(@RequestBody String json, @RequestParam String action, HttpServletRequest request) {
        Cookie canUpdateCookie = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_EDIT_PHONE_NUMBER");
        if(canUpdateCookie == null || !canUpdateCookie.getValue().equalsIgnoreCase("true")){
            return new ResponseWrapper(false, "Do not have permission to update phone number routing rule", 0, null);
        }

        PhoneNumberRoutingRules routingRules = (PhoneNumberRoutingRules) convertJson(json, PhoneNumberRoutingRules.class);
        Integer employeeNumber = getEmployeeNumberFromCookie(request);
        PhoneNumberRoutingRules existing = configurationService.getRoutingRuleByNumber(routingRules.getPhoneNumber());
        if(action.equalsIgnoreCase("add")){
            if(existing != null){
                return new ResponseWrapper(false, routingRules.getPhoneNumber() + " already exists.", 0, null);
            }else{
                logAction(null, ActionLog.CONFIGURATION_ACTION, "Request to add routing rule for phone number[" + routingRules.getPhoneNumber() + "] from employee[" + employeeNumber + "]");
                routingRules.setCreateDateTime(new Date());
                routingRules.setCreatedEmployeeNumber(employeeNumber);
                configurationService.persistRoutingRule(routingRules);
                return new ResponseWrapper(true, "Complete", 1, routingRules);
            }
        }else if(action.equalsIgnoreCase("update")){
            logAction(null, ActionLog.CONFIGURATION_ACTION, "Request to update routing rule for phone number[" + routingRules.getPhoneNumber() + "] from employee[" + employeeNumber + "]");
            if(existing != null){
                routingRules.setCreateDateTime(existing.getCreateDateTime());
                routingRules.setCreatedEmployeeNumber(existing.getCreatedEmployeeNumber());
            }
            routingRules.setUpdatedDateTime(new Date());
            routingRules.setUpdatedEmployeeNumber(employeeNumber);
            configurationService.persistRoutingRule(routingRules);
            return new ResponseWrapper(true, "Complete", 1, routingRules);
        }else{
            return new ResponseWrapper(false, "Do not know what to do with action " + action, 0, null);
        }
    }

    /*
     * Search for Campaign entity by ID.
     */
    @ResponseBody
    @RequestMapping(value="secure/campaign/{campaignPhoneNumber}", method= RequestMethod.GET, produces = "application/json")
    public ResponseWrapper getCampaign(@PathVariable("campaignPhoneNumber") Long campaignPhoneNumber) {
        logger.debug("Search for Campaign by phone number[" + campaignPhoneNumber + "].");
        Campaign campaign = configurationService.getCampaign(campaignPhoneNumber);

        return new ResponseWrapper(true, "", 1, campaign);
    }

    /*
     * Return all CalLResolutionCode entities
     */
    @ResponseBody
    @RequestMapping(value="secure/reason-resolution-codes", method= RequestMethod.GET, produces = "application/json")
    public ResponseWrapper getCallReasonResolutionsCodes() {
        logger.debug("Search for all Reason Resolution Codes.");
        List<CallResolutionCode> codes = configurationService.getCallReasonResolutionsCodes();

        return new ResponseWrapper(true, "", codes.size(), codes);
    }

    /*
     * Add / Update a CalLResolutionCode entity
     */
    @ResponseBody
    @RequestMapping(value="secure/reason-resolution-code/{code}", method= RequestMethod.POST, produces = "application/json")
    public ResponseWrapper saveCampaign(@RequestBody String json, @PathVariable("code") String code, @RequestParam String action, HttpServletRequest request) {
        logger.debug("Request to persist a Call Reason Resolution Codes.");

        Cookie canUpdateCookie = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_EDIT_REASON_RESOLUTION");
        if(canUpdateCookie == null || !canUpdateCookie.getValue().equalsIgnoreCase("true")){
            return new ResponseWrapper(false, "Do not have permission to update reason/resolution code", 0, null);
        }

        CallResolutionCode rrCode = (CallResolutionCode) convertJson(json, CallResolutionCode.class);
        String type = rrCode.getIsReason() ? "reason" : "resolution";
        Integer employeeNumber = getEmployeeNumberFromCookie(request);
        if(action.equalsIgnoreCase("add")){
            logAction(null, ActionLog.CONFIGURATION_ACTION, "Request to add " + type + " code[" + rrCode.getCode() + "] from employee[" + employeeNumber + "]");
            CallResolutionCode existing = configurationService.getCallResolutionCode(rrCode.getCode());
            if(existing != null){
                return new ResponseWrapper(false, rrCode.getCode() + " already exists.", 0, null);
            }else{
                configurationService.persistCallResolutionCode(rrCode);
                return new ResponseWrapper(true, "Complete", 1, rrCode);
            }
        }else if(action.equalsIgnoreCase("update")){
            logAction(null, ActionLog.CONFIGURATION_ACTION, "Request to update " + type + " code[" + rrCode.getCode() + "] from employee[" + employeeNumber + "]");
            configurationService.persistCallResolutionCode(rrCode);
            return new ResponseWrapper(true, "Complete", 1, rrCode);
        }else{
            return new ResponseWrapper(false, "Do not know what to do with action " + action, 0, null);
        }
    }

    /*
     * Search for CallReasonResolutionCode entity by ID.
     */
    @ResponseBody
    @RequestMapping(value="reason-resolution-code/{code}", method= RequestMethod.GET, produces = "application/json")
    public ResponseWrapper getCallReasonResolutionCodes(@PathVariable("code") String code) {
        logger.debug("Search for Call Reason Resolution Codes[" + code + "].");
        CallResolutionCode rrCode = configurationService.getCallResolutionCode(code);

        return new ResponseWrapper(true, "", 1, rrCode);
    }

    /*
     * Return all Campaign entities
     */
    @ResponseBody
    @RequestMapping(value="secure/campaign", method= RequestMethod.GET, produces = "application/json")
    public ResponseWrapper getCallCampaigns() {
        logger.debug("Search for all Campaigns.");
        List<PhoneNumber> numbers = configurationService.getAllPhoneNumbers();

        return new ResponseWrapper(true, "", numbers.size(), numbers);
    }

    /*
     * Add / Update a Phone Number entity
     */
    @ResponseBody
    @RequestMapping(value="secure/campaign/{campaignPhoneNumber}", method= RequestMethod.POST, produces = "application/json")
    public ResponseWrapper saveCampaign(@PathVariable("campaignPhoneNumber") Long phoneNumber, @RequestParam String json) {
        logger.debug("Request to persist a Campaign.");

        Campaign campaign = (Campaign) convertJson(json, Campaign.class);
//        configurationService.persistCampaign(campaign);

        return new ResponseWrapper(true, "Complete", 1, campaign);
    }

    /**
     * Update a Facility's Message block
     */
    @ResponseBody
    @RequestMapping(value="secure/facility-message-update/{facilityID}", method= RequestMethod.POST, produces = "application/json")
    public ResponseWrapper updateFacilityMessage(@PathVariable("facilityID") Integer facilityId, @RequestBody String json, HttpServletRequest request) {
        Map<String, String> map = BaseJsonConverter.convertToMap(json);
        String message = map.get("message");
        Integer employeeNumber = getEmployeeNumberFromCookie(request);
        logger.debug("Updating Facility ID[" + facilityId + "] Message to [" + message + "].");
        Cookie canUpdateCookie = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_EDIT_FACILITY");
        if(canUpdateCookie == null || !canUpdateCookie.getValue().equalsIgnoreCase("true")){
            return new ResponseWrapper(false, "Do not have permission to update phone number", 0, null);
        }
        logAction(null, ActionLog.CONFIGURATION_ACTION, "Updating Facility ID[" + facilityId + "] Message to [" + message + "] from employee[" + employeeNumber + "].");
        Facility facility = configurationService.updateFacilityFacilityMessage(facilityId, message);
        ResponseWrapper wrapper = new ResponseWrapper(true, "Updated", 1, facility);
        if(facility != null){
            wrapper.setMessage("Succeed updating notes for Facility ID" + facilityId);
        }else{
            wrapper.setMessage("Fail to update notes for Facility ID " + facilityId);
        }
        return wrapper;
    }

    /**
     * Provide controls to sync DB data with Mongo data.
     */
    @ResponseStatus(HttpStatus.OK)
    @RequestMapping(value="secure/sync-oracle-to-mongo", method= RequestMethod.PUT)
    public void syncOracleToMongo() {
        configurationService.rebuildMongoEnvironment();
    }

    @RequestMapping(value = "secure/tester", method = RequestMethod.GET)
    public String loadTestPage(@RequestParam String entity, ModelMap model){
        if(entity.equals("campaign")) {
            model.addAttribute("campaignList", configurationService.findAllCampaign());
            return "campaignTester";
        } else if(entity.equals("facilityInfo")) {
            model.addAttribute("facilityList", configurationService.findAllFacility());
            return "facilityInfoTester";
        } else if(entity.equals("phoneNumber")) {
                model.addAttribute("phoneNumberList", configurationService.getAllPhoneNumbers());
                return "phoneNumberList";
        } else if(entity.equals("reasonResolutions")) {
            model.addAttribute("reasonResolutionList", configurationService.getCallReasonResolutionsCodes());
            return "reasonResolutionList";
        } else if(entity.equals("routingRule")){
            model.addAttribute("routingRuleList", configurationService.findAllRoutingRules());
            return "routingRuleList";
        } else {
            return "";
        }
    }

    @RequestMapping(value = "secure/facilityMessageEdit", method = RequestMethod.GET)
    public String loadPageToEditFacilityMessage(ModelMap model, HttpServletRequest request){
        Cookie canUpdateCookie = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_EDIT_FACILITY");
        if(canUpdateCookie == null || !canUpdateCookie.getValue().equalsIgnoreCase("true")){
            return "403";
        }
        return "facilityMessageEdit";
    }

    @RequestMapping(value = "secure/phoneNumberEdit", method = RequestMethod.GET)
    public String loadPageToEditPhoneNumber(ModelMap model, HttpServletRequest request){
        Cookie canUpdateCookie = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_EDIT_PHONE_NUMBER");
        if(canUpdateCookie == null || !canUpdateCookie.getValue().equalsIgnoreCase("true")){
            return "403";
        }
        return "phoneNumberEdit";
    }

    @RequestMapping(value = "secure/campaignEdit", method = RequestMethod.GET)
    public String loadPageToEditCampaign(ModelMap model){
        return "campaignEdit";
    }

    @RequestMapping(value = "secure/reasonResolutionEdit", method = RequestMethod.GET)
    public String loadPageToEditReasonResolution(ModelMap model, HttpServletRequest request){
        Cookie canUpdateCookie = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_EDIT_REASON_RESOLUTION");
        if(canUpdateCookie == null || !canUpdateCookie.getValue().equalsIgnoreCase("true")){
            return "403";
        }
        return "reasonResolutionEdit";
    }

    @RequestMapping(value = "campaigninfo", method = RequestMethod.GET)
    public String loadCampaignInfo(ModelMap modelMap,
                                   @RequestParam Long dnis,
                                   @RequestParam String uniqueCallId,
                                   @RequestParam(value = "lang", defaultValue = "en", required = false) String language,
                                   @RequestParam(value = "numberPress", defaultValue = "2", required = false) Integer numberPress,
                                   @RequestParam Integer employeeNumber){
        Campaign campaign = configurationService.getCampaign(dnis);
        logger.debug("campaign: {}", campaign);
        modelMap.addAttribute("campaign", campaign);
        modelMap.addAttribute("success", campaign != null ? true : false);
        modelMap.addAttribute("uuid", uniqueCallId);
        modelMap.addAttribute("employeeNumber", employeeNumber);
        modelMap.addAttribute("language", language);
        modelMap.addAttribute("numberPress", numberPress);

        return "campaignInfo";
    }

    @ResponseBody
    @RequestMapping(value = "secure/update-team", method = RequestMethod.GET, produces = "application/json")
    public ResponseWrapper updateTeam(@RequestParam Integer supervisorId,
                               @RequestParam String agentsInTeam,
                               HttpServletRequest request){
        logger.debug("about to update team with supervisor ID[" + supervisorId + "] and agents in team[" + agentsInTeam + "]");
        logAction(null, ActionLog.CONFIGURATION_ACTION, "about to update team with supervisor ID[" + supervisorId + "] and agents in team[" + agentsInTeam + "]");
        Cookie canUpdateTeam = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_EDIT_TEAM");
        ResponseWrapper responseWrapper = new ResponseWrapper();
        if(canUpdateTeam == null || !canUpdateTeam.getValue().equalsIgnoreCase("true")){
            responseWrapper.setSuccess(false);
            responseWrapper.setMessage("Do not have permission to edit team");
        }else{
            boolean result = configurationService.updateCallCenterEmployee(supervisorId, agentsInTeam);
            if(result){
                responseWrapper.setSuccess(true);
                responseWrapper.setData(configurationService.getTeamHierarchy());
            }else{
                responseWrapper.setSuccess(false);
                responseWrapper.setMessage("Fail to update team managed by [" + supervisorId + "] with agents in team [" + agentsInTeam + "]");
            }
        }
        return responseWrapper;
    }

    @ResponseBody
    @RequestMapping(value = "pulse-check", method = RequestMethod.GET, produces = "text/plain")
    public String checkAppPulse() {
        return "HEALTHY";
    }

    @RequestMapping(value = "secure/update-dev-email-recipient", method = RequestMethod.GET)
    @ResponseStatus(value = HttpStatus.ACCEPTED)
    public void updateDevEmailRecipient(){
        communicationService.updateEmailRecipients();
    }
}
