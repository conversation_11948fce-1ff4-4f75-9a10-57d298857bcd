package com.smilebrands.callcentersupport.domain.helper;

import java.io.File;
import java.io.FilenameFilter;

public class InclusionFileNameFilter implements FilenameFilter {

  private String[] inclusions;

  public InclusionFileNameFilter(String inclusions) {
      if(inclusions != null){
          this.inclusions = inclusions.split(",");
      }
  }

  public boolean accept(File file, String str) {

    for( String s : inclusions ){
        if( str.toLowerCase().endsWith(s)) return true;
        if(str.toLowerCase().indexOf(".") == -1) return true;
    }
    return false;
  }
}