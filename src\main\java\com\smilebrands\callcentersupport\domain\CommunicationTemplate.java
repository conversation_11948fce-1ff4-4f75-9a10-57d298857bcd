package com.smilebrands.callcentersupport.domain;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * Created by phongpham on 10/16/17.
 */
@Document
public class CommunicationTemplate {

    @Id
    private Long templateId;
    private String templateName;
    private String content;
    private String communicationType;
    private String replyTo;

    private boolean active = true;
    private Date createDateTime = new Date();

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCommunicationType() {
        return communicationType;
    }

    public void setCommunicationType(String communicationType) {
        this.communicationType = communicationType;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public Date getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(Date createDateTime) {
        this.createDateTime = createDateTime;
    }

    public String getReplyTo() {
        return replyTo;
    }

    public void setReplyTo(String replyTo) {
        this.replyTo = replyTo;
    }

    public Long getReplyToAsPhoneNumber(){
        Long phoneNumber = null;
        try{
            phoneNumber = Long.valueOf(getReplyTo());
        }catch (NumberFormatException nfe){
            System.out.println("Fail to get reply to as phone number for template[" + this.getTemplateId() + "] and replyTo[" + this.getReplyTo() + "]");
        }
        return phoneNumber;
    }
}
