package com.smilebrands.callcenter.web.filter;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Filter to handle incomplete requests and provide better error messages
 * and logging for troubleshooting.
 */
public class IncompleteRequestFilter implements Filter {
    private static final Logger logger = LoggerFactory.getLogger(IncompleteRequestFilter.class);

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // No initialization needed
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        String method = httpRequest.getMethod();
        String uri = httpRequest.getRequestURI();
        String contentType = httpRequest.getContentType();
        int contentLength = httpRequest.getContentLength();

        // Log request details for debugging
        logger.debug("Processing request: " + method + " " + uri +
                " - Content-Type: " + contentType + ", Content-Length: " + contentLength);

        try {
            // Continue with the request processing
            chain.doFilter(request, response);
        } catch (IllegalStateException e) {
            String errorMessage = e.getMessage();

            // Check if this is an incomplete request error
            if (errorMessage != null && errorMessage.contains("INCOMPLETE")) {
                logger.warn("Caught incomplete request: " + method + " " + uri +
                        " - Content-Type: " + contentType + ", Content-Length: " + contentLength);
                logger.warn("Exception: " + errorMessage, e);

                // Log additional request details that might help diagnose the issue
                logger.warn("Request details: Remote IP: " + httpRequest.getRemoteAddr() +
                        ", User-Agent: " + httpRequest.getHeader("User-Agent"));

                // Check if response is already committed before forwarding to JSP
                if (!httpResponse.isCommitted()) {
                    // Set appropriate response status and forward to error page
                    httpResponse.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                    request.getRequestDispatcher("/WEB-INF/views/error/incomplete-request.jsp")
                           .forward(request, response);
                } else {
                    logger.warn("Cannot forward to JSP - response already committed");
                }
            } else if (errorMessage != null && (errorMessage.contains("getOutputStream() after getWriter()") || errorMessage.contains("No writer"))) {
                // This is the specific error we're trying to fix - stream conflicts
                logger.error("Caught stream conflict error: " + method + " " + uri +
                        " - Content-Type: " + contentType + ", Content-Length: " + contentLength);
                logger.error("Exception: " + errorMessage, e);

                // Log additional request details
                logger.error("Request details: Remote IP: " + httpRequest.getRemoteAddr() +
                        ", User-Agent: " + httpRequest.getHeader("User-Agent"));

                // Don't try to write to response as it's already in an inconsistent state
                // Just log the error and let it propagate
                throw e;
            } else {
                // For other IllegalStateException errors, rethrow
                throw e;
            }
        }
    }

    @Override
    public void destroy() {
        // No cleanup needed
    }
}
