<%@ attribute name="team" required="true"  type="java.lang.Object"%>
<%@ taglib tagdir="/WEB-INF/tags" prefix="myTags" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<li>
    <c:choose>
        <c:when test="${fn:length(team.team) > 0}">
            <label class="tree-toggler nav-header" employeeNumber="${team.employeeNumber}" managerNumber="${team.managerNumber}">
                ${team.employeeName}
            </label>
            <ul class="nav nav-list tree">
                <c:forEach var="t" items="${team.team}">
                    <myTags:tree team="${t}"/>
                </c:forEach>
            </ul>
        </c:when>
        <c:otherwise>
            <label employeeNumber="${team.employeeNumber}" managerNumber="${team.managerNumber}" style="font-weight: normal;">
                ${team.employeeName}
            </label>
        </c:otherwise>
    </c:choose>
</li>
