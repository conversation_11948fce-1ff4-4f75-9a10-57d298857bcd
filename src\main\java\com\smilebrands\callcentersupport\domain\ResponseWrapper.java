package com.smilebrands.callcentersupport.domain;

import javax.xml.bind.annotation.XmlRootElement;

/**
 * User: <PERSON><PERSON>
 * Date: 6/24/14
 */
@XmlRootElement
public class ResponseWrapper {

    private Boolean success;
    private String message;
    private Integer count;
    private Object data;

    public ResponseWrapper() {}

    public ResponseWrapper(boolean success, String message, Integer count, Object data) {
        this.success = success;
        this.message = message;
        this.count = count;
        this.data = data;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
