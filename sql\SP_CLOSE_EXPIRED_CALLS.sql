CREATE OR REPLACE PROCEDURE "CISCOADM"."SP_CLOSE_EXPIRED_CALLS"


/*---------------------------------------------------------------------------------*/
/* Procedure: SP_PROCESS_OTHER_APPOINTMENTS                                        */
/*                                                                                 */
/* Author: <PERSON>                                                          */
/* Date Written: 08/08/2014                                                        */
/*                                                                                 */
/* Purpose: This Routine processes other appts that have not been                  */
/*                                                                                 */
/*                                                                                 */
/* Input Variables:                                                                */
/*                                                                                 */
/*                                                                                 */
/*                                                                                 */
/*                                                                                 */
/* Returns:                                                                        */
/*                                                                                 */
/*                                                                                 */
/* Revisions                                                                       */
/*                                                                                 */
/*                                                                                 */
/*                                                                                 */
/*---------------------------------------------------------------------------------*/

AS

V_APPT_LOG_STATUS               VARCHAR2(20);
V_CALL_LOG_STATUS               VARCHAR2(10);
V_APPT_LOG_ID                   APPT_CALL_LOG.APPT_LOG_ID%TYPE;



ERR_NUM                         NUMBER;
ERR_MSG                         VARCHAR2(512);
V_STORED_PROCEDURE              VARCHAR2(30) := $$PLSQL_UNIT;



BEGIN

/*---------------------------------------------------------------------------*/
/* Get Ros to close                                                          */
/*---------------------------------------------------------------------------*/
        BEGIN

        FOR R IN (SELECT CL.CALL_LOG_ID,
                         CL.SESSION_ID,
                         CL.EMPLOYEE_NUMBER,
                         CL.FACILITY_ID,
                         CL.TEMP_PATIENT_ID,
                         CL.CLINIC_ID,
                         CL.PATIENT_ID,
                         CL.UNIQUE_ID,
                         CL.CALL_CENTER_TIME,
                         TRUNC(CL.CALL_TIME) AS CALL_TIME,
                         TRIM(CL.CALL_LOG_STATUS) AS CALL_LOG_STATUS,
                         TRIM(CL.APPT_LOG_STATUS) AS APPT_LOG_STATUS,
                         UPPER(NVL(FV.FAC_ATTR_VALUE,'FALSE')) AS IS_LIBERTY,
                         CL.APPT_DATE AS APPT_DATE,
                         NVL(TRUNC(CL.APPT_DATE) + 4,TRUNC(CL.CALL_CENTER_TIME) + 4) as CLOSE_DATE,
                         (TRUNC(CL.CREATE_DATETIME) + 2)  AS MISSING_DATE,
                         CL.CREATE_DATETIME,
                         CL.IS_ORTHO,
                         CL.APPT_VALUE,
                         CL.APPT_PROVIDER_ID AS PROVIDER_ID,
                         CASE WHEN CL.ADMIN_OVERRIDDEN = 1 THEN CL.ALLOW_CREDIT ELSE 0 END AS ALLOW_CREDIT

                FROM V_AGENT_CALL_LOG CL

                LEFT OUTER JOIN FACILITY_ATTR_VALUES FV
                    ON FV.FACILITY_ID = CL.FACILITY_ID AND
                       FV.FAC_ATTRIBUTE_ID = 69
                WHERE UPPER(CALL_LOG_STATUS) = 'ACTIVE' AND
                      APPT_LOG_STATUS <> 'SHOW' AND
                     NVL(TRUNC(CL.APPT_DATE) + 4,TRUNC(CL.CALL_CENTER_TIME) + 4) < SYSDATE)

           LOOP

/*---------------------------------------------------------------------------*/
/* Get Appt Value                                                            */
/*---------------------------------------------------------------------------*/

    V_APPT_LOG_STATUS := 'CLOSED';
    V_CALL_LOG_STATUS := 'CLOSED';

/*---------------------------------------------------------------------------*/
/* Add new status to Appt Call Log                                           */
/*---------------------------------------------------------------------------*/

    BEGIN

    INSERT INTO APPT_CALL_LOG(
                APPT_LOG_ID,
                CALL_LOG_ID,
                CLINIC_ID,
                UNIQUE_ID,
                PROVIDER_ID,
                APPT_LOG_STATUS,
                APPT_DATE,
                APPT_VALUE,
                LAST_UPD_PROGRAM,
                CREATE_EMPLOYEE,
                CREATE_DATETIME,
                UPDATE_EMPLOYEE,
                UPDATE_TIMESTAMP,
                IS_ORTHO)

	     VALUES(APPT_CALL_LOG_ID_SEQ.NEXTVAL,
                R.CALL_LOG_ID,
                R.CLINIC_ID,
                R.UNIQUE_ID,
                R.PROVIDER_ID,
                V_APPT_LOG_STATUS,
                R.APPT_DATE,
                R.APPT_VALUE,
                V_STORED_PROCEDURE,
                110026,
                SYSDATE,
                NULL,
                NULL,
                R.IS_ORTHO)
        RETURNING APPT_LOG_ID
        INTO V_APPT_LOG_ID;

    END;


/*---------------------------------------------------------------------------*/
/* Close Agent Call Log on Terminal Event                                    */
/*---------------------------------------------------------------------------*/

    BEGIN

    UPDATE AGENT_CALL_LOG
       SET CALL_LOG_STATUS = V_CALL_LOG_STATUS,
           LAST_APPT_LOG_ID = V_APPT_LOG_ID,
           ALLOW_CREDIT = R.ALLOW_CREDIT,
           UPDATE_EMPLOYEE = 110026,
           UPDATE_TIMESTAMP = SYSDATE
    WHERE CALL_LOG_ID = R.CALL_LOG_ID;

    END;

/*---------------------------------------------------------------------------*/
/* INsert Into Process Log                                                   */
/*---------------------------------------------------------------------------*/

    BEGIN


    INSERT INTO CALL_PROCESS_LOG(
                CALLED_PROC,
                CALL_LOG_ID,
                FACILITY_ID,
                CLINIC_ID,
                UNIQUE_ID,
                CALL_TIME,
                CLOSE_DATE,
                CREATE_DATE,
                CALL_LOG_STATUS,
                APPT_LOG_STATUS,
                CONTINUE_PROCESSING,
                UPDATE_LOG,
                APPT_FOUND,
                TODAYS_DATE)

	     VALUES(V_STORED_PROCEDURE,
                R.CALL_LOG_ID,
                R.FACILITY_ID,
                R.CLINIC_ID,
                R.UNIQUE_ID,
                R.CALL_CENTER_TIME,
                SYSDATE,
                NULL,
                V_CALL_LOG_STATUS,
                V_APPT_LOG_STATUS,
                0,
                0,
                0,
                SYSDATE);

    END;

/*---------------------------------------------------------------------------*/
/* End Loop Process                                                          */
/*---------------------------------------------------------------------------*/
    END LOOP;
    END;


/*---------------------------------------------------------------------------*/
/* Error Occured - Add error to log                                          */
/*---------------------------------------------------------------------------*/

    EXCEPTION WHEN OTHERS THEN

    ERR_NUM := SQLCODE;
    ERR_MSG := SUBSTR(SQLERRM, 1, 512);

    INSERT INTO APPLICATION_ERROR_LOG(
                ERROR_ID,
                STORED_PROCEDURE,
                ERROR_NUMBER,
                ERROR_MSG,
                ERROR_DATE)
      VALUES(
               APPLICATION_ERROR_LOG_SEQ.NEXTVAL,
               V_STORED_PROCEDURE,
               ERR_NUM,
               ERR_MSG,
               SYSDATE);

END;