<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">

    <!-- Datasource -->
    <bean id="dataSource" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName" value="jdbc/dataSource" />
    </bean>

    <bean id="postgresDataSource" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName" value="jdbc/postgresDataSource" />
    </bean>

    <!-- MONGO CONNECTION -->
    <bean id="mongoDBReplicaSet" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName" value="mongo/replicaSet" />
    </bean>
    <bean id="mongoDBPort" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName" value="mongo/mongoPort" />
    </bean>
    <bean id="mongoDBName" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName" value="mongo/mongoDBName" />
    </bean>
    <bean id="mongoAuthDB" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName" value="mongo/mongoAuthDB" />
    </bean>
    <bean id="mongoDBUser" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName" value="mongo/mongoUser" />
    </bean>
    <bean id="mongoDBPass" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName" value="mongo/mongoPass" />
    </bean>

    <!--WEB REQUEST PROPERTIES-->
    <bean id="redirectUrl" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName" value="webRequest/redirectUrl"/>
    </bean>
    <bean id="ciscoUrl" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName" value="webRequest/ciscoUrl"/>
    </bean>
    <bean id="callBackCiscoUrl" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName" value="webRequest/callBackCiscoUrl"/>
    </bean>
    <bean id="devMode" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName" value="webRequest/devMode"/>
    </bean>

</beans>