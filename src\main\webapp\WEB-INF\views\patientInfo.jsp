<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<%@ taglib uri="../customTag.tld" prefix="custom" %>
<c:if test="${patientCount > 0}">
    <input type="text" style="display:none;" id="appointmentPhone" value="${appointmentPhone}"/>
    <input type="text" style="display:none;" id="patientName" value="${primaryPatient.patientFullName}"/>
    <c:set var="extraPatientTxt" value=""/>
    <c:set var="extraAppointmentTxt" value=""/>
    <c:if test="${patientCount > 1}">
        <c:set var="extraPatientTxt" value="<span class='badge extra-info-cls patient' style='cursor: pointer;'>+${patientCount-1}</span>"/>
    </c:if>
    <c:if test="${primaryAppointmentCount > 1}">
        <input type="text" style="display:none;" id="foundPatientId" value="${primaryPatient.patientId}"/>
        <c:set var="extraAppointmentTxt" value="<span class='badge extra-info-cls appointment' style='cursor: pointer;'>+${primaryAppointmentCount-1}</span>"/>
    </c:if>
    <div class="row">
        <div class="col-md-3"><strong>Patient Found</strong></div>
        <div class="col-md-8">${primaryPatient.patientFullName} ${extraPatientTxt} using phone number ${appointmentPhoneTxt}</div>
    </div>
    <div class="row">
        <div class="col-md-3"><strong>Next Appointment</strong></div>
        <div class="col-md-8">${nextAppointmentDateTime} at ${facilityName} ${extraAppointmentTxt}</div>
    </div>
</c:if>
<c:if test="${previousCallOn != null}">
    <div class="row">
        <div class="col-md-3"><strong>Most Recent Call</strong></div>
        <div class="col-md-8">from <custom:fmtPhone phone="${ani}"/> on ${previousCallOn}</div>
    </div>
</c:if>
<div class="modal fade" id="appointmentSearch" tabindex="-1" role="dialog" aria-labelledby="appointmentSearchModalLabel" aria-hidden="true" style="top:100px;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title" id="appointmentSearchModalLabel"></h4>
            </div>
            <div class="modal-body" style="padding-bottom:0px;">
                <div id="appointment-display_list" class="list-group" style="padding-top:20px;" display-position="bottom">
                </div>
            </div>
        </div>
    </div>
</div>