package com.smilebrands.callcentersupport.service;

import com.smilebrands.callcentersupport.domain.ActionLog;
import com.smilebrands.callcentersupport.domain.CallCenter;
import com.smilebrands.callcentersupport.domain.CallbackRequest;
import com.smilebrands.callcentersupport.domain.InboundCall;
import com.smilebrands.callcentersupport.domain.constants.AgentType;
import com.smilebrands.callcentersupport.domain.constants.Language;
import com.smilebrands.callcentersupport.domain.constants.SkillType;
import com.smilebrands.callcentersupport.repo.MongoRepository;
import com.smilebrands.callcentersupport.util.HttpUtils;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.swing.*;
import java.util.Date;

/**
 * Created by phongpham on 1/8/16.
 */
@Service
public class CallbackServiceImpl extends BaseService implements CallbackService {

    @Autowired
    protected MongoRepository mongoRepository;

    @Autowired
    protected HttpUtils httpUtils;

    @Autowired
    @Qualifier("REDIRECT_URL")
    private String redirectUrl;

    @Autowired
    @Qualifier("CALLBACK_CISCO_URL")
    private String callbackCiscoUrl;

    @Override
    public CallbackRequest registerCallbackRequest(String uuid, Long ani, String agentType, String callCenter) {
        CallbackRequest callbackRequest = mongoRepository.getCallbackRequest(uuid);
        if(callbackRequest == null){
            callbackRequest = new CallbackRequest();
            callbackRequest.setUuid(uuid);
            callbackRequest.setPhoneNumber(ani != null ? ani : 0);

            InboundCall inboundCall = mongoRepository.getInboundCall(uuid);

            callbackRequest.setAgentType(agentType);
            callbackRequest.setLanguage(inboundCall != null && inboundCall.getLanguage() != null && inboundCall.getLanguage().trim().length() > 0 ? inboundCall.getLanguage() : "en");
            String cc = "Irvine";
            if(callCenter != null && callCenter.trim().length() > 0){
                cc = callCenter;
            }else if(inboundCall != null && inboundCall.getPhoneNumber() != null
                    && inboundCall.getPhoneNumber().getCallCenterName() != null
                    && inboundCall.getPhoneNumber().getCallCenterName().trim().length() > 0){
                cc = inboundCall.getPhoneNumber().getCallCenterName();
            }
            callbackRequest.setCallCenter(cc);

            callbackRequest.setRequestDateTime(VelocityUtils.getDateAsString(new Date(), "yyyy-MM-dd HHmmss"));
            callbackRequest.setPendingProcessedDateTime(VelocityUtils.getDateAsString(new Date(), "yyyy-MM-dd HHmmss"));
            mongoRepository.persistCallbackRequest(callbackRequest);

            putCallbackToQueue(callbackRequest);

        }
        return callbackRequest;
    }

    public CallbackRequest putCallbackToQueue(CallbackRequest callbackRequest){
        CallCenter callCenter = mongoRepository.getCallCenterByName(callbackRequest.getCallCenter());
        if(callCenter != null){
            AgentType agentType = AgentType.getByValue(callbackRequest.getAgentType());
            Language language = Language.getByValue(callbackRequest.getLanguage());
            int agentId = callCenter.getAgentId(agentType, SkillType.CB, language);
            if(agentId > 0){
                String url = callbackCiscoUrl + "?destPhoneNo=" + callbackRequest.getPhoneNumber() + "&grabAgent=" + agentId + "&uuid=" + callbackRequest.getUuid();
                String queueName = callbackRequest.getCallCenter().substring(0, 3).toUpperCase() + "_CB_" + agentType + "_" + language;
                callbackRequest.setQueueName(queueName);
                mongoRepository.addLogAction(callbackRequest.getUuid(), ActionLog.CALL_BACK, "About to send to url[" + url + "]");
                httpUtils.sendGet(url);
            }else{
                mongoRepository.addLogAction(callbackRequest.getUuid(), ActionLog.CALL_BACK, "Fail to find agent ID for call back " +
                                                "with agent type[" + callbackRequest.getAgentType() + "] " +
                                                "and language[" + callbackRequest.getLanguage() + "]");
            }
        }else{
            mongoRepository.addLogAction(callbackRequest.getUuid(), ActionLog.CALL_BACK, "Fail to find call center with name[" + callbackRequest.getCallCenter() + "]");
        }
        return callbackRequest;
    }

    @Override
    public CallbackRequest registerCallbackNumber(String uuid, Long ani) {
        CallbackRequest callbackRequest = mongoRepository.getCallbackRequest(uuid);
        if(callbackRequest == null){
            callbackRequest = new CallbackRequest();
            callbackRequest.setUuid(uuid);
            callbackRequest.setRequestDateTime(VelocityUtils.getDateAsString(new Date(), "yyyy-MM-dd HHmmss"));
        }
        callbackRequest.setPhoneNumber(ani != null ? ani : 0);
        mongoRepository.persistCallbackRequest(callbackRequest);
        return callbackRequest;
    }

    @Override
    public CallbackRequest findCallbackRequestById(String uuid) {
        return mongoRepository.getCallbackRequest(uuid);
    }

    @Override
    public CallbackRequest persistCallbackRequest(CallbackRequest callbackRequest) {
        return mongoRepository.persistCallbackRequest(callbackRequest);
    }
}
