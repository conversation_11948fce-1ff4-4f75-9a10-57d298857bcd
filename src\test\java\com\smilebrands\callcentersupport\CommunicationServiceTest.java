package com.smilebrands.callcentersupport;

import com.smilebrands.callcentersupport.domain.CommunicationTemplate;
import com.smilebrands.callcentersupport.domain.InboundCall;
import com.smilebrands.callcentersupport.repo.MongoRepository;
import com.smilebrands.callcentersupport.service.CallService;
import com.smilebrands.callcentersupport.service.CommunicationService;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoOperations;

import java.util.HashMap;

/**
 * Created by phongpham on 7/14/14.
 */
public class CommunicationServiceTest extends BaseTest {

    @Autowired
    CommunicationService communicationService;

    @Autowired
    CallService callService;

    @Autowired
    MongoRepository mongoRepository;

    @Autowired
    private MongoOperations mongoOperations;

    @Test
    public void testSendSms(){
//        InboundCall call = callService.findInboundCall("caeec0ef-2079-4383-8e4e-47caae2e89f4");
        InboundCall call = callService.findInboundCall("dab49b0b-5562-4e2b-b8a0-247770d6fe45");
        String msg = "Thanks for your interest in One Smile Dental Plan. Click the link below to learn how to save 20-40% on all dental services with the OneSmile Dental Plan www.onesmiledentalplan.com. ";
        logger.debug("{}", communicationService.sendSMS("9493315916", msg, 10230, null));
    }

    @Test
    public void testSendEmail(){
        communicationService.sendNotificationViaEmail(null, "<EMAIL>", new HashMap(), "appointment-template.vm", "hello world");
    }

    @Test
    public void testSendEmailWithNoTemplate(){
        communicationService.sendNotificationViaEmail("<EMAIL>", "hello world", "testing");
    }

    @Test
    public void testToGenerateTemplates(){
        CommunicationTemplate template = new CommunicationTemplate();
        template.setTemplateId(mongoRepository.getNextSequenceValue("template"));
        template.setTemplateName("OneSmile Dental Plan Intro");
        template.setCommunicationType("SMS");
        template.setContent("Thanks for your interest in One Smile Dental Plan. Click the link below to learn how to save 20-40% on all dental services with the OneSmile Dental Plan www.onesmiledentalplan.com.");
        template.setReplyTo("8334176453");
        mongoOperations.save(template);

        template = new CommunicationTemplate();
        template.setTemplateId(mongoRepository.getNextSequenceValue("template"));
        template.setTemplateName("Test1");
        template.setCommunicationType("SMS");
        template.setContent("Testing...");
        mongoOperations.save(template);
    }

    @Test
    public void testGetReplyToAsPhoneNumber(){
        CommunicationTemplate template = new CommunicationTemplate();
        template.setTemplateId(1l);
        template.setReplyTo("123");
        Assert.assertEquals(Long.valueOf("123"), template.getReplyToAsPhoneNumber());
        template.setReplyTo("1.23");
        Assert.assertNull("Expect reply to as null for 1.23", template.getReplyToAsPhoneNumber());
        template.setReplyTo("hello");
        Assert.assertNull("Expect reply to as null for hello", template.getReplyToAsPhoneNumber());
        template.setReplyTo(null);
        Assert.assertNull("Expect reply to as null for null", template.getReplyToAsPhoneNumber());
    }
}
