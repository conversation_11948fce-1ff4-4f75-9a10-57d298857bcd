package com.smilebrands.callcentersupport.domain.rowmapper;

import com.smilebrands.callcentersupport.domain.CallCenterEmployee;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Created by phongpham on 6/30/16.
 */
public class CallCenterEmployeeRowMapper implements RowMapper<CallCenterEmployee> {
    @Override
    public CallCenterEmployee mapRow(ResultSet rs, int rowNum) throws SQLException {
        CallCenterEmployee cce = new CallCenterEmployee();
        if(checkIfColumnExists(rs, "EMP_SUPERVISOR_ID")){
            cce.setEmpSupervisorId(rs.getLong("EMP_SUPERVISOR_ID"));
        }
        cce.setEmployeeNumber(rs.getInt("EMPLOYEE_NUMBER"));
        cce.setEmployeeFirstName(rs.getString("FIRST_NAME"));
        cce.setEmployeeLastName(rs.getString("LAST_NAME"));
        cce.setEmailAddress(rs.getString("EMAIL_ADDRESS"));
        cce.setEmployeeName(rs.getString("EMPLOYEE_NAME"));
        cce.setSupervisorNumber(rs.getInt("SUPERVISOR_EMPLOYEE_NUMBER"));
        cce.setSupervisorName(rs.getString("SUPERVISOR_EMPLOYEE_NAME"));
        cce.setCallCenter(rs.getString("CALL_CENTER"));
        if(checkIfColumnExists(rs, "IS_ACTIVE")){
            cce.setActive(rs.getBoolean("IS_ACTIVE"));
        }
        if(checkIfColumnExists(rs, "IS_SUPERVISOR")){
            cce.setSupervisor(rs.getBoolean("IS_SUPERVISOR"));
        }
        return cce;
    }

    private boolean checkIfColumnExists(ResultSet rs, String columnName){
        try {
            int columnCount = rs.getMetaData().getColumnCount();
            for(int i=1; i<=columnCount; i++){
                if(rs.getMetaData().getColumnName(i).equals(columnName)){
                    return true;
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
}
