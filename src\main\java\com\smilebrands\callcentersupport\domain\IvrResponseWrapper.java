package com.smilebrands.callcentersupport.domain;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * Created by phongpham on 7/11/14.
 */
@XmlRootElement
public class IvrResponseWrapper {

    private Boolean success;
    private String message;
    private int count;

    private List<Facility> facilities;
    private Facility facility;

    private InboundCall inboundCall;
    private CallbackRequest callbackRequest;

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    @XmlElement(name = "facility")
    public List<Facility> getFacilities() {
        return facilities;
    }

    public void setFacilities(List<Facility> facilities) {
        this.facilities = facilities;
    }

    public Facility getFacility() {
        return facility;
    }

    public void setFacility(Facility facility) {
        this.facility = facility;
    }

    public InboundCall getInboundCall() {
        return inboundCall;
    }

    public void setInboundCall(InboundCall inboundCall) {
        this.inboundCall = inboundCall;
    }

    public CallbackRequest getCallbackRequest() {
        return callbackRequest;
    }

    public void setCallbackRequest(CallbackRequest callbackRequest) {
        this.callbackRequest = callbackRequest;
    }
}
