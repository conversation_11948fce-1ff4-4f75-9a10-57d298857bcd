package com.smilebrands.callcentersupport.repo;

import com.smilebrands.callcentersupport.domain.*;
import com.smilebrands.callcentersupport.domain.constants.AttributeId;
import com.smilebrands.callcentersupport.domain.helper.MapToFacility;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * User: <PERSON><PERSON>
 * Date: 6/25/14
 */
@Repository
public class FacilityRepositoryImpl extends JdbcRepository implements FacilityRepository {

    @Override
    public Facility findFacilityByFacilityId(Integer facilityId) {
        Facility facility = null;
        List<Map<String, Object>> facilityOptions = jdbcTemplate.queryForList(GET_FACILITY_COLLECTION + " AND f.FACILITY_ID = " + facilityId);
        if (facilityOptions.size() > 0) {
            facility = MapToFacility.convert(facilityOptions.get(0));
            facility.getEmployees().put("Dentists", findFacilityProvider(facilityId));
            facility.getEmployees().put("Managers", findFacilityManagement(facilityId));
            populateFacilityByAttributeValues(facility);

            List<Facility> neighborFacilities = findFacilitiesByZipCode(facility.getZip());
            for(int i = 0; i < neighborFacilities.size(); i++){
                Facility neighborFacility = neighborFacilities.get(i);
                if(neighborFacility.getFacilityId().equals(facilityId)){
                    neighborFacilities.remove(neighborFacility);
                    i--;
                }
            }
            facility.setNeighborFacilities(neighborFacilities);

            // -- Pull Call Center Support values (PSR / CSR Support & PSR / CSR Queue Priority) from Postgres
            List<Map<String, Object>> facilitySupportValues = getFacilitySupportValues(facilityId);
            for (Map<String, Object> supportValues : facilitySupportValues) {
                logger.debug(supportValues.toString());
//                Integer fid = Integer.valueOf(supportValues.get("facility_id").toString());
                Integer psrSupport = supportValues.get("psr_supported") != null & supportValues.get("psr_supported").toString().equalsIgnoreCase("true") ? 1 : 0;
                Integer csrSupport = supportValues.get("csr_supported") != null & supportValues.get("csr_supported").toString().equalsIgnoreCase("true") ? 1 : 0;
                Integer psrPriority = supportValues.get("psr_queue_priority") != null ? Integer.valueOf(supportValues.get("psr_queue_priority").toString()) : 0;
                Integer csrPriority = supportValues.get("csr_queue_priority") != null ? Integer.valueOf(supportValues.get("csr_queue_priority").toString()) : 0;

                facility.setPsrSupported(psrSupport > 0);
                facility.setCsrSupported(csrSupport > 0);
                facility.setPsrPriority(psrPriority);
                facility.setCsrPriority(csrPriority);
            }
        }

        return facility;
    }

    @Override
    public List<Facility> findAllFacilities(boolean fullLoad) {
        List<Facility> facilities = new ArrayList<Facility>();
        Map<Integer, Facility> facilityMap = new HashMap<Integer, Facility>();
        List<Map<String, Object>> facilityOptions = jdbcTemplate.queryForList(GET_FACILITY_COLLECTION);
        int cnt = 1;
        for (Map<String, Object> officeMap : facilityOptions) {
            Facility facility = MapToFacility.convert(officeMap);
            logger.debug("(" + cnt + " out of " + facilityOptions.size() + ") About to process facility[" + facility.getFacilityId() + "-" + facility.getName() + "]");
            cnt++;
            if (fullLoad) {
                facility.getEmployees().put("Dentists", findFacilityProvider(facility.getFacilityId()));
                facility.getEmployees().put("Managers", findFacilityManagement(facility.getFacilityId()));
                populateFacilityByAttributeValues(facility);

                List<Facility> neighborFacilities = findFacilitiesByZipCode(facility.getZip());
                for(int i = 0; i < neighborFacilities.size(); i++){
                    Facility neighborFacility = neighborFacilities.get(i);
                    if(neighborFacility.getFacilityId().equals(facility.getFacilityId())){
                        neighborFacilities.remove(neighborFacility);
                        i--;
                    }
                }
                facility.setNeighborFacilities(neighborFacilities);
            }

            facilityMap.put(facility.getFacilityId(), facility);
        }

        // -- Pull Call Center Support values (PSR / CSR Support & PSR / CSR Queue Priority) from Postgres
        List<Map<String, Object>> facilitySupportValues = getFacilitySupportValues();
        for (Map<String, Object> supportValues : facilitySupportValues) {
            Integer facilityId = Integer.valueOf(supportValues.get("facility_id").toString());
            Integer psrSupport = supportValues.get("psr_supported") != null & supportValues.get("psr_supported").toString().equalsIgnoreCase("true") ? 1 : 0;
            Integer csrSupport = supportValues.get("csr_supported") != null & supportValues.get("csr_supported").toString().equalsIgnoreCase("true") ? 1 : 0;
            Integer psrPriority = supportValues.get("psr_queue_priority") != null ? Integer.valueOf(supportValues.get("psr_queue_priority").toString()) : 0;
            Integer csrPriority = supportValues.get("csr_queue_priority") != null ? Integer.valueOf(supportValues.get("csr_queue_priority").toString()) : 0;

            Facility f = facilityMap.get(facilityId);
            if (f != null) {
                f.setPsrSupported(psrSupport > 0);
                f.setCsrSupported(csrSupport > 0);
                f.setPsrPriority(psrPriority);
                f.setCsrPriority(csrPriority);
            }
        }

        facilities.addAll(facilityMap.values());
        return facilities;
    }

    @Override
    public void populateFacilityByAttributeValues(Facility facility) {
        List<AttributeValue> results = jdbcTemplate.query(GET_OFFICE_ATTRIBUTES, new Object[]{ facility.getFacilityId()}, new AttributeValueMapper());
        for(AttributeValue result : results){
            Integer groupId = result.getGroupId();
            if(groupId.equals(AttributeId.GROUP_SPECIALTY_SERVICES.getValue())){
                facility.getSpecialServices().add(result);
                continue;
            }else if(groupId.equals(AttributeId.GROUP_ALERT.getValue())){
                facility.getAlerts().add(result);
                continue;
            }
            Integer attributeId = result.getAttributeId();
            String value = result.getValue();
            AttributeId attributeIdEnum = AttributeId.getById(attributeId, "FACILITY");

            switch(attributeIdEnum){
                case ATTRIBUTE_QSI_ID:
                    facility.setQsiClinicId(value != null ? Integer.valueOf(value) : 0);
                    break;
                case ATTRIBUTE_MONDAY_HOUR:
                    facility.setMondayHour(value);
                    break;
                case ATTRIBUTE_TUESDAY_HOUR:
                    facility.setTuesdayHour(value);
                    break;
                case ATTRIBUTE_WEDNESDAY_HOUR:
                    facility.setWednesdayHour(value);
                    break;
                case ATTRIBUTE_THURSDAY_HOUR:
                    facility.setThursdayHour(value);
                    break;
                case ATTRIBUTE_FRIDAY_HOUR:
                    facility.setFridayHour(value);
                    break;
                case ATTRIBUTE_SATURDAY_HOUR:
                    facility.setSaturdayHour(value);
                    break;
                case ATTRIBUTE_SUNDAY_HOUR:
                    facility.setSundayHour(value);
                    break;
                case ATTRIBUTE_CROSS_STREETS:
                    facility.setCrossStreets(value);
                    break;
                case ATTRIBUTE_SPECIAL_NOTES:
                    facility.setSpecialNotes(value);
                    break;
                case ATTRIBUTE_ON_CALL_NAME:
                    facility.setDoctorOnCallName(value);
                    break;
                case ATTRIBUTE_ON_CALL_NUMBER:
                    facility.setDoctorOnCallNumber(value);
                    break;
                case ATTRIBUTE_LANGUAGES_SPOKEN:
                    facility.setLanguagesSpoken(value);
                    break;
                case ATTRIBUTE_ACCEPTED_STATE_PLAN:
                    facility.setAcceptedStatePlans(value);
                    break;
                case ATTRIBUTE_AGE_LIMITED:
                    facility.setAgeLimit(value);
                    break;
                case ATTRIBUTE_NITROUS_OFFERED:
                    facility.setNitrousOffered(value);
                    break;
                case ATTRIBUTE_IV_SEDATION:
                    facility.setIvSedation(value);
                    break;
                case ATTRIBUTE_OFFICE_HOURS_NOTES:
                    facility.setHoursNotes(value);
                    break;
                case ATTRIBUTE_OPEN_BOOK_SCHEDULING_ENABLED:
                    facility.setOpenBookSchedulingEnabled(value.trim().equalsIgnoreCase("true"));
                    break;
                default:
                    //logger.debug("Set facility[" + facility.getFacilityId() + "] property for Id[" + attributeId + "], name[" + result.getName() +"] and value[" + value + "]");
            }
        }
    }

    @Override
    public ArrayList<Employee> findFacilityProvider(Integer facilityId) {
        List<Map<String, Object>> employeeList = jdbcTemplate.queryForList(GET_OFFICE_PROVIDERS, facilityId);

        ArrayList<Employee> dentistList = new ArrayList<Employee>();
        for (Map<String, Object> employeeMap : employeeList) {
            Provider provider = new Provider(
                    (String)employeeMap.get("FIRST_NAME"),
                    (String)employeeMap.get("LAST_NAME"),
                    (String)employeeMap.get("TITLE")
            );
            provider.setPrefix((String) employeeMap.get("PREFIX"));
            provider.setServiceProviderId(Integer.valueOf(employeeMap.get("SERVICE_PROVIDER_ID").toString()));
            provider.setGender((String) employeeMap.get("GENDER"));
            provider.setSpecialty((String)employeeMap.get("SPECIALTY"));
            provider.setLicenseNumber((String)employeeMap.get("LICENSE_NUMBER"));
            provider.setLicenseState((String)employeeMap.get("LICENSE_STATE"));
            provider.setInstitutionName((String) employeeMap.get("INSTITUTION_NAME"));
            provider.setDegreeEarned((String) employeeMap.get("DEGREE_EARNED"));
            provider.setDegreeSuffix((String) employeeMap.get("DEGREE_SUFFIX"));
            if(employeeMap.get("DEGREE_EARNED_YEAR") != null){
                provider.setDegreeEarnedYear(Integer.valueOf(employeeMap.get("DEGREE_EARNED_YEAR").toString()));
            }
            if(employeeMap.get("START_PRACTICE_YEAR") != null){
                provider.setStartPracticeYear(Integer.valueOf(employeeMap.get("START_PRACTICE_YEAR").toString()));
            }
            if(employeeMap.get("PROVIDER_ID") != null){
                provider.setQsiProviderId(Integer.valueOf(employeeMap.get("PROVIDER_ID").toString()));
            }
            if(employeeMap.get("NPI_NUMBER") != null){
                provider.setNpiNumber(Long.valueOf(employeeMap.get("NPI_NUMBER").toString()));
            }

            populateProviderByAttributeValues(provider, facilityId);
            provider.setProviderInsurances(getProviderInsuranceByFacility(facilityId, provider.getServiceProviderId()));

            dentistList.add(provider);

        }

        return dentistList;
    }

    @Override
    public void populateProviderByAttributeValues(Provider provider, Integer facilityId) {
        List<AttributeValue> results = jdbcTemplate.query(GET_FACILITY_PROVIDER_ATTRIBUTES, new Object[]{provider.getServiceProviderId(), facilityId}, new AttributeValueMapper());
        for(AttributeValue attributeValue : results){
            Integer attributeId = attributeValue.getAttributeId();
            AttributeId attributeIdEnum = AttributeId.getById(attributeId, "PROVIDER");
            String value = attributeValue.getValue();
            switch (attributeIdEnum){
                case ATTRIBUTE_PROVIDER_LANGUAGES_SPOKEN:
                    provider.setLanguages(value);
                    break;
                case ATTRIBUTE_PROVIDER_SCHEDULE_NOTES:
                    provider.setScheduleNotes(value);
                    break;
                default:
            }
        }
    }

    @Override
    public ArrayList<Employee> findFacilityOfficeManager(Integer facilityId) {
        List<Map<String, Object>>  employeeList = jdbcTemplate.queryForList(GET_OFFICE_MANAGER, facilityId);
        ArrayList<Employee> managerList = new ArrayList<Employee>();
        for (Map<String, Object> employeeMap : employeeList) {
            Employee employee = new Employee(
                    (String)employeeMap.get("FIRST_NAME"),
                    (String)employeeMap.get("LAST_NAME"),
                    (String)employeeMap.get("TITLE")
            );
            managerList.add(employee);
        }

        return managerList;
    }

    @Override
    public ArrayList<Employee> findFacilityManagement(Integer facilityId) {
        List<Map<String, Object>>  employeeList = getOfficeManager(facilityId);
        if(employeeList.size() > 0){
            BigDecimal employeeNumber = (BigDecimal)employeeList.get(0).get("EMPLOYEE_NUMBER");
            List<Map<String, Object>> temp = jdbcTemplate.queryForList(GET_OFFICE_MANAGEMENT, employeeNumber);
            if(temp.size() > 0){
                employeeList.addAll(temp);
            }
        }
        ArrayList<Employee> managerList = new ArrayList<Employee>();
        Set<Integer> employeeNumbers = new HashSet<Integer>();
        for (int i = employeeList.size() - 1; i >= 0; i--) {
            Map<String, Object> employeeMap = employeeList.get(i);
            BigDecimal employeeNumber = (BigDecimal)employeeMap.get("EMPLOYEE_NUMBER");
            if(employeeNumber != null && !employeeNumbers.contains(employeeNumber.intValue())){
                employeeNumbers.add(employeeNumber.intValue());
                Employee employee = new Employee(
                        (String)employeeMap.get("FIRST_NAME"),
                        (String)employeeMap.get("LAST_NAME"),
                        (String)employeeMap.get("TITLE"),
                        (String)employeeMap.get("EMAIL_ADDRESS")
                );
                String email = employee.getEmail();
                if(email != null && email.length() > 0){
                    String[] temp = email.split("\\.");
                    email = "";
                    for(String str : temp){
                        email += str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase() + ".";
                    }
                    email = email.substring(0, email.length() - 1);
                    employee.setEmail(email);
                }
                managerList.add(employee);
            }
        }

        return managerList;
    }

    public List<Map<String, Object>> getOfficeManager(int facilityId) {
//        logger.debug("Searching for Office Manager for facility id: " + facilityId);
        return jdbcTemplate.queryForList(GET_OFFICE_MANAGER, facilityId);
    }


    @Override
    public List<ProviderInsurance> getProviderInsuranceByFacility(Integer facilityId, Integer serviceProviderId) {
        return jdbcTemplate.query(GET_PROVIDER_INSURANCE, new Object[]{facilityId, serviceProviderId}, new RowMapper<ProviderInsurance>() {
            @Override
            public ProviderInsurance mapRow(ResultSet rs, int rowNum) throws SQLException {
                ProviderInsurance pi = new ProviderInsurance();
                pi.setProviderId(rs.getInt("PROVIDER_ID"));
                pi.setOfficeId(rs.getInt("OFFICE_ID"));
                pi.setFacility(rs.getInt("FACILITY_ID"));
                pi.setPayerName(rs.getString("PAYER_NAME"));
                pi.setPayerStatus(rs.getString("PAYER_STATUS"));
                pi.setStartDate(rs.getDate("START_DATE"));
                pi.setEndDate(rs.getDate("END_DATE"));
                pi.setStatusDate(rs.getDate("STATUS_DATE"));
                pi.setEffectiveDate(rs.getString("EFFECTIVE_DATE"));
                return pi;
            }
        });
    }


    @Override
    public List<Facility> findFacilitiesByZipCode(String zipCode) {
        jdbcTemplate.setMaxRows(7);
        List<Facility> facilities = new ArrayList<Facility>();
        List<Map<String, Object>> facilityOptions = jdbcTemplate.queryForList(GET_FACILITIES_BY_ZIPCODE, new Object[]{zipCode});

        for (Map<String, Object> officeMap : facilityOptions) {
            facilities.add(MapToFacility.convert(officeMap));
        }
        jdbcTemplate.setMaxRows(0);
        return facilities;
    }

    class AttributeValueMapper implements RowMapper<AttributeValue>{
        public AttributeValue mapRow(ResultSet rs, int i) throws SQLException {

            AttributeValue fav = new AttributeValue();
            fav.setAttributeValueId(rs.getInt("ATTR_VALUE_ID"));
            fav.setAttributeId(rs.getInt("ATTRIBUTE_ID"));
            fav.setGroupId(rs.getInt("GROUP_ID"));
            fav.setName(rs.getString("ATTRIBUTE_NAME"));
            fav.setDescription(rs.getString("ATTRIBUTE_DESCRIPTION"));
            fav.setValue(rs.getString("ATTR_VALUE"));
            if(fav.getValue() == null){
                fav.setValue("");
            }
            fav.setCreatedBy(rs.getInt("CREATE_EMPLOYEE"));
            fav.setCreatedOn(rs.getTimestamp("CREATE_DATETIME"));
            fav.setCreatedByName(rs.getString("EMP_NAME"));

            return fav;
        }
    }
}
