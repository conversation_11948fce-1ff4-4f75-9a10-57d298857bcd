package com.smilebrands.callcentersupport.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Calendar;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * Created by phongpham on 8/26/14.
 */
public class TranslateUtil {

    private final static Map<String, Object> spanishMap = new HashMap<String, Object>();

    public static final Logger logger = LoggerFactory.getLogger(VelocityUtils.class);

    static{
        Map<String, String> weekDayMap = new HashMap<String, String>();
        weekDayMap.put("Monday", "Lunes");
        weekDayMap.put("Tuesday", "Martes");
        weekDayMap.put("Wednesday","Miercoles");
        weekDayMap.put("Thursday","Jueves");
        weekDayMap.put("Friday","Viernes");
        weekDayMap.put("Saturday","Sabado");
        weekDayMap.put("Sunday","Domingo");
        spanishMap.put("weekDay", weekDayMap);

        Map<String, String> monthMap = new HashMap<String, String>();
        monthMap.put("January", "Enero");
        monthMap.put("February", "Febrero");
        monthMap.put("March", "Marzo");
        monthMap.put("April", "Abril");
        monthMap.put("May", "Mayo");
        monthMap.put("June", "Junio");
        monthMap.put("July", "Julio");
        monthMap.put("August", "Agosto");
        monthMap.put("September", "Septiembre");
        monthMap.put("October", "Octubre");
        monthMap.put("November", "Noviembre");
        monthMap.put("December", "Diciembre");
        spanishMap.put("month", monthMap);

        Map<Integer, Map<String, String>> numberMap = new HashMap<Integer, Map<String, String>>();
        Map<String, String> oneMap = new HashMap<String, String>();
        oneMap.put("count", "Uno");
        oneMap.put("ordinal", "Primero");
        oneMap.put("time", "Una");
        numberMap.put(1, oneMap);
        Map<String, String> twoMap = new HashMap<String, String>();
        twoMap.put("count", "Dos");
        numberMap.put(2, twoMap);
        Map<String, String> threeMap = new HashMap<String, String>();
        threeMap.put("count", "Tres");
        numberMap.put(3, threeMap);
        Map<String, String> fourMap = new HashMap<String, String>();
        fourMap.put("count", "Cuatro");
        numberMap.put(4, fourMap);
        Map<String, String> fiveMap = new HashMap<String, String>();
        fiveMap.put("count", "Cinco");
        numberMap.put(5, fiveMap);
        Map<String, String> sixMap = new HashMap<String, String>();
        sixMap.put("count", "Seis");
        numberMap.put(6, sixMap);
        Map<String, String> sevenMap = new HashMap<String, String>();
        sevenMap.put("count", "Siete");
        numberMap.put(7, sevenMap);
        Map<String, String> eightMap = new HashMap<String, String>();
        eightMap.put("count", "Ocho");
        numberMap.put(8, eightMap);
        Map<String, String> nineMap = new HashMap<String, String>();
        nineMap.put("count", "Nueve");
        numberMap.put(9, nineMap);
        Map<String, String> tenMap = new HashMap<String, String>();
        tenMap.put("count", "Diez");
        numberMap.put(10, tenMap);
        Map<String, String> elevenMap = new HashMap<String, String>();
        elevenMap.put("count", "Once");
        numberMap.put(11, elevenMap);
        Map<String, String> twelveMap = new HashMap<String, String>();
        twelveMap.put("count", "Doce");
        numberMap.put(12, twelveMap);
        Map<String, String> thirteenMap = new HashMap<String, String>();
        thirteenMap.put("count", "Trece");
        numberMap.put(13, thirteenMap);
        Map<String, String> fourteenMap = new HashMap<String, String>();
        fourteenMap.put("count", "Catorce");
        numberMap.put(14, fourteenMap);
        Map<String, String> fifteenMap = new HashMap<String, String>();
        fifteenMap.put("count", "Quince");
        numberMap.put(15, fifteenMap);
        Map<String, String> sixteenMap = new HashMap<String, String>();
        sixteenMap.put("count", "Dieciseis");
        numberMap.put(16, sixteenMap);
        Map<String, String> seventeenMap = new HashMap<String, String>();
        seventeenMap.put("count", "Diecisiete");
        numberMap.put(17, seventeenMap);
        Map<String, String> eighteenMap = new HashMap<String, String>();
        eighteenMap.put("count", "Dieciocho");
        numberMap.put(18, eighteenMap);
        Map<String, String> nineteenMap = new HashMap<String, String>();
        nineteenMap.put("count", "Diecinueve");
        numberMap.put(19, nineteenMap);
        Map<String, String> twentyMap = new HashMap<String, String>();
        twentyMap.put("count", "Veinte");
        numberMap.put(20, twentyMap);
        for(int i=1; i<10; i++){
            Map<String, String> twenty_ishMap = new HashMap<String, String>();
            Map<String, String> tmp = numberMap.get(i);
            if(tmp != null && tmp.get("count") != null){
                twenty_ishMap.put("count", "Veinti" + tmp.get("count").toLowerCase());
                numberMap.put(20+i, twenty_ishMap);
            }
        }
        Map<String, String> thirtyMap = new HashMap<String, String>();
        thirtyMap.put("count", "Treinta");
        numberMap.put(30, thirtyMap);
        for(int i=1; i<10; i++){
            Map<String, String> thirty_ishMap = new HashMap<String, String>();
            Map<String, String> tmp = numberMap.get(i);
            if(tmp != null && tmp.get("count") != null){
                thirty_ishMap.put("count", "Treinta y " + tmp.get("count").toLowerCase());
                numberMap.put(30+i, thirty_ishMap);
            }
        }
        Map<String, String> fortyMap = new HashMap<String, String>();
        fortyMap.put("count", "Cuarenta");
        numberMap.put(40, fortyMap);
        for(int i=1; i<10; i++){
            Map<String, String> thirty_ishMap = new HashMap<String, String>();
            Map<String, String> tmp = numberMap.get(i);
            if(tmp != null && tmp.get("count") != null){
                thirty_ishMap.put("count", "Cuarenta y " + tmp.get("count").toLowerCase());
                numberMap.put(40+i, thirty_ishMap);
            }
        }
        Map<String, String> fiftyMap = new HashMap<String, String>();
        fiftyMap.put("count", "Cincuenta");
        numberMap.put(50, fiftyMap);
        for(int i=1; i<10; i++){
            Map<String, String> thirty_ishMap = new HashMap<String, String>();
            Map<String, String> tmp = numberMap.get(i);
            if(tmp != null && tmp.get("count") != null){
                thirty_ishMap.put("count", "Cincuenta y " + tmp.get("count").toLowerCase());
                numberMap.put(50+i, thirty_ishMap);
            }
        }
        Map<String, String> sixtyMap = new HashMap<String, String>();
        sixtyMap.put("count", "Sesenta");
        numberMap.put(60, sixtyMap);
        spanishMap.put("number",numberMap);

        Map<Integer, String> cardinalMap = new HashMap<Integer, String>();
        cardinalMap.put(100, "cien");
        cardinalMap.put(1000, "mil");
        spanishMap.put("cardinal", cardinalMap);

        Map<String, Object> phraseMap = new HashMap<String, Object>();
        phraseMap.put("We have found an appointment for", "Hemos encontrado una cita para");
        phraseMap.put("for", "para");
        phraseMap.put("closed", "cerrado");
        spanishMap.put("phrase", phraseMap);
    }

    public static String translateMonth(int month, String language){
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.MONTH, month);
        String monthStr = cal.getDisplayName(Calendar.MONTH, Calendar.LONG, Locale.US);
        if(language.equalsIgnoreCase("SP")){
            Map<String,String> monthMap = (Map<String, String>)spanishMap.get("month");
            if(monthMap.get(monthStr) != null && monthMap.get(monthStr) != null){
               monthStr = monthMap.get(monthStr);
            }
        }
        return monthStr;
    }

    public static String translateWeekDay(int weekDay, String language){
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_WEEK, weekDay);
        String weekDayStr = cal.getDisplayName(Calendar.DAY_OF_WEEK, Calendar.LONG, Locale.US);
        if(language.equalsIgnoreCase("SP")){
            Map<String, String> weekDayMap = (Map<String, String>)spanishMap.get("weekDay");
            if(weekDayMap != null && weekDayMap.get(weekDayStr) != null){
                weekDayStr = weekDayMap.get(weekDayStr);
            }
        }
        return weekDayStr;
    }

    public static String translateNumber(int number, String type, String language){
        String result = number + "";
        if(language.equalsIgnoreCase("SP")){
            result = "";
            Map<String, Object> temp = (Map<String, Object>)spanishMap.get("number");
            if(temp != null){
                if(number <= 60 && temp.get(number) != null){
                    Map<String, String> numberMap = (Map<String, String>)temp.get(number);
                    if(numberMap != null){
                        result = numberMap.get(type) != null ? numberMap.get(type) : numberMap.get("count");
                    }
                }else if(number >= 1000){
                    String numberStr = number + "";
                    Integer thousands = Integer.valueOf(numberStr.substring(0, numberStr.length() - 3));
                    Integer remainder = Integer.valueOf(numberStr.substring(numberStr.length() - 3, numberStr.length()));
                    result = translateNumber(thousands, "count", language);
                    result += " mil " + translateNumber(remainder, type, language);
                }
            }
        }
        return result;
    }

    public static String translatePhrase(String phrase, String language){
        String result = phrase;
        if(phrase != null && language.equalsIgnoreCase("SP")){
            if(spanishMap.get("phrase") != null){
                if(((Map<String, String>)spanishMap.get("phrase")).get(phrase.trim()) != null){
                    result = (((Map<String, String>) spanishMap.get("phrase")).get(phrase.trim()));
                }else if(((Map<String, String>)spanishMap.get("phrase")).get(phrase.trim().toLowerCase()) != null){
                    result = (((Map<String, String>) spanishMap.get("phrase")).get(phrase.trim().toLowerCase()));
                }else if(((Map<String, String>)spanishMap.get("phrase")).get(phrase.trim().toUpperCase()) != null){
                    result = (((Map<String, String>) spanishMap.get("phrase")).get(phrase.trim().toUpperCase()));
                }
            }
        }
        return result;
    }

    public static String translateDate(int dayOfMonth, int month, int year, String language, String prefix){
        String result = "";
        Calendar cal = Calendar.getInstance();
        cal.set(year, month, dayOfMonth);
        result = prefix + cal.getDisplayName(Calendar.MONTH, Calendar.LONG, Locale.US);
        result += " " + VelocityUtils.getOrdinalValue(cal.get(Calendar.DAY_OF_MONTH));
        result += " " + cal.get(Calendar.YEAR);
        if(language.equalsIgnoreCase("SP")){
            String spDayOfMonth = translateNumber(dayOfMonth, "ordinal", language);
            String spMonth = translateMonth(month, language);
            String spYear = translateNumber(year, "count", language);
            if(spDayOfMonth != null && spDayOfMonth.trim().length() > 0
                    && spMonth != null && spMonth.trim().length() > 0
                    && spYear != null && spYear.trim().length() > 0){
                result = "el " + spDayOfMonth + " de " + spMonth + " " + spYear;
            }
        }
        return result;
    }

    public static String translateTime(int hour, int minute, String language, String prefix){
        String result = prefix + hour + ":" + minute;
        if(language.equalsIgnoreCase("SP")){
            String spHour = translateNumber(hour, "time", language);
            String spMinute = translateNumber(minute, "time", language);
            if(spHour != null && spHour.trim().length() > 0
                    && (minute == 0 || (spMinute != null && spMinute.trim().length() > 0))){
                if(hour > 1){
                    result = "a las ";
                }else{
                    result = "a la ";
                }
                result += spHour + " " + spMinute;
            }
        }
        return result;
    }

    public static String translateAMPM(String ampPm, String language){
        String result = ampPm;
        if(language.equalsIgnoreCase("SP")){
            String tmp = result.substring(0,1);
            tmp += " M";
            result = tmp;
        }
        return result;
    }
}
