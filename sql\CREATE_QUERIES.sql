CREATE TABLE "CISCOADM"."CALL_LOG_FILE"  (
	"CALL_LOG_FILE_ID" 	NUMBER(10) NOT NULL,
	"UUID"             	VARCHAR2(50) NOT NULL,
	"EMPLOYEE_NUMBER" NUMBER(10) NOT NULL,
	"FILE_PATH"        	VARCHAR2(100) NOT NULL,
	"FILE_TYPE"        	VARCHAR2(25) NOT NULL,
	"FILE_SOURCE" VARCHAR2(25) NOT NULL,
	"CREATE_DATETIME"  	TIMESTAMP NOT NULL,
	"IS_ACTIVE"        	NUMBER(1) NOT NULL,
	"UPDATE_DATETIME"   TIMESTAMP NULL,
	"INACTIVE_DATETIME"	TIMESTAMP NULL,
	"INACTIVE_EMPLOYEE"	NUMBER(10) NULL,
	CONSTRAINT "CALL_LOG_FILE_PK" PRIMARY KEY("CALL_LOG_FILE_ID")

)
STORAGE( BUFFER_POOL DEFAULT )
;

CREATE INDEX "CISCOADM"."UUID_FILE_TYPE_IDX"
	ON "CISCOADM"."CALL_LOG_FILE"("UUID", "FILE_TYPE")
;

CREATE SEQUENCE "CISCOADM"."CALL_LOG_FILE_ID_SEQ"
	INCREMENT BY 1
	START WITH 1
	NOMAXVALUE
	NOMINVALUE
	NOCYCLE
	NOORDER
;

CREATE SYNONYM "BNDAPP"."CALL_LOG_FILE"
	FOR "CISCOADM"."CALL_LOG_FILE"
;

grant select, insert, update on ciscoadm.CALL_LOG_FILE to bndapp;

CREATE SYNONYM "BNDAPP"."CALL_LOG_FILE_ID_SEQ"
	FOR "CISCOADM"."CALL_LOG_FILE_ID_SEQ"
;

grant select on ciscoadm.CALL_LOG_FILE_ID_SEQ to bndapp;

CREATE INDEX "CISCOADM"."UUID_EMPLOYEE_NUMBER_IDX"
	ON "CISCOADM"."AGENT_CALL_LOG"("UUID", "EMPLOYEE_NUMBER")
;