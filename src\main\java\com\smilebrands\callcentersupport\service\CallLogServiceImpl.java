package com.smilebrands.callcentersupport.service;

import com.smilebrands.callcentersupport.domain.*;
import com.smilebrands.callcentersupport.repo.CallRepository;
import com.smilebrands.callcentersupport.repo.JdbcRepository;
import com.smilebrands.callcentersupport.repo.MongoRepository;
import com.smilebrands.callcentersupport.repo.jpa.AppointmentCallLogRepository;
import com.smilebrands.callcentersupport.repo.jpa.CallLogRepository;
import com.smilebrands.callcentersupport.util.DateTimeUtils;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * Created by phongpham on 11/10/14.
 */
@Service
@Transactional(readOnly = true)
public class CallLogServiceImpl extends BaseService implements CallLogService {

    @Autowired
    private CallRepository callRepository;

    @Autowired
    private CallLogRepository callLogRepository;

    @Autowired
    private AppointmentCallLogRepository appointmentCallLogRepository;

    @Autowired
    private MongoRepository mongoRepository;

    @Autowired
    protected JdbcRepository jdbcRepository;

    @Autowired
    private CommunicationService communicationService;

    @Autowired
    private CallService callService;

    @Override
    @Transactional(readOnly = false)
    public List<CallLog> populateCallLogForAgentCall(AgentCall agentCall, Integer employeeNumber, Integer facilityId, String screenType, boolean sendExceptionEmail) {
        return doPopulateCallLogForAgentCall(agentCall, employeeNumber, facilityId, screenType, sendExceptionEmail, "MONGO");
    }

    @Override
    @Transactional(readOnly = false)
    public List<CallLog> doPopulateCallLogForAgentCall(AgentCall agentCall, Integer employeeNumber, Integer facilityId, String screenType, boolean sendExceptionEmail, String entrySource){
        List<CallLog> callLogs = new ArrayList<CallLog>();
        List<Patient> patients = new ArrayList<Patient>();
        int callLogCount = 0;
        String message = "";

        if (screenType != null && !screenType.equalsIgnoreCase("wr") && (agentCall.getSessionId() == null || agentCall.getSessionId().indexOf("abc") != -1)) {
            message = "Agent Call with UUID[" + agentCall.getUuid() + "]" + " has invalid session ID[" + agentCall.getSessionId() + "]";
            communicationService.sendNotificationViaEmail(CommunicationServiceImpl.DEVELOPER_EMAIL, message, "Warning on populating call log for agent call");
            return callLogs;
        }
        if (callService.isOldScreenPop(null, agentCall.getUuid(), 1)) {
            message = "Try to populate call log for old call on " + agentCall.getCallRegisteredDate();
            mongoRepository.addLogAction(agentCall.getUuid(), ActionLog.SCREEN_POP_ACTION, message);
            return callLogs;
        }
        int appointmentCallLogCount = 0;
        patients.addAll(agentCall.getPatients());
        String callCenterName = callService.getCallCenterName(employeeNumber, agentCall.getUuid(), screenType, false);
        CallCenter callCenter = mongoRepository.getCallCenterByName(callCenterName);
        Integer locationOffset = callCenter != null ? callCenter.getTimezoneOffset() : null;
        Set<String> keyForAppointmentCallLog = new HashSet<String>();

        for(LoadedEmployee employee : agentCall.getLoadedEmployees()){
            LoadedEmployee logEmployee = null;
            if((employeeNumber == null || employeeNumber.equals(0) || employeeNumber.equals(employee.getEmployeeNumber()))
                    && (screenType == null || screenType.equalsIgnoreCase(employee.getScreenType()))){
                logEmployee = employee;
            }
            if(logEmployee != null){
                CallLog callLog = new CallLog();
                CallLog existing = null;
                if(logEmployee.getCallLogId() != null){
                    logger.debug("M2O :: Searching for existing Call Log entry in ORACLE for callLogId [" + logEmployee.getCallLogId() + "]");
                    existing = callLogRepository.findOne(logEmployee.getCallLogId());
                }

                String session = logEmployee.getSessionId() != null && logEmployee.getSessionId().trim().length() > 0 ? logEmployee.getSessionId() : agentCall.getSessionId();

                callLog.setEntrySource(entrySource);
                callLog.setUuid(agentCall.getUuid());
                callLog.setSessionId(session);
                callLog.setAgentCallCenterName(callCenterName);
                callLog.setCallCenterName(callService.getCallCenterName(logEmployee.getEmployeeNumber(), agentCall.getUuid(), logEmployee.getScreenType(), true));

                setQueueName(agentCall.getUuid(), callLog);

                callLog.setEmployeeNumber(employee.getEmployeeNumber());
                callLog.setAgentType(employee.getScreenType());
                callLog.setPhoneType(agentCall.getPhoneType());
                callLog.setFacilityViewCount(agentCall.getLoadedFacilities().size());
                callLog.setCreateEmployeeNumber(logEmployee.getEmployeeNumber());
                callLog.setCreateDateTime(new Date());

                callLog.setPartnerId(logEmployee.getPartnerId());
                callLog.setPartnerSessionId(logEmployee.getPartnerSessionId());
                callLog.setPartnerStartDateTime(logEmployee.getPartnerStartDateTime());
                callLog.setPartnerTimezone(logEmployee.getPartnerTimezone());
                callLog.setPartnerRingTime(logEmployee.getPartnerRingTime());
                callLog.setPartnerTalkTime(logEmployee.getPartnerTalkTime());
                callLog.setPartnerHoldTime(logEmployee.getPartnerHoldTime());
                callLog.setPartnerWorkTime(logEmployee.getPartnerWorkTime());
                callLog.setPartnerCalledNumber(logEmployee.getPartnerCalledNumber());
                callLog.setPartnerTeamId(logEmployee.getPartnerTeamId());
                callLog.setPartnerMetServiceLevel(logEmployee.getPartnerMetServiceLevel());

                if(existing == null){
                    callLog.setReasonCode(employee.getReasonCode());
                    callLog.setResolutionCode(employee.getResolutionCode());
                    callLog.setCallDateTime(DateTimeUtils.getGMTTimeFromLocationOffset(employee.getLoadedDateTime(), locationOffset));
                }else{
//                    logger.debug("Call Log with ID[" + callLog.getCallLogId() + "] and UUID[" + callLog.getUuid() + "] already exists in Oracle.");
                    message = "Try to populate agent call log for UUID[" + agentCall.getUuid() + "] with employee number[" + logEmployee.getEmployeeNumber() + "] and call log ID[" + existing.getCallLogId() + "]";
//                    communicationService.sendNotificationViaEmail(CommunicationServiceImpl.DEVELOPER_EMAIL, message, "Warning on populating call log for agent call");
                    logger.debug("M2O :: {}", message);
//                    continue;
                    callLog.setReasonCode(employee.getReasonCode());
                    callLog.setResolutionCode(employee.getResolutionCode());
                    callLog.setCallDateTime(existing.getCallDateTime());
                    callLog.setUpdateDateTime(new Date());
                    callLog.setUpdateEmployeeNumber(logEmployee.getEmployeeNumber());

                    if(existing.getAppointmentCallLogId() != null){
                        AppointmentCallLog existingAppointment = appointmentCallLogRepository.findOne(existing.getAppointmentCallLogId());
                        if(existingAppointment != null && existingAppointment.getAppointmentStatus().equalsIgnoreCase("NO_APPT_SCHEDULED")){
                            callLog.setCallLogId(existing.getCallLogId());
                            callLog.setLogStatus("ACTIVE");
                        }
                    }else{
                        callLog.setCallLogId(existing.getCallLogId());
                        callLog.setLogStatus("ACTIVE");
                    }
                }

                boolean callLogHasAppointment = false;
                boolean existingAppointment = false;
                for(int i=0; i<patients.size(); i++){
                    Patient patient = patients.get(i);
                    if(patient.getEmpEntered().equals(logEmployee.getEmployeeNumber())
                            && patient.getScreenType() != null && patient.getScreenType().equalsIgnoreCase(screenType)){
                        String key = patient.getPatientId() + ":::" + patient.getFacilityId() + ":::" + patient.getNextAppointmentDate() + ":::" + patient.getNextAppointmentTime();
                        if(keyForAppointmentCallLog.contains(key)){
                            continue;
                        }
                        keyForAppointmentCallLog.add(key);
                        AppointmentCallLog appointment = null;
                        if(patient.getAppointmentCallLogId() != null){
                            logger.debug("M2O :: Find appointment call log with ID[" + patient.getAppointmentCallLogId() + "]");
                            appointment = appointmentCallLogRepository.findOne(patient.getAppointmentCallLogId());
                        }
                        if(appointment == null){
                            appointment= new AppointmentCallLog();
                        }else{
                            logger.warn("M2O :: Appointment with APPT_CALL_LOG_ID[" + appointment.getAppointmentCallLogId() + "] already exists in Oracle!");
                            message = "Try to populate appointment call log for UUID[" + agentCall.getUuid() + "] with employee number[" + logEmployee.getEmployeeNumber() + "]," +
                                    " patient ID [" + patient.getPatientId() + "] @ " + patient.getNextAppointmentDate() + " " + patient.getNextAppointmentTime() + "," +
                                    " and appointment call log ID[" + patient.getAppointmentCallLogId() + "]";
                            if(sendExceptionEmail){
                                communicationService.sendNotificationViaEmail(CommunicationServiceImpl.DEVELOPER_EMAIL, message, "Warning on populating appointment call log for agent call");
                            }else{
                                logger.debug("M2O :: {}", message);
                            }
                            existingAppointment = true;
                            continue;
                        }

                        Date apptDateTime = VelocityUtils.parseDate(patient.getNextAppointmentDate() + " " + patient.getNextAppointmentTime(), "MM-dd-yyyy HHmm");
                        if(apptDateTime == null){
                            apptDateTime = patient.getNextAppointmentDateTime();
                        }
//                        if(screenType.equalsIgnoreCase("csr")){
//                            callLog.setAllowCredit(false);
//                        }

                        appointment.setTempPatientId(patient.getPatientId());
                        appointment.setFacilityId(patient.getFacilityId());
                        appointment.setAppointmentDateTime(apptDateTime);
                        appointment.setCreateDateTime(new Date());
                        appointment.setCreateEmployeeNumber(patient.getEmpEntered());
                        appointment.setIsOrtho(patient.isOrtho());
                        appointment.setInsuranceWaiting(patient.isInsuranceWaiting());
                        appointment.setEmailCaptured(patient.isEmailCaptured());

                        CallLog callLogForAppointment = null;
                        if(callLogHasAppointment){
                            try {
                                callLogForAppointment = callLog.copy();
                                callLogForAppointment.setCallLogId(null);
                                callLogForAppointment.setAppointmentCallLogId(null);
                                callLogForAppointment = callLogRepository.save(callLogForAppointment);
                                logger.debug("M2O :: Saved CallLog for employee [" + callLogForAppointment.getEmployeeNumber() + "]");
                                callLogCount++;
                                logEmployee.setCallLogId(callLog.getCallLogId());
                                callLogs.add(callLogForAppointment);
                            } catch (CloneNotSupportedException e) {
                                e.printStackTrace();
                            }
                        }else{
                            if(callLog.getCallLogId() == null){
                                callLog.setCallDateTime(DateTimeUtils.getGMTTimeFromLocationOffset(employee.getLoadedDateTime(), locationOffset));
                                callLog = callLogRepository.save(callLog);
                                logEmployee.setCallLogId(callLog.getCallLogId());
                                callLogs.add(callLog);
                                callLogCount++;
                            }
                            callLogForAppointment = callLog;
                            callLogHasAppointment = true;
                        }
                        callLogForAppointment.setTempPatientId(patient.getPatientId());
                        callLogForAppointment.setFacilityId(patient.getFacilityId());
                        callLogForAppointment.setEmailCaptured(patient.isEmailCaptured());
                        callLogForAppointment.setInsuranceWaiting(patient.isInsuranceWaiting());
                        if(!patient.isOrtho()){
                            Facility facility = mongoRepository.findFacilityDetail(patient.getFacilityId());
                            callLogForAppointment.setLiberty(facility != null ? facility.isLibertySupported() : false);
                            callLogForAppointment.setOpenBook(facility != null ? facility.isOpenBookSchedulingEnabled() : false);
                        }else{
                            callLogForAppointment.setLiberty(false);
                            callLogForAppointment.setOpenBook(false);
                        }
                        appointment.setCallLogId(callLogForAppointment.getCallLogId());
                        appointment = appointmentCallLogRepository.save(appointment);
                        logger.debug("M2O :: Saved Appointment CallLog [" + appointment.getAppointmentCallLogId() + "] for Employee [" + callLogForAppointment.getEmployeeNumber() + "] " +
                                "with Patient ID[" + callLogForAppointment.getTempPatientId() + "] " +
                                "and Facility ID[" + callLogForAppointment.getFacilityId() + "] " +
                                " @ " + appointment.getAppointmentDateTime());
                        callLogForAppointment.setAppointmentCallLogId(appointment.getAppointmentCallLogId());
                        callLogForAppointment = callLogRepository.save(callLogForAppointment);
                        appointmentCallLogCount++;
                        patient.setAppointmentCallLogId(appointment.getAppointmentCallLogId());
                        patients.remove(patient);
                        i--;
                    }
                }

                if(!callLogHasAppointment && !existingAppointment){
                    if(callLog.getCallLogId() == null){
                        callLog = callLogRepository.save(callLog);
                        logger.debug("M2O :: Saved CallLog for Employee [" + callLog.getEmployeeNumber() + "] and got CallLogId [" + callLog.getCallLogId() + "]");
                        callLogCount++;
                        logEmployee.setCallLogId(callLog.getCallLogId());
                        callLogs.add(callLog);
                    }
                    AppointmentCallLog appointmentCallLog = null;
                    if(existing != null && existing.getAppointmentCallLogId() != null){
                        appointmentCallLog = appointmentCallLogRepository.findOne(existing.getAppointmentCallLogId());
                        if(appointmentCallLog != null && !appointmentCallLog.getAppointmentStatus().equalsIgnoreCase("NO_APPT_SCHEDULED")){
                            appointmentCallLog = null;
                        }
                        if(callLog.getCallLogId() == null){
                            callLog.setCallLogId(existing.getCallLogId());
                        }
                    }
                    if(appointmentCallLog == null){
                        appointmentCallLog = new AppointmentCallLog();
                        appointmentCallLog.setCallLogId(callLog.getCallLogId());
                        appointmentCallLog.setAppointmentStatus("NO_APPT_SCHEDULED");
                        appointmentCallLog.setCreateEmployeeNumber(logEmployee.getEmployeeNumber());
                        appointmentCallLog = appointmentCallLogRepository.save(appointmentCallLog);
                        appointmentCallLogCount++;
                    }
                    if(facilityId != null && facilityId > 0){
                        callLog.setFacilityId(facilityId);
                        Facility facility = mongoRepository.findFacilityDetail(facilityId);
                        callLog.setLiberty(facility != null ? facility.isLibertySupported() : false);
                        callLog.setOpenBook(facility != null ? facility.isOpenBookSchedulingEnabled() : false);
                    }
                    callLog.setAppointmentCallLogId(appointmentCallLog.getAppointmentCallLogId());
                    callLog.setLogStatus("CLOSED");
                    callLogRepository.save(callLog);
                }
            }
        }
        logger.debug("M2O :: Update agent call with ID[" + agentCall.getUuid() + "] " +
                "with " + callLogCount + " call log(s) " +
                " and " + appointmentCallLogCount + " appointment call log(s).");
        mongoRepository.updateAgentCall(agentCall);
        return callLogs;
    }

    @Override
    public List<V_CallLog> getCallLogsByDateAndSupervisor(Integer reportForAgentId, Date callDate, boolean checkForEdit, Integer agentId) {
//        Date dt = new Date();
        List<V_CallLog> list = callRepository.findAgentCallLogs(reportForAgentId, callDate);
//        logger.debug("time taken to query: {}", (new Date().getTime()-dt.getTime()));
//        dt = new Date();
        Set<Integer> supervisedAgentIds = new HashSet<Integer>();
        if(list.size() > 0 && agentId != null && !agentId.equals(0)){
            supervisedAgentIds = getSupervisedAgentIds(agentId);
        }
//        logger.debug("time taken to query supervised agent ids: {}", (new Date().getTime()-dt.getTime()));
//        dt = new Date();
        if(checkForEdit){
            for(V_CallLog v : list){
                String agentType = v.getAgentCallLog().getAgentType();
                Integer employeeNumber = v.getAgentCallLog().getEmployeeNumber();
                if(agentId == null || agentId.equals(0)
                        || agentId.equals(employeeNumber)
                        || supervisedAgentIds.contains(employeeNumber)
                        ){
                    v.getAgentCallLog().setEditable(true);
                }else{
                    v.getAgentCallLog().setEditable(false);
                }
            }
        }

//        logger.debug("time taken to load call logs: {}", (new Date().getTime()-dt.getTime()));
        return list;
    }

    @Override
    public Set<Integer> getSupervisedAgentIds(Integer supervisorId) {
        Set<Integer> supervisedAgentIds = new HashSet<Integer>();
        List<CallCenterEmployee> team = jdbcRepository.getSupervisedAgents(supervisorId);
        for(CallCenterEmployee cce : team){
            supervisedAgentIds.add(cce.getEmployeeNumber());
        }
        return supervisedAgentIds;
    }

    @Override
    @Transactional(readOnly = false)
    public String updateCallLog(Map<String, Object> map, Integer createEmployeeNumber){
        String message = "";
        int update = 0;
        int add = 0;
        List<Map<String, Object>> patients = (List<Map<String, Object>>)map.get("patients");
        String uuid = (String)map.get("uuid");
        String screenType = (String)map.get("screenType");
        screenType = screenType != null ? screenType.toLowerCase() : "psr";
        Integer employeeNumber = Integer.valueOf((String) map.get("employeeNumber"));
        AgentCall agentCall = mongoRepository.getAgentCall(uuid);
        Date current = new Date();
        String callCenterName = callService.getCallCenterName(employeeNumber, uuid, screenType, false);
        CallCenter callCenter = mongoRepository.getCallCenterByName(callCenterName);
        Integer locationOffset = callCenter != null ? callCenter.getTimezoneOffset() : null;
        Set<String> keyForAppointmentCallLog = new HashSet<String>();
        String existingCallCenterName = null;

        for(Map<String, Object> patient : patients){
            CallLog callLog = null;
            AppointmentCallLog appointmentCallLog = null;
            Integer facilityId = Integer.valueOf((String)patient.get("facilityId"));
            Long patientId = Long.valueOf((String)patient.get("patientId"));
            String apptDateTimeStr = (String)patient.get("nextAppointmentDateTime");
            Date apptDateTime = VelocityUtils.parseDate(apptDateTimeStr, "yyyy-MM-dd'T'HH:mm:ss");

            String key = patientId + ":::" + facilityId + ":::" + apptDateTimeStr;
            if(keyForAppointmentCallLog.contains(key)){
                continue;
            }
            keyForAppointmentCallLog.add(key);

            Facility facility = mongoRepository.findFacilityDetail(facilityId);
            boolean emailCaptured = (Boolean)patient.get("emailCaptured");
            boolean insuranceWaiting = (Boolean)patient.get("insuranceWaiting");//patient.get("insuranceWaiting") != null && patient.get("insuranceWaiting").equalsIgnoreCase("true");
            boolean isOrtho = (Boolean)patient.get("isOrtho");
            if(patient.get("agentCallLogId") != null && ((String)patient.get("agentCallLogId")).trim().length() > 0){
                callLog = callLogRepository.findOne(Long.valueOf((String)patient.get("agentCallLogId")));
            }
//            if(patient.get("apptCallLogId") != null && ((String)patient.get("apptCallLogId")).trim().length() > 0){
            if(callLog != null){
                appointmentCallLog = appointmentCallLogRepository.findOne(callLog.getAppointmentCallLogId());
            }

            boolean checkIfAppointmentExist = jdbcRepository.checkIfApptCallLogExist(patientId, apptDateTime, facilityId, appointmentCallLog != null ? appointmentCallLog.getAppointmentCallLogId() : null);
            boolean allowCredit = patient.get("allowCredit") != null ? (Boolean)patient.get("allowCredit") : false;

            if(callLog != null && appointmentCallLog != null){
                existingCallCenterName = callLog.getCallCenterName();
                boolean changeAllowCredit = false;

                if(patient.get("allowCredit") != null){
                    changeAllowCredit = callLog.getAllowCredit() != allowCredit;
                }
                String existingStr = VelocityUtils.getDateAsString(appointmentCallLog.getAppointmentDateTime(), "yyyy-MM-dd'T'HH:mm:ss");
                if(!facilityId.equals(callLog.getFacilityId()) || !patientId.equals(callLog.getTempPatientId()) || !apptDateTimeStr.equalsIgnoreCase(existingStr)
                        || emailCaptured != callLog.isEmailCaptured() || insuranceWaiting != callLog.isInsuranceWaiting()
                        || isOrtho != appointmentCallLog.getIsOrtho() || changeAllowCredit){

                    if(checkIfAppointmentExist){
                        throw new RuntimeException("Appointment Call Log exists for patient ID[" + patientId + "] @ office[" + facility.getFacilityId() + "-" + facility.getName() + "] at [" + apptDateTimeStr + "]");
                    }

                    callLog.setFacilityId(facilityId);
                    callLog.setPatientId(null);
                    callLog.setQsiUniqueId(null);
                    callLog.setTempPatientId(patientId);
                    callLog.setCallLogUpdated(0);
                    callLog.setLogStatus("ACTIVE");
                    callLog.setEmailCaptured(emailCaptured);
                    callLog.setInsuranceWaiting(insuranceWaiting);
                    callLog.setUpdateDateTime(current);
                    callLog.setUpdateEmployeeNumber(createEmployeeNumber);
                    callLog.setAllowCredit(allowCredit);
                    if(changeAllowCredit){
                        callLog.setAdminOverridden(true);
                    }
                    if(!isOrtho && facility != null){
                        callLog.setLiberty(facility.isLibertySupported());
                        callLog.setOpenBook(facility.isOpenBookSchedulingEnabled());
                    }else{
                        callLog.setLiberty(false);
                        callLog.setOpenBook(false);
                    }

                    appointmentCallLog = new AppointmentCallLog();
                    appointmentCallLog.setAppointmentDateTime(apptDateTime);
                    appointmentCallLog.setCreateDateTime(current);
                    appointmentCallLog.setCreateEmployeeNumber(createEmployeeNumber);
                    appointmentCallLog.setCallLogId(callLog.getCallLogId());
                    appointmentCallLog.setTempPatientId(patientId);
                    appointmentCallLog.setFacilityId(facilityId);
                    appointmentCallLog.setIsOrtho(isOrtho);
                    appointmentCallLog.setAllowCredit(allowCredit);
                    appointmentCallLog.setEmailCaptured(emailCaptured);
                    appointmentCallLog.setInsuranceWaiting(insuranceWaiting);

                    appointmentCallLog = appointmentCallLogRepository.save(appointmentCallLog);

                    callLog.setAppointmentCallLogId(appointmentCallLog.getAppointmentCallLogId());
                    logger.debug("about to update call log with ID[" + callLog.getCallLogId() + "]");
                    callLogRepository.save(callLog);
                    logger.debug("finish updating call log with ID[" + callLog.getCallLogId() + "]");
                    update++;
                }
            }else{
                if(checkIfAppointmentExist){
                    throw new RuntimeException("Appointment Call Log exists for patient ID[" + patientId + "] @ office[" + facility.getFacilityId() + "-" + facility.getName() + "] at [" + apptDateTimeStr + "]");
                }

                LoadedEmployee loadedEmployee = agentCall.findLoadedEmployee(employeeNumber, screenType);

                callLog = new CallLog();
                if(createEmployeeNumber != null && !createEmployeeNumber.equals(employeeNumber)){
                    callLog.setEntrySource("SUPERVISOR");
                }else{
                    callLog.setEntrySource("AGENT_IN");
                }
                if(allowCredit){
                    callLog.setAdminOverridden(true);
                }

                String session = loadedEmployee.getSessionId() != null && loadedEmployee.getSessionId().trim().length() > 0 ? loadedEmployee.getSessionId() : agentCall.getSessionId();

                callLog.setUuid(agentCall.getUuid());
                callLog.setSessionId(session);
                callLog.setAgentCallCenterName(callCenterName);
                callLog.setCallCenterName(callService.getCallCenterName(loadedEmployee.getEmployeeNumber(), agentCall.getUuid(), loadedEmployee.getScreenType(), true));
                if((callLog.getCallCenterName() == null || callLog.getCallCenterName().trim().length() == 0)
                        && (existingCallCenterName != null && existingCallCenterName.trim().length() > 0)){
                    callLog.setCallCenterName(existingCallCenterName);
                }

                setQueueName(agentCall.getUuid(), callLog);

                callLog.setEmployeeNumber(employeeNumber);
                callLog.setAgentType(screenType);
                callLog.setPhoneType(agentCall.getPhoneType());
                callLog.setFacilityViewCount(agentCall.getLoadedFacilities().size());
                callLog.setCreateEmployeeNumber(createEmployeeNumber);
                callLog.setCreateDateTime(current);
                callLog.setResolutionCode(loadedEmployee.getResolutionCode());
                callLog.setReasonCode(loadedEmployee.getReasonCode());
                callLog.setCallDateTime(DateTimeUtils.getGMTTimeFromLocationOffset(loadedEmployee.getLoadedDateTime(), locationOffset));

                if(!isOrtho && facility != null){
                    callLog.setLiberty(facility.isLibertySupported());
                    callLog.setOpenBook(facility.isOpenBookSchedulingEnabled());
                }else{
                    callLog.setLiberty(false);
                    callLog.setOpenBook(false);
                }

                callLog.setPartnerId(loadedEmployee.getPartnerId());
                callLog.setPartnerSessionId(loadedEmployee.getPartnerSessionId());
                callLog.setPartnerStartDateTime(loadedEmployee.getPartnerStartDateTime());
                callLog.setPartnerTimezone(loadedEmployee.getPartnerTimezone());
                callLog.setPartnerRingTime(loadedEmployee.getPartnerRingTime());
                callLog.setPartnerTalkTime(loadedEmployee.getPartnerTalkTime());
                callLog.setPartnerHoldTime(loadedEmployee.getPartnerHoldTime());
                callLog.setPartnerWorkTime(loadedEmployee.getPartnerWorkTime());
                callLog.setPartnerCalledNumber(loadedEmployee.getPartnerCalledNumber());
                callLog.setPartnerTeamId(loadedEmployee.getPartnerTeamId());
                callLog.setPartnerMetServiceLevel(loadedEmployee.getPartnerMetServiceLevel());

//                if(screenType.equalsIgnoreCase("csr")){
//                    callLog.setAllowCredit(false);
//                }

                callLog.setTempPatientId(patientId);
                callLog.setFacilityId(facilityId);
                callLog.setEmailCaptured(emailCaptured);
                callLog.setInsuranceWaiting(insuranceWaiting);

                callLog = callLogRepository.save(callLog);

                appointmentCallLog = new AppointmentCallLog();
                appointmentCallLog.setAppointmentDateTime(apptDateTime);
                appointmentCallLog.setCreateDateTime(current);
                appointmentCallLog.setCreateEmployeeNumber(createEmployeeNumber);
                appointmentCallLog.setCallLogId(callLog.getCallLogId());
                appointmentCallLog.setFacilityId(facilityId);
                appointmentCallLog.setTempPatientId(patientId);
                appointmentCallLog.setIsOrtho(isOrtho);
                appointmentCallLog.setAllowCredit(allowCredit);
                appointmentCallLog.setEmailCaptured(emailCaptured);
                appointmentCallLog.setInsuranceWaiting(insuranceWaiting);

                appointmentCallLog = appointmentCallLogRepository.save(appointmentCallLog);

                callLog.setAppointmentCallLogId(appointmentCallLog.getAppointmentCallLogId());
                callLogRepository.save(callLog);

                Patient patientMongo = new Patient();
                patientMongo.setFacilityId(facilityId);
                patientMongo.setPatientId(patientId);
                patientMongo.setSourceSystem(facility.isLibertySupported() ? "LIBERTY" : "QSI");
                patientMongo.setNextAppointmentDateTime(apptDateTime);
                patientMongo.setNextAppointmentDate(VelocityUtils.getDateAsString(apptDateTime, null));
                patientMongo.setNextAppointmentTime(VelocityUtils.getDateAsString(apptDateTime, "HHmm"));
                patientMongo.setEmpEntered(createEmployeeNumber);
                patientMongo.setScreenType(screenType);
                patientMongo.setCreateDate(VelocityUtils.getDateAsString(current, null));
                patientMongo.setCreateTime(VelocityUtils.getDateAsString(current, "HHmmss"));
                patientMongo.setSequence(agentCall.getPatients().size() + 1);
                patientMongo.setEmailCaptured(emailCaptured);
                patientMongo.setInsuranceWaiting(insuranceWaiting);
                patientMongo.setIsOrtho(isOrtho);
                agentCall.getPatients().add(patientMongo);

                add++;
            }
        }
        if(add > 0){
            message += " add " + add + " call log(s)";
        }
        if(update > 0){
            message += (message.length() > 0 ? " and " : " ") + "update " + update + " call log(s)";
        }
        if(message.trim().length() > 0){
            message = "Successfully " + message.trim() + ".";
            mongoRepository.updateAgentCall(agentCall);
            logger.debug("update agent call[" + agentCall.getUuid() + "] with [" + agentCall.getPatients().size() + "] patient(s)");
        }else{
            message = "There is no update.";
        }
        return message;
    }

    @Override
    @Transactional(readOnly = false)
    public String addCallLog(Map<String, Object> map, Integer createEmployeeNumber) {
        String screenType = (String)map.get("screenType");
        screenType = screenType != null ? screenType.toLowerCase() : "";
        String callCenterName = (String)map.get("callCenter");
        Integer employeeNumber = Integer.valueOf((String) map.get("employeeNumber"));
        String reason = (String)map.get("reason");
        String resolution = (String)map.get("resolution");
        Date callTime = VelocityUtils.parseDate((String)map.get("callDateTime"), "yyyy-MM-dd'T'HH:mm:ss");
        CallCenter callCenter = mongoRepository.getCallCenterByName(callCenterName);
        Integer locationOffset = callCenter != null ? callCenter.getTimezoneOffset() : null;
        Date current = new Date();
        String uuid = map.get("uuid") != null && map.get("uuid").toString().trim().length() > 0
                ? map.get("uuid").toString().trim() : callService.generateUUID();
        String sessionId = (String)map.get("sessionId");
        Set<String> keyForAppointmentCallLog = new HashSet<String>();


        CallLog callLog = null;
        int add = 0;
        int update = 0;

        List<Map<String, Object>> patients = (List<Map<String, Object>>)map.get("patients");
        if(patients.size() > 0){
            for(Map<String, Object> patient : patients){
                AppointmentCallLog appointmentCallLog = null;

                Integer facilityId = Integer.valueOf((String)patient.get("facilityId"));
                Long patientId = Long.valueOf((String)patient.get("patientId"));
                String apptDateTimeStr = (String)patient.get("nextAppointmentDateTime");
                Date apptDateTime = VelocityUtils.parseDate(apptDateTimeStr, "yyyy-MM-dd'T'HH:mm:ss");
                Facility facility = mongoRepository.findFacilityDetail(facilityId);
                Long appointmentCallLogId = patient.get("apptCallLogId") != null && patient.get("apptCallLogId").toString().trim().length() > 0
                        ? Long.valueOf((String)patient.get("apptCallLogId")) : null;
                Long agentCallLogId = patient.get("agentCallLogId") != null && patient.get("agentCallLogId").toString().trim().length() > 0
                        ? Long.valueOf((String) patient.get("agentCallLogId")) : null;
                boolean emailCaptured = (Boolean)patient.get("emailCaptured");
                boolean insuranceWaiting = (Boolean)patient.get("insuranceWaiting");
                boolean isOrtho = (Boolean)patient.get("isOrtho");
                boolean allowCredit = patient.get("allowCredit") != null ? (Boolean)patient.get("allowCredit") : false;

                String key = patientId + ":::" + facilityId + ":::" + apptDateTimeStr;
                if(keyForAppointmentCallLog.contains(key)){
                    continue;
                }
                keyForAppointmentCallLog.add(key);

                boolean checkIfAppointmentExist = jdbcRepository.checkIfApptCallLogExist(patientId, apptDateTime, facilityId, appointmentCallLogId);

                if(agentCallLogId != null){
                    callLog = callLogRepository.findOne(agentCallLogId);
                }else{
                    callLog = createBasicCallLog(uuid, sessionId, "AGENT_IN", callCenterName, employeeNumber, screenType
                            , createEmployeeNumber, resolution, reason
                            , DateTimeUtils.getGMTTimeFromLocationOffset(callTime, locationOffset)
                            , current, true);
                }

                if(!isOrtho && facility != null){
                    callLog.setLiberty(facility.isLibertySupported());
                    callLog.setOpenBook(facility.isOpenBookSchedulingEnabled());
                }else{
                    callLog.setLiberty(false);
                    callLog.setOpenBook(false);
                }

//                if(appointmentCallLogId != null){
                if(callLog != null && callLog.getAppointmentCallLogId() != null && callLog.getAppointmentCallLogId() > 0){
                    appointmentCallLog = appointmentCallLogRepository.findOne(appointmentCallLogId);
                }

                if(callLog.getCallLogId() != null
                        && appointmentCallLog != null && appointmentCallLog.getAppointmentCallLogId() != null){
                    String existingStr = VelocityUtils.getDateAsString(appointmentCallLog.getAppointmentDateTime(), "yyyy-MM-dd'T'HH:mm:ss");
                    boolean changeAllowCredit = false;

                    if(patient.get("allowCredit") != null){
                        changeAllowCredit = callLog.getAllowCredit() != allowCredit;
                    }

                    if(!facilityId.equals(callLog.getFacilityId()) || !patientId.equals(callLog.getTempPatientId()) || !apptDateTimeStr.equalsIgnoreCase(existingStr)
                            || emailCaptured != callLog.isEmailCaptured() || insuranceWaiting != callLog.isInsuranceWaiting()
                            || isOrtho != appointmentCallLog.getIsOrtho() || changeAllowCredit){

                        if(checkIfAppointmentExist){
                            throw new RuntimeException("Appointment Call Log exists for patient ID[" + patientId + "] @ office[" + facility.getFacilityId() + "-" + facility.getName() + "] at [" + apptDateTimeStr + "]");
                        }

                        callLog.setAppointmentCallLogId(null);
                        callLog.setTempPatientId(patientId);
                        callLog.setPatientId(null);
                        callLog.setQsiUniqueId(null);
                        callLog.setFacilityId(facilityId);
                        callLog.setCallLogUpdated(0);
                        callLog.setLogStatus("ACTIVE");
                        callLog.setEmailCaptured(emailCaptured);
                        callLog.setInsuranceWaiting(insuranceWaiting);
                        callLog.setAllowCredit(allowCredit);
                        if(changeAllowCredit){
                            callLog.setAdminOverridden(true);
                        }

                        boolean updateResult = persistAgentAppointmentCallLog(callLog, apptDateTime, createEmployeeNumber, isOrtho, allowCredit, emailCaptured, insuranceWaiting);
                        if(updateResult){
                            update++;
                        }
                    }
                }else{
                    if(checkIfAppointmentExist){
                        throw new RuntimeException("Appointment Call Log exists for patient ID[" + patientId + "] @ office[" + facility.getFacilityId() + "-" + facility.getName() + "] at [" + apptDateTimeStr + "]");
                    }
//                    if(screenType.equalsIgnoreCase("csr")){
//                        callLog.setAllowCredit(false);
//                    }

                    callLog.setCallLogId(null);
                    callLog.setAppointmentCallLogId(null);
                    callLog.setTempPatientId(patientId);
                    callLog.setFacilityId(facilityId);
                    callLog.setEmailCaptured(emailCaptured);
                    callLog.setInsuranceWaiting(insuranceWaiting);
                    if(allowCredit){
                        callLog.setAdminOverridden(true);
                    }
                    boolean addResult = persistAgentAppointmentCallLog(callLog, apptDateTime, createEmployeeNumber, isOrtho, allowCredit, emailCaptured, insuranceWaiting);
                    if(addResult){
                        add++;
                    }
                }

            }
        }else{
            callLog = createBasicCallLog(uuid, sessionId, "AGENT_IN", callCenterName, employeeNumber, screenType
                    , createEmployeeNumber, resolution, reason
                    , DateTimeUtils.getGMTTimeFromLocationOffset(callTime, locationOffset)
                    , current, true);
            callLog = callLogRepository.save(callLog);

            AppointmentCallLog appointmentCallLog = new AppointmentCallLog();
            appointmentCallLog.setCallLogId(callLog.getCallLogId());
            appointmentCallLog.setAppointmentStatus("NO_APPT_SCHEDULED");
            appointmentCallLog.setCreateEmployeeNumber(createEmployeeNumber);
            appointmentCallLog = appointmentCallLogRepository.save(appointmentCallLog);

            callLog.setAppointmentCallLogId(appointmentCallLog.getAppointmentCallLogId());
            callLog.setLogStatus("CLOSED");
            callLogRepository.save(callLog);
            add = 1;
        }
        String message = "";
        if(add > 0){
            message += " add " + add + " call log(s)";
        }
        if(update > 0){
            message += (message.length() > 0 ? " and " : " ") + "update " + update + " call log(s)";
        }
        if(message.trim().length() > 0){
            message = "Successfully " + message.trim() + ".";
        }else{
            message = "There is no update.";
        }
        return message;
    }

    private CallLog createBasicCallLog(String uuid, String sessionId, String entryType, String callCenterName, Integer employeeNumber, String screenType, Integer createEmployeeNumber, String resolution, String reason, Date callTime, Date current, boolean doAdd){
        CallLog callLog = new CallLog();
        callLog.setUuid(uuid);
        callLog.setEntrySource(entryType);
        callLog.setSessionId(sessionId);
        if(doAdd){
            callLog.setCallCenterName(callCenterName);
            callLog.setAgentCallCenterName(callService.getCallCenterName(employeeNumber,uuid, screenType, false));
        }else{
            callLog.setCallCenterName(callService.getCallCenterName(employeeNumber, uuid, screenType, true));
            callLog.setAgentCallCenterName(callCenterName);
        }

        callLog.setEmployeeNumber(employeeNumber);
        callLog.setAgentType(screenType.toLowerCase());
        callLog.setCreateEmployeeNumber(createEmployeeNumber);
        callLog.setCreateDateTime(current);
        callLog.setResolutionCode(resolution);
        callLog.setReasonCode(reason);
        callLog.setCallDateTime(callTime);
        callLog.setCallLogUpdated(1);

        setQueueName(uuid, callLog);

        return callLog;
    }

    private void setQueueName(String uuid, CallLog callLog){
        InboundCall inboundCall = mongoRepository.getInboundCall(uuid);
        if(inboundCall != null){
            PhoneNumber phoneNumber = inboundCall.getPhoneNumber();
            if(phoneNumber == null || phoneNumber.getCallCenterQueueName() == null || phoneNumber.getCallCenterQueueName().trim().length() == 0){
                phoneNumber = mongoRepository.findPhoneNumber(inboundCall.getDnis());
            }
            if((phoneNumber == null || phoneNumber.getCallCenterQueueName() == null || phoneNumber.getCallCenterQueueName().trim().length() == 0)
                    && (inboundCall.getTargetFacilityId() != null && inboundCall.getTargetFacilityId() > 0)){
                phoneNumber = mongoRepository.findPhoneNumberByFacility(inboundCall.getTargetFacilityId(), "ML");
            }
            if(phoneNumber != null){
                callLog.setQueueName(phoneNumber.getCallCenterQueueName());
            }
        }
    }

    private boolean persistAgentAppointmentCallLog(CallLog callLog, Date apptDateTime, Integer createEmployeeNumber, boolean isOrtho, boolean allowCredit, boolean emailCaptured, boolean insuranceWaiting){
        callLog = callLogRepository.save(callLog);

        AppointmentCallLog appointmentCallLog = new AppointmentCallLog();
        appointmentCallLog.setAppointmentDateTime(apptDateTime);
        appointmentCallLog.setCreateDateTime(new Date());
        appointmentCallLog.setCreateEmployeeNumber(createEmployeeNumber);
        appointmentCallLog.setCallLogId(callLog.getCallLogId());
        appointmentCallLog.setFacilityId(callLog.getFacilityId());
        appointmentCallLog.setTempPatientId(callLog.getTempPatientId());
        appointmentCallLog.setIsOrtho(isOrtho);
        appointmentCallLog.setAllowCredit(allowCredit);
        appointmentCallLog.setEmailCaptured(emailCaptured);
        appointmentCallLog.setInsuranceWaiting(insuranceWaiting);

        appointmentCallLog = appointmentCallLogRepository.save(appointmentCallLog);

        callLog.setAppointmentCallLogId(appointmentCallLog.getAppointmentCallLogId());
        callLogRepository.save(callLog);
        return true;
    }

    @Override
    public List<LinkedHashMap<String, String>> getCallLogDetailReport(String startDate, String endDate, List<Integer> agentIds, String dateType, String reportCriteria) {
        Date start = new Date();
        String agents = "";
        if(agentIds != null){
            for(Integer id : agentIds){
                agents += id + ",";
            }
        }
        List<LinkedHashMap<String, String>> list = jdbcRepository.getCallLogDetailReport(startDate, endDate, agentIds, dateType, reportCriteria);
        mongoRepository.addLogAction(null, ActionLog.REPORT_ACTION, "Getting report for start[" + startDate + "], end[" + endDate + "], agents[" + agents + "] and date type[" + dateType + "] took " + (new Date().getTime() - start.getTime() + " ms for the size of [" + list.size() + "]."));
        return list;
    }

    @Override
    @Transactional(readOnly = false)
    public CallLog updateCallLogWithPartnerInfo(Long callLogId, LoadedEmployee loadedEmployee) {
        CallLog callLog = callLogRepository.findOne(callLogId);
        if(callLog != null){
            callLog.setPartnerId(loadedEmployee.getPartnerId());
            callLog.setPartnerSessionId(loadedEmployee.getPartnerSessionId());
            callLog.setPartnerStartDateTime(loadedEmployee.getPartnerStartDateTime());
            callLog.setPartnerTimezone(loadedEmployee.getPartnerTimezone());
            callLog.setPartnerRingTime(loadedEmployee.getPartnerRingTime());
            callLog.setPartnerTalkTime(loadedEmployee.getPartnerTalkTime());
            callLog.setPartnerHoldTime(loadedEmployee.getPartnerHoldTime());
            callLog.setPartnerWorkTime(loadedEmployee.getPartnerWorkTime());
            callLog.setPartnerCalledNumber(loadedEmployee.getPartnerCalledNumber());
            callLog.setPartnerTeamId(loadedEmployee.getPartnerTeamId());
            callLog.setPartnerMetServiceLevel(loadedEmployee.getPartnerMetServiceLevel());

            callLogRepository.save(callLog);
        }
        return callLog;
    }

//    @Override
//    public String getCallCenterName(Integer employeeNumber, String uuid, String screenType){
//        String callCenterName = "Irvine";
//        CallCenterEmployee cce = jdbcRepository.getCallCenterEmployeeByNumber(employeeNumber, false);
//        if(cce != null && cce.getCallCenter() != null){
//            callCenterName = cce.getCallCenter();
//        }else{
//            InboundCall call = mongoRepository.getInboundCall(uuid);
//            if(call.getPhoneNumber() != null){
//                callCenterName = call.getPhoneNumber().getCallCenterName();
//            }else{
//                PhoneNumber phoneNumber = mongoRepository.findPhoneNumber(call.getDnis());
//                if(phoneNumber != null){
//                    callCenterName = phoneNumber.getCallCenterName();
//                }
//            }
//        }
//        return callCenterName;
//    }
}
