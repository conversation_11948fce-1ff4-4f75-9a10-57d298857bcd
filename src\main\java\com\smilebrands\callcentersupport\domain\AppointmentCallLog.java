package com.smilebrands.callcentersupport.domain;

import com.smilebrands.callcentersupport.util.VelocityUtils;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * User: <PERSON><PERSON>
 * Date: 8/13/14
 */
@Entity
@Table(name = "APPT_CALL_LOG")
public class AppointmentCallLog {

    private Long appointmentCallLogId;
    private Long callLogId;
    private Integer qsiClinicId;
    private Integer facilityId;
    private Integer qsiUniqueId;
    private Long tempPatientId;
    private Boolean isOrtho;
    private Boolean allowCredit;
    private Boolean emailCaptured;
    private Boolean insuranceWaiting;
    private Integer providerId;
    private String appointmentStatus = "UNVERIFIED";
    private Date appointmentDateTime;
    private BigDecimal appointmentValue;
    private String lastUpdateProgram;
    private Integer createEmployeeNumber;
    private Date createDateTime = new Date();
    private Integer updateEmployeeNumber;
    private Date updateDateTime;


    @Id
    @GeneratedValue(generator = "ApptCallLogSeq")
    @SequenceGenerator(name = "ApptCallLogSeq", sequenceName = "APPT_CALL_LOG_ID_SEQ", allocationSize = 1)
    @Column(name = "APPT_LOG_ID")
    public Long getAppointmentCallLogId() {
        return appointmentCallLogId;
    }

    public void setAppointmentCallLogId(Long appointmentCallLogId) {
        this.appointmentCallLogId = appointmentCallLogId;
    }

    @Column(name = "CALL_LOG_ID")
    public Long getCallLogId() {
        return callLogId;
    }

    public void setCallLogId(Long callLogId) {
        this.callLogId = callLogId;
    }

    @Column(name = "CLINIC_ID")
    public Integer getQsiClinicId() {
        return qsiClinicId;
    }

    public void setQsiClinicId(Integer qsiClinicId) {
        this.qsiClinicId = qsiClinicId;
    }

    @Column(name = "FACILITY_ID")
    public Integer getFacilityId() {
        return facilityId;
    }

    public void setFacilityId(Integer facilityId) {
        this.facilityId = facilityId;
    }

    @Column(name = "TEMP_PATIENT_ID")
    public Long getTempPatientId() {
        return tempPatientId;
    }

    public void setTempPatientId(Long tempPatientId) {
        this.tempPatientId = tempPatientId;
    }

    @Column(name = "IS_ORTHO")
    public Boolean getIsOrtho() {
        return isOrtho;
    }

    public void setIsOrtho(Boolean isOrtho) {
        this.isOrtho = isOrtho;
    }

    @Column(name = "ALLOW_CREDIT")
    public Boolean getAllowCredit() {
        return allowCredit;
    }

    public void setAllowCredit(Boolean allowCredit) {
        this.allowCredit = allowCredit;
    }

    @Column(name = "EMAIL_CAPTURED")
    public Boolean getEmailCaptured() {
        return emailCaptured;
    }

    public void setEmailCaptured(Boolean emailCaptured) {
        this.emailCaptured = emailCaptured;
    }

    @Column(name = "WAITING_ON_INSURANCE")
    public Boolean getInsuranceWaiting() {
        return insuranceWaiting;
    }

    public void setInsuranceWaiting(Boolean insuranceWaiting) {
        this.insuranceWaiting = insuranceWaiting;
    }

    @Column(name = "UNIQUE_ID")
    public Integer getQsiUniqueId() {
        return qsiUniqueId;
    }

    public void setQsiUniqueId(Integer qsiUniqueId) {
        this.qsiUniqueId = qsiUniqueId;
    }

    @Column(name = "PROVIDER_ID")
    public Integer getProviderId() {
        return providerId;
    }

    public void setProviderId(Integer providerId) {
        this.providerId = providerId;
    }

    @Column(name = "APPT_LOG_STATUS")
    public String getAppointmentStatus() {
        return appointmentStatus;
    }

    public void setAppointmentStatus(String appointmentStatus) {
        this.appointmentStatus = appointmentStatus;
    }

    @Column(name = "APPT_DATE")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getAppointmentDateTime() {
        return appointmentDateTime;
    }

    public void setAppointmentDateTime(Date appointmentDateTime) {
        this.appointmentDateTime = appointmentDateTime;
    }

    @Column(name = "APPT_VALUE")
    public BigDecimal getAppointmentValue() {
        return appointmentValue;
    }

    public void setAppointmentValue(BigDecimal appointmentValue) {
        this.appointmentValue = appointmentValue;
    }

    @Column(name = "LAST_UPD_PROGRAM")
    public String getLastUpdateProgram() {
        return lastUpdateProgram;
    }

    public void setLastUpdateProgram(String lastUpdateProgram) {
        this.lastUpdateProgram = lastUpdateProgram;
    }

    @Column(name = "CREATE_EMPLOYEE")
    public Integer getCreateEmployeeNumber() {
        return createEmployeeNumber;
    }

    public void setCreateEmployeeNumber(Integer createEmployeeNumber) {
        this.createEmployeeNumber = createEmployeeNumber;
    }

    @Column(name = "CREATE_DATETIME")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(Date createDateTime) {
        this.createDateTime = createDateTime;
    }

    @Column(name = "UPDATE_EMPLOYEE")
    public Integer getUpdateEmployeeNumber() {
        return updateEmployeeNumber;
    }

    public void setUpdateEmployeeNumber(Integer updateEmployeeNumber) {
        this.updateEmployeeNumber = updateEmployeeNumber;
    }

    @Column(name = "UPDATE_TIMESTAMP")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(Date updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    @Transient
    public String getAppointmentDateTimeStr(){
        return VelocityUtils.getDateAsString(this.appointmentDateTime, "MM-dd-yyyy HH:mm");
    }

    @Transient
    public boolean isEditable(){
        boolean result = false;
        if(this.appointmentStatus != null && this.appointmentStatus.trim().length() > 0){
            if(this.appointmentStatus.equalsIgnoreCase("UNVERIFIED")
                    || this.appointmentStatus.equalsIgnoreCase("VERIFIED")
                    || this.appointmentStatus.equalsIgnoreCase("PATIENT_NOT_FOUND")
                    || this.appointmentStatus.equalsIgnoreCase("NO_APPT_SCHEDULED")){
                result = true;
            }
        }
        return result;
    }
}
