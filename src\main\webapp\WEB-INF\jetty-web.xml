<?xml version="1.0"?>
<!DOCTYPE Configure PUBLIC "-//Mort Bay Consulting//DTD Configure//EN" "http://jetty.mortbay.org/configure.dtd">
<Configure class="org.eclipse.jetty.webapp.WebAppContext">

    <!-- Configure annotation scanning to exclude problematic files -->
    <Call name="setAttribute">
        <Arg>org.eclipse.jetty.server.webapp.ContainerIncludeJarPattern</Arg>
        <Arg>.*/[^/]*servlet-api-[^/]*\.jar$|.*/javax.servlet.jsp.jstl-.*\.jar$|.*/[^/]*taglibs.*\.jar$</Arg>
    </Call>

    <!-- Exclude module-info.class and versioned entries that cause scanning issues -->
    <Call name="setAttribute">
        <Arg>org.eclipse.jetty.annotations.AnnotationConfiguration.WEBINF_JAR_PATTERN</Arg>
        <Arg>
            (?!.*(?:asm-[0-9]|jaxb-api-[0-9]|jaxb-impl-[0-9]|module-info\.class|META-INF/versions/)).*\.jar$
        </Arg>
    </Call>

    <!-- Configure class exclusions for annotation scanning -->
    <Call name="setAttribute">
        <Arg>org.eclipse.jetty.annotations.AnnotationConfiguration.CLASS_INHERITANCE_MAP</Arg>
        <Arg>org.eclipse.jetty.annotations.ClassInheritanceMap</Arg>
    </Call>

    <!-- Set context parameters for better error handling -->
    <Set name="throwUnavailableOnStartupException">true</Set>

    <New id="devAppTrackerDb" class="org.eclipse.jetty.plus.jndi.Resource">
        <Arg></Arg>
        <Arg>jdbc/appTracker</Arg>
        <Arg>
            <New class="com.zaxxer.hikari.HikariDataSource">
                <Arg>
                    <New class="com.zaxxer.hikari.HikariConfig">
                        <Set name="dataSource">
                            <New class="oracle.jdbc.pool.OracleDataSource">
                                <Set name="URL">**********************************************</Set>
                                <Set name="user">apptkrapp</Set>
                                <Set name="password">bndtkr</Set>
                            </New>
                        </Set>

                        <Set name="minimumIdle">1</Set>>
                        <Set name="maximumPoolSize">5</Set>>
                        <Set name="connectionTimeout">20000</Set>
                        <Set name="idleTimeout">300000</Set>
                        <Set name="maxLifetime">1200000</Set>
                        <Set name="leakDetectionThreshold">30000</Set>
                        <Set name="connectionTestQuery">SELECT 1 FROM DUAL</Set>
                    </New>
                </Arg>
            </New>
        </Arg>
    </New>

    <New class="org.eclipse.jetty.plus.jndi.Resource">
        <Arg></Arg>
        <Arg>bnd/appTrackerHome</Arg>
        <Arg type="java.lang.String">http://moe.bnd.corp:9080/at/</Arg>
    </New>

    <New id="devDataSource" class="org.eclipse.jetty.plus.jndi.Resource">
        <Arg></Arg>
        <Arg>jdbc/dataSource</Arg>
        <Arg>
            <New class="com.zaxxer.hikari.HikariDataSource">
                <Arg>
                    <New class="com.zaxxer.hikari.HikariConfig">
                        <Set name="dataSource">
                            <!--&lt;!&ndash;-->
                            <New class="oracle.jdbc.pool.OracleDataSource">
                                <Set name="URL">**********************************************</Set>
                                <Set name="user">bndapp</Set>
                                <Set name="password">d3v4bnd</Set>
                            </New>
                            <!--&ndash;&gt;-->
                            <!--
                            <New class="oracle.jdbc.pool.OracleDataSource">
                                <Set name="URL">*******************************************************************************************************************************************************************************************)))</Set>
                                <Set name="user">bndapp</Set>
                                <Set name="password">bnd4pp</Set>
                            </New>
                            -->
                        </Set>

                        <Set name="minimumIdle">1</Set>>
                        <Set name="maximumPoolSize">5</Set>>
                        <Set name="connectionTimeout">20000</Set>
                        <Set name="idleTimeout">300000</Set>
                        <Set name="maxLifetime">1200000</Set>
                        <Set name="leakDetectionThreshold">30000</Set>
                        <Set name="connectionTestQuery">SELECT 1 FROM DUAL</Set>
                    </New>
                </Arg>
            </New>
        </Arg>
    </New>

    <New id="devPostgresDataSource" class="org.eclipse.jetty.plus.jndi.Resource">
        <Arg></Arg>
        <Arg>jdbc/postgresDataSource</Arg>
        <Arg>
            <New class="com.zaxxer.hikari.HikariDataSource">
                <Arg>
                    <New class="com.zaxxer.hikari.HikariConfig">
                        <Set name="dataSource">
                            <New class="org.postgresql.ds.PGSimpleDataSource">
                                <Set name="databaseName">warehouse</Set>
                                <Set name="serverName">maggie.bnd.corp</Set>
                                <Set name="portNumber">5432</Set>
                                <Set name="user">cdw_admin</Set>
                                <Set name="password">1d4r3yu0</Set>
                            </New>
                        </Set>

                        <Set name="minimumIdle">1</Set>>
                        <Set name="maximumPoolSize">5</Set>>
                        <Set name="connectionTimeout">20000</Set>
                        <Set name="idleTimeout">300000</Set>
                        <Set name="maxLifetime">1200000</Set>
                        <Set name="leakDetectionThreshold">30000</Set>
                        <Set name="connectionTestQuery">SELECT 1</Set>
                    </New>
                </Arg>
            </New>
        </Arg>
    </New>

    <New id="replicaSet" class="org.eclipse.jetty.plus.jndi.EnvEntry">
        <Arg></Arg>
        <Arg>mongo/replicaSet</Arg>
        <!--<Arg type="java.lang.String">bart.bnd.corp:27017;marge.bnd.corp:27017</Arg>-->
        <!--<Arg type="java.lang.String">itchy.bnd.corp:27017;todd.bnd.corp:27017;crusty.bnd.corp:27017</Arg>-->
        <Arg type="java.lang.String">ackbar.bnd.corp:27017;holdo.bnd.corp:27017;andor.bnd.corp:27017</Arg>
        <Arg type="boolean">true</Arg>
    </New>
    <New id="mongoDBName" class="org.eclipse.jetty.plus.jndi.EnvEntry">
        <Arg></Arg>
        <Arg>mongo/mongoDBName</Arg>
        <Arg type="java.lang.String">callCenter</Arg>
        <Arg type="boolean">true</Arg>
    </New>
    <New id="mongoPort" class="org.eclipse.jetty.plus.jndi.EnvEntry">
        <Arg></Arg>
        <Arg>mongo/mongoPort</Arg>
        <Arg type="java.lang.String">27017</Arg>
        <Arg type="boolean">true</Arg>
    </New>
    <New id="mongoAuthDB" class="org.eclipse.jetty.plus.jndi.EnvEntry">
        <Arg></Arg>
        <Arg>mongo/mongoAuthDB</Arg>
        <Arg type="java.lang.String">admin</Arg>
        <Arg type="boolean">true</Arg>
    </New>
    <New id="mongoUser" class="org.eclipse.jetty.plus.jndi.EnvEntry">
        <Arg></Arg>
        <Arg>mongo/mongoUser</Arg>
        <!--<Arg type="java.lang.String">EMPTY</Arg>-->
        <Arg type="java.lang.String">callcenter</Arg>
        <Arg type="boolean">true</Arg>
    </New>
    <New id="mongoPass" class="org.eclipse.jetty.plus.jndi.EnvEntry">
        <Arg></Arg>
        <Arg>mongo/mongoPass</Arg>
        <!--<Arg type="java.lang.String">EMPTY</Arg>-->
        <Arg type="java.lang.String">callC3Nt37$</Arg>
        <Arg type="boolean">true</Arg>
    </New>

    <!--WEB REQUEST PROPERTIES-->
    <New id="redirectUrl" class="org.eclipse.jetty.plus.jndi.EnvEntry">
        <Arg></Arg>
        <Arg>webRequest/redirectUrl</Arg>
        <Arg type="java.lang.String">http://leghorn.bnd.corp:9898/callcenter/response-from-cisco/</Arg>
        <Arg type="boolean">true</Arg>
    </New>
    <New id="ciscoUrl" class="org.eclipse.jetty.plus.jndi.EnvEntry">
        <Arg></Arg>
        <Arg>webRequest/ciscoUrl</Arg>
        <Arg type="java.lang.String">http://webdialer.bnd.corp:9080/irvineWebCall</Arg>
        <Arg type="boolean">true</Arg>
    </New>
    <New id="callBackCiscoUrl" class="org.eclipse.jetty.plus.jndi.EnvEntry">
        <Arg></Arg>
        <Arg>webRequest/callBackCiscoUrl</Arg>
        <Arg type="java.lang.String">http://webdialer.bnd.corp:9080/webCallBack</Arg>
        <Arg type="boolean">true</Arg>
    </New>
    <New id="devMode" class="org.eclipse.jetty.plus.jndi.EnvEntry">
        <Arg></Arg>
        <Arg>webRequest/devMode</Arg>
        <Arg type="java.lang.Boolean">true</Arg>
        <Arg type="boolean">true</Arg>
    </New>

</Configure>