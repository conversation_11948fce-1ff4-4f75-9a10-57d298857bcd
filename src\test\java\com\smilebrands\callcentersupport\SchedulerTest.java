package com.smilebrands.callcentersupport;

import com.smilebrands.callcentersupport.domain.schedule.Task;
import com.smilebrands.callcentersupport.repo.MongoRepository;
import com.smilebrands.callcentersupport.service.ScheduleService;
import com.smilebrands.callcentersupport.service.ScheduleServiceImpl;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.net.Inet4Address;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Created by phongpham on 11/6/14.
 */
//@ContextConfiguration(locations = {"classpath:spring-quartz.xml"})
public class SchedulerTest extends BaseTest {

    @Autowired
    MongoOperations mongoOperations;

    @Autowired
    MongoRepository mongoRepository;

    @Autowired
    ScheduleService scheduleService;

    @Test
    public void createTask(){
//        Task task = new Task();
//        task.setTaskId(mongoRepository.getNextSequenceValue("task"));
//        task.setTaskName("processWebRequest");
//        mongoOperations.save(task);

//        Task checkLongWebRequest = new Task();
//        checkLongWebRequest.setTaskId(mongoRepository.getNextSequenceValue("task"));
//        checkLongWebRequest.setTaskName("checkLongWebRequest");
//        checkLongWebRequest.setDuration(60*60);     //1-hour
//        mongoOperations.save(checkLongWebRequest);

//        for(int i=0; i<5; i++){
//            Task task = new Task();
//            task.setTaskId(mongoRepository.getNextSequenceValue("task"));
//            task.setTaskName("task-" + (i+1));
//            task.setDuration(60*60);     //1-hour
//            task.setParallel(true);
//            mongoOperations.save(task);
//        }

//        Task healthyCheck = new Task();
//        healthyCheck.setTaskId(mongoRepository.getNextSequenceValue("task"));
//        healthyCheck.setTaskName("processScreenPopHealthCheck");
//        healthyCheck.setParallel(true);
//        healthyCheck.setDuration(10*60);    //10-minute
//        mongoOperations.save(healthyCheck);

//        Task callLogReport = new Task();
//        callLogReport.setTaskId(mongoRepository.getNextSequenceValue("task"));
//        callLogReport.setTaskName("generateCallLogReport");
//        List<String> scheduledTimes = new ArrayList<String>();
//        scheduledTimes.add("05:00");
//        scheduledTimes.add("09:00");
//        scheduledTimes.add("10:30");
//        scheduledTimes.add("12:30");
//        scheduledTimes.add("13:30");
//        callLogReport.setScheduledTimes(scheduledTimes);
//        callLogReport.setScheduledTimeZone("US/Pacific");
//        mongoOperations.save(callLogReport);

//        Task woiReport = new Task();
//        woiReport.setTaskId(mongoRepository.getNextSequenceValue("task"));
//        woiReport.setTaskName("generateCallLogReport");
//        woiReport.setDescription("CTI_Waiting_On_Insurance");
//        List<String> scheduledTimes = new ArrayList<String>();
//        scheduledTimes.add("05:00");
//        woiReport.setScheduledTimes(scheduledTimes);
//        woiReport.setScheduledTimeZone("US/Pacific");
//        woiReport.setAcquiredBy("helloWorld");
//        mongoOperations.save(woiReport);
//
//        Task noShowReport = new Task();
//        noShowReport.setTaskId(mongoRepository.getNextSequenceValue("task"));
//        noShowReport.setTaskName("generateCallLogReport");
//        noShowReport.setDescription("CTI_No_Show");
//        scheduledTimes = new ArrayList<String>();
//        scheduledTimes.add("05:00");
//        noShowReport.setScheduledTimes(scheduledTimes);
//        noShowReport.setScheduledTimeZone("US/Pacific");
//        noShowReport.setAcquiredBy("helloWorld");
//        mongoOperations.save(noShowReport);
//
//        Task patientIdErrorReport = new Task();
//        patientIdErrorReport.setTaskId(mongoRepository.getNextSequenceValue("task"));
//        patientIdErrorReport.setTaskName("generateCallLogReport");
//        patientIdErrorReport.setDescription("CTI_Temp_Patient_Error");
//        scheduledTimes = new ArrayList<String>();
//        scheduledTimes.add("05:00");
//        patientIdErrorReport.setScheduledTimes(scheduledTimes);
//        patientIdErrorReport.setScheduledTimeZone("US/Pacific");
//        patientIdErrorReport.setAcquiredBy("helloWorld");
//        mongoOperations.save(patientIdErrorReport);

//        Task holidayReport = new Task();
//        holidayReport.setTaskId(mongoRepository.getNextSequenceValue("task"));
//        holidayReport.setTaskName("generateCallLogReport");
//        holidayReport.setDescription("CTI_Booked_On_Holiday");
//        List<String> scheduledTimes = new ArrayList<String>();
//        scheduledTimes.add("05:00");
//        holidayReport.setScheduledTimes(scheduledTimes);
//        holidayReport.setScheduledTimeZone("US/Pacific");
//        holidayReport.setAcquiredBy("helloWorld");
//        mongoOperations.save(holidayReport);

//        Task scaErrorReport = new Task();
//        scaErrorReport.setTaskId(mongoRepository.getNextSequenceValue("task"));
//        scaErrorReport.setTaskName("generateCallLogReport");
//        scaErrorReport.setDescription("CTI_SCA_Ortho_Error");
//        List<String> scheduledTimes = new ArrayList<String>();
//        scheduledTimes.add("05:00");
//        scaErrorReport.setScheduledTimes(scheduledTimes);
//        scaErrorReport.setScheduledTimeZone("US/Pacific");
//        scaErrorReport.setAcquiredBy("helloWorld");
//        mongoOperations.save(scaErrorReport);

//        Task orthoTypeErrorReport = new Task();
//        orthoTypeErrorReport.setTaskId(mongoRepository.getNextSequenceValue("task"));
//        orthoTypeErrorReport.setTaskName("generateCallLogReport");
//        orthoTypeErrorReport.setDescription("CTI_Ortho_Type_Error");
//        List<String> scheduledTimes = new ArrayList<String>();
//        scheduledTimes.add("05:00");
//        orthoTypeErrorReport.setScheduledTimes(scheduledTimes);
//        orthoTypeErrorReport.setScheduledTimeZone("US/Pacific");
//        orthoTypeErrorReport.setAcquiredBy("helloWorld");
//        mongoOperations.save(orthoTypeErrorReport);

        Task screenPopReport = new Task();
        screenPopReport.setTaskId(mongoRepository.getNextSequenceValue("task"));
        screenPopReport.setTaskName("generateCallLogReport");
        screenPopReport.setDescription("CTI_Screenpop_Open");
        List<String> scheduledTimes = new ArrayList<String>();
        scheduledTimes.add("05:00");
        screenPopReport.setScheduledTimes(scheduledTimes);
        screenPopReport.setScheduledTimeZone("US/Pacific");
        screenPopReport.setAcquiredBy("helloWorld");
        mongoOperations.save(screenPopReport);

    }

    @Test
    public void testGetMethodInfo(){
        StackTraceElement[] elements = Thread.currentThread().getStackTrace();
        String currentClassName = this.getClass().getName();
        String methodName = "";
        String nodeName = "";
        try {
            nodeName = Inet4Address.getLocalHost().getHostName().trim();

        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex);
        }
        for(StackTraceElement ste : elements){
            if(ste != null && ste.getClassName().equals(currentClassName)){
                methodName = ste.getMethodName();
                break;
            }
        }
        logger.debug("nodeName: {}", nodeName);
        logger.debug("className: {}", currentClassName);
        logger.debug("methodName: {}", methodName);
    }

    @Test
    public void testGetTask(){
//        acquireTaskJob.acquireTask();
        while(true){}
    }

    @Test
    public void testRunCallLogTask(){
        Task task = mongoOperations.findById(6, Task.class);
        logger.debug(task.toString());
        logger.debug(task.getScheduledTimes().toString());
        if(task != null){
            scheduleService.generateCallLogReport(task);
        }
    }

    @Test
    public void testSendReports(){
        List<Task> tasks = mongoOperations.find(new Query(Criteria.where("taskName").is("generateCallLogReport")), Task.class);
        for(Task task : tasks){
            logger.debug(task.getTaskId() + ":::" + task.getTaskName() + ":::" + task.getDescription());
            scheduleService.generateCallLogReport(task);
        }
    }

    @Test
    public void releaseTask(){
        List<Task> tasks = mongoOperations.findAll(Task.class);
        String current = VelocityUtils.getDateAsString(new Date(), "yyyy-MM-dd");
        for(Task task : tasks){
            String acquiredOn = task.getAcquiredOn();
            if(acquiredOn != null && acquiredOn.compareTo(current) < 0){
                logger.debug("about to release task[" + task.getTaskName() + "-" + task.getDescription() + "] acquired on " + acquiredOn);
                task.setAcquiredBy(null);
                task.setAcquiredOn(null);
//                mongoOperations.save(task);
            }
        }
    }

    @Test
    public void testReportByDateTypes() {
        String dateFormat = "dd-MMM-yyyy";
        Calendar current = Calendar.getInstance();
        current.add(Calendar.MONTH, -1);
        current.set(Calendar.DAY_OF_MONTH, 1);
        String startDate = VelocityUtils.getDateAsString(current.getTime(), dateFormat);
        current.set(Calendar.DAY_OF_MONTH, current.getActualMaximum(Calendar.DAY_OF_MONTH));
        String endDate = VelocityUtils.getDateAsString(current.getTime(), dateFormat);

        String recipients[] = new String[]{ "<EMAIL>"};

        logger.debug("Start: " + startDate + " ... End:" + endDate + " ... Recipients: " + recipients.length);

        /*scheduleService.emailReport(startDate, endDate, "latest", recipients, null, null);
        scheduleService.emailReport(startDate, endDate, "appt", recipients, null, null);
        scheduleService.emailReport(startDate, endDate, "call", recipients, null, null); */
    }
}
