<?xml version="1.0" encoding="UTF-8"?>
<beans:beans xmlns="http://www.springframework.org/schema/mvc"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:beans="http://www.springframework.org/schema/beans"
             xmlns:mvc="http://www.springframework.org/schema/mvc"
             xmlns:task="http://www.springframework.org/schema/task"
             xmlns:context="http://www.springframework.org/schema/context"
             xmlns:tx="http://www.springframework.org/schema/tx"
             xsi:schemaLocation="
        http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-3.0.xsd
        http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd
        http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">


    <!-- Bugsnag Integration -->
    <beans:bean id="bugsnag" class="com.bugsnag.Bugsnag">
        <beans:constructor-arg name="apiKey" value="b867a9cc6a2bbc29616d151d4904fdfb"/>
    </beans:bean>

    <!-- DispatcherServlet Context: defines this servlet's request-processing infrastructure -->
    <!-- Enables the Spring MVC @Controller programming model -->
    <mvc:annotation-driven/>
    <tx:annotation-driven/>

    <!--
      -  Spring MVC Configurations and Components.
     -->
    <!-- Handles HTTP GET requests for /resources/** by efficiently serving up static resources in the ${webappRoot}/resources directory -->
    <resources mapping="/images/**" location="/images/" />
    <resources mapping="/script/**" location="/script/" />
    <resources mapping="/style/**" location="/style/" />
    <resources mapping="/bootstrap/**" location="/bootstrap/" />
    <resources mapping="/chart/**" location="/chart/" />

    <!-- Forwards requests to the "/" resource to the "welcome" view -->
    <mvc:view-controller path="/403" view-name="403"/>

    <!-- Web Controllers -->
    <context:component-scan base-package="com.smilebrands.callcentersupport.controllers"/>

    <beans:bean class="com.smilebrands.callcentersupport.async.BackgroundExecutor"/>

    <!-- Used by @Async annotations in the schedule event aspect. -->
    <task:annotation-driven executor="myExecutor" scheduler="myScheduler"/>
    <task:executor id="myExecutor" pool-size="5"/>
    <task:scheduler id="myScheduler" />

    <!-- JSON VIEW -->
    <beans:bean class="org.springframework.web.servlet.view.BeanNameViewResolver">
        <beans:property name="order" value="0"/>
    </beans:bean>
    <beans:bean name="jsonView" class="org.springframework.web.servlet.view.json.MappingJackson2JsonView"/>

    <beans:bean name="xmlView" class="org.springframework.web.servlet.view.xml.MarshallingView">
        <beans:constructor-arg>
            <beans:bean class="org.springframework.oxm.jaxb.Jaxb2Marshaller">
                <beans:property name="classesToBeBound">
                    <beans:list>
                        <beans:value>com.smilebrands.callcentersupport.domain.InboundCall</beans:value>
                    </beans:list>
                </beans:property>
            </beans:bean>
        </beans:constructor-arg>
    </beans:bean>

    <!-- Resolves views selected for rendering by @Controllers to .jsp resources in the /WEB-INF/views directory -->
    <beans:bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <beans:property name="prefix" value="/WEB-INF/views/" />
        <beans:property name="suffix" value=".jsp" />
        <beans:property name="order" value="1" />
    </beans:bean>

    <beans:import resource="spring-aop.xml" />

</beans:beans>

