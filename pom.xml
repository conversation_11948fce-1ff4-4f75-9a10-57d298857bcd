<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>CallCenterSupport</groupId>
    <artifactId>CallCenterSupport</artifactId>
    <version>1.0</version>
    <packaging>war</packaging>
    <name>Call Center Support</name>

    <scm>
        <connection>scm:git:********************:server-apps/CallCenterSupport.git</connection>
    </scm>

    <properties>
        <java.version>1.7</java.version>
        <junit.version>4.8.1</junit.version>
        <jetty.version>9.4.51.v20230217</jetty.version>

        <log4j.version>1.2.14</log4j.version>
        <org.slf4j.version>1.6.6</org.slf4j.version>
        <joda.time.version>2.2</joda.time.version>
        <logback.classic>1.0.7</logback.classic>

        <mongo.version>3.6.4</mongo.version>
        <hazelcast.version>3.1.3</hazelcast.version>
        <ojdbc.version>11.1.0.7.0</ojdbc.version>
        <postgres.version>42.2.16</postgres.version>
        <org.aspectj.version>1.7.4</org.aspectj.version>
        <org.codehaus.jackson.version>1.9.8</org.codehaus.jackson.version>

        <jackson-core>2.3.1</jackson-core>
        <jackson-annotations>2.3.0</jackson-annotations>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <org.springframework.version>4.0.2.RELEASE</org.springframework.version>
        <spring.data.core.version>1.8.2.RELEASE</spring.data.core.version>
        <spring.data.version>1.8.2.RELEASE</spring.data.version>

        <hibernate.version>3.6.6.Final</hibernate.version>
        <hibernatevalidator.version>4.2.0.Final</hibernatevalidator.version>

        <!-- Jetty port configuration -->
        <jetty.port>8080</jetty.port>
        <jetty.stopPort>8081</jetty.stopPort>
    </properties>

    <dependencies>
        <!-- Add Jetty Servlet dependency -->
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-servlet</artifactId>
            <version>${jetty.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- JSP Support - Using Eclipse JSP implementation -->
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>apache-jsp</artifactId>
            <version>${jetty.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mortbay.jasper</groupId>
            <artifactId>apache-jsp</artifactId>
            <version>8.5.40</version>
        </dependency>

        <!-- JAXB Support - using widely available versions for Java 11 compatibility -->
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.3.1</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-core</artifactId>
            <version>*******</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>2.3.1</version>
        </dependency>
        <dependency>
            <groupId>com.smilebrands.security</groupId>
            <artifactId>securitymanager</artifactId>
            <version>1.2</version>
            <classifier>client</classifier>
        </dependency>

        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
            <version>1.4</version>
        </dependency>

        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>activation</artifactId>
            <version>1.1.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity</artifactId>
            <version>1.6.3</version>
            <type>jar</type>
        </dependency>

        <!-- Spring Framework dependencies -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
            <version>${org.springframework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
            <version>${org.springframework.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${org.springframework.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons.logging</artifactId>
                    <groupId>commons.logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
            <version>${org.springframework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>${org.springframework.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-orm</artifactId>
            <version>${org.springframework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-oxm</artifactId>
            <version>${org.springframework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
            <version>${org.springframework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-jpa</artifactId>
            <version>${spring.data.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.aspectj</groupId>
                    <artifactId>aspectjrt</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
            <version>${org.aspectj.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>${org.springframework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${org.springframework.version}</version>
        </dependency>

        <!-- Hibernate - Start-->
        <dependency>
            <groupId>org.hibernate.javax.persistence</groupId>
            <artifactId>hibernate-jpa-2.0-api</artifactId>
            <version>1.0.1.Final</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-entitymanager</artifactId>
            <version>${hibernate.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Hibernate - End-->

        <!-- Hibernate c3p0 connection pool -->
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-c3p0</artifactId>
            <version>${hibernate.version}</version>
        </dependency>
        <!-- Test dependencies -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${org.springframework.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- Log dependencies -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${org.slf4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
            <version>${org.slf4j.version}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
            <version>${org.slf4j.version}</version>
            <scope>runtime</scope>
        </dependency>

        <!-- Jackson JSON -->
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-core-asl</artifactId>
            <version>${org.codehaus.jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
            <version>${org.codehaus.jackson.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson-core}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson-annotations}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson-core}</version>
        </dependency>

        <!-- mongodb java driver -->
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongo-java-driver</artifactId>
            <version>${mongo.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-mongodb</artifactId>
            <version>1.7.2.RELEASE</version>
        </dependency>
        <!-- MONGO End -->

        <!-- Database dependencies -->
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>ojdbc5</artifactId>
            <version>${ojdbc.version}</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgres.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP-java7</artifactId>
            <version>2.4.13</version>
            <!--<scope>compile</scope>-->
        </dependency>

        <!-- Servlet dependencies -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
            <version>2.5</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.servlet.jsp</groupId>
            <artifactId>jsp-api</artifactId>
            <version>2.1</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>jstl</artifactId>
            <version>1.2</version>
        </dependency>
        <!-- Added for JSTL fix -->
        <dependency>
            <groupId>taglibs</groupId>
            <artifactId>standard</artifactId>
            <version>1.1.2</version>
        </dependency>

        <!-- JSF dependencies - using javax namespace for compatibility -->
        <dependency>
            <groupId>com.sun.faces</groupId>
            <artifactId>jsf-api</artifactId>
            <version>2.2.20</version>
        </dependency>
        <dependency>
            <groupId>com.sun.faces</groupId>
            <artifactId>jsf-impl</artifactId>
            <version>2.2.20</version>
        </dependency>

        <!-- JAXB dependencies -->
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>1.3.2</version>
        </dependency>

        <!-- XML dependencies -->
        <dependency>
            <groupId>org.jdom</groupId>
            <artifactId>jdom</artifactId>
            <version>1.1</version>
        </dependency>

        <!-- Using publicly available Quartz instead -->
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>2.3.2</version>
        </dependency>

        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.1</version>
        </dependency>

        <!-- SMS Dependencies-->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4</version>
        </dependency>
        <dependency>
            <groupId>com.twilio.sdk</groupId>
            <artifactId>twilio-java-sdk</artifactId>
            <version>3.4.1</version>
        </dependency>

        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>${joda.time.version}</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.1</version>
        </dependency>

        <!-- Excel Processing Libraries -->
        <dependency>
            <groupId>net.sourceforge.jexcelapi</groupId>
            <artifactId>jxl</artifactId>
            <version>2.6.12</version>
        </dependency>

        <!-- Apache POI for Excel file processing (for compatibility) -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>3.17</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.17</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>3.17</version>
        </dependency>

        <!-- Using a similar library for media processing -->
        <dependency>
            <groupId>it.sauronsoftware</groupId>
            <artifactId>jave</artifactId>
            <version>1.0.2</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/jave-1.0.2.jar</systemPath>
        </dependency>

        <!-- AWS -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-core</artifactId>
            <version>1.10.0</version>
        </dependency>

        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk</artifactId>
            <version>1.10.0</version>
        </dependency>

        <!-- PDF Processing -->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.24</version>
        </dependency>

        <!-- Bugsnag -->
        <dependency>
            <groupId>com.bugsnag</groupId>
            <version>[3.0,4.0]</version>
            <artifactId>bugsnag</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.gmaven</groupId>
                <artifactId>gmaven-plugin</artifactId>
                <version>1.4</version>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy-all</artifactId>
                        <version>1.8.0</version>
                    </dependency>
                    <dependency>
                        <groupId>org.codehaus.gmaven.runtime</groupId>
                        <artifactId>gmaven-runtime-1.8</artifactId>
                        <version>1.4</version>
                        <exclusions>
                            <exclusion>
                                <groupId>org.codehaus.groovy</groupId>
                                <artifactId>groovy-all</artifactId>
                            </exclusion>
                        </exclusions>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <configuration>
                            <providerSelection>1.7</providerSelection>
                        </configuration>
                        <goals>
                            <goal>generateStubs</goal>
                            <goal>compile</goal>
                            <goal>generateTestStubs</goal>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>1.7</source>
                    <target>1.7</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-maven-plugin</artifactId>
                <version>${jetty.version}</version>
                <dependencies>
                    <!-- Add JNDI support -->
                    <dependency>
                        <groupId>org.eclipse.jetty</groupId>
                        <artifactId>jetty-plus</artifactId>
                        <version>${jetty.version}</version>
                    </dependency>
                    <!-- Add database drivers for JNDI resources -->
                    <dependency>
                        <groupId>com.oracle.database.jdbc</groupId>
                        <artifactId>ojdbc8</artifactId>
                        <version>********</version>
                    </dependency>
                    <dependency>
                        <groupId>org.postgresql</groupId>
                        <artifactId>postgresql</artifactId>
                        <version>42.2.18</version>
                    </dependency>
                    <dependency>
                        <groupId>com.zaxxer</groupId>
                        <artifactId>HikariCP</artifactId>
                        <version>3.4.5</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <httpConnector>
                        <port>${jetty.port}</port>
                        <idleTimeout>60000</idleTimeout>
                    </httpConnector>
                    <scanIntervalSeconds>2</scanIntervalSeconds>
                    <stopPort>${jetty.stopPort}</stopPort>
                    <stopKey>foo</stopKey>
                    <webApp>
                        <contextPath>/callcenter</contextPath>
                    </webApp>
                    <!-- Configure annotation scanning with exclusions for problematic files -->
                    <systemProperties>
                        <systemProperty>
                            <name>org.eclipse.jetty.annotations.maxWait</name>
                            <value>120</value>
                        </systemProperty>
                        <systemProperty>
                            <name>org.eclipse.jetty.annotations.scanClasspath</name>
                            <value>true</value>
                        </systemProperty>
                        <!-- Exclude module-info.class files that cause scanning issues -->
                        <systemProperty>
                            <name>org.eclipse.jetty.annotations.AnnotationConfiguration.MULTI_THREADED</name>
                            <value>false</value>
                        </systemProperty>
                        <systemProperty>
                            <name>org.eclipse.jetty.annotations.AnnotationConfiguration.CLASS_INHERITANCE_MAP</name>
                            <value>org.eclipse.jetty.annotations.ClassInheritanceMap</value>
                        </systemProperty>
                    </systemProperties>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
                <version>1.1</version>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>create</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <shortRevisionLength>9</shortRevisionLength>
                    <doCheck>false</doCheck>
                    <doUpdate>false</doUpdate>
                    <timestampFormat>{0,date,yyyy-MM-dd HH:mm:ss}</timestampFormat>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <webResources>
                        <resource>
                            <directory>${basedir}/src/main/webapp/WEB-INF/views</directory>
                            <filtering>true</filtering>
                            <includes>
                                <include>**/*.jsp</include>
                            </includes>
                        </resource>
                    </webResources>
                </configuration>
            </plugin>
        </plugins>

        <finalName>callcenter</finalName>
    </build>

    <profiles>
        <profile>
            <id>test</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.7</version>
                        <executions>
                            <execution>
                                <id>war</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target name="package-war">
                                        <copy file="${project.basedir}/lib/jave-1.0.2.jar"
                                              tofile="${project.build.directory}/${project.build.finalName}/WEB-INF/lib/jave-1.0.2.jar">
                                        </copy>
                                        <echo>Copy jave.jar to WEB-INF/lib folder</echo>

                                        <copy file="${project.basedir}/src/main/webapp/WEB-INF/lib/jstl-1.2.jar"
                                              tofile="${project.build.directory}/${project.build.finalName}/WEB-INF/lib/jstl-1.2.jar">
                                        </copy>
                                        <echo>Copy jstl-1.2.jar to WEB-INF/lib folder</echo>

                                        <copy file="${project.basedir}/src/main/webapp/WEB-INF/lib/standard-1.1.2.jar"
                                              tofile="${project.build.directory}/${project.build.finalName}/WEB-INF/lib/standard-1.1.2.jar">
                                        </copy>
                                        <echo>Copy standard-1.1.2.jar to WEB-INF/lib folder</echo>

                                        <!-- Create war file -->
                                        <war destfile="${project.build.directory}/${project.build.finalName}.war"
                                             webxml="${project.build.directory}/${project.build.finalName}/WEB-INF/web.xml"
                                             compress="true">
                                            <classes dir="${project.build.directory}/classes" />
                                            <fileset dir="${project.build.directory}/${project.build.finalName}" />
                                        </war>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>prod</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.7</version>
                        <executions>
                            <execution>
                                <id>war</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                <target name="package-war">
                                    <delete file="${project.build.directory}/${project.build.finalName}/WEB-INF/jetty-web.xml"></delete>
                                    <echo>Delete dev jetty web xml file</echo>
                                    <!-- Add Jetty Web PROD XML file to the build -->
                                    <copy file="${project.build.directory}/${project.build.finalName}/WEB-INF/jetty-web-prod.xml"
                                          tofile="${project.build.directory}/${project.build.finalName}/WEB-INF/jetty-web.xml">
                                    </copy>
                                    <echo>Swap jetty-web-prod to jetty-web</echo>

                                    <delete file="${project.build.directory}/${project.build.finalName}/WEB-INF/jetty-web-prod.xml"></delete>
                                    <echo>Delete dev jetty web prod xml file</echo>

                                    <copy file="${project.basedir}/lib/jave-1.0.2.jar"
                                          tofile="${project.build.directory}/${project.build.finalName}/WEB-INF/lib/jave-1.0.2.jar">
                                    </copy>
                                    <echo>Copy jave.jar to WEB-INF/lib folder</echo>

                                    <!-- Create war file -->
                                    <war destfile="${project.build.directory}/${project.build.finalName}.war"
                                         webxml="${project.build.directory}/${project.build.finalName}/WEB-INF/web.xml"
                                         compress="true">
                                        <classes dir="${project.build.directory}/classes" />
                                        <fileset dir="${project.build.directory}/${project.build.finalName}" />
                                    </war>
                                </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
