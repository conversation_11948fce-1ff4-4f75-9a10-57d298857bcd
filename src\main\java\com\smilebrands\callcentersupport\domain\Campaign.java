package com.smilebrands.callcentersupport.domain;

import com.smilebrands.callcentersupport.domain.Facility;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

/**
 * Date: 4/20/12
 * Time: 11:03 AM
 */
@Document
public class Campaign {

    @Id
    private Long campaignPhoneNumber;
    private String campaignName;
    private String callCenter;
    private boolean limitedSupport;
    private List<Facility> facilities = new ArrayList<Facility>();

    public String getCampaignName() {
        return campaignName;
    }

    public void setCampaignName(String campaignName) {
        this.campaignName = campaignName;
    }

    public String getCallCenter() {
        return callCenter;
    }

    public void setCallCenter(String callCenter) {
        this.callCenter = callCenter;
    }

    public boolean getLimitedSupport() {
        return limitedSupport;
    }

    public void setLimitedSupport(boolean limitedSupport) {
        this.limitedSupport = limitedSupport;
    }

    public List<Facility> getFacilities() {
        return facilities;
    }

    public void setFacilities(List<Facility> facilities) {
        this.facilities = facilities;
    }

    public Long getCampaignPhoneNumber() {
        return campaignPhoneNumber;
    }

    public void setCampaignPhoneNumber(Long campaignPhoneNumber) {
        this.campaignPhoneNumber = campaignPhoneNumber;
    }

    @Override
    public String toString() {
        return "Campagin: " + campaignPhoneNumber + " " + campaignName;
    }

    @Override
    public boolean equals(Object obj) {
        Campaign c = (Campaign) obj;
        return this.getCampaignPhoneNumber().equals(c.getCampaignPhoneNumber());
    }

    @Override
    public int hashCode() {
        return campaignPhoneNumber.hashCode();
    }
}
