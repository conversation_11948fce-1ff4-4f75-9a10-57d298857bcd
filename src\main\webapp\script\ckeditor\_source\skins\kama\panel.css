/*
Copyright (c) 2003-2012, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/

.cke_skin_kama .cke_panel
{
	border: 1px solid #8f8f73;
	background-color: #fff;

	width: 120px;
	height: 100px;

	overflow: hidden;

	-moz-border-radius: 3px;
	-webkit-border-radius: 3px;
	border-radius: 3px;
}

.cke_skin_kama .cke_contextmenu
{
	margin: 0;
}

/* Ideally we would use "inherit here"... but you know... IE :( */
.cke_skin_kama .cke_panel iframe
{
	width: 100%;
	height: 100%;
}

/*
 * All the following styles are to be used inside the iframe that holds panel
 * contents. We don't use the cke_skin_default there to avoid the reset to be
 * active.
 * This is not an issue as we'll never have two skins running inside the same
 * panel iframe.
 */

html.cke_skin_kama_container
{
	overflow: auto;
	overflow-x: hidden;
}

body.cke_panel_frame
{
	overflow: auto;
}

.cke_panel_frame .cke_label
{
	display: none;
}

ul.cke_panel_list
{
	list-style-type: none;
	margin: 3px;
	padding: 0px;
	white-space: nowrap;
}

li.cke_panel_listItem
{
	margin: 0px;
}

.cke_panel_listItem a
{
	padding: 2px;
	display: block;
	border: 1px solid #fff;
	color: inherit !important;
	text-decoration: none;
	overflow: hidden;
	text-overflow: ellipsis;
}

/* IE6 */
* html .cke_panel_listItem a
{
	width : 100%;

	/* IE is not able to inherit the color, so we must force it to black */
	color: #000;
}

/* IE7 */
*:first-child+html .cke_panel_listItem a
{
	/* IE is not able to inherit the color, so we must force it to black */
	color: #000;
}

.cke_panel_listItem.cke_selected a
{
	border: 1px solid #ccc;
	background-color: #e9f5ff;
}

.cke_panel_listItem a:hover,
.cke_panel_listItem a:focus,
.cke_panel_listItem a:active
{
	border-color: #316ac5;
	background-color: #dff1ff;
}

.cke_hc .cke_panel_listItem.cke_selected a,
.cke_hc .cke_panel_listItem a:hover,
.cke_hc .cke_panel_listItem a:focus,
.cke_hc .cke_panel_listItem a:active
{
	border-width: 3px;
	padding: 0px;
}

.cke_panel_grouptitle
{
	font-size: 11px;
	font-family: 'Microsoft Sans Serif' , Tahoma, Arial, Verdana, Sans-Serif;
	font-weight: bold;
	white-space: nowrap;
	background-color: #dcdcdc;
	color: #000;
	margin:0px;
	padding:3px;
}

.cke_panel_listItem p,
.cke_panel_listItem h1,
.cke_panel_listItem h2,
.cke_panel_listItem h3,
.cke_panel_listItem h4,
.cke_panel_listItem h5,
.cke_panel_listItem h6,
.cke_panel_listItem pre
{
	margin-top: 3px;
	margin-bottom: 3px;
}

.cke_skin_kama .cke_button_textcolor_panel,
.cke_skin_kama .cke_button_bgcolor_panel
{
	width: 150px;
	height: 135px;
}

.cke_colorblock
{
	padding: 3px;
   font-size: 11px;
   font-family: 'Microsoft Sans Serif', Tahoma, Arial, Verdana, Sans-Serif;
}

.cke_colorblock,
.cke_colorblock a
{
	text-decoration: none;
	color: #000;
}

span.cke_colorbox
{
	width: 10px;
	height: 10px;
   border: #808080 1px solid;
	float: left;
}

.cke_rtl span.cke_colorbox
{
	float: right;
}

a.cke_colorbox
{
	border: #fff 1px solid;
	padding: 2px;
	float: left;
}

.cke_rtl a.cke_colorbox
{
	float: right;
}

a:hover.cke_colorbox,
a:focus.cke_colorbox,
a:active.cke_colorbox
{
	border: #316ac5 1px solid;
	background-color: #dff1ff;
}

a.cke_colorauto,
a.cke_colormore
{
	border: #fff 1px solid;
	padding: 2px;
	display: block;
	cursor: pointer;
}

a:hover.cke_colorauto,
a:hover.cke_colormore,
a:focus.cke_colorauto,
a:focus.cke_colormore,
a:active.cke_colorauto,
a:active.cke_colormore
{
	border: #316ac5 1px solid;
	background-color: #dff1ff;
}
