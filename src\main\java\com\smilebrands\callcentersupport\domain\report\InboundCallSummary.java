package com.smilebrands.callcentersupport.domain.report;

import com.smilebrands.callcentersupport.domain.InboundCall;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

/**
 * Created by phongpham on 8/13/14.
 */
@Document
public class InboundCallSummary {
    @Id
    private InboundCallSummaryId summaryId;
    private InboundCallSummaryValue value;
    private boolean isCombined;

    public InboundCallSummary(){

    }

    public InboundCallSummary(InboundCallSummaryId id, InboundCallSummaryValue value){
        this.summaryId = id;
        this.value = value;
    }

    public InboundCallSummaryId getSummaryId() {
        return summaryId;
    }

    public void setSummaryId(InboundCallSummaryId summaryId) {
        this.summaryId = summaryId;
    }

    public InboundCallSummaryValue getValue() {
        return value;
    }

    public void setValue(InboundCallSummaryValue value) {
        this.value = value;
    }

    public boolean isCombined() {
        return isCombined;
    }

    public void setCombined(boolean isCombined) {
        this.isCombined = isCombined;
    }
}
