package com.smilebrands.callcentersupport;

import com.smilebrands.callcentersupport.domain.schedule.Task;
import com.smilebrands.callcentersupport.util.BaseJsonConverter;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * User: <PERSON><PERSON>
 * Date: 6/25/14
 */
@ContextConfiguration(locations = {"classpath:spring-mongo.xml",
                                   "classpath:test-datasource.xml",
                                   "classpath:spring-jdbc.xml",
                                   "classpath:spring-jpa.xml",
                                   "classpath:spring-services.xml"})
public class BaseTest extends AbstractJUnit4SpringContextTests {

    final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Test
    public void testGenerateHash(){
        try {
            String str = "CALL_CENTER_SUPPORT_SYNC_MONGO";
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.update(str.getBytes(Charset.forName("UTF-8")));
            final byte[] resultByte = messageDigest.digest();
            final String result = new String(Hex.encodeHex(resultByte));
            logger.debug("result: {}", result);
            logger.debug("DigestUtil: {}", DigestUtils.md5Hex(str));
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void date() {
        DateFormat dateFormat = new SimpleDateFormat("MM-dd-yyyy");

        Calendar start = Calendar.getInstance();
        logger.debug(dateFormat.format(start.getTime()));

        start.add(Calendar.DATE, -1);
        logger.debug(dateFormat.format(start.getTime()));
    }

    @Test
    public void compareTest() {

        List<String> times = new ArrayList<String>();
        times.add("05:00");

        Task task = new Task();
        task.setScheduledTimes(times);
        task.setScheduledTimeZone("US/Pacific");

        Calendar cal = Calendar.getInstance();
        if(task.getScheduledTimeZone() != null && task.getScheduledTimeZone().trim().length() > 0){
            Calendar temp = Calendar.getInstance(TimeZone.getTimeZone(task.getScheduledTimeZone()));
            cal.set(temp.get(Calendar.YEAR), temp.get(Calendar.MONTH), temp.get(Calendar.DAY_OF_MONTH), temp.get(Calendar.HOUR_OF_DAY), temp.get(Calendar.MINUTE));
        }

        String timeStr = VelocityUtils.getDateAsString(cal.getTime(), "HH:mm");
        logger.debug("timeStr: {}", timeStr);
        int idx = task.getScheduledTimes().indexOf(timeStr);
        if(task.getScheduledTimes() != null && idx != -1) {
            logger.debug("running call log report at " + cal.getTime());
        }
    }

}
