package com.smilebrands.callcentersupport;

import com.mongodb.Mongo;
import com.mongodb.MongoClient;
import com.smilebrands.callcentersupport.domain.*;
import com.smilebrands.callcentersupport.repo.FacilityRepository;
import com.smilebrands.callcentersupport.repo.JdbcRepository;
import com.smilebrands.callcentersupport.repo.MongoRepository;
import com.smilebrands.callcentersupport.service.ConfigurationService;
import com.smilebrands.callcentersupport.util.DateTimeUtils;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileWriter;
import java.net.URL;
import java.util.*;

/**
 * User: Marlin Clark
 * Date: 6/25/14
 */
public class ConfigurationServiceTest extends BaseTest {

    @Autowired
    ConfigurationService configurationService;

    @Autowired
    FacilityRepository facilityRepository;

    @Autowired
    MongoRepository mongoRepository;

    @Autowired
    JdbcRepository jdbcRepository;

    @Autowired
    private MongoOperations mongoOperations;


    @Test
    public void spamNumberTest() {
        List<PhoneNumberRoutingRules> spamNumbers = jdbcRepository.getSpamPhoneNumbers();
        for (PhoneNumberRoutingRules spamRule : spamNumbers) {
            mongoOperations.save(spamRule);
        }
    }

    @Test
    public void phoneNumberTest() {
        PhoneNumber number = new PhoneNumber();
        number.setPhoneNumber(8444007645L);
        number.setBrandName("Bright Now! Dental");
        number.setIvrPromptFile("hello.mp3");
        number.setPhoneType("TF");
        number.setCallCenterName("Plano");
        number.setCallCenterQueueName("Bright Now Queue");

        logger.debug(number.toString());

        configurationService.persistPhoneNumber(number);

        number = new PhoneNumber();
        number.setPhoneNumber(8448007645L);
        number.setBrandName("Monarch Dental");
        number.setIvrPromptFile("hello.mp3");
        number.setPhoneType("TF");
        number.setCallCenterName("Plano");
        number.setCallCenterQueueName("Monarch Queue");

        logger.debug(number.toString());

        configurationService.persistPhoneNumber(number);

        number = new PhoneNumber();
        number.setPhoneNumber(8447007645L);
        number.setBrandName("Castle Dental");
        number.setIvrPromptFile("hello.mp3");
        number.setPhoneType("TF");
        number.setCallCenterName("Plano");
        number.setCallCenterQueueName("Castle Queue");

        logger.debug(number.toString());

        configurationService.persistPhoneNumber(number);

    }

    /*
        Plano
        38325 - English
        38326 – Spanish

        Irvine
        6401 - English
        6402 – Spanish
     */
    @Test
    public void phoneNumberAniConfig() {
        PhoneNumber number = new PhoneNumber();
        number.setPhoneNumber(38325L);
        number.setBrandName("Plano English Transfer");
        number.setSearchOnAni(true);
        number.setPhoneType("TX");
        number.setLanguage("en");
        number.setCallCenterName("Plano");
        number.setCallCenterQueueName("Bright Now Queue");

        logger.debug(number.toString());
        configurationService.persistPhoneNumber(number);

        number = new PhoneNumber();
        number.setPhoneNumber(38326L);
        number.setBrandName("Plano Spanish Transfer");
        number.setSearchOnAni(true);
        number.setPhoneType("TX");
        number.setLanguage("sp");
        number.setCallCenterName("Plano");
        number.setCallCenterQueueName("Bright Now Queue");

        logger.debug(number.toString());
        configurationService.persistPhoneNumber(number);

        number = new PhoneNumber();
        number.setPhoneNumber(6401L);
        number.setBrandName("Irvine English Transfer");
        number.setSearchOnAni(true);
        number.setPhoneType("TX");
        number.setLanguage("en");
        number.setCallCenterName("Irvine");

        logger.debug(number.toString());
        configurationService.persistPhoneNumber(number);

        number = new PhoneNumber();
        number.setPhoneNumber(6402L);
        number.setBrandName("Irvine Spanish Transfer");
        number.setSearchOnAni(true);
        number.setPhoneType("TX");
        number.setLanguage("sp");
        number.setCallCenterName("Irvine");

        logger.debug(number.toString());
        configurationService.persistPhoneNumber(number);

    }

    @Test
    public void campaignTest() {
        Campaign campaign = new Campaign();
        campaign.setCampaignPhoneNumber(7144281200L);
        campaign.setCampaignName("Sample Campaign");
        campaign.setCallCenter("Irvine");
        campaign.setCampaignPhoneNumber(7144281200L);
        campaign.setLimitedSupport(false);


        configurationService.persistCampaign(campaign);

        logger.debug(configurationService.getCampaign(7144281200L).toString());

    }

    @Test
    public void testLoadFacilityInfo(){
        List<Facility> facilityList = facilityRepository.findAllFacilities(true);
        for(Facility fac : facilityList){
            logger.debug("{}", fac);
        }
        logger.debug("size: {}", facilityList.size());
    }

    @Test
    public void testGetAllCampaign(){
        logger.debug("{}", configurationService.findAllCampaign());
    }

    @Test
    public void testGetReasonResolution(){
        List<CallResolutionCode> list = configurationService.getCallReasonCodes("PSR");
        logger.debug("PSR size: {}", list.size());
        list = configurationService.getCallReasonCodes("CSR");
        logger.debug("CSR size: {}", list.size());
    }

    @Test
    public void testGetTeam(){
        List<Map<String, Object>> list = configurationService.getTeamHierarchy();
        logger.debug("team: {}", list);
    }

    @Test
    public void testUpdateRequest(){
        configurationService.processUpdateFacilityRequest(null);
    }

    @Test
    public void testLoadMarketingNumber(){
        URL url = this.getClass().getResource("/CTI_upload-0720217.xls");
        String fileName = url.getFile();

        try{
            Workbook workbook = Workbook.getWorkbook(new File(fileName));
            Sheet sheet = workbook.getSheet(0);
            Cell siteCell = sheet.findCell("Site code");
            Cell numberCell = sheet.findCell("DID");
            Cell campaignCell = sheet.findCell("Campaign");
            Cell callCenterCell = sheet.findCell("Call center");
            Cell ccSupportedCell = sheet.findCell("Call Center Supported");
//            Cell queueCell = sheet.findCell("Queue");
            if(siteCell == null || numberCell == null || campaignCell == null
                    || callCenterCell == null || ccSupportedCell == null
//                    || queueCell == null
                ){
                throw new RuntimeException("Invalid Data");
            }
            int siteCol = siteCell.getColumn();
            int numberCol = numberCell.getColumn();
            int campaignCol = campaignCell.getColumn();
            int callCenterCol = callCenterCell.getColumn();
            int ccSupportedCol = ccSupportedCell.getColumn();
//            int queueCol = queueCell.getColumn();

            Map<Integer, Boolean> ccSupportedMap = new HashMap<Integer, Boolean>();
            LinkedHashMap<String, Set<String>> dataError = new LinkedHashMap<String, Set<String>>();
            int irvCnt = 0;
            int plaCnt = 0;

            for(int row = 1; row<sheet.getRows(); row++){
                siteCell = sheet.getCell(siteCol, row);
                numberCell = sheet.getCell(numberCol, row);
                campaignCell = sheet.getCell(campaignCol, row);
                callCenterCell = sheet.getCell(callCenterCol, row);
                ccSupportedCell = sheet.getCell(ccSupportedCol, row);
//                queueCell = sheet.getCell(queueCol, row);

                Set<String> error = new HashSet<String>();

                int siteCode = Integer.valueOf(siteCell.getContents());
                long number = Long.valueOf(numberCell.getContents());
                String campaign = campaignCell.getContents();
                String callCenter = callCenterCell.getContents();
                boolean ccSupported = true || ccSupportedCell.getContents() != null && ccSupportedCell.getContents().equalsIgnoreCase("y") ? true : false;
//                String queue = queueCell.getContents();
                Facility facility = mongoRepository.findFacilityDetail(siteCode);
                String key = siteCode + (facility != null ? ("-" + facility.getName()) : "");
                if(ccSupportedMap.containsKey(siteCode)){
                    if(ccSupportedMap.get(siteCode) != ccSupported){
                        logger.debug("Inconsistent data for facility[" + siteCode + "]...");
                        error.add("Inconsistent value of [callCenterSupported]...");
                    }
                }else{
                    ccSupportedMap.put(siteCode, ccSupported);
                }
                if(facility != null && facility.isCsrSupported() != ccSupported){
//                    error.add("[callCenterSupported] is set to [" + ccSupported + "] while office is set to [" + facility.isCsrSupported() + "]...");
                }
                PhoneNumber existing = mongoRepository.findPhoneNumber(number);
                if(existing != null && existing.isActive()){
                    logger.debug("[" + number + "] already exists...to[" + siteCode + "] from[" + existing.getFacilityId() + "]");
                    existing = null;
//                    error.add("[" + number + "] already exists...to[" + siteCode + "] from[" + existing.getFacilityId() + "]");
                }

                if(facility == null){
                    logger.debug("Site Code[" + siteCode + "] does not exist...");
                    error.add("Site Code[" + siteCode + "] does not exist...");
                }else if(facility.getPhoneNumber() == null){
                    logger.debug("Site Code[" + siteCode + "] does not have transfer number...");
                    error.add("Site Code[" + siteCode + "] does not have transfer number...");
                }
                if(error.size() > 0){
                    if(dataError.containsKey(key)){
                        dataError.get(key).addAll(error);
                    }else{
                        dataError.put(key, error);
                    }
                }else if(existing == null){
                    String facilityBrand = facility.getBrandName();
                    PhoneNumber campaignNumber = new PhoneNumber();
                    campaignNumber.setActive(true);
                    campaignNumber.setPhoneNumber(number);
                    campaignNumber.setPhoneType("CM");
                    campaignNumber.setFacilityId(facility.getFacilityId());
                    campaignNumber.setCampaignName(campaign);
                    campaignNumber.setBrandName(facility.getBrandName());
//                    campaignNumber.setCallCenterName(callCenter != null && callCenter.equalsIgnoreCase("IRV") ? "Irvine" : "Plano");
//                    campaignNumber.setCallCenterQueueName(queue);
                    campaignNumber.setCallCenterName(callCenter);
                    if(callCenter.equalsIgnoreCase("Irvine")){
                        campaignNumber.setCallCenterQueueName("IRV_Campaign");
                        irvCnt++;
                    }else{
                        campaignNumber.setCallCenterQueueName("PLA_Campaign");
                        plaCnt++;
                    }

                    campaignNumber.setCallCenterSupported(ccSupported);
                    campaignNumber.setPsrSupported(ccSupported);
                    campaignNumber.setCsrSupported(ccSupported);

                    PhoneNumber officeNumber = mongoRepository.findPhoneNumberByFacility(facility.getFacilityId(), "ML");
                    if(officeNumber != null){
                        campaignNumber.setTransferNumber(officeNumber.getTransferNumber());
                    }

                    if(facilityBrand != null){
                        facilityBrand = facilityBrand.toLowerCase();
                        if(facilityBrand.indexOf("bright") != -1){
                            campaignNumber.setVoiceMailRoute("brightnow");
                        }else if(facilityBrand.indexOf("castle") != -1){
                            campaignNumber.setVoiceMailRoute("castle");
                        }else if(facilityBrand.indexOf("monarch") != -1){
                            campaignNumber.setVoiceMailRoute("monarch");
                        }else if(facilityBrand.indexOf("newport") != -1){
                            campaignNumber.setVoiceMailRoute("newport");
                        }else{
                            campaignNumber.setVoiceMailRoute("smilebrands");
                        }
                    }
                    campaignNumber.setLanguage("en");
                    campaignNumber.setIvrName("MainIVR");
                    campaignNumber.setIvrPromptFile("hello.mp3");
                    campaignNumber.setCreatedBy(102029);
                    campaignNumber.setCreatedOn(new Date());
                    mongoRepository.persistPhoneNumber(campaignNumber);
                    logger.debug("insert new phone number[" + number + "]");
                }

//                logger.debug(row + "/" + siteCode + ":::" + number + ":::" + campaign + ":::" + callCenter + ":::" + ccSupported + ":::" + queue);
            }
            int count = 1;
            for(String siteCode : dataError.keySet()){
                System.out.println(count + "/Error for " + siteCode);
                for(String str : dataError.get(siteCode)){
                    System.out.println("\t" + str);
                }
                System.out.println();
                count++;
            }
            System.out.println("irvine cnt: " + irvCnt);
            System.out.println("plano cnt: " + plaCnt);
        }catch(Exception ex){
            ex.printStackTrace();
        }
    }

    @Test
    public void testUpdatePhoneNumber(){
        URL url = this.getClass().getResource("/Update_Marketing_Numbers.xls");
        String fileName = url.getFile();

        try{
            Workbook workbook = Workbook.getWorkbook(new File(fileName));
            Sheet sheet = workbook.getSheet(0);
            Cell siteCell = sheet.findCell("Location Number");
            Cell numberCell = sheet.findCell("Telephone Number");
            Cell campaignCell = sheet.findCell("Campaign");
            Cell callCenterCell = sheet.findCell("Call Center");
            Cell ccSupportedCell = sheet.findCell("Call Center Supported");
            Cell queueCell = sheet.findCell("Queue");
            if(siteCell == null || numberCell == null || campaignCell == null
                    || callCenterCell == null || ccSupportedCell == null
                    || queueCell == null){
                throw new RuntimeException("Invalid Data");
            }
            int siteCol = siteCell.getColumn();
            int numberCol = numberCell.getColumn();
            int campaignCol = campaignCell.getColumn();
            int callCenterCol = callCenterCell.getColumn();
            int ccSupportedCol = ccSupportedCell.getColumn();
            int queueCol = queueCell.getColumn();

            Map<Integer, Boolean> ccSupportedMap = new HashMap<Integer, Boolean>();
            LinkedHashMap<String, Set<String>> dataError = new LinkedHashMap<String, Set<String>>();

            for(int row = 1; row<sheet.getRows(); row++){
                siteCell = sheet.getCell(siteCol, row);
                numberCell = sheet.getCell(numberCol, row);
                campaignCell = sheet.getCell(campaignCol, row);
                callCenterCell = sheet.getCell(callCenterCol, row);
                ccSupportedCell = sheet.getCell(ccSupportedCol, row);
                queueCell = sheet.getCell(queueCol, row);

                Set<String> error = new HashSet<String>();

                int siteCode = Integer.valueOf(siteCell.getContents());
                long number = Long.valueOf(numberCell.getContents());
                String campaign = campaignCell.getContents();
                String callCenter = callCenterCell.getContents();
                boolean ccSupported = ccSupportedCell.getContents() != null && ccSupportedCell.getContents().equalsIgnoreCase("yes") ? true : false;
                String queue = queueCell.getContents();
                Facility facility = mongoRepository.findFacilityDetail(siteCode);
                String key = siteCode + (facility != null ? ("-" + facility.getName()) : "");
                if(ccSupportedMap.containsKey(siteCode)){
                    if(ccSupportedMap.get(siteCode) != ccSupported){
                        logger.debug("Inconsistent data for facility[" + siteCode + "]...");
                        error.add("Inconsistent value of [callCenterSupported]...");
                    }
                }else{
                    ccSupportedMap.put(siteCode, ccSupported);
                }
                if(facility == null){
                    error.add(siteCode + " not found");
                }
                if(facility != null && facility.isCsrSupported() != ccSupported){
//                    error.add("[callCenterSupported] is set to [" + ccSupported + "] while office is set to [" + facility.isCsrSupported() + "]...");
                }
                PhoneNumber existing = mongoRepository.findPhoneNumber(number);
                if(existing == null){
                    error.add(number + " not found");
                }else if(existing != null){
                    existing.setFacilityId(siteCode);
                    existing.setCampaignName(campaign);
                    existing.setCallCenterName(callCenter);
                    existing.setCallCenterQueueName(queue);
                    existing.setCallCenterSupported(ccSupported);
                    existing.setPsrSupported(ccSupported);
                    existing.setCsrSupported(ccSupported);

                    mongoRepository.persistPhoneNumber(existing);
                }
                if(error.size() > 0){
                    dataError.put(key, error);
                }
            }
            int count = 1;
            for(String key : dataError.keySet()){
                Set<String> error = (Set)dataError.get(key);
                logger.debug(count + "/", key);
                for(String str : error){
                    logger.debug("\t" + str);
                }
                count++;
            }
        }catch(Exception ex){
            ex.printStackTrace();
        }
    }

    @Test
    public void testLoadFBAndGoogleCampaign(){
        URL url = this.getClass().getResource("/CTI_Load_04102017.xls");
        String fileName = url.getFile();

        try{
            Workbook workbook = Workbook.getWorkbook(new File(fileName));
            Sheet sheet = workbook.getSheet(0);
            Cell siteCell = sheet.findCell("SiteCode");
            Cell callCenterCell = sheet.findCell("Call Center");
            Cell googleNumberCell = sheet.findCell("Campaign Google");
            Cell fbNumberCell = sheet.findCell("Campaign Facebook");

            Cell line1Cell = sheet.findCell("Transfer Line");
            Cell queueCell = sheet.findCell("Queue Name");
            if(siteCell == null || googleNumberCell == null || fbNumberCell == null
                    || callCenterCell == null || line1Cell == null || queueCell == null){
                throw new RuntimeException("Invalid Data");
            }
            int siteCol = siteCell.getColumn();
            int googleNumberCol = googleNumberCell.getColumn();
            int fbNumberCol = fbNumberCell.getColumn();
            int callCenterCol = callCenterCell.getColumn();
            int line1Col = line1Cell.getColumn();
            int queueCol = queueCell.getColumn();
            boolean ccSupported = true;

            LinkedHashMap<String, Set<String>> dataError = new LinkedHashMap<String, Set<String>>();
            String gglCampaignName = "Google Campaign";
            String fbCampaignName = "Facebook Campaign";
            int insertCnt = 0;

            for(int row = 1; row<sheet.getRows(); row++) {
                siteCell = sheet.getCell(siteCol, row);
                googleNumberCell = sheet.getCell(googleNumberCol, row);
                fbNumberCell = sheet.getCell(fbNumberCol, row);
                callCenterCell = sheet.getCell(callCenterCol, row);
                line1Cell = sheet.getCell(line1Col, row);
                queueCell = sheet.getCell(queueCol, row);

                Set<String> error = new HashSet<String>();

                int siteCode = Integer.valueOf(siteCell.getContents());
                String callCenter = callCenterCell.getContents();
                long googleNumber = Long.valueOf(googleNumberCell.getContents());
                long fbNumber = Long.valueOf(fbNumberCell.getContents());
                long line1 = Long.valueOf(line1Cell.getContents());
                String queue = queueCell.getContents();
                boolean googleExists = mongoRepository.findPhoneNumber(googleNumber) != null;
                boolean fbExists = mongoRepository.findPhoneNumber(fbNumber) != null;
                if(googleExists || fbExists){
                    logger.debug("Data at row[" + row + "] has google number[" + googleNumber + "-" + googleExists + "] or fb number[" + fbNumber + "-" + fbExists + "]");
                }
                Facility facility = mongoRepository.findFacilityDetail(siteCode);
                String key = siteCode + (facility != null ? ("-" + facility.getName()) : "");


                if(facility == null){
                    logger.debug("Site Code[" + siteCode + "] does not exist...");
                    error.add("Site Code[" + siteCode + "] does not exist...");
                }
                if(error.size() > 0){
                    if(dataError.containsKey(key)){
                        dataError.get(key).addAll(error);
                    }else{
                        dataError.put(key, error);
                    }
                }else{
                    PhoneNumber campaignNumber = generateCampaignNumber(googleNumber, line1, ccSupported, facility, gglCampaignName, callCenter, queue);
                    mongoRepository.persistPhoneNumber(campaignNumber);
                    insertCnt++;
                    logger.debug("insert new phone number[" + googleNumber + "] to campaign[" + gglCampaignName + "]");
                    campaignNumber = generateCampaignNumber(fbNumber, line1, ccSupported, facility, fbCampaignName, callCenter, queue);
                    mongoRepository.persistPhoneNumber(campaignNumber);
                    insertCnt++;
                    logger.debug("insert new phone number[" + fbNumber + "] to campaign[" + fbCampaignName + "]");
                }
            }
            logger.debug("number of insert: {}", insertCnt);


        }catch (Exception ex){
            ex.printStackTrace();
        }
    }

    private PhoneNumber generateCampaignNumber(Long number, Long tranferNumber
                                                , boolean ccSupported, Facility facility
                                                , String campaignName, String callCenter
                                                , String queueName){
        String facilityBrand = facility.getBrandName();
        PhoneNumber campaignNumber = new PhoneNumber();
        campaignNumber.setActive(true);
        campaignNumber.setPhoneNumber(number);
        campaignNumber.setPhoneType("CM");
        campaignNumber.setFacilityId(facility.getFacilityId());
        campaignNumber.setCampaignName(campaignName);
        campaignNumber.setBrandName(facility.getBrandName());
        campaignNumber.setCallCenterName(callCenter);
        campaignNumber.setCallCenterQueueName(queueName);

        campaignNumber.setCallCenterSupported(ccSupported);
        campaignNumber.setPsrSupported(ccSupported);
        campaignNumber.setCsrSupported(ccSupported);

        if(tranferNumber != null){
            campaignNumber.setTransferNumber(tranferNumber);
        }else{
            PhoneNumber officeNumber = mongoRepository.findPhoneNumberByFacility(facility.getFacilityId(), "ML");
            if (officeNumber != null) {
                campaignNumber.setTransferNumber(officeNumber.getTransferNumber());
            }
        }


        if (facilityBrand != null) {
            facilityBrand = facilityBrand.toLowerCase();
            if (facilityBrand.indexOf("bright") != -1) {
                campaignNumber.setVoiceMailRoute("brightnow");
            } else if (facilityBrand.indexOf("castle") != -1) {
                campaignNumber.setVoiceMailRoute("castle");
            } else if (facilityBrand.indexOf("monarch") != -1) {
                campaignNumber.setVoiceMailRoute("monarch");
            } else if (facilityBrand.indexOf("newport") != -1) {
                campaignNumber.setVoiceMailRoute("newport");
            } else {
                campaignNumber.setVoiceMailRoute("smilebrands");
            }
        }
        campaignNumber.setLanguage("en");
        campaignNumber.setIvrName("MainIVR");
        campaignNumber.setIvrPromptFile("hello.mp3");
        campaignNumber.setCreatedBy(102029);
        campaignNumber.setCreatedOn(new Date());
        return campaignNumber;
    }

    @Test
    public void testUpdateCMNumber(){
        Query query = new Query(Criteria.where("phoneType").is("CM").and("isActive").is(true));
        query.with(new Sort(Sort.Direction.ASC, "facilityId"));
        List<PhoneNumber> list = mongoOperations.find(query, PhoneNumber.class);
        logger.debug("list size: {}", list.size());
        int count = 0;
        for(PhoneNumber phoneNumber : list){
            String campaignName = phoneNumber.getCampaignName() != null ? phoneNumber.getCampaignName() : "";
            if(!campaignName.equalsIgnoreCase("Vic") && !campaignName.equalsIgnoreCase("Shared Mail") && !campaignName.equalsIgnoreCase("eMail")){
                continue;
            }
            count++;
            logger.debug(count + "/" + phoneNumber.getFacilityId() + ":::" + phoneNumber.getPhoneNumber() + ":::" + phoneNumber.getCampaignName() + ":::" + phoneNumber.getTransferNumber());
            if(phoneNumber.getFacilityId() != 0){
                PhoneNumber officeNumber = mongoRepository.findPhoneNumberByFacility(phoneNumber.getFacilityId(), "ML");
                logger.debug("\toffice transferred number: " + (officeNumber != null ? officeNumber.getTransferNumber() : "not exists"));
                if(officeNumber != null){
                    phoneNumber.setTransferNumber(officeNumber.getTransferNumber());
                    mongoRepository.persistPhoneNumber(phoneNumber);
                }
            }
        }
    }

    @Test
    public void testRemoveNumber(){
        Long number = 7148690665L;
        PhoneNumber phoneNumber = mongoRepository.findPhoneNumber(number);
        if(phoneNumber != null && phoneNumber.getPhoneNumber() > 0){
            phoneNumber.setPhoneNumber(phoneNumber.getPhoneNumber() * -1);
            mongoRepository.persistPhoneNumber(phoneNumber);
        }
        phoneNumber = mongoRepository.findPhoneNumber(number);
        if(phoneNumber != null){
            mongoOperations.remove(phoneNumber);
        }
    }

    @Test
    public void testLoad1800Dentist(){
        URL url = this.getClass().getResource("/1-800-Dentist.xls");
        String fileName = url.getFile();

        try{
            Workbook workbook = Workbook.getWorkbook(new File(fileName));
            Sheet sheet = workbook.getSheet(0);
            Cell citeCodeCell = sheet.findCell("Site code");
            Cell callCenterCell = sheet.findCell("Call center");
            Cell campaignCell = sheet.findCell("Campaign");
            Cell didCell = sheet.findCell("DID");
            int cnt = 1;
            if(citeCodeCell != null && callCenterCell != null
                && campaignCell != null && didCell != null){
                int citeCodeIdx = citeCodeCell.getColumn();
                int callCenterIdx = callCenterCell.getColumn();
                int campaignIdx = campaignCell.getColumn();
                int didIdx = didCell.getColumn();
                for(int row=1; row<sheet.getRows(); row++){
                    int citeCode = Integer.valueOf(sheet.getCell(citeCodeIdx, row).getContents());
                    String callCenter = sheet.getCell(callCenterIdx, row).getContents();
                    String campaign = sheet.getCell(campaignIdx, row).getContents();
                    long did = Long.valueOf(sheet.getCell(didIdx, row).getContents());
                    PhoneNumber existingNumber = mongoRepository.findPhoneNumber(did);
                    if(existingNumber != null){
                        logger.debug("Phone number[" + did + "] already exists as [" + existingNumber.getPhoneType() + "]");
                    }else{
                        PhoneNumber officeNumber = mongoRepository.findPhoneNumberByFacility(citeCode);
                        Facility facility = mongoRepository.findFacilityDetail(citeCode);
                        if(officeNumber == null){
                            logger.debug("Office[" + citeCode + "] does not have ML");
                        }else if(officeNumber.getTransferNumber() == null || officeNumber.getTransferNumber() <= 0){
                            logger.debug("Office[" + citeCode + "] does not have transfered number.");
                        }else{
                            logger.debug("Office[" + citeCode + "], did[" + did + "] is good to go.");
                            String facilityBrand = facility.getBrandName();
                            PhoneNumber campaignNumber = new PhoneNumber();
                            campaignNumber.setActive(true);
                            campaignNumber.setPhoneNumber(did);
                            campaignNumber.setPhoneType("CM");
                            campaignNumber.setFacilityId(facility.getFacilityId());
                            campaignNumber.setCampaignName(campaign);
                            campaignNumber.setBrandName(facility.getBrandName());
                            campaignNumber.setCallCenterName(callCenter);
                            if(callCenter.equalsIgnoreCase("Plano")){
                                campaignNumber.setCallCenterQueueName("PLA_Campaign");
                            }else{
                                campaignNumber.setCallCenterQueueName("IRV_Campaign");
                            }

                            campaignNumber.setCallCenterSupported(true);
                            campaignNumber.setPsrSupported(true);
                            campaignNumber.setCsrSupported(true);
                            campaignNumber.setTransferNumber(officeNumber.getTransferNumber());

                            if(facilityBrand != null){
                                facilityBrand = facilityBrand.toLowerCase();
                                if(facilityBrand.indexOf("bright") != -1){
                                    campaignNumber.setVoiceMailRoute("brightnow");
                                }else if(facilityBrand.indexOf("castle") != -1){
                                    campaignNumber.setVoiceMailRoute("castle");
                                }else if(facilityBrand.indexOf("monarch") != -1){
                                    campaignNumber.setVoiceMailRoute("monarch");
                                }else if(facilityBrand.indexOf("newport") != -1){
                                    campaignNumber.setVoiceMailRoute("newport");
                                }else{
                                    campaignNumber.setVoiceMailRoute("smilebrands");
                                }
                            }
                            campaignNumber.setLanguage("en");
                            campaignNumber.setIvrName("MainIVR");
                            campaignNumber.setIvrPromptFile("hello.mp3");
                            campaignNumber.setCreatedBy(102029);
                            campaignNumber.setCreatedOn(new Date());
                            mongoRepository.persistPhoneNumber(campaignNumber);
                            logger.debug(cnt + "/insert new campaign phone.");
                            cnt++;
                        }
                    }
                }
            }else{
                logger.debug("Fail to find columns..");
            }
        }catch (Exception ex){

        }
    }

    @Test
    public void updatePhoneNumber(){
        Long number = 4403459068L;
        int facilityId = 24220;
        PhoneNumber phoneNumber = mongoRepository.findPhoneNumber(number);
        if(phoneNumber != null && phoneNumber.getPhoneType().equalsIgnoreCase("CM")){
            phoneNumber.setFacilityId( facilityId);
            if(phoneNumber.getTransferNumber() == null){
                PhoneNumber officeNumber = mongoRepository.findPhoneNumberByFacility(facilityId, "ML");
                if(officeNumber != null){
                    phoneNumber.setTransferNumber(officeNumber.getTransferNumber());
                }
            }
            mongoRepository.persistPhoneNumber(phoneNumber);
        }
    }

    @Test
    public void testReportOnCMNumber(){
        Query query = new Query(Criteria.where("phoneType").is("CM").and("isActive").is(true));
        query.with(new Sort(Sort.Direction.ASC, "facilityId"));
        List<PhoneNumber> list = mongoOperations.find(query, PhoneNumber.class);
        int count = 0;
        StringBuilder sb = new StringBuilder();
        sb.append("Line,Office Id,Campaign Number,Campaign Name,Transfer Number,Call Center,Queue");
        for(PhoneNumber phoneNumber : list){
            String campaignName = phoneNumber.getCampaignName() != null ? phoneNumber.getCampaignName() : "";
            if(!campaignName.equalsIgnoreCase("Vic") && !campaignName.equalsIgnoreCase("Shared Mail") && !campaignName.equalsIgnoreCase("eMail")){
                continue;
            }
            count++;
            sb.append("\n" + count + "," + phoneNumber.getFacilityId() + "," + phoneNumber.getPhoneNumber() + "," + phoneNumber.getCampaignName()
                        + "," + phoneNumber.getTransferNumber() + "," + phoneNumber.getCallCenterName() + "," + phoneNumber.getCallCenterQueueName());

        }
        try{
            FileWriter fw = new FileWriter(new File("Campaign Numbers.csv"));
            fw.write(sb.toString());
            fw.flush();
            fw.close();
        }catch(Exception ex){
            ex.printStackTrace();
        }
    }

    @Test
    public void testGetDateByOffset(){
        Calendar cal = Calendar.getInstance();
        String forward = "\n", backward = "\n";
//        for(int i=0; i<7; i++){
            for(int j=0; j<100; j++){
                Date dt = DateTimeUtils.adjustDateByOffset(cal.getTime(), j+1, new Integer[]{Calendar.SUNDAY});
                forward += (j+1) + " days forward from [" + cal.getTime() + "]: " + dt + "\n";
                dt = DateTimeUtils.adjustDateByOffset(cal.getTime(), (-1)*(j+1), new Integer[]{Calendar.SUNDAY});
                backward += (j+1) + " days back from [" + cal.getTime() + "]: " + dt + "\n";
            }
            backward += "\n\n";
            forward += "\n\n";
            cal.add(Calendar.DAY_OF_MONTH, 1);
//        }
        logger.debug("{}", forward);
        logger.debug("{}", backward);
        logger.debug("{}", Calendar.SUNDAY);
        logger.debug("{}", Calendar.MONDAY);
        logger.debug("{}", Calendar.TUESDAY);
        logger.debug("{}", Calendar.WEDNESDAY);
        logger.debug("{}", Calendar.THURSDAY);
        logger.debug("{}", Calendar.FRIDAY);
        logger.debug("{}", Calendar.SATURDAY);
    }

    @Test
    public void testGetReportDayForNoShow(){
        Calendar cal = Calendar.getInstance();
        cal.set(2016, Calendar.MARCH, 21);
        Integer[] excludeDays = new Integer[]{Calendar.SATURDAY, Calendar.SUNDAY};
        for(int i =0; i<7; i++){
            Calendar desiredCal = Calendar.getInstance();
            desiredCal.setTime(DateTimeUtils.adjustDateByOffset(cal.getTime(), -4, excludeDays));
            logger.debug(cal.getTime() + " ::: "
                    + desiredCal.getTime() + "-" + (desiredCal.get(Calendar.DAY_OF_WEEK) == Calendar.FRIDAY ? DateTimeUtils.adjustDateByOffset(cal.getTime(), -4, new Integer[]{Calendar.SUNDAY}) : desiredCal.getTime()));
            cal.add(Calendar.DAY_OF_MONTH, 1);
        }
    }

    @Test
    public void testLoadInboundCall(){
        try{
            Mongo itchy = new MongoClient("itchy.bnd.corp");
            Mongo bart = new MongoClient("bart.bnd.corp");
            MongoOperations itchyOps = new MongoTemplate(itchy, "callCenter");
            MongoOperations bartOps = new MongoTemplate(bart, "callCenter");
            Calendar cal = Calendar.getInstance();
            cal.set(2016, Calendar.APRIL, 1);
            int month = cal.get(Calendar.MONTH);
            List<String> dates = new ArrayList<String>();
            while(cal.get(Calendar.MONTH) == month){
                dates.add(VelocityUtils.getDateAsString(cal.getTime(), "MM-dd-yyyy"));
                cal.add(Calendar.DAY_OF_MONTH, 1);
            }
            Query query = new Query(Criteria.where("createDate").in(dates));
//            query.with(new Sort(Sort.Direction.ASC, "createDateTime"));
            logger.debug("Query: {}", query);
            List<InboundCall> itchyInboundCalls = itchyOps.find(query, InboundCall.class);
            logger.debug("itchy inbound calls size: {}", itchyInboundCalls.size());
            for(InboundCall inboundCall : itchyInboundCalls){
                bartOps.save(inboundCall);
            }
        }catch (Exception ex){
            ex.printStackTrace();
        }
    }

    @Test
    public void testUpdateCallCenterEmployee() {
        Integer supervisorId=102104;
        String changedToAgents = "114873,102323,117872,115303,118430,118177,118239,118602,118131,118022"; //,118022 <-- removed & added back

        configurationService.updateCallCenterEmployee(supervisorId, changedToAgents);

        /*CallCenterEmployee supervisor = jdbcRepository.getCallCenterEmployeeByNumber(supervisorId, false);
        logger.debug("Supervisor: " + supervisor);
        logger.debug("Supervisor's Supervisor: " + supervisor.getSupervisorNumber() + " --> " + supervisor.getSupervisorName());

        List<CallCenterEmployee> currentAgents = configurationService.getSupervisedAgents(supervisorId);
        logger.debug(currentAgents.toString());

        List<Integer> listCurrentAgents = new ArrayList<Integer>();
        List<Integer> listChangeAgents = new ArrayList<Integer>();
        List<Integer> listCommonAgents = new ArrayList<Integer>();

        for (CallCenterEmployee e : currentAgents) {
            listCurrentAgents.add(e.getEmployeeNumber());
        }

        String ids[] = StringUtils.commaDelimitedListToStringArray(changedToAgents);
        for (String s : ids) {
            listChangeAgents.add(Integer.valueOf(s));
        }

        // -- remove the added supervisor id
        listCurrentAgents.remove(supervisorId);

        // -- find common list agents between the to groups
        listCommonAgents.addAll(listChangeAgents);
        listCommonAgents.retainAll(listCurrentAgents);


        // -- find new agents in the current list
        listChangeAgents.removeAll(listCommonAgents);

        // -- find existing agents that are not in the new list
        listCurrentAgents.removeAll(listCommonAgents);


        logger.debug("Supervised Agents being Removed:  " + listCurrentAgents.toString());
        logger.debug("Supervised Agents being Added:  " + listChangeAgents.toString());
        */
    }

    @Test
    public void showDifferences() {
        String changes = "114873,102323,117872,115303,118430,118177,118239,118602,118131,118022";
        String originals = "114873,102323,117872,115303,118430,118177,118239,118602,118131,110840";

        List<Integer> listOfChanges = new ArrayList<Integer>();
        String ids[] = StringUtils.commaDelimitedListToStringArray(changes);
        for (String s : ids) {
            listOfChanges.add(Integer.valueOf(s));
        }

        List<Integer> listOfOriginals = new ArrayList<Integer>();
        ids = StringUtils.commaDelimitedListToStringArray(originals);
        for (String s : ids) {
            listOfOriginals.add(Integer.valueOf(s));
        }

        logger.debug("Changes : " + " --> " + listOfChanges);
        logger.debug("Originals : " + " --> " + listOfOriginals);

        List<Integer> commonAgents = new ArrayList<Integer>();
        commonAgents.addAll(listOfChanges);

        commonAgents.retainAll(listOfOriginals);
        logger.debug("Common between the two list: " + commonAgents.toString());

        listOfChanges.removeAll(commonAgents);
        listOfOriginals.removeAll(commonAgents);

        logger.debug("Agents Removed : " + " --> " + listOfChanges);
        logger.debug("Agents Added : " + " --> " + listOfOriginals);



    }


    @Test
    public void testMongoGetEmployee() {
        CallCenterEmployee cce = mongoRepository.findByEmployeeNumber(102104);
        logger.debug(cce.toString());

        CallCenterEmployee existing = jdbcRepository.getCallCenterEmployeeByNumber(102323, false);
        logger.debug(existing.toString());
        logger.debug(existing.getSupervisorName());
        logger.debug(existing.getSupervisorNumber().toString());

        if(existing != null){
            Integer supervisorNumber = existing.getSupervisorNumber();
            if(supervisorNumber != null && !supervisorNumber.equals(102104)){
                logger.debug("Running inactivation sql");
            }else{
                logger.debug("Nothing has changed.");
            }
        }

    }

    @Test
    public void syncMongoEmployees() {
        List<CallCenterEmployee> mg_emps =  mongoOperations.find(new Query(Criteria.where("isActive").is(true)), CallCenterEmployee.class);
        logger.debug("Found " + mg_emps.size() + " active Mongo records.");

        List<CallCenterEmployee> db_emps = jdbcRepository.getCallCenterEmployee(false);
        logger.debug("Found " + db_emps.size() + " active Oracle records.");


        List<Long> db_keys = new ArrayList<Long>();
        for (CallCenterEmployee emp : db_emps) {
            db_keys.add(emp.getEmpSupervisorId());
        }
        logger.debug("Generated " + db_keys.size() + " db keys");


        List<Long> mg_expired_keys = new ArrayList<Long>();
        List<Long> mg_active_keys = new ArrayList<Long>();

        for (CallCenterEmployee emp : mg_emps) {
            if (! db_keys.contains(emp.getEmpSupervisorId())) {
                mg_expired_keys.add(emp.getEmpSupervisorId());
            } else {
                mg_active_keys.add(emp.getEmpSupervisorId());
            }
        }
        logger.debug(mg_expired_keys.size() + " Mongo keys are not active!");
        logger.debug(mg_active_keys.size() + " Mongo keys are still active.");


        List<Long> missingInMongo = new ArrayList<Long>();
        for (CallCenterEmployee emp : db_emps) {
            List<CallCenterEmployee> cce = mongoOperations.find(new Query(Criteria.where("id").is(emp.getEmpSupervisorId())), CallCenterEmployee.class);
            if (cce.size() > 0) {
                logger.debug(emp.getEmpSupervisorId() + " is in Mongo");
            } else {
                missingInMongo.add(emp.getEmpSupervisorId());
            }
        }

        logger.debug(missingInMongo.toString());

    }

    @Test
    public void getMongoEmployee() {
        List<CallCenterEmployee> cce = mongoOperations.find(new Query(Criteria.where("id").is(1L)), CallCenterEmployee.class);
        logger.debug(cce.toString());
    }

    @Test
    public void getOracleEmployees() {
        List<CallCenterEmployee> db_emps = jdbcRepository.getCallCenterEmployee(false);
        logger.debug("Found " + db_emps.size() + " active Oracle records.");

        mongoOperations.insert(db_emps, "CallCenterEmployee2");
    }

    @Test
    public void rebuildMongoEmployees() {
        List<CallCenterEmployee> callCenterEmployees = jdbcRepository.getCallCenterEmployee(false);
        logger.debug("Loading " + callCenterEmployees.size() + " Call Center Employees");

        List<Object> currentEmployeeNumbers = mongoRepository.getDistinctField("callCenterEmployee", "employeeNumber");
        int cnt = 0;


        for (CallCenterEmployee cce : callCenterEmployees){
            //persistCallCenterEmployee(cce);
            logger.debug(cce.getEmployeeNumber() + " --> " + cce.getEmployeeName());

            cnt++;
            int idx = currentEmployeeNumbers.indexOf(cce.getEmployeeNumber());
            if(idx != -1){
                currentEmployeeNumbers.remove(idx);
                logger.debug("Removed " + cce.getEmployeeName() + " from mongo employee list");
            }
        }

        logger.debug(currentEmployeeNumbers.size() + " mongo employees left in the list");

        for(Object obj : currentEmployeeNumbers){
            logger.debug("Checking left over employee " + obj);
            CallCenterEmployee cce = mongoRepository.findByEmployeeNumber((Integer)obj);
            if (cce != null) {
                //cce.setActive(false);
                //cce.setInactiveDateTime(new Date());
                //persistCallCenterEmployee(cce);

                logger.debug("Inactivating CCE " + cce.getEmployeeNumber() + " --> " + cce.getEmployeeName());
            }
        }

    }

    @Test
    public void testTeamHierarchy() {
        configurationService.getTeamHierarchy();
    }

    @Test
    public void testToFixCallCenterNameInPhoneNumber(){
        List<PhoneNumber> phoneNumbers = mongoOperations.find(new Query(Criteria.where("callCenterName").nin(new String[]{"Irvine", "Plano"})), PhoneNumber.class);
        int cnt = 1;
        for(PhoneNumber pn : phoneNumbers){
            System.out.println(cnt + "/" + pn.getPhoneNumber() + ":::" + pn.getCallCenterName() + ":::" + pn.getCreatedBy() + ":::" + pn.getCreatedOn());
            String callCenter = pn.getCallCenterName();
            pn.setCallCenterName(callCenter != null && callCenter.equalsIgnoreCase("IRV") ? "Irvine" : "Plano");
            mongoRepository.persistPhoneNumber(pn);
            cnt++;
        }

    }


}
