package com.smilebrands.callcentersupport.domain;

import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

/**
 * User: <PERSON><PERSON>
 * Date: 6/26/14
 */
@Document
public class LoadedFacility implements Serializable {

    private Integer facilityId;
    @Transient
    private Date loadDateTime;
    private String loadDate;
    private String loadTime;

    public LoadedFacility() {}

    public LoadedFacility(Integer facilityId, Date loadDateTime) {
        this.facilityId = facilityId;
        this.loadDateTime = loadDateTime;
        this.loadDate = VelocityUtils.getDateAsString(loadDateTime, null);
        this.loadTime = VelocityUtils.getDateAsString(loadDateTime, "HHmmss");
    }

    public Integer getFacilityId() {
        return facilityId;
    }

    public void setFacilityId(Integer facilityId) {
        this.facilityId = facilityId;
    }

    public Date getLoadDateTime() {
        return loadDateTime;
    }

    public void setLoadDateTime(Date loadDateTime) {
        this.loadDateTime = loadDateTime;
    }

    public String getLoadDate() {
        return loadDate;
    }

    public void setLoadDate(String loadDate) {
        this.loadDate = loadDate;
    }

    public String getLoadTime() {
        return loadTime;
    }

    public void setLoadTime(String loadTime) {
        this.loadTime = loadTime;
    }
}
