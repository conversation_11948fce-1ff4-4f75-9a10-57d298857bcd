package com.smilebrands.callcentersupport.domain;

import com.smilebrands.callcentersupport.domain.constants.AgentType;
import com.smilebrands.callcentersupport.domain.constants.Language;
import com.smilebrands.callcentersupport.domain.constants.SkillType;

import java.util.Date;

/**
 * Created by phongpham on 1/21/16.
 */
public class CallCenterAgent {

    private AgentType agentType;
    private SkillType skillType;
    private Language language;
    private int agentId;

    private int createdBy;
    private Date createdOn = new Date();
    private int updatedBy;
    private Date updatedOn;

    public CallCenterAgent(){}

    public CallCenterAgent(AgentType at, SkillType st, Language l, int ai){
        this.agentType = at;
        this.skillType = st;
        this.language = l;
        this.agentId = ai;
    }

    public AgentType getAgentType() {
        return agentType;
    }

    public void setAgentType(AgentType agentType) {
        this.agentType = agentType;
    }

    public SkillType getSkillType() {
        return skillType;
    }

    public void setSkillType(SkillType skillType) {
        this.skillType = skillType;
    }

    public Language getLanguage() {
        return language;
    }

    public void setLanguage(Language language) {
        this.language = language;
    }

    public int getAgentId() {
        return agentId;
    }

    public void setAgentId(int agentId) {
        this.agentId = agentId;
    }

    public int getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(int createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreatedOn() {
        return createdOn;
    }

    public void setCreatedOn(Date createdOn) {
        this.createdOn = createdOn;
    }

    public int getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(int updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getUpdatedOn() {
        return updatedOn;
    }

    public void setUpdatedOn(Date updatedOn) {
        this.updatedOn = updatedOn;
    }
}
