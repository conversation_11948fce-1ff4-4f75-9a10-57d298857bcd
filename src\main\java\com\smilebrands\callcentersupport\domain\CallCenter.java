package com.smilebrands.callcentersupport.domain;

import com.smilebrands.callcentersupport.domain.constants.AgentType;
import com.smilebrands.callcentersupport.domain.constants.HourType;
import com.smilebrands.callcentersupport.domain.constants.Language;
import com.smilebrands.callcentersupport.domain.constants.SkillType;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by phongpham on 8/14/14.
 */
@Document
public class CallCenter {
    @Id
    private String callCenterName;
    private Integer facilityId;
    private String timezone;
    private String timezoneShort;
    private Integer timezoneOffset;
    private Long callbackPhoneNumber;
    private List<String> callCenterHours = new ArrayList<String>();
    private String libertyUrl = "http://liberty.bnd.corp/scheduler";
    private String openBookUrl = "https://openbook.smilebrands.com/scheduling/ui";

    private List<BusinessHour> callCenterBusinessHours = new ArrayList<BusinessHour>();
    private List<CallCenterAgent> agents = new ArrayList<CallCenterAgent>();

    public String getCallCenterName() {
        return callCenterName;
    }

    public void setCallCenterName(String callCenterName) {
        this.callCenterName = callCenterName;
    }

    public Integer getFacilityId() {
        return facilityId;
    }

    public void setFacilityId(Integer facilityId) {
        this.facilityId = facilityId;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }


    public String getTimezoneShort() {
        return timezoneShort;
    }

    public void setTimezoneShort(String timezoneShort) {
        this.timezoneShort = timezoneShort;
    }

    public Integer getTimezoneOffset() {
        return timezoneOffset;
    }

    public void setTimezoneOffset(Integer timezoneOffset) {
        this.timezoneOffset = timezoneOffset;
    }

    public Long getCallbackPhoneNumber() {
        return callbackPhoneNumber;
    }

    public void setCallbackPhoneNumber(Long callbackPhoneNumber) {
        this.callbackPhoneNumber = callbackPhoneNumber;
    }

    public String getLibertyUrl() {
        return libertyUrl;
    }

    public void setLibertyUrl(String libertyUrl) {
        this.libertyUrl = libertyUrl;
    }

    public String getOpenBookUrl() {
        return openBookUrl;
    }

    public void setOpenBookUrl(String openBookUrl) {
        this.openBookUrl = openBookUrl;
    }

    public List<String> getCallCenterHours() {
        return callCenterHours;
    }

    public void setCallCenterHours(List<String> callCenterHours) {
        this.callCenterHours = callCenterHours;
    }

    public List<BusinessHour> getCallCenterBusinessHours() {
        return callCenterBusinessHours;
    }

    public void setCallCenterBusinessHours(List<BusinessHour> callCenterBusinessHours) {
        this.callCenterBusinessHours = callCenterBusinessHours;
    }

    public List<CallCenterAgent> getAgents() {
        return agents;
    }

    public void setAgents(List<CallCenterAgent> agents) {
        this.agents = agents;
    }

    public String getDisplayHours(){
        StringBuilder sb = new StringBuilder();
        if(this.getCallCenterHours() != null){
            int size = this.getCallCenterHours().size();
            for(int i= 0; i<size; i++){
                if(i == size - 1){
                    sb.append("and ").append(this.getCallCenterHours().get(i));
                }else{
                    sb.append(this.getCallCenterHours().get(i)).append(", ");
                }
            }
        }
        return sb.toString();
    }

    public BusinessHour getBusinessHour(HourType ht, int dow){
        BusinessHour result = null;
        for(BusinessHour bh : this.callCenterBusinessHours){
            if(bh.getHourType() == ht && bh.getDayOfWeek() == dow){
                result = bh;
                break;
            }
        }
        return result;
    }

    public int getAgentId(AgentType at, SkillType st, Language l){
        int result = -1;
        for(CallCenterAgent cca : agents){
            if((at == AgentType.NA || cca.getAgentType() == at)
                && (st == cca.getSkillType())
                && (l == Language.NA || cca.getLanguage() == l)
                ){
                result = cca.getAgentId();
                break;
            }
        }
        return result;
    }
}
