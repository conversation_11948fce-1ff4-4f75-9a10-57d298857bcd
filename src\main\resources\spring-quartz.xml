<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
          http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">


    <bean id="scheduleServiceObj" class="com.smilebrands.callcentersupport.service.ScheduleServiceImpl"/>

    <!-- Acquire Task Job Detail. -->
    <bean id="quartzAcquireTaskJob" class="org.springframework.scheduling.quartz.JobDetailBean">
        <property name="jobClass" value="com.smilebrands.callcentersupport.scheduler.AcquireTaskJob"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="scheduleService" value-ref="scheduleServiceObj"/>
            </map>
        </property>
    </bean>

    <!-- SET TRIGGER TIME ZONE TO US/PACIFIC-->
    <bean id="timeZone" class="java.util.TimeZone" factory-method="getTimeZone">
        <constructor-arg value="US/Pacific"/>
    </bean>
    <!-- Acquire Task Cron Trigger -->
    <bean id="acquireTaskCronTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
        <property name="jobDetail" ref="quartzAcquireTaskJob"/>
        <property name="cronExpression" value="0 0/1 5-20 ? * MON-SAT *"/>    <!-- Runs every minute from 5AM to 8PM Pacific Time from Monday to Saturday -->
        <property name="timeZone" ref="timeZone"/>
    </bean>

    <bean id="schedulerFactory" class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
        <property name="jobDetails">
            <list>
                <ref bean="quartzAcquireTaskJob"/>
            </list>
        </property>

        <property name="triggers">
            <list>
                <ref bean="acquireTaskCronTrigger"/>
            </list>
        </property>
    </bean>
</beans>