CREATE OR REPLACE PROCEDURE "CISCOADM"."SL_PROCESS_VERIFIED_APPTS"(
P_CALL_LOG_ID           IN      NUMBER,
P_FACILITY_ID           IN      NUMBER,
P_<PERSON><PERSON><PERSON><PERSON>_ID             IN      NUMBER,
P_UNIQUE_ID             IN      NUMBER,
P_CALL_CENTER_TIME      IN      TIMESTAMP,
P_CLOSE_DATE            IN      DATE,
P_APPT_DATE             IN      DATE,
P_IS_ORTHO              IN      NUMBER DEFAULT 0,
IO_CALL_LOG_STATUS      IN OUT  VARCHAR2,
IO_APPT_LOG_STATUS      IN OUT  VARCHAR2,
IO_CONTINUE_PROCESSING  IN OUT  NUMBER)

/*---------------------------------------------------------------------------------*/
/* Procedure: SP_PROCESS_VERIFIED_APPOINTMENTS                                     */
/*                                                                                 */
/* Author: <PERSON>                                                          */
/* Date Written: 08/08/2014                                                        */
/*                                                                                 */
/* Purpose: This Routine Verifys Unverified Appointments (Non Liberty)             */
/*                                                                                 */
/*                                                                                 */
/* Input Variables:                                                                */
/*                                                                                 */
/*                                                                                 */
/*                                                                                 */
/*                                                                                 */
/* Returns:                                                                        */
/*                                                                                 */
/*                                                                                 */
/* Revisions                                                                       */
/*                                                                                 */
/*                                                                                 */
/*                                                                                 */
/*---------------------------------------------------------------------------------*/

AS

V_UNIQUE_ID                     PATIENTS.UNIQUE_ID%TYPE := P_UNIQUE_ID;
V_FACILITY_ID                   PATIENTS.FACILITY_ID%TYPE := P_FACILITY_ID;
V_CLINIC_ID                     APPT_CALL_LOG.CLINIC_ID%TYPE := P_CLINIC_ID;
V_CALL_LOG_ID                   AGENT_CALL_LOG.CALL_LOG_ID%TYPE := P_CALL_LOG_ID;
V_APPT_LOG_STATUS               APPT_CALL_LOG.APPT_LOG_STATUS%TYPE := IO_APPT_LOG_STATUS;
V_CALL_LOG_STATUS               AGENT_CALL_LOG.CALL_LOG_STATUS%TYPE := 'ACTIVE';
V_APPT_LOG_ID                   APPT_CALL_LOG.APPT_LOG_ID%TYPE;
V_CALL_CENTER_TIME              AGENT_CALL_LOG.CALL_TIME%TYPE := P_CALL_CENTER_TIME;

V_APPT_VALUE                    APPT_CALL_LOG.APPT_VALUE%TYPE := 0;
V_START_DATE                    DATE := TRUNC(P_CALL_CENTER_TIME);
V_CLOSE_DATE                    DATE:= P_CLOSE_DATE;
V_APPT_DATE                     TIMESTAMP(6) := P_APPT_DATE;

V_PROVIDER_ID                   APPOINTMENTS.PROVIDER_ID%TYPE;
V_UPDATE_LOG                    NUMBER(1,0) := 0;
V_APPT_FOUND                    NUMBER(1,0) := 0;
V_PATIENT_VISIT                 NUMBER := 0;
V_TODAY                         DATE := TRUNC(SYSDATE);

V_AGENT_TYPE                    AGENT_CALL_LOG.AGENT_TYPE%TYPE;
V_ALLOW_CREDIT                  AGENT_CALL_LOG.ALLOW_CREDIT%TYPE;
V_ADMIN_OVERRIDDEN              AGENT_CALL_LOG.ADMIN_OVERRIDDEN%TYPE;


ERR_NUM                         NUMBER;
ERR_MSG                         VARCHAR2(512);
V_STORED_PROCEDURE              VARCHAR2(30) := $$PLSQL_UNIT;


BEGIN

/*---------------------------------------------------------------------------*/
/* Get Allow_Credit and Admin_Overridden                                     */
/*---------------------------------------------------------------------------*/
    BEGIN
        V_ALLOW_CREDIT := 0;
        V_ADMIN_OVERRIDDEN := 0;
        V_AGENT_TYPE := ' ';

        SELECT nvl(LOWER(AGENT_TYPE), ' '), nvl(ALLOW_CREDIT,0), nvl(ADMIN_OVERRIDDEN,0)
            INTO V_AGENT_TYPE, V_ALLOW_CREDIT, V_ADMIN_OVERRIDDEN
        FROM AGENT_CALL_LOG
        WHERE CALL_LOG_ID = V_CALL_LOG_ID;

    EXCEPTION WHEN OTHERS THEN
        V_AGENT_TYPE := ' ';
        V_ALLOW_CREDIT := 0;
        V_ADMIN_OVERRIDDEN := 0;

    END;

/*---------------------------------------------------------------------------*/
/* Get Appt Value                                                            */
/*---------------------------------------------------------------------------*/

    V_APPT_VALUE := 0;
    V_PATIENT_VISIT := 0;
    V_UPDATE_LOG := 0;
    V_CALL_LOG_STATUS := 'ACTIVE';

    --DBMS_OUTPUT.PUT_LINE('Process Call Log[' || V_CALL_LOG_ID || '] with UniqueId[' || V_UNIQUE_ID ||'] and APPT_DATE[' || V_APPT_DATE || ']');

    BEGIN

    SELECT 1 INTO V_PATIENT_VISIT
    FROM SCHEDULE_EVENT se
    WHERE se.FACILITY_ID = V_FACILITY_ID
        AND se.PATIENT_ID = V_UNIQUE_ID
        AND se.START_DATETIME = V_APPT_DATE
        AND EXISTS (SELECT NULL FROM SCHEDULE_EVENT_STATUS ss where ss.SCH_EVENT_ID = se.SCH_EVENT_ID and ss.STATUS_TYPE IN ('ARRIVED', 'COMPLETED', 'DISMISSED'));


    IF NVL(V_PATIENT_VISIT,0) > 0 THEN
    V_APPT_LOG_STATUS := 'SHOW';
    V_CALL_LOG_STATUS := 'CLOSED';
    V_UPDATE_LOG := 1;
    V_APPT_FOUND := 1;

    IF V_ADMIN_OVERRIDDEN = 0 AND V_AGENT_TYPE = 'psr' THEN
        V_ALLOW_CREDIT := 1;
    END IF;

    END IF;

    EXCEPTION WHEN OTHERS THEN
    V_APPT_VALUE := 0;
    V_PATIENT_VISIT := 0;
    V_UPDATE_LOG := 0;
    V_APPT_FOUND := 0;
    END;

/*---------------------------------------------------------------------------*/
/* Appt not found -  Test for Rescheduled                                    */
/*---------------------------------------------------------------------------*/
    IF V_APPT_FOUND = 0 THEN

/*---------------------------------------------------------------------------*/
/* Find Liberty Appointments                                                 */
/*---------------------------------------------------------------------------*/

    BEGIN

    --DBMS_OUTPUT.PUT_LINE('Check if there is future appointment ortho with UNIQUE ID[' || V_UNIQUE_ID || '] and APPT_DATE[' || V_APPT_DATE ||']: ' || (nvl(V_PATIENT_VISIT,0)));

    SELECT SE.START_DATETIME,
           SE.SERVICE_PROVIDER_ID

      INTO V_APPT_DATE,
           V_PROVIDER_ID

    FROM SCHEDULE_EVENT SE

    LEFT OUTER JOIN SCHEDULE_EVENT_STATUS SS
        ON SE.SCH_EVENT_STATUS_ID = SS.SCH_EVENT_STATUS_ID

    WHERE SE.FACILITY_ID = V_FACILITY_ID
          AND SS.STATUS_TYPE NOT IN ('RESERVED', 'UNSCHEDULED', 'CANCELLED', 'RESCHEDULED')
          AND SE.PATIENT_ID = V_UNIQUE_ID
          AND TRUNC(SE.START_DATETIME) >= TRUNC(V_APPT_DATE)
          AND SE.START_DATETIME !=  V_APPT_DATE
          AND NOT EXISTS (
            SELECT NULL FROM SCHEDULE_EVENT SE2
                JOIN SCHEDULE_EVENT_STATUS SS2 ON SE2.SCH_EVENT_STATUS_ID = SS2.SCH_EVENT_STATUS_ID
            WHERE SE2.FACILITY_ID = SE.FACILITY_ID AND SE2.PATIENT_ID = SE.PATIENT_ID
                AND SS2.STATUS_TYPE NOT IN ('RESERVED', 'UNSCHEDULED', 'CANCELLED', 'RESCHEDULED')
                AND TRUNC(SE2.START_DATETIME) >= TRUNC(V_APPT_DATE) AND SE2.START_DATETIME < SE.START_DATETIME
          );


    V_APPT_FOUND := 1;
    V_UPDATE_LOG := 1;
    V_APPT_LOG_STATUS := 'RESCHEDULED';
    V_CALL_LOG_STATUS := 'ACTIVE';

    IF V_ADMIN_OVERRIDDEN = 0 AND V_AGENT_TYPE = 'psr' THEN
        V_ALLOW_CREDIT := 1;
    END IF;

    EXCEPTION WHEN OTHERS THEN

    --V_APPT_DATE := NULL;
    V_PROVIDER_ID := NULL;
    V_APPT_FOUND := 0;
    V_UPDATE_LOG := 0;

    END;
    END IF;

/*---------------------------------------------------------------------------*/
/* Find Appointment Value for Ortho (clinic 9)                                    */
/*---------------------------------------------------------------------------*/

    IF V_APPT_FOUND = 0 THEN

    BEGIN

    SELECT SUM(AMOUNT_TOT),
           SUM(NVL(TC.IS_PATIENT_VISIT,0)),
           MAX(PROVIDER_ID)

      INTO V_APPT_VALUE,
           V_PATIENT_VISIT,
           V_PROVIDER_ID

   FROM PATIENT_TRANSACTIONS PT

   LEFT OUTER JOIN TRANSACTION_CODES TC
        ON PT.CLINIC_ID = TC.CLINIC_ID AND
           PT.TRANS_CODE = TC.TRANS_CODE

   WHERE PT.CLINIC_ID = 9 AND
         PT.UNIQUE_ID = V_UNIQUE_ID AND
         PT.DATE_POSTED >= TRUNC(V_APPT_DATE) AND
         TC.RCA_CATEGORY = 'REV' AND
         TC.IS_PATIENT_VISIT = 1;

    --DBMS_OUTPUT.PUT_LINE('Check for appointment value for ortho with UNIQUE ID[' || V_UNIQUE_ID || '] and APPT_DATE[' || V_APPT_DATE ||']: ' || (nvl(V_PATIENT_VISIT,0)));

    IF NVL(V_PATIENT_VISIT,0) > 0 THEN

    V_APPT_LOG_STATUS := 'SHOW';
    V_CALL_LOG_STATUS := 'CLOSED';
    V_UPDATE_LOG := 1;
    V_APPT_FOUND := 1;

    IF V_ADMIN_OVERRIDDEN = 0 AND V_AGENT_TYPE = 'psr' THEN
        V_ALLOW_CREDIT := 1;
    END IF;

    END IF;

    EXCEPTION WHEN OTHERS THEN
    V_APPT_VALUE := 0;
    V_PATIENT_VISIT := 0;
    V_UPDATE_LOG := 0;
    V_APPT_FOUND := 0;
    END;

    END IF;

/*---------------------------------------------------------------------------*/
/* Appt not found -  Test for Rescheduled (Ortho clinic ID 9)                */
/*---------------------------------------------------------------------------*/
    IF V_APPT_FOUND = 0 THEN

    BEGIN

    SELECT APPT_DATE,
           PROVIDER_ID

      INTO V_APPT_DATE,
           V_PROVIDER_ID

    FROM APPOINTMENTS

    WHERE CLINIC_ID = 9 AND
          UNIQUE_ID = V_UNIQUE_ID AND
          TRUNC(APPT_DATE) > TRUNC(V_APPT_DATE) AND
          ROWNUM = 1;

    V_APPT_FOUND := 1;
    V_UPDATE_LOG := 1;
    V_APPT_LOG_STATUS := 'RESCHEDULED';
    V_CALL_LOG_STATUS := 'ACTIVE';

    IF V_ADMIN_OVERRIDDEN = 0  AND V_AGENT_TYPE = 'psr' THEN
        V_ALLOW_CREDIT := 1;
    END IF;

    EXCEPTION WHEN OTHERS THEN

    V_PROVIDER_ID := NULL;
    V_APPT_FOUND := 0;
    V_UPDATE_LOG := 0;

    END;
    END IF;

/*---------------------------------------------------------------------------*/
/* Test for Close Date -  No Appt Found                                      */
/*---------------------------------------------------------------------------*/
    IF V_APPT_FOUND = 0 AND
       V_CLOSE_DATE < V_TODAY THEN

    BEGIN

    V_APPT_LOG_STATUS := 'CLOSED';
    V_CALL_LOG_STATUS := 'CLOSED';
    V_UPDATE_LOG := 1;

    IF V_ADMIN_OVERRIDDEN = 0 THEN
        V_ALLOW_CREDIT := 0;
    END IF;

    END;

    END IF;

/*---------------------------------------------------------------------------*/
/* Add new status to Appt Call Log                                           */
/*---------------------------------------------------------------------------*/

    IF V_UPDATE_LOG = 1 THEN
    --DBMS_OUTPUT.PUT_LINE('Call Log ID: ' || P_CALL_LOG_ID || '::: UNIQUE_ID:' || V_UNIQUE_ID || '::: New Appt Log Status: ' || V_APPT_LOG_STATUS || '::: V_APPT_FOUND:' || V_APPT_FOUND);

/*---------------------------------------------------------------------------*/
/* Add new status to Appt Call Log                                           */
/*---------------------------------------------------------------------------*/

    BEGIN

    INSERT INTO APPT_CALL_LOG(
                APPT_LOG_ID,
                CALL_LOG_ID,
                CLINIC_ID,
                UNIQUE_ID,
                PROVIDER_ID,
                APPT_LOG_STATUS,
                APPT_DATE,
                APPT_VALUE,
                LAST_UPD_PROGRAM,
                CREATE_EMPLOYEE,
                CREATE_DATETIME,
                UPDATE_EMPLOYEE,
                UPDATE_TIMESTAMP,
                IS_ORTHO)

	     VALUES(APPT_CALL_LOG_ID_SEQ.NEXTVAL,
                P_CALL_LOG_ID,
                P_CLINIC_ID,
                P_UNIQUE_ID,
                V_PROVIDER_ID,
                V_APPT_LOG_STATUS,
                V_APPT_DATE,
                V_APPT_VALUE,
                V_STORED_PROCEDURE,
                110026,
                SYSDATE,
                NULL,
                NULL,
                P_IS_ORTHO)
        RETURNING APPT_LOG_ID
        INTO V_APPT_LOG_ID;

    END;


/*---------------------------------------------------------------------------*/
/* Close Agent Call Log on Terminal Event                                    */
/*---------------------------------------------------------------------------*/

    BEGIN

    UPDATE AGENT_CALL_LOG
       SET CALL_LOG_STATUS = V_CALL_LOG_STATUS,
           LAST_APPT_LOG_ID = V_APPT_LOG_ID,
           UPDATE_EMPLOYEE = 110026,
           UPDATE_TIMESTAMP = SYSDATE,
           ALLOW_CREDIT = V_ALLOW_CREDIT
    WHERE CALL_LOG_ID = V_CALL_LOG_ID;

    END;
/*---------------------------------------------------------------------------*/
/* End of Update                                                             */
/*---------------------------------------------------------------------------*/
    END IF;

/*---------------------------------------------------------------------------*/
/* INsert Into Process Log                                                   */
/*---------------------------------------------------------------------------*/

    INSERT INTO CALL_PROCESS_LOG(
                CALLED_PROC,
                CALL_LOG_ID,
                FACILITY_ID,
                CLINIC_ID,
                UNIQUE_ID,
                CALL_TIME,
                CLOSE_DATE,
                CREATE_DATE,
                CALL_LOG_STATUS,
                APPT_LOG_STATUS,
                CONTINUE_PROCESSING,
                UPDATE_LOG,
                APPT_FOUND,
                TODAYS_DATE)

	     VALUES(V_STORED_PROCEDURE,
                P_CALL_LOG_ID,
                P_FACILITY_ID,
                P_CLINIC_ID,
                P_UNIQUE_ID,
                P_CALL_CENTER_TIME,
                P_CLOSE_DATE,
                NULL,
                IO_CALL_LOG_STATUS,
                IO_APPT_LOG_STATUS,
                IO_CONTINUE_PROCESSING,
                V_UPDATE_LOG,
                V_APPT_FOUND,
                V_TODAY);

/*---------------------------------------------------------------------------*/
/* Error Occured - Add error to log                                          */
/*---------------------------------------------------------------------------*/

    EXCEPTION WHEN OTHERS THEN

    ERR_NUM := SQLCODE;
    ERR_MSG := SUBSTR(SQLERRM, 1, 512);

    INSERT INTO APPLICATION_ERROR_LOG(
                ERROR_ID,
                STORED_PROCEDURE,
                ERROR_NUMBER,
                ERROR_MSG,
                ERROR_DATE)
      VALUES(
               APPLICATION_ERROR_LOG_SEQ.NEXTVAL,
               V_STORED_PROCEDURE,
               ERR_NUM,
               ERR_MSG,
               SYSDATE);
END;