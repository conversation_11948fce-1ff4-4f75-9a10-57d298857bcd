package com.smilebrands.callcentersupport.repo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * User: <PERSON><PERSON>
 * Date: 6/26/14
 */
@Repository
public class FacilityEmailRepositoryImpl extends JdbcRepository implements FacilityEmailRepository {

    @Autowired
    @Qualifier("emailAccountSql")
    protected String emailAccountSql;

    @Autowired
    @Qualifier("ccEmailAccountSql")
    protected String ccEmailAccountSql;

    @Override
    public List<String> getCallCenterEmail(Long facility_id) {
        logger.debug("Searching for a valid Email for Facility: " + facility_id);
        List<String> accts = new ArrayList();

        if (facility_id != null) {
            accts = jdbcTemplate.queryForList(ccEmailAccountSql, new Object[]{ facility_id }, String.class);
            logger.debug(accts.size() + " account(s) have been located.");

            if (accts.size() == 0)
                logger.warn("Not able to find an Email Address for this facility!");

        }

        return accts;
    }

    @Override
    public List<String> getApptEmail(Long facility_id) {
        logger.debug("Searching for a valid Email for Facility: " + facility_id);
        List<String> accts = new ArrayList();

        if (facility_id != null) {
            accts = jdbcTemplate.queryForList(emailAccountSql, new Object[]{ facility_id }, String.class);
            logger.debug(accts.size() + " account(s) have been located.");

            if (accts.size() == 0)
                logger.warn("Not able to find an Email Address for this facility!");

        }

        return accts;
    }
}
