<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!-- Call Center Datasource -->
    <!-- PROD -->
    <!--&lt;!&ndash;-->
    <bean id="dataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <property name="dataSource">
            <bean class="oracle.jdbc.pool.OracleDataSource">
                <property name="URL" value="***********************************************************************************************************************************************************************************************)))"/>
                <property name="user" value="bndapp"/>
                <property name="password" value="bnd4pp"/>
            </bean>
        </property>
        <property name="connectionTestQuery" value="SELECT 1 FROM DUAL" />
        <property name="maximumPoolSize" value="3" />
        <property name="idleTimeout" value="300000" />
        <property name="maxLifetime" value="1200000" />
    </bean>
    <!--&ndash;&gt;-->

    <!-- DEV -->
    <!--
    <bean id="dataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <property name="dataSource">
            <bean class="oracle.jdbc.pool.OracleDataSource">
                <property name="URL" value="**********************************************"/>
                <property name="user" value="bndapp"/>
                <property name="password" value="d3v4bnd"/>
            </bean>
        </property>
        <property name="connectionTestQuery" value="SELECT 1 FROM DUAL" />
        <property name="maximumPoolSize" value="3" />
        <property name="idleTimeout" value="300000" />
        <property name="maxLifetime" value="1200000" />
    </bean>
    -->

    <bean id="postgresDataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <property name="dataSource">
            <bean class="org.postgresql.ds.PGSimpleDataSource">
                <property name="databaseName" value="warehouse"/>
                <property name="serverName" value="maggie.bnd.corp"/>
                <property name="portNumber" value="5432"/>
                <property name="user" value="cdw_admin"/>
                <property name="password" value="1d4r3yu0"/>
            </bean>
        </property>
        <property name="connectionTestQuery" value="SELECT 1" />
        <property name="maximumPoolSize" value="3" />
        <property name="idleTimeout" value="300000" />
        <property name="maxLifetime" value="1200000" />
    </bean>
    <!--&ndash;&gt;-->

    <!-- **********  START  ::  CALL CENTER SUPPORT CONVERSION STATEMENTS  ********** -->
    <bean id="CONVR_CALL_CENTER_LIST" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT c.look_up_dnis, c.facility_id, fv.fac_attr_value, c.transfer_number,
                c.call_center_supported, c.call_center, c.call_center_queue

                FROM call_center_support c
                LEFT OUTER JOIN facility f ON c.facility_id = f.facility_id
                LEFT OUTER JOIN facility_attr_values fv ON fv.facility_id = c.facility_id AND fv.fac_attribute_id = 7

                WHERE CALL_CENTER_QUEUE not like 'Marketing%'
                AND CALL_CENTER_QUEUE not like '%Press_4'

                ORDER BY c.facility_id
            </value>
        </constructor-arg>
    </bean>
    <bean id="CONVR_CAMPAIGN_LIMITED" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                select c.CAMPAIGN_LOOK_UP_DNIS, c.CAMPAIGN_NAME, c.CALL_CENTER, c.LIMITED_SUPPORT,
                    cf.FACILITY_ID, f.FACILITY_NAME,
                    (ad.line_one || ' ' || ad.line_two) as ADDRESS,
                    ad.city, ad.state, ad.zipcode

                from CALL_CENTER_CAMPAIGN c

                    left outer join CALL_CENTER_CAMPAIGN_FACILITY cf on c.CAMPAIGN_ID = cf.CAMPAIGN_ID
                    left outer join FACILITY f on cf.FACILITY_ID = f.FACILITY_ID
                    left outer join CALL_CENTER_SUPPORT cs on cf.FACILITY_ID = cs.FACILITY_ID
                    left JOIN FACILITY_ADDRESS fa ON cs.FACILITY_ID = fa.FACILITY_ID AND fa.ADDRESS_TYPE = 'REAL' AND fa.UNLINK_DATETIME IS NULL
                    left JOIN ADDRESS ad ON fa.ADDRESS_ID = ad.ADDRESS_ID

                where LIMITED_SUPPORT = 'Y'
                order by c.CAMPAIGN_ID
            </value>
        </constructor-arg>
    </bean>
    <bean id="CONVR_CAMPAIGN" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                select c.CAMPAIGN_LOOK_UP_DNIS, c.CAMPAIGN_NAME, c.CALL_CENTER, c.LIMITED_SUPPORT

                from CALL_CENTER_CAMPAIGN c
                where LIMITED_SUPPORT = 'N'
                order by c.CAMPAIGN_ID
            </value>
        </constructor-arg>
    </bean>


    <!-- **********  END  ::  CALL CENTER SUPPORT CONVERSION STATEMENTS  ********** -->

    <bean id="redirectUrl" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>http://moe.bnd.corp:9030/callcenter/response-from-cisco/</value>
        </constructor-arg>
    </bean>
    <bean id="ciscoUrl" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>http://10.1.251.120:9080/irvineWebCall</value>
        </constructor-arg>
    </bean>
    <bean id="callBackCiscoUrl" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>http://10.1.251.120:9080/webCallBack</value>
        </constructor-arg>
    </bean>
    <bean id="mongoDBReplicaSet" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <!--<value>ned.bnd.corp:27017,scratchy.bnd.corp:27017,rod.bnd.corp:27017</value>-->
            <value>marge.bnd.corp:27017;bart.bnd.corp:27017;clark.bnd.corp:27017</value>
            <!--<value>itchy.bnd.corp:27017</value>-->
            <!--<value>selma.bnd.corp:27017;edna.bnd.corp:27017</value>-->
            <!--<value>beanie.bnd.corp:27017;blue.bnd.corp:27017;gonzo.bnd.corp:27017</value>-->
        </constructor-arg>
    </bean>
    <bean id="mongoDBPort" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>27017</value>
        </constructor-arg>
    </bean>
    <bean id="mongoDBName" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>callCenter</value>
        </constructor-arg>
    </bean>
    <bean id="mongoDBUser" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <!--<value>callCenterAdmin</value>-->
            <value>EMPTY</value>
            <!--<value>call_center_mstr</value>-->
        </constructor-arg>
    </bean>
    <bean id="mongoDBPass" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <!--<value>c@!!Adm1n</value>-->
            <value>EMPTY</value>
            <!--<value>c4llC3nt3r</value>-->
        </constructor-arg>
    </bean>


</beans>