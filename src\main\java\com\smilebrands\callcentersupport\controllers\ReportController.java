package com.smilebrands.callcentersupport.controllers;

import com.smilebrands.callcentersupport.domain.*;
import com.smilebrands.callcentersupport.domain.report.InboundCallSummary;
import com.smilebrands.callcentersupport.service.CallLogService;
import com.smilebrands.callcentersupport.service.CallService;
import com.smilebrands.callcentersupport.service.ConfigurationService;
import com.smilebrands.callcentersupport.util.BaseJsonConverter;
import com.smilebrands.callcentersupport.util.ExcelExporter;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

/**
 * Created by phongpham on 8/12/14.
 */
@Controller
public class ReportController extends AbstractController{

    @Autowired
    private CallService callService;
    @Autowired
    private CallLogService callLogService;
    @Autowired
    private ConfigurationService configurationService;

    public static final int REPORT_TYPE_CALL_VOLUME = 1;
    public static final int REPORT_TYPE_CALL_LOG = 2;
    public static final int REPORT_TYPE_AGENT_CALL = 3;
    public static final int REPORT_TYPE_AGENT_CALL_LOG = 4;
    public static final int REPORT_TYPE_SCREEN_POP = 5;

    @RequestMapping(value = "secure/report/{reportType}", method = RequestMethod.GET)
    public String loadReport(@PathVariable Integer reportType,
                             @RequestParam(required = false) String requestAgentIdStr,
                             @RequestParam(required = false) String date,
                             @RequestParam(required = false) String endDate,
                             ModelMap model,
                             HttpServletRequest request,
                             HttpServletResponse response) {

        Integer agentId = getEmployeeNumberFromCookie(request);
        List<CallCenterEmployee> list = configurationService.getSupervisedAgents(agentId);
        boolean isSupervisor = list.size() > 1;

        logger.debug("load report type[" + reportType + "] with agentId[" + agentId + "] and date[" + date + "] and isSupervisor[" + isSupervisor + "]");
        if(date == null || date.trim().length() == 0){
            date = VelocityUtils.getDateAsString(new Date(), null);
        }
        Date processDate = VelocityUtils.parseDate(date, null);
        model.addAttribute("agentId", agentId);
        model.addAttribute("reportDate", date);
        model.addAttribute("reportType", reportType);
        model.addAttribute("endDate", endDate);

        Cookie downloadCookie = new Cookie("APPTRACKER.CALL_CENTER_SUPPORT.DOWNLOAD_COOKIES", "");
        downloadCookie.setPath("/callcenter/");
        downloadCookie.setDomain(".bnd.corp");
        response.addCookie(downloadCookie);

        Cookie canEditCallLogCookie = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_EDIT_CALL_LOG");
        boolean canEditCallLog = canEditCallLogCookie != null && canEditCallLogCookie.getValue().equalsIgnoreCase("true");
        if(list.size() <= 1 && canEditCallLog){
            list = configurationService.getCallCenterResource(false);
        }

        if(reportType.equals(REPORT_TYPE_CALL_VOLUME)){
            Cookie canViewReport = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_ACCESS_CALL_CENTER_REPORT");
            if(canViewReport == null || !canViewReport.getValue().equalsIgnoreCase("true")){
                return "403";
            }

            model.addAttribute("inboundCallSummaryList", callService.getInboundCallSummary(null, null, null, processDate));
            return "callVolumeReport";
        }else if(reportType.equals(REPORT_TYPE_CALL_LOG)){
            Cookie canSyncCallLogCookie = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_SYNC_CALL_LOG");
            Cookie canAuditCallLogCookie = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_AUDIT_CALL_LOG");

            boolean canSyncCallLog = canSyncCallLogCookie != null && canSyncCallLogCookie.getValue().equalsIgnoreCase("true");
            boolean canAuditCallLog = canAuditCallLogCookie != null && canAuditCallLogCookie.getValue().equalsIgnoreCase("true");
//            if(!canSyncCallLog
//                && !canEditCallLog
//                && !isSupervisor){
//                return "403";
//            }
            model.addAttribute("canSyncCallLog", canSyncCallLog);
            model.addAttribute("canEditCallLog", canEditCallLog);
            model.addAttribute("canAuditCallLog", canAuditCallLog);
            model.addAttribute("callLogList", callLogService.getCallLogsByDateAndSupervisor(( !canSyncCallLog && !canEditCallLog && !canAuditCallLog ? agentId : 0 ) , processDate, !canEditCallLog, agentId));
            model.addAttribute("facilityList", callService.getDistinctFieldFromCollection("facility", "_id"));
            model.addAttribute("resolutionCodeList", configurationService.getCallResolutionCodes("ALL"));
            model.addAttribute("reasonList", configurationService.getCallReasonCodes("ALL"));
            model.addAttribute("teamList", list);
            model.addAttribute("hasTeam", list.size() > 1);
            model.addAttribute("callList", callService.getCiscoAgentCall(canEditCallLog, list, processDate));

            return "agentCallLogList";
        }else if(reportType.equals(REPORT_TYPE_AGENT_CALL)){
            model.addAttribute("agentCallList", getAgentCallList(date, request, requestAgentIdStr, list));
            return "agentCallList";
        }else if(reportType.equals(REPORT_TYPE_SCREEN_POP)){
            Cookie canViewReport = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_ACCESS_CALL_CENTER_REPORT");
            if(canViewReport == null || !canViewReport.getValue().equalsIgnoreCase("true")){
                return "403";
            }
            Date start = VelocityUtils.parseDate(date, null);
            Date end = null;
            if(endDate != null){
                end = VelocityUtils.parseDate(endDate, null);
            }
            String startStr = VelocityUtils.getDateAsString(start, "yyyy-MM-dd");
            String endStr = end != null ? VelocityUtils.getDateAsString(end, "yyyy-MM-dd") : null;
            model.addAttribute("screenPops", callService.getScreenPops(startStr, endStr));
            return "longScreenPopReport";
        }else{
            return "";
        }
    }

    @ResponseBody
    @RequestMapping(value = "secure/export/{reportType}", method = RequestMethod.GET, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void exportReport(@PathVariable Integer reportType,
                             @RequestParam(required = false) String requestAgentIdStr,
                             @RequestParam(required = false) String date,
                             @RequestParam(required = false) String downloadId,
                             @RequestParam(required = false) String startDate,
                             @RequestParam(required = false) String endDate,
                             @RequestParam(required = false) String dateType,
                             ModelMap model,
                             HttpServletRequest request,
                             HttpServletResponse response)throws IOException {
        Integer agentId = getEmployeeNumberFromCookie(request);
        List<CallCenterEmployee> list = configurationService.getSupervisedAgents(agentId);
        boolean isSupervisor = list.size() > 1;

        logger.debug("export report type[" + reportType + "] with agentId[" + agentId + "] and date[" + date + "]");
        if(date == null || date.trim().length() == 0){
            date = VelocityUtils.getDateAsString(new Date(), null);
        }
        Date processDate = VelocityUtils.parseDate(date, null);
        model.addAttribute("agentId", agentId);
        model.addAttribute("reportDate", date);
        model.addAttribute("reportType", reportType);
        String filePath = "";
        String fileName = "";
        List<LinkedHashMap<String, String>> mapList = new ArrayList<LinkedHashMap<String, String>>();
        if(reportType.equals(REPORT_TYPE_CALL_VOLUME)){
            Cookie canViewReport = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_ACCESS_CALL_CENTER_REPORT");
            if((canViewReport != null && canViewReport.getValue().equalsIgnoreCase("true"))){
                fileName = "Inbound Call Summary on " + VelocityUtils.getDateAsString(processDate, "dd-MMM-yyyy");
                List<InboundCallSummary> inboundCallSummaries = callService.getInboundCallSummary(null, null, null, processDate);
                for(InboundCallSummary ics : inboundCallSummaries){
                    if(!ics.isCombined()){
                        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
                        map.put("Call Center Name", ics.getSummaryId().getCallCenterName());
                        map.put("Phone Type", ics.getSummaryId().getPhoneType());
                        map.put("Number Press", ics.getSummaryId().getMenuPress().toString());
                        map.put("Process Date", VelocityUtils.getDateAsString(ics.getSummaryId().getProcessDate(), null));
                        map.put("Call Count", ics.getValue().getCallCount().toString());
                        map.put("Call Center Supported", ics.getValue().getSupportedCount().toString());
                        map.put("New Patient Count", ics.getValue().getNewPatCount().toString());
                        map.put("Existing Patient Count", ics.getValue().getExtPatCount().toString());
                        map.put("No PSR Count", ics.getValue().getNoPSRCount().toString());
                        map.put("No CSR Count", ics.getValue().getNoCSRCount().toString());
                        map.put("Transfer to Office Count", ics.getValue().getTrnOffCount().toString());
                        map.put("Office to Office Count", ics.getValue().getOffOffCount().toString());
                        map.put("PSR Pop Count", ics.getValue().getPsrPopCount().toString());
                        map.put("CSR Pop Count", ics.getValue().getCsrPopCount().toString());
                        map.put("ANI Recognition Count", ics.getValue().getAniRecCount().toString());
                        map.put("Query Timeout Count", ics.getValue().getQueryTimeoutCount().toString());
                        map.put("Search not Found", ics.getValue().getSearchNotFoundCount().toString());
                        map.put("CSR PSR Count", ics.getValue().getCsrPsrCount().toString());
                        map.put("Request to call back", ics.getValue().getRequestCallback().toString());
                        map.put("Handled call back request", ics.getValue().getHandledCallback().toString());
                        mapList.add(map);
                    }
                }
            }
        }else if(reportType.equals(REPORT_TYPE_CALL_LOG)){
            Cookie canSyncCallLog = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_SYNC_CALL_LOG");
            Cookie canEditCallLog = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_EDIT_CALL_LOG");
            if((canSyncCallLog != null && canSyncCallLog.getValue().equalsIgnoreCase("true"))
                    || (canEditCallLog != null && canEditCallLog.getValue().equalsIgnoreCase("true"))
                    || isSupervisor){
                List<V_CallLog> callLogList = callLogService.getCallLogsByDateAndSupervisor(isSupervisor ? agentId : null, processDate, false, agentId);
                fileName = "Call Log Report " + (agentId != null && agentId != 0 ? ("for " + agentId) + " " : "") + "on " + VelocityUtils.getDateAsString(processDate, "dd-MMM-yyyy");
                for(V_CallLog vc : callLogList){
                    LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
                    map.put("UUID", vc.getAgentCallLog().getUuid());
                    map.put("Call Center", vc.getAgentCallLog().getCallCenterName());
                    map.put("Employee Number", vc.getAgentCallLog().getEmployeeNumber().toString());
                    map.put("Employee Name", vc.getAgentCallLog().getEmployeeName());
                    map.put("Call Time", VelocityUtils.getDateAsString(vc.getAgentCallLog().getCallDateTime(), "HH:mm:ss"));
                    map.put("Duration", vc.getAgentCallLog().getCallDuration().toString());
                    map.put("Call Log Status", vc.getAgentCallLog().getLogStatus());
                    map.put("Facility ID", vc.getAgentCallLog().getFacilityId().toString());
                    map.put("Patient ID", vc.getAgentCallLog().getPatientId().toString());
                    map.put("Temp Patient ID", vc.getAgentCallLog().getTempPatientId().toString());
                    map.put("Appointment Date Time", vc.getAppointmentCallLog().getAppointmentDateTimeStr());
                    map.put("Appointment Status", vc.getAppointmentCallLog().getAppointmentStatus());
                    mapList.add(map);
                }
            }
        }else if(reportType.equals(REPORT_TYPE_AGENT_CALL)){
            List<AgentCall> agentCallList = getAgentCallList(date, request, requestAgentIdStr, list);
            fileName = "Agent Activities " + (agentId != null && agentId != 0 ? ("for " + agentId) + " " : "") + "on " + VelocityUtils.getDateAsString(processDate, "dd-MMM-yyyy");
            for(AgentCall agentCall : agentCallList){
                for(LoadedEmployee employee : agentCall.getLoadedEmployees()){
                    LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
                    map.put("UUID", agentCall.getUuid());
                    map.put("ANI", agentCall.getAni() != null ? agentCall.getAni().toString() : "");
                    map.put("Call Center", agentCall.getCallCenterName());
                    map.put("Employee Number", employee.getEmployeeNumber().toString());
                    Date loadedDateTime = VelocityUtils.parseDate(employee.getLoadedDate() + " " + employee.getLoadedTime(), "MM-dd-yyyy HHmmss");
                    map.put("Loaded Time", VelocityUtils.getDateAsString(loadedDateTime, "MM-dd-yyyyy HH:mm:ss"));
                    if(employee.getUnloadedDate() != null){
                        Date unloadedDateTime = VelocityUtils.parseDate(employee.getUnloadedDate() + " " + employee.getUnloadedTime(), "MM-dd-yyyy HHmmss");
                        map.put("Unloaded Time", VelocityUtils.getDateAsString(unloadedDateTime, "MM-dd-yyyy HH:mm:ss"));
                    }else{
                        map.put("Unloaded Time", "");
                    }
                    map.put("Screen Type", employee.getScreenType());
                    map.put("Reason", employee.getReasonCode());
                    map.put("Resolution", employee.getResolutionCode());
                    int appointmentCount = 0;
                    for(Patient patient : agentCall.getPatients()){
                        if(patient.getEmpEntered().equals(employee.getEmployeeNumber())){
                            appointmentCount++;
                        }
                    }
                    map.put("Number of booked appointments", appointmentCount + "");
                    mapList.add(map);
                }
            }
        }else if(reportType.equals(REPORT_TYPE_AGENT_CALL_LOG)){
            logger.debug("export report type 4 with start[" + startDate + "] and end[" + endDate + "]");
            Cookie canViewReport = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_ACCESS_CALL_CENTER_REPORT");
            List<Integer> agentIds  = new ArrayList<Integer>();
            if(requestAgentIdStr != null && requestAgentIdStr.trim().length() > 0){
                String[]temp = requestAgentIdStr.split(",");
                for(String str : temp){
                    try{
                        Integer emp = Integer.valueOf(str);
                        agentIds.add(emp);
                    }catch(Exception ex){
                        logAction(null, ActionLog.REPORT_ACTION, "Fail to convert agent ID for [" + str + "] from [" + requestAgentIdStr + "]");
                    }
                }
            }else if(list.size() > 1){
                for(CallCenterEmployee cce : list){
                    agentIds.add(cce.getEmployeeNumber());
                }
            }
            if((canViewReport != null && canViewReport.getValue().equalsIgnoreCase("true"))
                    || agentIds.size() > 0){
                mapList = callLogService.getCallLogDetailReport(startDate, endDate, agentIds, dateType, null);
            }
            fileName = "Call Center Detail - [" + startDate + " to " + endDate + "]";
        }else if(reportType.equals(REPORT_TYPE_SCREEN_POP)){
            fileName = "Screen Pop Report from " + date + " to " + endDate;
            List<ScreenPopTime> screenPops = callService.getScreenPops(date, endDate);
            for(ScreenPopTime spt : screenPops){
                LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
                map.put("UUID", spt.getUuid());
                map.put("ANI", spt.getAni() != null ? spt.getAni().toString() : "");
                map.put("Call Center", spt.getCallCenter());
                map.put("Employee", spt.getEmployeeName().toString());
                map.put("Screen Type", spt.getScreenType());
                map.put("Loaded Time", spt.getPopDateTime());
                map.put("Unloaded Time", spt.getUnloadDateTime() != null ? spt.getUnloadDateTime() : "");
                map.put("Duration", spt.getDuration());
                mapList.add(map);
            }
        }
//        if(mapList.size() > 0){
        filePath = VelocityUtils.convertListMapToCSV(mapList, fileName, true);
//        }
        if(filePath.trim().length() > 0){
            // Use ExcelExporter utility to handle response properly
            ExcelExporter.writeExcelToResponse(response, filePath, fileName + ".csv");
        }
        logger.debug("Map list size: " + mapList.size() + "::: filePath: " + filePath);
        Cookie downloadCookie = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.DOWNLOAD_COOKIES");
        if(downloadCookie == null){
            downloadCookie = new Cookie("APPTRACKER.CALL_CENTER_SUPPORT.DOWNLOAD_COOKIES", downloadId);
            downloadCookie.setPath("/callcenter/");
            response.addCookie(downloadCookie);
            logger.debug("add download cookie with value: {}", downloadId);
        }else{
            if(downloadCookie.getValue().trim().length() == 0){
                downloadCookie.setValue(downloadId);
            }else{
                downloadCookie.setValue(downloadCookie.getValue() + "," + downloadId);
            }
            logger.debug("update download cookie with value: {}", downloadCookie.getValue());
        }
    }

    private List<AgentCall> getAgentCallList(String date, HttpServletRequest request, String requestAgentIdStr, List<CallCenterEmployee> supervisedAgents){
        List<Integer> agentIds = new ArrayList<Integer>();
        if(requestAgentIdStr != null && requestAgentIdStr.trim().length() > 0){
            String[] temp = requestAgentIdStr.split(",");
            for(String str : temp){
                try{
                    Integer empNo = Integer.valueOf(str);
                    agentIds.add(empNo);
                }catch (Exception ex){

                }
            }
        }else if(supervisedAgents.size() > 0){
            for(CallCenterEmployee cce : supervisedAgents){
                agentIds.add(cce.getEmployeeNumber());
            }
        }
        if(agentIds.size() > 0){
            return callService.searchAgentCall(date, agentIds);
        }else{
            Cookie canViewReport = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_ACCESS_CALL_CENTER_REPORT");
            if(canViewReport != null && canViewReport.getValue().equalsIgnoreCase("true")){
                return callService.searchAgentCall(date, agentIds);
            }else{
                return new ArrayList<AgentCall>();
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "secure/check-download-status/{downloadId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseWrapper checkDownloadStatus(HttpServletRequest request, @PathVariable String downloadId){
        Cookie downloadCookie = getCookie(request,"APPTRACKER.CALL_CENTER_SUPPORT.DOWNLOAD_COOKIES");
        logger.debug("checking download cookie[" + (downloadCookie != null ? downloadCookie.getValue() : "") + "] with download ID[" + downloadId + "]");
        if(downloadCookie != null && downloadCookie.getValue().indexOf(downloadId) != -1){
            String[] cookieValue = downloadCookie.getValue().split(",");
            String newValue = "";
            for(int i=0; i<cookieValue.length; i++){
                if(!cookieValue[i].equalsIgnoreCase(downloadId)){
                    if(newValue.trim().length() == 0){
                        newValue = cookieValue[i];
                    }else{
                        newValue += "," + cookieValue[i];
                    }
                }
            }
            downloadCookie.setValue(newValue);
            logger.debug("new download cookie: {}", newValue);
            return new ResponseWrapper(true, "Done", 1, null);
        }else{
            return new ResponseWrapper(false, "Not done yet", 0, null);
        }
    }

    @ResponseBody
    @RequestMapping(value = "secure/stream-file/{fileId}", method = RequestMethod.GET, produces =  MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void streamFile(@PathVariable Long fileId, ModelMap modelMap
            , @RequestParam(required = false) Boolean attachment
            , HttpServletResponse response)throws IOException{
        CallLogFile logFile = callService.findCallLogFileById(fileId);
        if(logFile != null){
            File file = new File(logFile.getFilePath());
            String fileExt = FilenameUtils.getExtension(file.getName());
            boolean mediaFile = false;

            // Set content type based on file extension
            if(fileExt.equalsIgnoreCase("mp3")){
                response.setContentType("audio/mpeg");
                mediaFile = true;
            }else if(fileExt.equalsIgnoreCase("wav")){
                response.setContentType("audio/x-wav");
                mediaFile = true;
            }

            FileInputStream in = null;
            ServletOutputStream out = null;
            try {
                in = new FileInputStream(file);
                out = response.getOutputStream();
                int fileLength = (int)file.length();

                if(mediaFile){
                    response.setContentLength(fileLength);
                    response.setHeader("Accept-Ranges", "bytes 0-" + (fileLength - 1) + "/" + fileLength);
                    response.setHeader("Content-Range", "bytes 0-" + (fileLength - 1) + "/" + fileLength);
                }

                response.setHeader("Content-Disposition", (attachment != null && attachment ? "attachment" : "inline") + "; filename=" + file.getName());
                response.setHeader("Pragma", "no-cache");
                response.setHeader("Cache-Control", "no-cache");

                IOUtils.copy(in, out);
                out.flush();
            } finally {
                IOUtils.closeQuietly(in);
                IOUtils.closeQuietly(out);
            }

            modelMap.addAttribute("success", true);
        }else{
            modelMap.addAttribute("success", false);
        }
//        return modelMap;
    }

    @ResponseBody
    @RequestMapping(value="secure/agent-call-report", method= RequestMethod.GET, produces = "application/json")
    public ResponseWrapper getAgentCallReport(ModelMap model, @RequestParam String date, @RequestParam Integer agentId) {
        List<Integer> agentIds = new ArrayList<Integer>();
        agentIds.add(agentId);
        List<AgentCall> agentCalls = callService.searchAgentCall(date, agentIds);
        return new ResponseWrapper(true, "", agentCalls.size(), agentCalls);
    }

    @ResponseBody
    @RequestMapping(value="secure/agent-appointment-search", method= RequestMethod.GET, produces = "application/json")
    public ResponseWrapper getAppointmentsByAgentAndUUID(ModelMap model, @RequestParam String uuid, @RequestParam Integer agentId) {
        logger.debug("Get appointments by agent[" + agentId + "] with UUID[" + uuid);
        AgentCall agentCall = callService.findAgentCall(uuid);
        List<Appointment> appointments = new ArrayList<Appointment>();
        if(agentCall != null){
            for(Patient p : agentCall.getPatients()){
                if(p.getEmpEntered().equals(agentId)){
                    Appointment appointment = new Appointment();
                    Facility facility = callService.findFacilityByFacilityId(p.getFacilityId());
                    if(facility != null){
                        Patient patient = callService.findPatientByIdAndFacility(p.getPatientId(), p.getFacilityId(), facility.isLibertySupported() ? "LIBERTY" : "QSI");
                        if(patient != null){
                            p.setPatientFirstName(patient.getPatientFirstName());
                            p.setPatientLastName(patient.getPatientLastName());
                            p.setDateOfBirth(patient.getDateOfBirth());
                        }
                    }
                    appointment.setPatient(p);
                    appointment.setAppointmentDateTime(p.getNextAppointmentDateTime());
                    appointment.setFacility(facility);
                    appointments.add(appointment);
                }
            }
        }
        return new ResponseWrapper(true, "", appointments.size(), appointments);
    }

    @ResponseBody
    @RequestMapping(value = "secure/sync-call-log", method = RequestMethod.GET, produces = "application/json")
    public ResponseWrapper syncCallLog(ModelMap model, @RequestParam String date, HttpServletRequest request){
        Cookie canSync = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_SYNC_CALL_LOG");
        if(canSync == null || canSync.getValue().equalsIgnoreCase("false")){
            return new ResponseWrapper(false, "Do not have permission to sync call log.", 0, null);
        }else{
            List<AgentCall> agentCalls = callService.searchAgentCall(date, null);
            int count = 0;
            for(AgentCall agentCall : agentCalls){
                List<CallLog> callLogs = callLogService.populateCallLogForAgentCall(agentCall, null, null, null, false);
                count += callLogs.size();
            }
            String message = "Successfully populate " + count + " Call Log(s) from " + agentCalls.size() + " Agent Call(s).";
            return new ResponseWrapper(true, message, 1, null);
        }
    }

    @ResponseBody
    @RequestMapping(value = "secure/update-call-log", method = RequestMethod.POST, produces = "application/json")
    public ResponseWrapper updateCallLog(ModelMap model, @RequestBody String json, HttpServletRequest request){
        this.setRequestData(json);

        Cookie canEdit = getCookie(request, "APPTRACKER.CALL_CENTER_SUPPORT.CAN_EDIT_CALL_LOG");
        Cookie employeeNumber = getCookie(request, "APPTRACKER.EMPLOYEE_NUMBER");
        Map<String, Object> map = BaseJsonConverter.convertToMapObject(json);
        logAction((String)map.get("uuid"), ActionLog.REPORT_ACTION, "Employee[" + (employeeNumber != null ?employeeNumber.getValue() : 0) + "] requests to update call log with[" + json + ".");
        Integer employeeCallLog = 0;
        Integer updateEmployee = employeeNumber != null && employeeNumber.getValue().trim().length() > 0
                ? Integer.valueOf(employeeNumber.getValue()) : null;
        Set<Integer> supervisedAgentIds = updateEmployee != null && !updateEmployee.equals(0)
                ? callLogService.getSupervisedAgentIds(updateEmployee) : new HashSet<Integer>();
        if(map.get("employeeNumber") != null){
            employeeCallLog = Integer.valueOf(map.get("employeeNumber").toString());
        }
        if(updateEmployee == null){
            return new ResponseWrapper(false, "Can not determine employee number.", 0, null);
        }else if(!updateEmployee.equals(employeeCallLog)
                && !supervisedAgentIds.contains(employeeCallLog)
                && (canEdit == null || !canEdit.getValue().equalsIgnoreCase("true"))){
            return new ResponseWrapper(false, "Do not have permission to update call log.", 0, null);
        }else{
            logger.debug("request to update call log: {}", json);

            String uuid = map.get("uuid") != null ? map.get("uuid").toString().trim() : null;
            logAction(uuid, ActionLog.REPORT_ACTION, "Request to update call log with json: " + json);
            boolean hasMongoRecord = callService.findAgentCall(uuid) != null;
            String msg = "";
            if(hasMongoRecord){
                if(callService.isOldScreenPop(null, uuid, 1)){
                    return new ResponseWrapper(false, "Call log is not allowed to modify at this point.", 0, null);
                }
                msg = callLogService.updateCallLog(map, Integer.valueOf(employeeNumber.getValue()));
            }else{
                Date callTime = VelocityUtils.parseDate((String)map.get("callDateTime"), "yyyy-MM-dd'T'HH:mm:ss");
                if(callService.isOldScreenPop(callTime, null, 1)){
                    return new ResponseWrapper(false, "Call date is past the editable period.", 0, null);
                }
                msg = callLogService.addCallLog(map, Integer.valueOf(employeeNumber.getValue()));
            }

            return new ResponseWrapper(true, msg, 1, null);
        }
    }

    @ResponseBody
    @RequestMapping(value = "secure/{reportType}/compare-call-volume", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseWrapper getCompareCallVolume(ModelMap modelMap,
                                                @RequestParam(required = false)String callCenterName,
                                                @RequestParam(required = false)String phoneType,
                                                @RequestParam(required = false)String numberPress,
                                                @RequestParam(required = false)String eventName){

        logger.debug("compare-call-volume with callCenterName[" + callCenterName + "] phoneType[" + phoneType + "] numberPress[" + numberPress + "]");
        Map<String, Object> data = callService.getInboundCallSummaryReport(callCenterName, phoneType, numberPress, eventName);
        ResponseWrapper wrapper = new ResponseWrapper(true, null, data.keySet().size(), data);
        return wrapper;
    }
}
