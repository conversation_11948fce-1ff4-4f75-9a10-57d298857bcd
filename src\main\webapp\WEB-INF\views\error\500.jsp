<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isErrorPage="true" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Server Error</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            line-height: 1.6;
        }
        .error-container {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            background-color: #f9f9f9;
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #d9534f;
        }
        .details {
            margin-top: 20px;
            padding: 10px;
            background-color: #f5f5f5;
            border-left: 4px solid #d9534f;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <h1>Internal Server Error</h1>
        <p>We're sorry, but something went wrong on our end. Our team has been notified of this issue.</p>
        
        <div class="details">
            <h3>Technical Details</h3>
            <p>Error Type: ${pageContext.exception.class.name}</p>
            <p>Message: ${pageContext.exception.message}</p>
            <p>Request URI: ${pageContext.errorData.requestURI}</p>
            <p>Status Code: ${pageContext.errorData.statusCode}</p>
        </div>
        
        <p>Please try again later or contact support if the issue persists.</p>
        <p><a href="<c:url value='/' />">Return to Home Page</a></p>
    </div>
</body>
</html>
