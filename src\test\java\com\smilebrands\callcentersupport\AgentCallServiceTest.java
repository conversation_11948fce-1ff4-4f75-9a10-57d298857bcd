package com.smilebrands.callcentersupport;

import com.smilebrands.callcentersupport.domain.*;
import com.smilebrands.callcentersupport.domain.constants.State;
import com.smilebrands.callcentersupport.domain.report.InboundCallSummary;
import com.smilebrands.callcentersupport.repo.CallRepository;
import com.smilebrands.callcentersupport.repo.JdbcRepository;
import com.smilebrands.callcentersupport.service.CallService;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;

import javax.xml.bind.*;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileWriter;
import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * User: Marlin Clark
 * Date: 6/26/14
 */
public class AgentCallServiceTest extends BaseTest {

    @Autowired
    CallService callService;

    @Autowired
    CallRepository callRepository;

    @Autowired
    MongoOperations mongoOperations;

    @Autowired
    protected JdbcTemplate jdbcTemplate;

    @Autowired
    protected JdbcRepository jdbcRepository;


    @Test
    public void facilityTest() {
        logger.debug(callService.findFacilityByFacilityId(10300).toString());
    }

    @Test
    public void campaignTest() {
        logger.debug(callService.findCampaign(9725431684L).toString());
    }

    @Test
    public void callServiceTest() {
        InboundCall inboundCall = callService.registerCall(9495054655L, 7144281202L, "SESSION.ID.100");
        logger.debug(inboundCall.getUuid());

        callService.registerCallEvent(inboundCall.getUuid(), "press 1");
        callService.registerCallEvent(inboundCall.getUuid(), "screen pop");

        callService.startAgentCall(inboundCall.getUuid(), 110840, 51010, "psr", null);

        callService.updateAgentCallWithFacility(inboundCall.getUuid(), 10230);
        callService.updateAgentCallWithFacility(inboundCall.getUuid(), 51010);
        callService.updateAgentCallWithFacility(inboundCall.getUuid(), 10300);

        callService.updateAgentCallWithPatientPhone(inboundCall.getUuid(), 9495054654L);

        callService.updateAgentCallWithResolutionCode(inboundCall.getUuid(), "RESOLUTION", "REASON");

        callService.updateAgentCallWithPatientDetails(inboundCall.getUuid(), 123456789L, "First Name", "Last Name", "Email Address", "yyyy-MM-dd'T'hh:mmaa", "EN", false, "Liberty");
        callService.updateAgentCallWithPatientDetails(inboundCall.getUuid(), 123456790L, "First Name", "Last Name", "Email Address", "yyyy-MM-dd'T'hh:mmaa", "EN", false, "Liberty");

        callService.updateAgentCallWithUnloadEvent(inboundCall.getUuid());
    }

    @Test
    public void funWithMarshall(){
        try{
//            IvrResponseWrapper wrapper = new IvrResponseWrapper();
//            List<Facility> facilities = callService.findFacilitiesByZipCode("92592");
//            wrapper.setFacilities(facilities);
//            wrapper.setFacility(facilities.get(0));
//            JAXBContext jaxbContext = JAXBContext.newInstance(IvrResponseWrapper.class);

//            InboundCall call = callService.findInboundCall("caeec0ef-2079-4383-8e4e-47caae2e89f4");
            InboundCall call = callService.findInboundCall("cba9138a-29d4-4d53-b295-ffe6bb290e21");
            logger.debug("create date time: {}", call.getCreateDateTime());
            JAXBContext jaxbContext = JAXBContext.newInstance(InboundCall.class);

            Marshaller marshaller = jaxbContext.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
            ByteArrayOutputStream os = new ByteArrayOutputStream();
//            marshaller.marshal(wrapper, os);
            marshaller.marshal(call, os);
            System.out.println(new String(os.toByteArray(), "UTF-8"));
        }catch (Exception ex){
            ex.printStackTrace();
        }

    }

    @Test
    public void testFindAppointmentByPhoneNumber(){
//        Appointment appointment = callService.findAppointmentByPatientPhoneNumber(9493886360L,13070, null);

        logger.debug("QSI SEARCH");
        Date dt = new Date();
        List<Appointment> appointments = callService.findAppointmentsByPatientPhoneNumber(9493063351L,null, "10101945");
//        List<Appointment> appointments = callService.findAppointmentsByPatientPhoneNumber(9495054654L,null, "06261972");
        logger.debug("time taken to search appointment (without facility): " +  (new Date().getTime()-dt.getTime()) + " for " + appointments.size() + " items.");

        dt = new Date();
        appointments = callService.findAppointmentsByPatientPhoneNumber(9493063351L, 13070, null);
        logger.debug("time taken to search appointment (by phone number and facility): " +  (new Date().getTime()-dt.getTime()) + " for " + appointments.size() + " items.");

        dt = new Date();
        appointments = callService.findAppointmentsByPatientPhoneNumber(9493063351L, 13070, "10101945");
        logger.debug("time taken to search appointment (by phone, facility, and dob): " +  (new Date().getTime()-dt.getTime()) + " for " + appointments.size() + " items.");
        logger.debug("LIBERTY SEARCH");

        dt = new Date();
        appointments = callService.findAppointmentsByPatientPhoneNumber(9512398858L,null, "04251943");
        logger.debug("time taken to search appointment (without facility): " +  (new Date().getTime()-dt.getTime()) + " for " + appointments.size() + " items.");

        dt = new Date();
        appointments = callService.findAppointmentsByPatientPhoneNumber(9512398858L, 10230, null);
        logger.debug("time taken to search appointment (by phone number and facility): " +  (new Date().getTime()-dt.getTime()) + " for " + appointments.size() + " items.");

        dt = new Date();
        appointments = callService.findAppointmentsByPatientPhoneNumber(9512398858L, 10230, "04251943");
        logger.debug("time taken to search appointment (by phone, facility, and dob): " +  (new Date().getTime()-dt.getTime()) + " for " + appointments.size() + " items.");

        dt = new Date();
        appointments = callService.findAppointmentsByPatientPhoneNumber(3105074369L, 13070, null);
        logger.debug("time taken to search appointment (by phone, facility, and dob): " +  (new Date().getTime()-dt.getTime()) + " for " + appointments.size() + " items.");

    }

    @Test
    public void testLookUpPatient(){
        Patient p = callRepository.findPatientByIdAndFacility(1085160L, 13070, "QSI");
        logger.debug("qsi patient: {}", p);
        p = callRepository.findPatientByIdAndFacility(1304000586L, 10230, "LIBERTY");
        logger.debug("liberty patient: {}", p);
    }

    @Test
    public void testFunWithState(){

        String stateStr = "hello";
        State state = null;
        try{
            state = State.valueOf(stateStr);
            logger.debug("state name of " + stateStr + " is  {}", state.getStateName());
        }catch (Exception ex){

        }

        stateStr = "WI";
        state = State.valueOf(stateStr);
        logger.debug("state name of " + stateStr + " is  {}", state.getStateName());
    }

    @Test
    public void funWithDateFormat(){
        Calendar calendar = Calendar.getInstance();
        logger.debug("Month: {}", calendar.getDisplayName(Calendar.MONTH, Calendar.LONG, Locale.US));
        logger.debug("Day of Month: {}", VelocityUtils.getOrdinalValue(calendar.get(Calendar.DAY_OF_MONTH)));
        logger.debug("Date: {}", calendar.getDisplayName(Calendar.DAY_OF_MONTH, Calendar.SHORT, Locale.US));
        logger.debug("Year: {}", calendar.get(Calendar.YEAR));
        logger.debug("Hour: {}", calendar.get(Calendar.HOUR));
        logger.debug("Minute: {}", calendar.get(Calendar.MINUTE));
        logger.debug("AM_PM: {}", calendar.getDisplayName(Calendar.AM_PM, Calendar.LONG, Locale.US));
    }

    @Test
    public void testGetTTSAppointment(){
        Appointment appointment = new Appointment();
        Calendar appointmentDate = Calendar.getInstance();
        appointment.setAppointmentDateTime(appointmentDate.getTime());
        Patient patient = new Patient();
        patient.setPatientFirstName("Phong");
        patient.setPatientLastName("Pham");
        appointment.setPatient(patient);
        Facility facility = callService.findFacilityByFacilityId(10230);
        appointment.setFacility(facility);
        logger.debug("tts appointment SP: {}", appointment.getTTSAppointment("sp"));
        logger.debug("tts appointment EN: {}", appointment.getTTSAppointment("en"));
        appointmentDate.set(2014, Calendar.AUGUST, 1, 9, 5);
        appointment.setAppointmentDateTime(appointmentDate.getTime());
        logger.debug("tts appointment SP: {}", appointment.getTTSAppointment("sp"));
        logger.debug("tts appointment EN: {}", appointment.getTTSAppointment("en"));
        appointmentDate.set(2014, Calendar.AUGUST, 2, 9, 5);
        appointment.setAppointmentDateTime(appointmentDate.getTime());
        logger.debug("tts appointment SP: {}", appointment.getTTSAppointment("sp"));
        logger.debug("tts appointment EN: {}", appointment.getTTSAppointment("en"));
        appointmentDate.set(2014, Calendar.AUGUST, 3, 9, 5);
        appointment.setAppointmentDateTime(appointmentDate.getTime());
        logger.debug("tts appointment SP: {}", appointment.getTTSAppointment("sp"));
        logger.debug("tts appointment EN: {}", appointment.getTTSAppointment("en"));
        appointmentDate.set(2014, Calendar.AUGUST, 4, 16, 5);
        appointment.setAppointmentDateTime(appointmentDate.getTime());
        logger.debug("tts appointment SP: {}", appointment.getTTSAppointment("sp"));
        logger.debug("tts appointment EN: {}", appointment.getTTSAppointment("en"));
        appointmentDate.set(2014, Calendar.AUGUST, 11, 10, 15);
        appointment.setAppointmentDateTime(appointmentDate.getTime());
        logger.debug("tts appointment SP: {}", appointment.getTTSAppointment("sp"));
        logger.debug("tts appointment EN: {}", appointment.getTTSAppointment("en"));
        appointmentDate.set(2014, Calendar.AUGUST, 21, 14, 25);
        appointment.setAppointmentDateTime(appointmentDate.getTime());
        logger.debug("tts appointment SP: {}", appointment.getTTSAppointment("sp"));
        logger.debug("tts appointment EN: {}", appointment.getTTSAppointment("en"));
        appointmentDate.set(2014, Calendar.AUGUST, 21, 13, 25);
        appointment.setAppointmentDateTime(appointmentDate.getTime());
        logger.debug("tts appointment SP: {}", appointment.getTTSAppointment("sp"));
        logger.debug("tts appointment EN: {}", appointment.getTTSAppointment("en"));
        facility = callService.findFacilityByFacilityId(12150);
        appointment.setFacility(facility);
        logger.debug("tts appointment SP: {}", appointment.getTTSAppointment("sp"));
        logger.debug("tts appointment EN: {}", appointment.getTTSAppointment("en"));
    }

    @Test
    public void testGetFacilityHour(){
        Facility facility = callService.findFacilityByFacilityId(10230);
        logger.debug(facility.getFacilityId() + " in SP: {}", facility.getTTSFacilityHour("SP", true));
        logger.debug(facility.getFacilityId() + " in EN: {}", facility.getTTSFacilityHour("EN", true));

        facility = callService.findFacilityByFacilityId(10300);
        logger.debug(facility.getFacilityId() + " in SP: {}", facility.getTTSFacilityHour("SP", true));
        logger.debug(facility.getFacilityId() + " in EN: {}", facility.getTTSFacilityHour("EN", true));

        facility = callService.findFacilityByFacilityId(51060);
        logger.debug(facility.getFacilityId() + " in SP: {}", facility.getTTSFacilityHour("SP", true));
        logger.debug(facility.getFacilityId() + " in EN: {}", facility.getTTSFacilityHour("EN", true));

        facility = callService.findFacilityByFacilityId(13070);
        logger.debug(facility.getFacilityId() + " in SP: {}", facility.getTTSFacilityHour("SP", true));
        logger.debug(facility.getFacilityId() + " in EN: {}", facility.getTTSFacilityHour("EN", true));
    }

    @Test
    public void testFindAgentCallByAni(){
        Long ani = 9493315916L;
        String uuid = "8ad779c1-fa77-4e07-aa43-4b8035ef9740";
        AgentCall agentCall = callService.findAgentCallByAni(uuid, ani);
        logger.debug(agentCall.getUuid() + ":::" + agentCall.getAni() + ":::"  + agentCall.getScreenPopOpenDateTime());
    }

    @Test
    public void testSearchAgentCall(){
        List<AgentCall> list = callService.searchAgentCall("08-11-2014", null);
        logger.debug("found {} agent call(s)", list.size());
//        for(AgentCall call : list){
//            logger.debug(call.getUuid() + ":::" + call.getCallRegisteredDate() + ":::" + call.getCallRegisteredTime());
//            for(LoadedEmployee emp : call.getLoadedEmployees()){
//                logger.debug("\t" + emp.getEmployeeNumber());
//            }
//        }
        list = callService.searchAgentCall("08-11-2014", new ArrayList<Integer>(){{add(114266);}});
        logger.debug("found {} agent call(s)", list.size());
    }

    @Test
    public void testCompareDate(){
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal2.add(Calendar.DAY_OF_YEAR, -366);
        logger.debug("last year: {}", cal2.getTime());
        logger.debug("is " + cal1.getTime() + " after last year check: {}", cal2.before(cal1));
        cal1.add(Calendar.DAY_OF_YEAR, -365);
        logger.debug("is " + cal1.getTime() + " after last year check: {}", cal2.before(cal1));
        cal1.add(Calendar.DAY_OF_YEAR, -2);
        logger.debug("is " + cal1.getTime() + " after last year check: {}", cal2.before(cal1));
    }

    @Test
    public void testLoadCallLog(){
        Calendar cal = Calendar.getInstance();
        cal.set(2014, Calendar.AUGUST, 15);
        List<V_CallLog> list = callRepository.findAgentCallLogs(null, cal.getTime());
        for(V_CallLog vCallLog : list){
            logger.debug("Agent Call Log: " + vCallLog.getAgentCallLog().getUuid() + " ::: " + vCallLog.getAgentCallLog().getEmployeeNumber() + " ::: " + vCallLog.getAgentCallLog().getLogStatus());
            if(vCallLog.getAppointmentCallLog() != null){
                logger.debug("\tAppointment: " + vCallLog.getAppointmentCallLog().getAppointmentDateTime() + " ::: " + vCallLog.getAppointmentCallLog().getAppointmentStatus());
            }
        }
        logger.debug("list size: {}", list.size());
    }

    @Test
    public void testLoadAgentCall(){
        AgentCall agentCall = callService.findAgentCall("7a78ff5e-8e64-4075-9ec3-ab2530c79c96");
        for(LoadedEmployee employee : agentCall.getLoadedEmployees()){
            logger.debug(employee.getEmployeeNumber() + ":::" + employee.getLoadedDateTime());
        }
    }

    @Test
    public void getTransferCallForDnis(){
        Long dnis = 38325L;
        String createDate = "09-11-2014";
        List<InboundCall> calls = mongoOperations.find(new Query(Criteria.where("dnis").is(dnis).and("createDate").is(createDate)), InboundCall.class);
        List<String> misplacedCall = new ArrayList<String>();
        List<String> uuids = new ArrayList<String>();
        for(InboundCall call : calls){
            uuids.add(call.getUuid());
            AgentCall agentCall = mongoOperations.findOne(new Query(Criteria.where("_id").is(call.getUuid())), AgentCall.class);
            if(agentCall != null){
                for(LoadedEmployee le : agentCall.getLoadedEmployees()){
                    String state = jdbcTemplate.queryForObject("select state from employee where employee_number=?", new Object[]{le.getEmployeeNumber()}, new RowMapper<String>() {
                        @Override
                        public String mapRow(ResultSet rs, int rowNum) throws SQLException {
                            return rs.getString("STATE");
                        }
                    });
                    if(state != null && state.equalsIgnoreCase("CA")){
                        misplacedCall.add("'" + call.getUuid() + "'");
                        break;
                    }
                }
            }
            if(call.getPhoneNumber() != null && call.getPhoneNumber().getFacilityId() != null){
                Facility facility = mongoOperations.findOne(new Query(Criteria.where("_id").is(call.getPhoneNumber().getFacilityId())), Facility.class);
                if(facility != null && facility.getFacilityId().equals(call.getTargetFacilityId())
                        && facility.getState().equalsIgnoreCase("CA")){
                    logger.debug("Call is from CA facility: {}", facility.getFacilityId());
                }
            }
        }
        logger.debug("there are " + misplacedCall.size() + " out of " + calls.size() + " misplaced call.");
        logger.debug("UUIDs: {}", misplacedCall);
    }

    @Test
    public void testLoadSummaryReport(){
        Map<String, Object> map = callService.getInboundCallSummaryReport(null, "ML", null, null);
        Set<String> keys = map.keySet();
        for(String key : keys){
            logger.debug(key + ":::" + map.get(key));
        }
        logger.debug("----------------------------------------------------------");
        map = callService.getInboundCallSummaryReport("Irvine", "ML", null, null);
        keys = map.keySet();
        for(String key : keys){
            logger.debug(key + ":::" + map.get(key));
        }
    }

    @Test
    public void updateMenuPressFromIntegerToString(){
        Calendar cal = Calendar.getInstance();
        cal.set(2014, Calendar.AUGUST, 1);
        long current = new Date().getTime();
        boolean keepGoing = true;
        do{
            List<InboundCallSummary> list = callService.getInboundCallSummary(null, null, null, cal.getTime());
            for(InboundCallSummary ics : list){
                if(ics.getSummaryId().getMenuPress() != null && ics.getSummaryId().getMenuPress().indexOf(":") == -1){
                    ics.getSummaryId().setMenuPress((Double.valueOf(ics.getSummaryId().getMenuPress()).intValue()) + "");
                    mongoOperations.save(ics);
                }
            }
            cal.add(Calendar.DAY_OF_YEAR, 1);
            keepGoing = current >= cal.getTime().getTime();
        }while(keepGoing);
    }

    @Test
    public void testLoadCallVolume(){
        Calendar cal = Calendar.getInstance();
//        cal.set(2014, Calendar.AUGUST, 9);
        List<InboundCallSummary> list = callService.getInboundCallSummary(null, null, null, cal.getTime());
        int planoCnt = 0;
        int irvineCnt = 0;
        int combinedCnt = 0;
        for(InboundCallSummary ics : list){
//            if(ics.getSummaryId().getMenuPress() != null && ics.getSummaryId().getMenuPress().indexOf(":") == -1){
//                ics.getSummaryId().setMenuPress((Double.valueOf(ics.getSummaryId().getMenuPress()).intValue()) + "");
//                mongoOperations.save(ics);
//            }
            String phoneType = ics.getSummaryId().getPhoneType();
            if(phoneType != null && phoneType.equalsIgnoreCase("ML")){
//                logger.debug("{}", ics.getSummaryId().getMenuPress());
                logger.debug(ics.getSummaryId().getCallCenterName() + ":::" + ics.getValue().getAniRecCount());
                if(ics.getSummaryId().getCallCenterName().equalsIgnoreCase("irvine")){
                    irvineCnt += ics.getValue().getAniRecCount();
                }else if(ics.getSummaryId().getCallCenterName().equalsIgnoreCase("plano")){
                    planoCnt += ics.getValue().getAniRecCount();
                }else if(ics.getSummaryId().getCallCenterName().equalsIgnoreCase("combined")){
                    combinedCnt += ics.getValue().getAniRecCount();
                }
            }
        }
        logger.debug("irvine count: {}", irvineCnt);
        logger.debug("plano count: {}", planoCnt);
        logger.debug("combined count: {}", combinedCnt);
//        list = callService.getInboundCallSummary(null, null, "-1", null);
//        logger.debug("list size: {}", list.size());
//        for(InboundCallSummary ics : list){
//            logger.debug(ics.getSummaryId().getCallCenterName() + "-" + ics.getSummaryId().getProcessDate() + "-" + ics.getSummaryId().getPhoneType());
//        }
    }

    @Test
    public void funWithHour(){
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 12);
        logger.debug(cal.get(Calendar.HOUR) + "");

        cal.set(Calendar.HOUR_OF_DAY, 14);
        logger.debug(cal.get(Calendar.HOUR) + "");
    }

    @Test
    public void testGetSupervisedAgent(){
        List<CallCenterEmployee> list = jdbcRepository.getSupervisedAgents(113312);
        logger.debug("list.size: {}", list.size());
        for(CallCenterEmployee cce: list){
            logger.debug(cce.getEmployeeNumber() + ":::" + cce.getEmployeeName());
        }
    }

    @Test
    public void testGetCallCenterEmployee(){
        List<CallCenterEmployee> mapList = jdbcRepository.getCallCenterResource(false);
        for(CallCenterEmployee cce : mapList){
            logger.debug(cce.getEmployeeNumber() + ":::" + cce.getEmployeeName());
        }
    }

    @Test
    public void checkIfApptCallLogExist(){
        Long patientId = 9257540L;
        Integer facilityId = 12020;
        Calendar appointmentDate = Calendar.getInstance();
        logger.debug("{}", jdbcRepository.checkIfApptCallLogExist(patientId, appointmentDate.getTime(), facilityId, null));
        appointmentDate.set(2014, Calendar.SEPTEMBER, 8, 11, 30, 0);
        logger.debug("{}", jdbcRepository.checkIfApptCallLogExist(patientId, appointmentDate.getTime(), facilityId, null));
        appointmentDate.set(2014, Calendar.SEPTEMBER, 22, 15, 0);
        patientId = 9079460L;
        facilityId = 41150;
        logger.debug("{}", jdbcRepository.checkIfApptCallLogExist(patientId, appointmentDate.getTime(), facilityId, null));
        logger.debug("{}", jdbcRepository.checkIfApptCallLogExist(patientId, appointmentDate.getTime(), facilityId, 1L));
        logger.debug("{}", jdbcRepository.checkIfApptCallLogExist(patientId, appointmentDate.getTime(), facilityId, 3020636L));

        facilityId = 10230;
        patientId = 123L;
        appointmentDate.set(2015, Calendar.JANUARY, 28, 8, 0, 0);
        logger.debug("3705069: {}", jdbcRepository.checkIfApptCallLogExist(patientId, appointmentDate.getTime(), facilityId, 3705069L));
        logger.debug("3705071: {}", jdbcRepository.checkIfApptCallLogExist(patientId, appointmentDate.getTime(), facilityId, 3705071L));
    }

    @Test
    public void testGetTeam(){
        List<Map<String, Object>> team = jdbcRepository.getTeamHierarchy();
        logger.debug("team: {}", team);
        logger.debug("team size: {}", team.size());
        Map<Integer, Object> map = new HashMap<Integer, Object>();
        Map<Integer, Object> managers = new HashMap<Integer, Object>();
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        for(Map<String, Object> record : team){
            int employeeNumber = (Integer) record.get("EMPLOYEE_NUMBER");
            int managerNumber = (Integer) record.get("SUPERVISOR_EMPLOYEE_NUMBER");
            Map<Integer, Object> agentKey = getAgentNode(employeeNumber, map);
            Map<String, Object> agentValue = null;
            if(agentKey == null){
                agentKey = new HashMap<Integer, Object>();
                agentValue = new HashMap<String, Object>();
                agentValue.put("employeeNumber", employeeNumber);
                agentValue.put("employeeName", record.get("EMPLOYEE_NAME"));
                agentValue.put("managerNumber", managerNumber);
                if(managers.get(employeeNumber) != null){
                    agentValue.put("team", managers.get(employeeNumber));
                    managers.remove(employeeNumber);
                    for(Map<Integer, Object> a : (List<Map<Integer, Object>>)agentValue.get("team")){
                        for(Integer key : a.keySet()){
                            if(map.containsKey(key)){
                                map.remove(key);
                            }
                        }
                    }
                }else{
                    agentValue.put("team", new ArrayList<Map<Integer, Object>>());
                }
                agentKey.put(employeeNumber, agentValue);
            }
            Map<Integer, Object> manager = getAgentNode(managerNumber, map);
            if(manager == null){
                map.put(employeeNumber, agentValue);
                if(managers.get(managerNumber) == null){
                    managers.put(managerNumber, new ArrayList<Map<Integer, Object>>());
                }
                ((List)managers.get(managerNumber)).add(agentKey);
            }else{
                if(manager.get("team") != null){
                    ((List)manager.get("team")).add(agentKey);
                    if(map.containsKey(employeeNumber)){
                        map.remove(agentKey);
                    }
                }
            }
        }
        for(Object obj : map.values()){
            Map<String, Object> agent = (Map)obj;
            finalizeTeamHierarchy(agent, "", result);
            System.out.println();
        }
        logger.debug("hello world");
    }

    private void finalizeTeamHierarchy(Map<String, Object> agent, String tab, List<Map<String, Object>> list){
        System.out.println(tab + agent.get("employeeNumber") + ":::" + agent.get("employeeName"));
        list.add(agent);
        if(agent.get("team") != null){
            List<Map<Integer, Object>> agentTeam = (List)agent.get("team");
            List<Map<String, Object>> teamList = new ArrayList<Map<String, Object>>();
            for(Map<Integer, Object> at : agentTeam){
                for(Integer key : at.keySet()){
                    finalizeTeamHierarchy((Map<String, Object>) at.get(key), tab + "\t", teamList);
                }
            }
            agent.put("team", teamList);
        }
    }


    @Test
    public void testGetManagerTeam(){
        Map<Integer, Object> root = new HashMap<Integer, Object>();
        Map<String, Object> tl1 = new HashMap<String, Object>();
        tl1.put("employeeNumber", 105138);
        tl1.put("employeeName", "Cheryl Dore");
        tl1.put("team", new ArrayList<Map<Integer, Object>>());
        Map<Integer, Object> key = new HashMap<Integer, Object>();
//        key.put(105138, tl1);
        root.put(105138, tl1);

        Map<Integer, Object> tl1Key = new HashMap<Integer, Object>();
        Map<String, Object> tl1Value = new HashMap<String, Object>();
        tl1Value.put("employeeNumber", 114236);
        tl1Value.put("employeeName", "Tamra Goodman");
        tl1Value.put("team", new ArrayList<Map<Integer, Object>>());
        tl1Key.put(114236, tl1Value);
        ((List)tl1.get("team")).add(tl1Key);

        Map<Integer, Object> tl2Key = new HashMap<Integer, Object>();
        Map<String, Object> tl2Value = new HashMap<String, Object>();
        tl2Value.put("employeeNumber", 112673);
        tl2Value.put("employeeName", "Betsy Engel");
        tl2Value.put("team", new ArrayList<Map<Integer, Object>>());
        tl2Key.put(112673, tl2Value);
        ((List)tl1Value.get("team")).add(tl2Key);

        Map<Integer, Object> tl3Key = new HashMap<Integer, Object>();
        Map<String, Object> tl3Value = new HashMap<String, Object>();
        tl3Value.put("employeeNumber", 113312);
        tl3Value.put("employeeName", "Edgar Alvarez");
        tl3Value.put("team", new ArrayList<Map<Integer, Object>>());
        tl3Key.put(113312, tl3Value);
        ((List)tl2Value.get("team")).add(tl3Key);

        Map<Integer, Object> tl4Key = new HashMap<Integer, Object>();
        Map<String, Object> tl4Value = new HashMap<String, Object>();
        tl4Value.put("employeeNumber", 116249);
        tl4Value.put("employeeName", "James Ayala");
        tl4Value.put("team", new ArrayList<Map<Integer, Object>>());
        tl4Key.put(116249, tl4Value);
        ((List)tl2Value.get("team")).add(tl4Key);

        Map<Integer, Object> agent1Key = new HashMap<Integer, Object>();
        Map<String, Object> agent1Value = new HashMap<String, Object>();
        agent1Value.put("employeeNumber", 102399);
        agent1Value.put("employeeName", "Charlotte Lynch");
        agent1Value.put("team", new ArrayList<Map<Integer, Object>>());
        agent1Key.put(102399, agent1Value);
        ((List)tl3Value.get("team")).add(agent1Key);

        Map<Integer, Object> agent2Key = new HashMap<Integer, Object>();
        Map<String, Object> agent2Value = new HashMap<String, Object>();
        agent2Value.put("employeeNumber", 103013);
        agent2Value.put("employeeName", "Alberto Diaz");
        agent2Value.put("team", new ArrayList<Map<Integer, Object>>());
        agent2Key.put(103013, agent2Value);
        ((List)tl3Value.get("team")).add(agent2Key);

        Map<Integer, Object> agent3Key = new HashMap<Integer, Object>();
        Map<String, Object> agent3Value = new HashMap<String, Object>();
        agent3Value.put("employeeNumber", 104754);
        agent3Value.put("employeeName", "Cara Poor");
        agent3Value.put("team", new ArrayList<Map<Integer, Object>>());
        agent3Key.put(104754, agent3Value);
        ((List)tl4Value.get("team")).add(agent3Key);

        logger.debug("{}", root);
        logger.debug("get agent node for employee 113312: {}", getAgentNode(113312, root));
        logger.debug("get agent node for employee 102399: {}", getAgentNode(102399, root));
        logger.debug("get agent node for employee 112673: {}", getAgentNode(112673, root));
        logger.debug("get agent node for employee 104754: {}", getAgentNode(104754, root));
    }

    public Map<Integer, Object> getAgentNode(int agentId, Map<Integer, Object> map){
        Map<Integer, Object> result = null;
        Set<Integer> ids = map.keySet();
        for(Integer id : ids){
            Map<Integer, Object> team = (Map)map.get(id);
            Map<String, Object> teamValue = (Map)team.get(id);
            if(id.equals(agentId)){
                result = team;
                break;
            }else if(team.get("team") != null){
                List<Map<Integer, Object>> agents = (List)team.get("team");
                for(Map<Integer, Object> agent : agents){
                    result = getAgentNode(agentId, agent);
                    if(result != null){
                        break;
                    }
                }
            }
        }
        return result;
    }

    @Test
    public void testCalculateDuration(){
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.set(2014, Calendar.OCTOBER, 8, 10, 56, 6);
        cal2.set(2014, Calendar.OCTOBER, 9, 10, 20, 11);
        long duration = cal2.getTime().getTime() - cal1.getTime().getTime();
        int diffSeconds = (int)duration / 1000 % 60;
        int diffMinutes = (int)duration / (60 * 1000) % 60;
        int diffHours = (int)duration / (60 * 60 * 1000);
        String result = diffHours + " hr " + diffMinutes + " min " + diffSeconds + " sec";
        logger.debug("result: {}", result);
        ScreenPopTime spt = new ScreenPopTime();
        spt.setUnloadDateTime("2014-10-09T10:20:11");
        spt.setPopDateTime("2014-10-08T10:56:06");
        logger.debug("duration: {}", spt.getDuration());
    }

    @Test
    public void updateAgentCallWithScreenType(){
        Calendar cal = Calendar.getInstance();
        cal.set(2014, Calendar.AUGUST, 1);
        long current = new Date().getTime();
        DateFormat df = new SimpleDateFormat("MM-dd-yyyy");
        logger.debug("cal.getTime().getTime(): {}", cal.getTime().getTime());
        logger.debug("current: {}", current);
        while(cal.getTime().getTime() <= current){
            List<AgentCall> agentCalls = mongoOperations.find(new Query(Criteria.where("callRegisteredDate").is(df.format(cal.getTime()))), AgentCall.class);
            logger.debug("process " + agentCalls.size() + " agent calls for " + df.format(cal.getTime()));
            for(AgentCall agentCall : agentCalls){
                boolean modified = false;
                for(Patient patient : agentCall.getPatients()){
                    if(patient.getScreenType() == null){
                        patient.setScreenType("psr");
                        modified = true;
                    }
                }
                if(modified){
                    mongoOperations.save(agentCall);
                }
            }
            cal.add(Calendar.DAY_OF_YEAR, 1);
        }

    }

    @Test
    public void zipCodeSearch(){
        List<Facility> facilities = callService.findFacilitiesByZipCode("76053");
        for(Facility facility : facilities){
            logger.debug("{}", facility.getName());
        }
        logger.debug("\n\n");
        facilities = callService.findFacilitiesByZipCode("76180");
        for(Facility facility : facilities){
            logger.debug("{}", facility.getName());
        }
        logger.debug("\n\n");
        facilities = callService.findFacilitiesByZipCode("84003");
        for(Facility facility : facilities){
            logger.debug("{}", facility.getName());
        }
    }

    @Test
    public void testIfCallbackEnabled(){
        boolean result = callService.checkIfCallbackEnable("Irvine");
        logger.debug("call back enabled for Irvine? {}", result);
        result = callService.checkIfCallbackEnable("Plano");
        logger.debug("call back enabled for Plano? {}", result);
        result = callService.checkIfCallbackEnable(null);
        logger.debug("call back enabled for null? {}", result);
    }

    @Test
    public void validateAppointmentWithDataMiner(){
        String sql = "select apt.APPT_LOG_STATUS, acl.CLINIC_ID, acl.UNIQUE_ID" +
                ", acl.TEMP_PATIENT_ID, pa.PATIENT_ID " +
                ", acl.FACILITY_ID " +
                ", fac.FACILITY_NAME " +
                ", apt.APPT_DATE " +
                ", acl.CREATE_DATETIME" +
                ", acl.CALL_LOG_ID " +
                ", acl.ENTRY_SOURCE " +
                ", emp.EMPLOYEE_NUMBER, emp.FIRST_NAME || ' ' || emp.LAST_NAME as emp_name " +
                "from agent_call_log acl " +
                "join APPT_CALL_LOG apt on apt.APPT_LOG_ID = acl.LAST_APPT_LOG_ID " +
                "join EMPLOYEE emp on emp.EMPLOYEE_NUMBER = acl.EMPLOYEE_NUMBER " +
                "join FACILITY fac on fac.FACILITY_ID = acl.FACILITY_ID " +
                "join PATIENTS pa on pa.CLINIC_ID = acl.CLINIC_ID and pa.UNIQUE_ID = acl.UNIQUE_ID " +
                "where 1=1 " +
                "and trunc(acl.CREATE_DATETIME) = ?1 " +
                "and apt.APPT_LOG_STATUS = 'UNVERIFIED' " +
                "and acl.UNIQUE_ID is not null " +
                "and not exists ( " +
                "select null from APPOINTMENTS apt2 where " +
                " apt2.CLINIC_ID = acl.CLINIC_ID and apt2.UNIQUE_ID = acl.UNIQUE_ID and apt2.APPT_DATE = apt.APPT_DATE " +
                ") order by acl.CREATE_DATETIME";
        Calendar cal = Calendar.getInstance();
        cal.set(2016, Calendar.MARCH, 21);
        DateFormat oracleDf = new SimpleDateFormat("dd-MMM-yyyy");
        DateFormat mssqlDf = new SimpleDateFormat("MM/dd/yyyy HH:mm");
        List<Map<String, Object>> list = jdbcTemplate.queryForList(sql, new Object[]{oracleDf.format(cal.getTime())});
        logger.debug("list size: {}", list.size());
        if(list.size() > 0){
            int count = 0;
            StringBuilder sb = new StringBuilder();
            sb.append("Call Log ID,Clinic ID,Unique ID,Appt Date,Exists");
            for(Map<String, Object> map : list){
                count++;
                Integer clinicId = ((BigDecimal)map.get("CLINIC_ID")).intValue();
                Long uniqueId = ((BigDecimal)map.get("UNIQUE_ID")).longValue();
                Date apptDate = (Date)map.get("APPT_DATE");
                Long callLogId = ((BigDecimal)map.get("CALL_LOG_ID")).longValue();

                String dataMinerSql = "select * from ";
                if(clinicId < 1100){
                    dataMinerSql += "appointments@dataminer";
                }else{
                    dataMinerSql += "appointments@castledm";
                }
                dataMinerSql += " where \"clinic_id\" = " + clinicId;
                dataMinerSql += " and \"unique_id\" = " + uniqueId;
                dataMinerSql += " and to_char(\"appt_date_time\", 'mm/dd/yyyy hh24:mi') = '" + mssqlDf.format(apptDate) + "'";
                List<Map<String,Object>> checkResult = jdbcTemplate.queryForList(dataMinerSql);
                boolean exists = checkResult.size() > 0;
                logger.debug(count + "/" + clinicId + ":::" + uniqueId + ":::" + mssqlDf.format(apptDate) + ":::" + exists);
                sb.append("\n" + callLogId + "," + clinicId + "," + uniqueId + "," + mssqlDf.format(apptDate) + "," + exists);
            }
            try{
                FileWriter fw = new FileWriter(new File("validate_appt_on_" + oracleDf.format(cal.getTime()) + ".csv"));
                fw.write(sb.toString());
                fw.close();
            }catch(Exception ex){
                ex.printStackTrace();
            }
        }
    }
}
