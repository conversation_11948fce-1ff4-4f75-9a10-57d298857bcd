<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd" >
<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<%@ taglib uri="../customTag.tld" prefix="custom" %>

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Smile Brands Call Center - Phone Number Tester</title>
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/bootstrap.min.css" >
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/style/redmond/jquery-ui-1.8.20.custom.css" />
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/call-template.css" >
</head>
<body>
    <input type="text" style="display:none;" id="reportType" value="${reportType}"/>
    <input type="text" style="display:none;" id="agentId" value="${agentId}"/>
    <input type="text" style="display:none;" id="reportDate" value="${reportDate}"/>
    <input type="text" style="display:none;" id="toDate" value="${endDate}"/>

    <div class="container main">
        <select id="facilityIDs" style="display: none;">
            <c:forEach var="facilityId" items="${facilityList}">
                <option value="${facilityId}"> ${facilityId}</option>
            </c:forEach>
        </select>
        <br/>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h1 class="panel-title gray">Screen Pop Report</h1>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-1">Load For:</div>
                    <div class="col-md-2">
                        <input class="agent-call-date report-date fromDate" name="datepicker" maxDate="0"></input>
                    </div>
                    <div class="col-md-1" style="width: 20px; padding: 0px; text-align: center; margin-left: -15px">
                        <span>TO</span>
                    </div>
                    <div class="col-md-2">
                        <input class="agent-call-date toDate" name="datepicker" maxDate="0"></input>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-1 col-md-offset-4">
                        <button id="loadBtn" class="btn btn-primary">Load</button>
                    </div>
                    <div class="col-md-2">
                        <button id="exportBtn" class="btn btn-primary" from="local">Export</button>
                    </div>
                    <div class="progress report-progress" style="display:none; width: 40%;">
                        <div class="progress-bar progress-bar-striped active"  role="progressbar" aria-valuenow="45" aria-valuemin="0" aria-valuemax="100" style="width: 100%">
                            <span class="progress-message">Please wait while pulling report...</span>
                        </div>
                    </div>
                </div>
                <br/>
                <div class="row filter-cmp">
                    <div class="col-md-1">Filter By:</div>
                    <div class="col-md-3">
                        <select filterOptionFor="screenPopList" style="width:100%;">
                            <option value="uuid">UUID</option>
                            <option value="callCenter">Call Center</option>
                            <option value="employeeName" selected>Employee</option>
                            <option value="logStatus">Call Log Status</option>
                            <option value="facilityId">Facility ID</option>
                            <option value="appointmentStatus">Appointment Status</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <input type="text" filterInputFor="screenPopList" style="width:90%;"/>
                        <span class="glyphicon glyphicon-remove pointer" removeFilterFor="screenPopList"></span>
                        <span class="" countFilterFor="screenPopList"></span>
                    </div>
                </div>
                <table id="screenPopList" class="table table-striped main">
                    <thead>
                        <tr>
                            <th sortField="uuid" class="pointer">UUID<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="ani" class="pointer">ANI<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="callCenterName" class="pointer">Call Center<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="employeeName" class="pointer">Employee<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="screenType" class="pointer">Screen Type<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="loadedTime" class="pointer">Loaded Time<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="unloadedTime" class="pointer">Unloaded Time<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="duration" class="pointer">Duration<span class="glyphicon padding-left-5"></span></th>
                        </tr>
                    </thead>
                    <tbody>
                        <c:forEach var="screenPop" items="${screenPops}">
                            <tr>
                                <td name="uuid" value="${screenPop.uuid}">${screenPop.uuid}</td>
                                <td name="ani" value="${screenPop.ani}">${screenPop.ani}</td>
                                <td name="callCenter" value="${screenPop.callCenter}">${screenPop.callCenter}</td>
                                <td name="employeeName" value="${screenPop.employeeName}">${screenPop.employeeName}</td>
                                <td name="screenType" value="${screenPop.screenType}">${screenPop.screenType}</td>
                                <td name="loadedTime" value="${screenPop.popDateTime}">${screenPop.popDateTime}</td>
                                <td name="unloadedTime" value="${screenPop.unloadDateTime}">${screenPop.unloadDateTime}</td>
                                <td name="duration" value="${screenPop.duration}">${screenPop.duration}</td>
                            </tr>
                        </c:forEach>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-1.7.2.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-ui-1.8.20.custom.min.js"></script>
    <script src="${pageContext.request.contextPath}/bootstrap/js/bootstrap.js"></script>

    <script type="text/javascript" src="${pageContext.request.contextPath}/script/sbi-script.js?_id=${buildNumber}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/reportActions.js?_id=${buildNumber}"></script>
</body>
</html>