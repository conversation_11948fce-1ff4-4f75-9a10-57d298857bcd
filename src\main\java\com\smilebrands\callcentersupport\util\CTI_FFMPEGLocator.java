package com.smilebrands.callcentersupport.util;

import it.sauronsoftware.jave.DefaultFFMPEGLocator;
import it.sauronsoftware.jave.FFMPEGLocator;

import java.io.*;

/**
 * Created by phong<PERSON>m on 5/5/15.
 */
public class CTI_FFMPEGLocator extends FFMPEGLocator {
    /**
     * Trace the version of the bundled ffmpeg executable. It's a counter: every
     * time the bundled ffmpeg change it is incremented by 1.
     */
    private static final int myEXEversion = 1;

    /**
     * The ffmpeg executable file path.
     */
    private String path;

    public CTI_FFMPEGLocator(){
        getFile();
    }

    public void getFile(){
        boolean isWindows;
        String os = System.getProperty("os.name").toLowerCase();
        String osVersionStr = System.getProperty("sun.arch.data.model");
        String fileName = "/ffmpeg";
        if(osVersionStr != null && osVersionStr.equalsIgnoreCase("64")){
            fileName += "_64";
        }
        if (os.indexOf("windows") != -1) {
            isWindows = true;
        } else {
            isWindows = false;
        }
        // Temp dir?
        File temp = new File(System.getProperty("java.io.tmpdir"), "jave-"
                + myEXEversion);
        if (!temp.exists()) {
            temp.mkdirs();
            temp.deleteOnExit();
        }
        // ffmpeg executable export on disk.
        String suffix = isWindows ? ".exe" : "";
        File exe = new File(temp, fileName + suffix);
        if (!exe.exists()) {
            copyFile(fileName + suffix, exe);
        }
        // pthreadGC2.dll
        if (isWindows) {
            File dll = new File(temp, "pthreadGC2.dll");
            if (!dll.exists()) {
                copyFile("pthreadGC2.dll", dll);
            }
        }
        // Need a chmod?
        if (!isWindows) {
            Runtime runtime = Runtime.getRuntime();
            try {
                runtime.exec(new String[] { "/bin/chmod", "755",
                        exe.getAbsolutePath() });
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        // Ok.
        this.path = exe.getAbsolutePath();
    }

    /**
     * Copies a file bundled in the package to the supplied destination.
     *
     * @param path
     *            The name of the bundled file.
     * @param dest
     *            The destination.
     * @throws RuntimeException
     *             If aun unexpected error occurs.
     */
    private void copyFile(String path, File dest) throws RuntimeException {
        InputStream input = null;
        OutputStream output = null;
        try {
            input = getClass().getResourceAsStream(path);
            output = new FileOutputStream(dest);
            byte[] buffer = new byte[1024];
            int l;
            while ((l = input.read(buffer)) != -1) {
                output.write(buffer, 0, l);
            }
        } catch (IOException e) {
            throw new RuntimeException("Cannot write file "
                    + dest.getAbsolutePath());
        } finally {
            if (output != null) {
                try {
                    output.close();
                } catch (Throwable t) {
                    ;
                }
            }
            if (input != null) {
                try {
                    input.close();
                } catch (Throwable t) {
                    ;
                }
            }
        }
    }

    @Override
    protected String getFFMPEGExecutablePath() {
        return path;
    }
}
