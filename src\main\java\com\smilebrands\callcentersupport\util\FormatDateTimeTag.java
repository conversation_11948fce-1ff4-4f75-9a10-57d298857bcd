package com.smilebrands.callcentersupport.util;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.jsp.tagext.SimpleTagSupport;
import java.util.Date;

/**
 * Created by IntelliJ IDEA.
 * User: ericjin
 * Date: 2/14/12
 * Time: 11:25 AM
 * To change this template use File | Settings | File Templates.
 */
public class FormatDateTimeTag extends SimpleTagSupport {
    final Logger log = LoggerFactory.getLogger(this.getClass());

    private Date date;
    private String dateFormat;

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getDateFormat() {
        return dateFormat;
    }

    public void setDateFormat(String dateFormat) {
        this.dateFormat = dateFormat;
    }

    public void doTag() {
        try {
            StringBuilder strBuilder = new StringBuilder("");

            if (date != null) {
                dateFormat = dateFormat == null ? "MM/dd/yyyy hh:mm:ss a" : dateFormat;
                String dateTimeFormatted = VelocityUtils.getDateAsString(date, dateFormat);
                strBuilder.append(dateTimeFormatted != null ? dateTimeFormatted.toLowerCase() : "");
            }
            getJspContext().getOut().write(strBuilder.toString());

        } catch(Exception ex) {
            log.error(ex.getMessage());
            ex.printStackTrace();
        }
    }

}
