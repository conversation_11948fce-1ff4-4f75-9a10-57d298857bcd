<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
          http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">


    <!-- **********  START  ::  FACILITY STATEMENTS  ********** -->
    <bean id="GET_FACILITY_COLLECTION" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT
                    l.FACILITY_ID,
                    f.FACILITY_NAME,
                    (ad.line_one || ' ' || ad.line_two) as ADDRESS,
                    ad.CITY,
                    ad.STATE,
                    ad.ZIPCODE,
                    ad.DESCRIPTION,
                    p.PHONE_NUMBER as PHONE,
                    x.PHONE_NUMBER as FAX,
                    fv.fac_attr_value as LIBERTY_SUPPORTED,
                    bn_fv.fac_attr_value as BRAND_NAME

                FROM FACILITY f
                    JOIN FACILITY_GEO_LOCATION l ON l.FACILITY_ID = f.FACILITY_ID
                    JOIN FACILITY_ADDRESS fa ON f.FACILITY_ID = fa.FACILITY_ID AND fa.ADDRESS_TYPE = 'REAL' AND fa.UNLINK_DATETIME IS NULL
                    JOIN ADDRESS ad ON fa.ADDRESS_ID = ad.ADDRESS_ID
                    JOIN FACILITY_ATTR_VALUES CCFV ON CCFV.FACILITY_ID = F.FACILITY_ID AND CCFV.FAC_ATTRIBUTE_ID = 261
                    LEFT OUTER JOIN FACILITY_PHONE_NUMBER fp ON fp.FACILITY_ID = f.FACILITY_ID AND fp.PHONE_NUMBER_TYPE = 'VOICE' AND fp.UNLINK_DATETIME IS NULL
                    LEFT OUTER JOIN PHONE_NUMBER p ON fp.PHONE_NUMBER_ID = p.PHONE_NUMBER_ID
                    LEFT OUTER JOIN FACILITY_PHONE_NUMBER fx ON fx.FACILITY_ID = f.FACILITY_ID AND fx.PHONE_NUMBER_TYPE = 'FAX' AND fx.UNLINK_DATETIME IS NULL
                    LEFT OUTER JOIN PHONE_NUMBER x ON fx.PHONE_NUMBER_ID = x.PHONE_NUMBER_ID
                    LEFT OUTER JOIN FACILITY_ATTR_VALUES FV ON FV.FACILITY_ID = F.FACILITY_ID AND FV.FAC_ATTRIBUTE_ID = 69
                    LEFT OUTER JOIN FACILITY_ATTR_VALUES BN_FV ON BN_FV.FACILITY_ID = F.FACILITY_ID AND BN_FV.FAC_ATTRIBUTE_ID = 7

                WHERE f.INACTIVATION_DATETIME IS NULL AND f.CLOSED_DATE IS NULL
            </value>
        </constructor-arg>
    </bean>

    <bean id="GET_OFFICE_MANAGER" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT emp.employee_number, first_name, last_name, title, email_address
                FROM Employee emp JOIN Facility_Employees fem on fem.employee_number=emp.employee_number and fem.unlink_datetime is null and fem.LINK_EMPLOYEE = 0
                WHERE employment_status = 'Active' AND fem.facility_id = ? AND title IN ('Office Manager', 'Executive Office Manager', 'Executive Office Manager/Specialty')
                ORDER BY first_name, last_name
            </value>
        </constructor-arg>
    </bean>

    <bean id="GET_OFFICE_MANAGEMENT" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT employee_number, first_name, last_name, title, email_address, manager_employee_number
                FROM employee
                WHERE employment_status = 'Active' and MANAGER_EMPLOYEE_NUMBER is not null
                CONNECT BY PRIOR manager_employee_number = employee_number
                START WITH employee_number = ?
            </value>
        </constructor-arg>
    </bean>

    <bean id="GET_EMPLOYEE_NAME" class="java.lang.String">
        <constructor-arg>
            <value type="java.lang.String">
                SELECT FIRST_NAME, LAST_NAME FROM EMPLOYEE WHERE EMPLOYMENT_STATUS='Active' AND EMPLOYEE_NUMBER = ?
            </value>
        </constructor-arg>
    </bean>

    <bean id="GET_OFFICE_PROVIDERS" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                select sp.service_provider_id,
                    CASE WHEN sp.taxonomy_code != '124Q00000X' THEN 'Dr' ELSE '' END as PREFIX,
                    sp.first_name, sp.last_name, emp.title, emp.gender, qpe.provider_id,
                    sp.npi_number, sp.license_number, sp.license_state, ptc.specialty_description as specialty,
                    spe.institution_name, spe.degree_earned, spe.degree_suffix, spe.degree_earned_year, spe.start_practice_year
                from facility_service_providers fsp
                    join service_providers sp on sp.service_provider_id=fsp.service_provider_id
                    join provider_taxonomy_codes ptc on ptc.taxonomy_code = sp.taxonomy_code
                    join facility fac on fac.facility_id=fsp.facility_id
                    join employee_to_service_provider esp on esp.service_provider_id=sp.service_provider_id and esp.UNLINK_DATETIME IS NULL
                    join employee emp on emp.employee_number=esp.employee_number and emp.employment_status='Active'
                    left join qsi_provider_extension qpe on qpe.service_provider_id=sp.service_provider_id and qpe.clinic_id=fac.qsi_clinic_id
                    left join providers p on p.CLINIC_ID = qpe.CLINIC_ID and p.PROVIDER_ID = qpe.PROVIDER_ID
                    left join service_provider_education spe on spe.service_provider_id=sp.service_provider_id
                where fsp.facility_id = ? and fsp.unlink_datetime is null and fsp.is_facility_provider=1
                    and upper(nvl(p.active_yn, 'Y')) = 'Y'
                order by fsp.is_lead_dentist desc,prefix, first_name, last_name
            </value>
        </constructor-arg>
    </bean>

    <bean id="GET_OFFICE_ATTRIBUTES" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT fav.fac_attr_value_id as ATTR_VALUE_ID, fav.fac_attribute_id as ATTRIBUTE_ID
                    ,fa.fac_attribute_name as ATTRIBUTE_NAME, fa.fac_attribute_description as ATTRIBUTE_DESCRIPTION
                    ,fg.fac_group_id as GROUP_ID, fav.fac_attr_value as ATTR_VALUE
                    ,fav.CREATE_DATETIME, fav.CREATE_EMPLOYEE, emp.FIRST_NAME || ' ' || emp.LAST_NAME as EMP_NAME
                FROM facility_attr_values fav
                    JOIN facility_attributes fa ON fav.fac_attribute_id = fa.fac_attribute_id AND fa.inactive_datetime IS NULL
                    JOIN facility_attr_groups fg ON fg.fac_group_id = fa.fac_group_id
                    LEFT JOIN EMPLOYEE emp on emp.EMPLOYEE_NUMBER = fav.CREATE_EMPLOYEE
                WHERE fav.facility_id = ? AND (fav.inactive_datetime IS NULL OR trunc(fav.inactive_datetime) &gt;= trunc(sysdate))
                ORDER BY fg.fac_group_id, fav.fac_attribute_id
            </value>
        </constructor-arg>
    </bean>

    <bean id="GET_FACILITY_PROVIDER_ATTRIBUTES" class="java.lang.String">
        <constructor-arg>
            <value type="java.lang.String">
                select pav.provider_attr_value_id as ATTR_VALUE_ID, pav.provider_attribute_id as ATTRIBUTE_ID,
                    pa.provider_attribute_name as ATTRIBUTE_NAME, pa.provider_attribute_description as ATTRIBUTE_DESCRIPTION,
                    pa.provider_group_id as GROUP_ID, pav.provider_attr_value as ATTR_VALUE,
                    pav.create_datetime, pav.create_employee, emp.first_name || ' ' || emp.last_name as EMP_NAME
                from provider_attr_values pav
                    join provider_attributes pa on pa.provider_attribute_id=pav.provider_attribute_id and pa.inactive_datetime is null
                    left join employee emp on emp.EMPLOYEE_NUMBER = pav.CREATE_EMPLOYEE
                where (pav.inactive_datetime IS NULL OR trunc(pav.inactive_datetime) &gt;= trunc(sysdate))
                    and pav.service_provider_id = ? and (pa.primary_foreign_type is null or (lower(pa.primary_foreign_type)='facility' and pav.primary_foreign_key = ?))
            </value>
        </constructor-arg>
    </bean>

    <bean id="GET_PROVIDER_INSURANCE" class="java.lang.String">
        <constructor-arg>
            <value type="java.lang.String">
                SELECT
                    CASE
                        WHEN sp.TAXONOMY_CODE != '124Q00000X' THEN 'Dr' ELSE ''
                    END AS PRX,
                    sp.SERVICE_PROVIDER_ID,
                    sp.FIRST_NAME,
                    sp.LAST_NAME,
                    sp.TAXONOMY_CODE,
                    sp.LICENSE_NUMBER,
                    sp.NPI_NUMBER,
                    ptc.SPECIALTY_DESCRIPTION AS SPECIALTY,
                    pins.*
                FROM SERVICE_PROVIDERS sp
                    JOIN FACILITY_SERVICE_PROVIDERS fsp on fsp.SERVICE_PROVIDER_ID=sp.SERVICE_PROVIDER_ID and fsp.UNLINK_DATETIME is null and fsp.IS_FACILITY_PROVIDER=1
                    JOIN PROVIDER_TAXONOMY_CODES ptc ON ptc.TAXONOMY_CODE = sp.TAXONOMY_CODE
                    JOIN ONEAPP_OFFICE_INFO oi on oi.FACILITY_ID=fsp.FACILITY_ID
                    JOIN ONEAPP_PROVIDER_INFO pi on (pi.NPI = sp.NPI_NUMBER
                        OR (EXISTS (SELECT NULL FROM ONEAPP_PROVIDER_LICENSE pl
                            WHERE pl.PROVIDER_ID=pi.PROVIDER_ID AND pl.LICENSE_NUMBER=sp.LICENSE_NUMBER AND pl.LICENSE_STATE=sp.LICENSE_STATE)))
                    JOIN ONEAPP_PROVIDER_INSURANCE pins on pins.PROVIDER_ID=pi.PROVIDER_ID and pins.OFFICE_ID=oi.OFFICE_ID
                WHERE fsp.FACILITY_ID = ? AND sp.SERVICE_PROVIDER_ID = ?
                ORDER BY payer_name
            </value>
        </constructor-arg>
    </bean>


    <bean id="GET_FACILITIES_BY_ZIPCODE" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT
                    f.FACILITY_ID,
                    f.FACILITY_NAME,
                    (ad.line_one || ' ' || ad.line_two) as ADDRESS,
                    ad.CITY,
                    ad.STATE,
                    ad.ZIPCODE,
                    p.PHONE_NUMBER as PHONE,
                    x.PHONE_NUMBER as FAX,
                    TRUNC(fr.DISTANCE) as DISTANCE,
                    TRUNC(fr.TRAVEL_TIME/60) as TRAVEL_TIME,
                    fv.fac_attr_value as LIBERTY_SUPPORTED

                FROM FACILITY f

                    JOIN FACILITY_RADIUS fr ON f.FACILITY_ID = fr.FACILITY_ID
                    JOIN FACILITY_ADDRESS fa ON f.FACILITY_ID = fa.FACILITY_ID AND fa.ADDRESS_TYPE = 'REAL' AND fa.UNLINK_DATETIME IS NULL
                    JOIN ADDRESS ad ON fa.ADDRESS_ID = ad.ADDRESS_ID
                    JOIN FACILITY_ATTR_VALUES CCFV ON CCFV.FACILITY_ID = F.FACILITY_ID AND CCFV.FAC_ATTRIBUTE_ID = 261
                    LEFT OUTER JOIN FACILITY_PHONE_NUMBER fp ON fp.FACILITY_ID = f.FACILITY_ID AND fp.PHONE_NUMBER_TYPE = 'VOICE' AND fp.UNLINK_DATETIME IS NULL
                    LEFT OUTER JOIN PHONE_NUMBER p ON fp.PHONE_NUMBER_ID = p.PHONE_NUMBER_ID
                    LEFT OUTER JOIN FACILITY_PHONE_NUMBER fx ON fx.FACILITY_ID = f.FACILITY_ID AND fx.PHONE_NUMBER_TYPE = 'FAX' AND fx.UNLINK_DATETIME IS NULL
                    LEFT OUTER JOIN PHONE_NUMBER x ON fx.PHONE_NUMBER_ID = x.PHONE_NUMBER_ID
                    LEFT OUTER JOIN FACILITY_ATTR_VALUES FV ON FV.FACILITY_ID = F.FACILITY_ID AND FV.FAC_ATTRIBUTE_ID = 69

                WHERE f.INACTIVATION_DATETIME IS NULL
                AND f.CLOSED_DATE IS NULL
                AND fr.ZIP_CODE = ?
                AND fr.DISTANCE &lt; 50
                ORDER BY fr.DISTANCE
            </value>
        </constructor-arg>
    </bean>

    <bean id="GET_ZIPCODE_FACILITIES" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT
                    fr.ZIP_CODE,
                    f.FACILITY_ID,
                    f.FACILITY_NAME,
                    (ad.line_one || ' ' || ad.line_two) as ADDRESS,
                    ad.CITY,
                    ad.STATE,
                    ad.ZIPCODE,
                    p.PHONE_NUMBER as PHONE,
                    x.PHONE_NUMBER as FAX,
                    TRUNC(fr.DISTANCE) as DISTANCE,
                    TRUNC(fr.TRAVEL_TIME/60) as TRAVEL_TIME,
                    fv.fac_attr_value as LIBERTY_SUPPORTED

                FROM FACILITY f

                    JOIN FACILITY_RADIUS fr ON f.FACILITY_ID = fr.FACILITY_ID
                    JOIN FACILITY_ADDRESS fa ON f.FACILITY_ID = fa.FACILITY_ID AND fa.ADDRESS_TYPE = 'REAL' AND fa.UNLINK_DATETIME IS NULL
                    JOIN ADDRESS ad ON fa.ADDRESS_ID = ad.ADDRESS_ID
                    JOIN FACILITY_ATTR_VALUES CCFV ON CCFV.FACILITY_ID = F.FACILITY_ID AND CCFV.FAC_ATTRIBUTE_ID = 261
                    LEFT OUTER JOIN FACILITY_PHONE_NUMBER fp ON fp.FACILITY_ID = f.FACILITY_ID AND fp.PHONE_NUMBER_TYPE = 'VOICE' AND fp.UNLINK_DATETIME IS NULL
                    LEFT OUTER JOIN PHONE_NUMBER p ON fp.PHONE_NUMBER_ID = p.PHONE_NUMBER_ID
                    LEFT OUTER JOIN FACILITY_PHONE_NUMBER fx ON fx.FACILITY_ID = f.FACILITY_ID AND fx.PHONE_NUMBER_TYPE = 'FAX' AND fx.UNLINK_DATETIME IS NULL
                    LEFT OUTER JOIN PHONE_NUMBER x ON fx.PHONE_NUMBER_ID = x.PHONE_NUMBER_ID
                    LEFT OUTER JOIN FACILITY_ATTR_VALUES FV ON FV.FACILITY_ID = F.FACILITY_ID AND FV.FAC_ATTRIBUTE_ID = 69

                WHERE fr.DISTANCE &lt; 100.1
                    AND f.INACTIVATION_DATETIME IS NULL
                    AND f.CLOSED_DATE IS NULL
                ORDER BY fr.ZIP_CODE
            </value>
        </constructor-arg>
    </bean>
    <!-- **********  END  ::  FACILITY STATEMENTS  ********** -->

    <!-- **********  START  ::  APPOINTMENT SEARCH STATEMENTS  ********** -->
    <bean id="GET_QSI_APPOINTMENT_PHONE" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT /*+ index(APPT) index(PAT,PATIENT_DOB_FIDX) */
                    PAT.PATIENT_ID, PAT.NAME_FIRST, PAT.NAME_LAST, PAT.GENDER, PAT.DATE_OF_BIRTH, PAT.DATE_LAST_VISIT,
                    FAC.FACILITY_NAME,
                    APPT.*
                FROM APPOINTMENTS APPT
                    JOIN PATIENTS PAT ON PAT.CLINIC_ID=APPT.CLINIC_ID AND PAT.UNIQUE_ID=APPT.UNIQUE_ID
                    JOIN FACILITY FAC ON FAC.QSI_CLINIC_ID=APPT.CLINIC_ID
                WHERE
                    (APPT_PHONE = ? OR PAT.PHONE_NUMBER1 = ? OR PAT.PHONE_NUMBER2 = ?)
                    AND (APPT.CLINIC_ID &lt; 1100 OR APPT.LOCATION_ID=PAT.LOCATION_ID)
                    AND (APPT.CLINIC_ID &lt; 1100 OR APPT.LOCATION_ID=FAC.QSI_LOCATION_ID)
                    '##DOB_CONDITION##'
                    '##FACILITY_CONDITION##'
                    AND TRUNC(APPT_DATE) > TRUNC(sysdate-1)
                ORDER BY APPT_DATE
            </value>
        </constructor-arg>
    </bean>

    <bean id="GET_QSI_PRIOR_APPT" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT /*+ index(APPT) index(PAT,PATIENT_DOB_FIDX) */
                    'X'
                FROM APPOINTMENTS APPT
                    JOIN PATIENTS PAT ON PAT.CLINIC_ID=APPT.CLINIC_ID AND PAT.UNIQUE_ID=APPT.UNIQUE_ID
                    JOIN FACILITY FAC ON FAC.QSI_CLINIC_ID=APPT.CLINIC_ID
                WHERE
                    (APPT_PHONE = ? OR PAT.PHONE_NUMBER1 = ? OR PAT.PHONE_NUMBER2 = ?)
                    AND (APPT.CLINIC_ID &lt; 1100 OR APPT.LOCATION_ID=PAT.LOCATION_ID)
                    AND (APPT.CLINIC_ID &lt; 1100 OR APPT.LOCATION_ID=FAC.QSI_LOCATION_ID)
                    AND FAC.FACILITY_ID = ?
                    AND TRUNC(APPT_DATE) BETWEEN TRUNC(SYSDATE-365) AND TRUNC(SYSDATE)
                    AND ROWNUM = 1
                ORDER BY APPT_DATE
            </value>
        </constructor-arg>
    </bean>

    <bean id="GET_LIBERTY_APPOINTMENT_PHONE" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT
                    P.FIRST_NAME, P.LAST_NAME, P.GENDER, P.DATE_OF_BIRTH, PD.PATIENT_DATE as DATE_LAST_VISIT,
                    FAC.FACILITY_NAME, FAC.QSI_CLINIC_ID,
                    EVT.*
                FROM LIBERTY_SCHEDULE_EVENT EVT
                    JOIN FACILITY FAC ON FAC.FACILITY_ID=EVT.FACILITY_ID
                    JOIN LIBERTY_PATIENT P ON P.PATIENT_ID=EVT.PATIENT_ID
                    LEFT JOIN LIBERTY_PATIENT_ATTRIBUTE_DATE PD ON PD.PATIENT_ID=P.PATIENT_ID AND PD.PATIENT_ATTRIBUTE_ID=272
                    LEFT JOIN LIBERTY_PATIENT_PHONE AP ON AP.PHONE_ID=P.APPOINTMENT_PHONE_ID AND AP.PHONE_ACTIVE=1
                    LEFT JOIN LIBERTY_PATIENT_PHONE HP ON HP.PHONE_ID=P.HOME_PHONE_ID AND HP.PHONE_ACTIVE=1
                    LEFT JOIN LIBERTY_PATIENT_PHONE MP ON MP.PHONE_ID=P.MOBILE_PHONE_ID AND MP.PHONE_ACTIVE=1
                WHERE TRUNC(START_DATETIME) > TRUNC(sysdate-1)
                    AND (AP.PHONE_NUMBER = ? OR HP.PHONE_NUMBER = ? OR MP.PHONE_NUMBER = ?)
                    '##DOB_CONDITION##'
                    '##FACILITY_CONDITION##'
                ORDER BY EVT.START_DATETIME
            </value>
        </constructor-arg>
    </bean>

    <bean id="GET_LIBERTY_PRIOR_APPT" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT
                    'X'
                FROM LIBERTY_SCHEDULE_EVENT EVT
                    JOIN FACILITY FAC ON FAC.FACILITY_ID=EVT.FACILITY_ID
                    JOIN LIBERTY_PATIENT P ON P.PATIENT_ID=EVT.PATIENT_ID
                    LEFT JOIN LIBERTY_PATIENT_ATTRIBUTE_DATE PD ON PD.PATIENT_ID=P.PATIENT_ID AND PD.PATIENT_ATTRIBUTE_ID=272
                    LEFT JOIN LIBERTY_PATIENT_PHONE AP ON AP.PHONE_ID=P.APPOINTMENT_PHONE_ID AND AP.PHONE_ACTIVE=1
                    LEFT JOIN LIBERTY_PATIENT_PHONE HP ON HP.PHONE_ID=P.HOME_PHONE_ID AND HP.PHONE_ACTIVE=1
                    LEFT JOIN LIBERTY_PATIENT_PHONE MP ON MP.PHONE_ID=P.MOBILE_PHONE_ID AND MP.PHONE_ACTIVE=1
                WHERE TRUNC(START_DATETIME) BETWEEN TRUNC(SYSDATE-365) AND TRUNC(SYSDATE)
                    AND (AP.PHONE_NUMBER = ? OR HP.PHONE_NUMBER = ? OR MP.PHONE_NUMBER = ?)
                    AND FAC.FACILITY_ID = ?
                    AND ROWNUM = 1
                ORDER BY EVT.START_DATETIME
            </value>
        </constructor-arg>
    </bean>

    <!-- **********  END  ::  APPOINTMENT SEARCH STATEMENTS  ********** -->


    <!-- **********  START  ::  PATIENT PHONE SEARCH STATEMENTS  ********** -->
    <bean id="GET_QSI_PATIENT_FACILITY" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT
                    PAT.PATIENT_ID, PAT.NAME_FIRST, PAT.NAME_LAST, FAC.FACILITY_NAME, FAC.FACILITY_ID, PAT.DATE_LAST_VISIT
                FROM PATIENTS PAT
                    JOIN FACILITY FAC ON FAC.QSI_CLINIC_ID=PAT.CLINIC_ID AND FAC.QSI_LOCATION_ID=PAT.LOCATION_ID AND FAC.FACILITY_ID=PAT.FACILITY_ID
                WHERE PAT.PHONE_NUMBER1 = ?
                ORDER BY PAT.DATE_LAST_VISIT DESC
            </value>
        </constructor-arg>
    </bean>
    <bean id="GET_LIBERTY_PATIENT_FACILITY" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT P.PATIENT_ID, P.FIRST_NAME, P.LAST_NAME, FAC.FACILITY_NAME, FAC.FACILITY_ID, EVT.START_DATETIME AS DATE_LAST_VISIT
                FROM LIBERTY_SCHEDULE_EVENT EVT
                    JOIN FACILITY FAC ON FAC.FACILITY_ID=EVT.FACILITY_ID
                    JOIN LIBERTY_PATIENT P ON P.PATIENT_ID=EVT.PATIENT_ID
                    JOIN LIBERTY_PATIENT_PHONE AP ON AP.PATIENT_ID = EVT.PATIENT_ID AND AP.PHONE_ACTIVE=1
                WHERE TRUNC(START_DATETIME) BETWEEN TRUNC(SYSDATE-365) AND TRUNC(SYSDATE)
                AND AP.PHONE_NUMBER = ?
                ORDER BY EVT.START_DATETIME DESC
            </value>
        </constructor-arg>
    </bean>
    <!-- **********  END  ::  PATIENT PHONE STATEMENTS  ********** -->



    <!-- **********  START  ::  CALL LINK STATEMENTS  ********** -->
    <bean id="CALL_DETAIL_PATIENTS_TO_LINK" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT LOADED_FACILITY_ID, TEMP_PATIENT_ID, UNIQUE_CALL_ID
                FROM CALL_CENTER_CALL_DETAIL det
                WHERE TRUNC(det.LOAD_DATETIME) > TRUNC(SYSDATE-7)
                AND det.TEMP_PATIENT_ID IS NOT NULL
                AND det.QSI_PATIENT_ID IS NULL
            </value>
        </constructor-arg>
    </bean>

    <bean id="QSI_PATIENT_LOOK_UP" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT UNIQUE_ID
                FROM PATIENTS
                WHERE FACILITY_ID = ?
                AND PATIENT_ID = ?
            </value>
        </constructor-arg>
    </bean>

    <bean id="CALL_DETAIL_PATIENT_LINK" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                UPDATE CALL_CENTER_CALL_DETAIL
                SET QSI_PATIENT_ID = ?
                WHERE UNIQUE_CALL_ID = ?
                AND LOADED_FACILITY_ID = ?
                AND TEMP_PATIENT_ID = ?
            </value>
        </constructor-arg>
    </bean>

    <bean id="UPDATE_CALL_WITH_PATIENT_PHONE" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                UPDATE CALL_CENTER_CALL_DETAIL
                SET PATIENT_PHONE_NUMBER = ?
                WHERE UNIQUE_CALL_ID = ?
                AND CALL_SEQUENCE = ?
            </value>
        </constructor-arg>
    </bean>

    <bean id="START_CALL_FROM_SCREEN_POP" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                INSERT INTO CALL_CENTER_CALL_DETAIL(UNIQUE_CALL_ID, CALL_SEQUENCE, LOAD_DATETIME, EMPLOYEE_NUMBER,
                LOADED_FACILITY_ID)
                VALUES(?,?,SYSDATE,?,?)
            </value>
        </constructor-arg>
    </bean>

    <bean id="LOOK_UP_QSI_PATIENT" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT INITCAP(P.NAME_FIRST) as FIRST_NAME, INITCAP(NAME_LAST) as LAST_NAME, LOWER(E.PATIENT_EMAIL) as
                EMAIL, P.DATE_OF_BIRTH
                FROM PATIENTS P
                LEFT OUTER JOIN PATIENTS_EMAIL E ON P.CLINIC_ID = E.CLINIC_ID AND P.UNIQUE_ID = E.UNIQUE_ID
                WHERE P.PATIENT_ID = ?
                    AND P.FACILITY_ID = ?
            </value>
        </constructor-arg>
    </bean>

    <bean id="LOOK_UP_LIBERTY_PATIENT" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT INITCAP(P.FIRST_NAME) as FIRST_NAME, INITCAP(LAST_NAME) as LAST_NAME, P.DATE_OF_BIRTH
                FROM LIBERTY_PATIENT P
                WHERE P.PATIENT_ID = ?
            </value>
        </constructor-arg>
    </bean>

    <bean id="UPDATE_CALL_WITH_PATIENT" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                UPDATE CALL_CENTER_CALL_DETAIL
                SET TEMP_PATIENT_ID = ?
                WHERE UNIQUE_CALL_ID = ?
                AND CALL_SEQUENCE = ?
            </value>
        </constructor-arg>
    </bean>

    <bean id="UPDATE_CALL_WITH_EMAIL" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                UPDATE CALL_CENTER_CALL_DETAIL
                SET EMAIL_ADDRESS = ?, EMAIL_DATETIME = SYSDATE, EMAIL_RESULT_CODE = ?
                WHERE UNIQUE_CALL_ID = ?
                AND CALL_SEQUENCE = ?
            </value>
        </constructor-arg>
    </bean>

    <bean id="UPDATE_CALL_WITH_RESOLUTION" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                UPDATE CALL_CENTER_CALL_DETAIL
                SET RESOLUTION_CODE = ?, RESOLUTION_DATETIME = SYSDATE,
                CALL_REASON_CODE = ?, CALL_REASON_DATETIME = SYSDATE
                WHERE UNIQUE_CALL_ID = ?
                AND CALL_SEQUENCE = ?
            </value>
        </constructor-arg>
    </bean>

    <bean id="UPDATE_CALL_WITH_UNLOAD_EVENT" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                UPDATE CALL_CENTER_CALL_DETAIL
                SET UNLOAD_DATETIME = SYSDATE
                WHERE UNIQUE_CALL_ID = ?
                AND CALL_SEQUENCE = ?
            </value>
        </constructor-arg>
    </bean>

    <bean id="UPDATE_CALL_WITH_UNLOAD_EVENT_AND_RESOLUTION" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                UPDATE CALL_CENTER_CALL_DETAIL
                SET UNLOAD_DATETIME = SYSDATE, RESOLUTION_CODE = ?, RESOLUTION_DATETIME = SYSDATE,
                    CALL_REASON_CODE = ?, CALL_REASON_DATETIME = SYSDATE
                WHERE UNIQUE_CALL_ID = ?
                AND CALL_SEQUENCE = ?
            </value>
        </constructor-arg>
    </bean>

    <bean id="QUEUE_EMAIL_CONFIRM" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                INSERT INTO NOTIFICATION_REQUEST
                SELECT
                NOTIFICATION_REQ_SEQ.NEXTVAL,
                'CONFIRMATION',
                TO_TIMESTAMP('##APPT_DATETIME##', 'yyyy/mm/dd hh24:mi'),
                '##PATIENT_ID##',
                '##EMAIL_ADDRESS##',
                '##FIRST_NAME##',
                '##LAST_NAME##',
                '##LANGUAGE##',
                f.FACILITY_ID,
                f.FACILITY_NAME,
                ad.LINE_ONE "FACILITY_ADDRESS1",
                ad.LINE_TWO "FACILITY_ADDRESS2",
                ad.CITY "FACILITY_CITY",
                ad.STATE "FACILITY_STATE",
                ad.ZIPCODE "FACILITY_ZIP",
                NVL(cc.FAC_ATTR_VALUE, p.PHONE_NUMBER) "FACILITY_PHONE_NUMBER",
                SYS_GUID(),
                SYSDATE

                FROM FACILITY f
                JOIN FACILITY_ADDRESS fa ON f.FACILITY_ID = fa.FACILITY_ID
                AND fa.ADDRESS_TYPE = 'REAL' AND fa.UNLINK_DATETIME IS NULL
                JOIN ADDRESS ad ON fa.ADDRESS_ID = ad.ADDRESS_ID
                LEFT OUTER JOIN FACILITY_PHONE_NUMBER fp ON fp.FACILITY_ID = f.FACILITY_ID
                AND fp.PHONE_NUMBER_TYPE = 'VOICE' AND fp.UNLINK_DATETIME IS NULL
                LEFT OUTER JOIN PHONE_NUMBER p ON fp.PHONE_NUMBER_ID = p.PHONE_NUMBER_ID
                LEFT OUTER JOIN FACILITY_ATTR_VALUES cc ON f.FACILITY_ID = cc.FACILITY_ID AND cc.FAC_ATTRIBUTE_ID = 156

                WHERE f.FACILITY_ID = ?
            </value>
        </constructor-arg>
    </bean>
    <!-- **********  END  ::  CALL LINK STATEMENTS  ********** -->


    <bean id="GET_CALL_CENTER_LIST" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT c.look_up_dnis, c.facility_id, c.main_number, c.fax_number, f.facility_name,
                    c.transfer_number, c.call_center_queue, c.call_center, c.call_center_supported,
                    addr.line_one, addr.line_two, addr.city, addr.state, addr.zipcode, fm.message, fv.fac_attr_value
                FROM call_center_support c
                    LEFT OUTER JOIN facility f ON c.facility_id = f.facility_id
                    LEFT OUTER JOIN facility_address fa ON c.facility_id = fa.facility_id
                    LEFT OUTER JOIN address addr ON fa.address_id = addr.address_id
                    LEFT OUTER JOIN call_center_facility_message fm ON f.facility_id = fm.facility_id
                    LEFT OUTER JOIN facility_attr_values fv ON fv.facility_id = c.facility_id AND fv.fac_attribute_id = 69
                WHERE (fa.address_type = 'REAL' AND fa.unlink_datetime is null) OR fa.address_type is null
                ORDER BY c.facility_id
            </value>
        </constructor-arg>
    </bean>

    <bean id="FACILITY_BY_CAMPAIGN" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                select DISTINCT cs.MAIN_NUMBER as LOOK_UP_DNIS, cf.FACILITY_ID, f.FACILITY_NAME,
                    (ad.line_one || ' ' || ad.line_two || '. ' || ad.city || ', ' || ad.state || ' ' || ad.zipcode) as ADDRESS

                from CALL_CENTER_CAMPAIGN_FACILITY cf
                    left outer join FACILITY f on cf.FACILITY_ID = f.FACILITY_ID
                    left outer join CALL_CENTER_SUPPORT cs on cf.FACILITY_ID = cs.FACILITY_ID
                    left JOIN FACILITY_ADDRESS fa ON cs.FACILITY_ID = fa.FACILITY_ID AND fa.ADDRESS_TYPE = 'REAL' AND fa.UNLINK_DATETIME IS NULL
                    left JOIN ADDRESS ad ON fa.ADDRESS_ID = ad.ADDRESS_ID

                where cf.CAMPAIGN_ID = ?
                and cs.CALL_CENTER_SUPPORTED = 'Y'
                AND cs.main_number = cs.look_up_dnis
                order by FACILITY_ID ASC

            </value>
        </constructor-arg>
    </bean>

    <bean id="FACILITY_BY_CALL_CENTER" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                select DISTINCT cs.MAIN_NUMBER as LOOK_UP_DNIS, cs.FACILITY_ID, f.FACILITY_NAME,
                    (ad.line_one || ' ' || ad.line_two || '. ' || ad.city || ', ' || ad.state || ' ' || ad.zipcode) as ADDRESS

                from CALL_CENTER_SUPPORT cs
                    left outer join FACILITY f on cs.FACILITY_ID = f.FACILITY_ID
                    left JOIN FACILITY_ADDRESS fa ON cs.FACILITY_ID = fa.FACILITY_ID AND fa.ADDRESS_TYPE = 'REAL' AND fa.UNLINK_DATETIME IS NULL
                    left JOIN ADDRESS ad ON fa.ADDRESS_ID = ad.ADDRESS_ID

                where cs.CALL_CENTER = ?
                and cs.CALL_CENTER_SUPPORTED = 'Y'
                AND cs.main_number = cs.look_up_dnis
                order by FACILITY_ID ASC

            </value>
        </constructor-arg>
    </bean>

    <bean id="FACILITY_BY_DNIS" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT c.look_up_dnis, c.facility_id, c.main_number, c.fax_number, f.facility_name,
                c.transfer_number, c.call_center_queue, c.call_center, c.call_center_supported,
                addr.line_one, addr.line_two, addr.city, addr.state, addr.zipcode, fm.message
                FROM call_center_support c
                LEFT OUTER JOIN facility f ON c.facility_id = f.facility_id
                LEFT OUTER JOIN facility_address fa ON c.facility_id = fa.facility_id
                LEFT OUTER JOIN address addr ON fa.address_id = addr.address_id
                LEFT OUTER JOIN call_center_facility_message fm on f.facility_id = fm.facility_id
                WHERE ((fa.address_type = 'REAL' AND fa.unlink_datetime is null) OR fa.address_type is null)
                    AND c.look_up_dnis=?
            </value>
        </constructor-arg>
    </bean>

    <bean id="GET_CALL_CENTER" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT * FROM call_center_support
                WHERE look_up_dnis = ?
            </value>
        </constructor-arg>
    </bean>

    <bean id="ALL_CAMPAIGN" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT * FROM call_center_campaign
                ORDER BY campaign_id
            </value>
        </constructor-arg>
    </bean>
    <bean id="GET_CAMPAIGN_BY_ID" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT * FROM call_center_campaign
                WHERE CAMPAIGN_ID = ?
            </value>
        </constructor-arg>
    </bean>



    <bean id="GET_OFFICE_PHONES" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT p.phone_number, fp.phone_number_type FROM FACILITY_PHONE_NUMBER fp
                LEFT OUTER JOIN PHONE_NUMBER p ON fp.phone_number_id = p.phone_number_id
                WHERE fp.facility_id = ? AND fp.unlink_datetime is null
            </value>
        </constructor-arg>
    </bean>



    <bean id="GET_NEIGHBOR_OFFICES" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT * FROM
                (
                SELECT
                l.FACILITY_ID,
                f.FACILITY_NAME,
                (ad.line_one || ' ' || ad.line_two || '. ' || ad.city || ', ' || ad.state || ' ' || ad.zipcode) as
                ADDRESS,
                p.PHONE_NUMBER,
                to_char(fr.DISTANCE, '99.99') AS DISTANCE

                FROM FACILITY f
                JOIN FACILITY_RADIUS fr ON f.FACILITY_ID = fr.FACILITY_ID
                JOIN FACILITY_GEO_LOCATION l ON l.FACILITY_ID = f.FACILITY_ID
                JOIN FACILITY_ADDRESS fa ON f.FACILITY_ID = fa.FACILITY_ID AND fa.ADDRESS_TYPE = 'REAL' AND
                fa.UNLINK_DATETIME IS NULL
                JOIN ADDRESS ad ON fa.ADDRESS_ID = ad.ADDRESS_ID
                LEFT OUTER JOIN FACILITY_PHONE_NUMBER fp ON fp.FACILITY_ID = f.FACILITY_ID AND fp.PHONE_NUMBER_TYPE =
                'VOICE' AND fp.UNLINK_DATETIME IS NULL
                LEFT OUTER JOIN PHONE_NUMBER p ON fp.PHONE_NUMBER_ID = p.PHONE_NUMBER_ID
                WHERE f.INACTIVATION_DATETIME IS NULL
                AND f.CLOSED_DATE IS NULL
                AND f.FACILITY_ID != ?
                AND fr.ZIP_CODE = ?
                AND fr.DISTANCE &lt; 30
                ORDER BY fr.DISTANCE

                )
                WHERE rownum &lt;= 5
            </value>
        </constructor-arg>
    </bean>
    <bean id="UPDATE_CALL_CENTER_SUPPORT" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                UPDATE CALL_CENTER_SUPPORT
                SET MAIN_NUMBER = ? , FAX_NUMBER = ? , CALL_CENTER_QUEUE = ?, CALL_CENTER = ?, CALL_CENTER_SUPPORTED =
                ?, TRANSFER_NUMBER = ?
                WHERE LOOK_UP_DNIS = ?
            </value>
        </constructor-arg>
    </bean>
    <bean id="UPDATE_CALL_CENTER_FACILITY_MESSAGE" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                UPDATE CALL_CENTER_FACILITY_MESSAGE
                SET MESSAGE = ?
                WHERE FACILITY_ID = ?
            </value>
        </constructor-arg>
    </bean>
    <bean id="INSERT_CALL_CENTER_FACILITY_MESSAGE" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                INSERT INTO CALL_CENTER_FACILITY_MESSAGE (FACILITY_ID,MESSAGE) values ( ?, ?)
            </value>
        </constructor-arg>
    </bean>

    <bean id="GET_CALL_RESOLUTION_CODE" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT * FROM CALL_CENTER_RESOLUTION_CODE WHERE IS_ACTIVE='Y' ORDER BY DISPLAY_ORDER
            </value>
        </constructor-arg>
    </bean>

    <bean id="GET_OFFICES_BY_ZIPCODE" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT DISTINCT * FROM (
                    SELECT
                        fac.FACILITY_ID,
                        fac.FACILITY_NAME,
                        (ad.line_one || ' ' || ad.line_two || '. ' || ad.city || ', ' || ad.state || ' ' || ad.zipcode) as ADDRESS,
                        ccs.MAIN_NUMBER as PHONE_NUMBER,
                        to_char(fr.DISTANCE, '99.99') as DISTANCE,
                        to_char(fr.TRAVEL_TIME/60, '99.99') as TRAVEL_TIME

                    FROM CALL_CENTER_CAMPAIGN cmp
                        JOIN CALL_CENTER_SUPPORT ccs ON cmp.CALL_CENTER = ccs.CALL_CENTER AND ccs.CALL_CENTER_SUPPORTED = 'Y'
                        JOIN FACILITY fac ON ccs.FACILITY_ID = fac.FACILITY_ID
                        JOIN FACILITY_RADIUS fr ON fac.FACILITY_ID = fr.FACILITY_ID
                        JOIN FACILITY_ADDRESS fa ON fac.FACILITY_ID = fa.FACILITY_ID AND fa.ADDRESS_TYPE = 'REAL' AND fa.UNLINK_DATETIME IS NULL
                        JOIN ADDRESS ad ON fa.ADDRESS_ID = ad.ADDRESS_ID

                    WHERE CAMPAIGN_LOOK_UP_DNIS = ?
                    AND ccs.main_number = ccs.look_up_dnis
                    AND ((cmp.LIMITED_SUPPORT = 'N')
                            OR
                        ((cmp.LIMITED_SUPPORT = 'Y')
                            AND (fac.FACILITY_ID IN (SELECT FACILITY_ID FROM CALL_CENTER_CAMPAIGN_FACILITY cccf WHERE cccf.CAMPAIGN_ID = cmp.CAMPAIGN_ID )))
                        )
                    AND fr.ZIP_CODE = ?
                    AND fr.DISTANCE &lt; 60
                    ORDER BY fr.DISTANCE
                )
                ORDER BY DISTANCE
            </value>
        </constructor-arg>
    </bean>



    <bean id="ADD_CALL_CENTER_SUPPORT" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                INSERT INTO CALL_CENTER_SUPPORT (FACILITY_ID,MAIN_NUMBER,FAX_NUMBER,
                CALL_CENTER_QUEUE,CALL_CENTER,CALL_CENTER_SUPPORTED,
                LOOK_UP_DNIS,TRANSFER_NUMBER) VALUES (?,?,?,?,?,?,?,?)
            </value>
        </constructor-arg>
    </bean>

    <bean id="DELETE_CALL_CENTER_SUPPORT_BY_DNIS" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                DELETE FROM CALL_CENTER_SUPPORT WHERE LOOK_UP_DNIS=?
            </value>
        </constructor-arg>
    </bean>

    <bean id="GET_AGENT_CALL_LOG" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT * FROM (
                     SELECT v.*, emp.FIRST_NAME || ' ' || emp.LAST_NAME as EMPLOYEE_NAME , cf.CALL_LOG_FILE_ID, cf.FILE_PATH
                     FROM V_AGENT_CALL_LOG v
                        LEFT JOIN EMPLOYEE emp ON emp.EMPLOYEE_NUMBER = v.EMPLOYEE_NUMBER
                        LEFT JOIN CALL_LOG_FILE cf ON cf.UUID = v.UUID AND cf.EMPLOYEE_NUMBER = v.EMPLOYEE_NUMBER AND cf.IS_ACTIVE = 1
                     WHERE TO_CHAR(CALL_TIME, 'YYYY-MM-dd HH24:MI:SS') BETWEEN ? AND ?
                     '##DIRECT_SUPERVISOR##'
                ) iv
                ORDER BY iv.CALL_CENTER_TIME, iv.UUID
            </value>
        </constructor-arg>
    </bean>

    <bean id="GET_CALL_LOG_DETAIL_REPORT" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT * FROM
                (
                    SELECT V.CALL_LOG_ID,
                        V.SESSION_ID,
                        V.CALL_CENTER,
                        V.CALL_CENTER_TIME,
                        TO_CHAR(V.CALL_CENTER_TIME, 'MM/DD/YYYY HH:MI:SS PM') AS CALL_START_TIME,
                        TO_CHAR(V.CALL_CENTER_TIME + (V.CALL_DURATION / 86400), 'MM/DD/YYYY HH:MI:SS PM') AS CALL_END_TIME,
                        CASE V.EMAIL_CAPTURED WHEN '1' THEN 'Yes' ELSE ' ' END AS EMAIL_CAPTURED,
                        CASE V.WAITING_ON_INSURANCE WHEN '1' THEN 'Yes' ELSE ' ' END AS WAITING_ON_INSURANCE,
                        CASE V.IS_ORIGINAL WHEN 1 THEN 'Yes' ELSE ' ' END AS IS_ORIGINAL,
                        V.EMPLOYEE_NUMBER,
                        E.FIRST_NAME||' '||E.LAST_NAME AS AGENT_NAME,
                        V.AGENT_TYPE,
                        V.FACILITY_ID,
                        F.FACILITY_NAME,
                        (SELECT CASE CALL_CENTER_SUPPORTED WHEN 'Y' THEN 'Yes' ELSE ' ' END FROM BNDNEW.CALL_CENTER_SUPPORT WHERE FACILITY_ID = F.FACILITY_ID AND ROWNUM = 1) AS "SUPPORTED",
                        V.CLINIC_ID,
                        V.PATIENT_ID,
                        V.TEMP_PATIENT_ID,
                        CASE IS_LIBERTY
                            WHEN 0 THEN P.NAME_FIRST||' '||P.NAME_LAST
                            ELSE LP.FIRST_NAME||' '||LP.LAST_NAME
                        END AS PATIENT_NAME,

                        CASE IS_LIBERTY
                            WHEN 0 THEN P.PATIENT_ADDRESS1||' '||P.PATIENT_ADDRESS2
                            ELSE LPA.ADDRESS_LINE1||' '||LPA.ADDRESS_LINE2
                        END AS PATIENT_ADDRESS,

                        CASE IS_LIBERTY
                            WHEN 0 THEN
                                CASE WHEN P.PATIENT_CITY IS NULL THEN ' ' ELSE P.PATIENT_CITY||', '||P.PATIENT_STATE||' '||P.ZIPCODE END
                            ELSE CASE WHEN LPA.ADDRESS_CITY IS NULL THEN ' ' ELSE LPA.ADDRESS_CITY||', '||LPA.ADDRESS_STATE||' '||LPA.ZIP_CODE END
                        END AS CIT_ST_ZIP,

                        CASE IS_LIBERTY
                            WHEN 0 THEN P.PHONE_NUMBER1
                            ELSE LPP1.PHONE_NUMBER
                        END AS PHONE_NUMBER1,

                        CASE IS_LIBERTY
                            WHEN 0 THEN P.PHONE_NUMBER2
                            ELSE LPP2.PHONE_NUMBER
                        END AS PHONE_NUMBER2,

                        CASE IS_LIBERTY
                            WHEN 0 THEN PE.PATIENT_EMAIL
                            ELSE LPE.EMAIL_ADDRESS
                        END AS EMAIL_ADDRESS,

                        CASE IS_LIBERTY
                            WHEN 0 THEN P.DATE_OF_BIRTH
                            ELSE LP.DATE_OF_BIRTH
                        END AS DATE_OF_BIRTH,

                        CASE IS_LIBERTY
                            WHEN 0 THEN TRIM(P.GENDER)
                            ELSE TRIM(LP.GENDER)
                        END AS GENDER,

                        CASE IS_LIBERTY
                            WHEN 0 THEN
                                (CASE WHEN P.CLINIC_ID &lt; 1100 AND NVL(INSTR(P.ALERTS,'*', 1, 1),0) &gt; 0 THEN 'SPANISH'
                                    WHEN P.CLINIC_ID &gt; 1100 AND NVL(INSTR(P.ALERTS,'@', 1, 1),0) &gt; 0 THEN 'ENGLISH'
                                    WHEN P.CLINIC_ID IS NULL THEN ' '
                                ELSE 'ENGLISH' END)
                        ELSE LP.PATIENT_LANGUAGE_TYPE END AS PATIENT_LANGUAGE,

                        TRIM(NVL(AC.LIBERTY_PLAN_TYPE,' ')) AS PLAN_TYPE,
                        TRIM(NVL(AC.LIBERTY_ACCT_GROUP,' ')) AS PLAN_GROUP,
                        REPLACE(RS.REFERRAL_SOURCE_NAME, 'REF,', '') AS REFERRAL_SOURCE,
                        TRIM(V.PHONE_TYPE) AS PHONE_TYPE,
                        V.REASON_CODE,
                        V.RESOLUTION_CODE,
                        V.CALL_LOG_STATUS,
                        V.RING_TIME,
                        V.TALK_TIME,
                        V.HOLD_TIME,
                        V.WORK_TIME,
                        V.APPT_CLINIC_ID,
                        (CASE V.IS_LIBERTY WHEN 0 THEN (SELECT FACILITY_ID FROM BNDNEW.APPOINTMENTS WHERE CLINIC_ID = V.APPT_CLINIC_ID AND UNIQUE_ID = V.APPT_UNIQUE_ID AND APPT_DATE = V.APPT_DATE AND ROWNUM = 1)
                        ELSE (SELECT FACILITY_ID FROM LIBERTY.SCHEDULE_EVENT WHERE PATIENT_ID = V.PATIENT_ID AND START_DATETIME = V.APPT_DATE AND ROWNUM = 1)
                        END) AS APPT_FACILITY_ID,
                        V.APPT_PROVIDER_ID,
                        TRIM((CASE V.IS_LIBERTY WHEN 0 THEN (SELECT APPT_TYPE FROM BNDNEW.APPOINTMENTS WHERE CLINIC_ID = V.APPT_CLINIC_ID AND UNIQUE_ID = V.APPT_UNIQUE_ID AND APPT_DATE = V.APPT_DATE AND ROWNUM = 1)
                        ELSE NULL
                        END)) AS APPT_TYPE,
                        V.APPT_LOG_STATUS,
                        (CASE WHEN V.APPT_LOG_STATUS != 'NO_APPT_SCHEDULED'
                            THEN (SELECT appt.APPT_LOG_STATUS FROM APPT_CALL_LOG appt
                            WHERE appt.CALL_LOG_ID=v.CALL_LOG_ID AND appt.APPT_LOG_ID &lt; v.LAST_APPT_LOG_ID
                                AND NOT EXISTS (SELECT null FROM APPT_CALL_LOG appt2
                                    WHERE appt2.CALL_LOG_ID = appt.CALL_LOG_ID AND appt2.appt_log_id &gt; appt.APPT_LOG_ID AND appt2.APPT_LOG_ID &lt; v.LAST_APPT_LOG_ID)
                            )
                            ELSE null END) AS PREVIOUS_APPT_LOG_STATUS,
                        --TO_CHAR(V.APPT_DATE, 'MM/DD/YYYY HH:MI:SS PM') AS APPT_DATE,
                        V.APPT_DATE,
                        (CASE WHEN V.APPT_LOG_STATUS != 'NO_APPT_SCHEDULED'
                            THEN (SELECT appt.APPT_DATE --TO_CHAR(appt.APPT_DATE, 'MM/DD/YYYY HH:MI:SS PM')
                                    FROM APPT_CALL_LOG appt
                                    WHERE appt.CALL_LOG_ID = v.CALL_LOG_ID AND appt.APPT_LOG_ID &lt; v.LAST_APPT_LOG_ID AND appt.APPT_DATE IS NOT null
                                       AND NOT EXISTS (SELECT null FROM APPT_CALL_LOG appt2
                                            WHERE appt2.CALL_LOG_ID = appt.CALL_LOG_ID and appt2.APPT_LOG_ID &gt; appt.APPT_LOG_ID and appt2.APPT_LOG_ID &lt; v.LAST_APPT_LOG_ID
                                       AND appt2.APPT_DATE IS NOT null)
                            )
                            ELSE null END) AS LATEST_APPT_DATE,
                        V.TEAM_ID,
                        CASE V.SERVICE_LEVEL_MET WHEN 'Y' THEN 'Yes' ELSE '' END AS SLA,
                        CASE V.IS_ORTHO WHEN 1 THEN 'Yes' ELSE '' END AS IS_ORTHO,
                        CASE V.ALLOW_CREDIT WHEN 1 THEN 'Yes' ELSE '' END AS ALLOW_CREDIT,
                        (CASE WHEN V.APPT_LOG_STATUS != 'NO_APPT_SCHEDULED'
                            THEN (SELECT 'Yes' FROM APPT_CALL_LOG appt WHERE appt.CALL_LOG_ID = v.CALL_LOG_ID AND appt.APPT_LOG_STATUS='VERIFIED'
                                    AND NOT EXISTS (SELECT null FROM APPT_CALL_LOG appt2
                                        WHERE appt2.CALL_LOG_ID = appt.CALL_LOG_ID AND appt2.APPT_LOG_STATUS='VERIFIED' AND appt2.APPT_LOG_ID &gt; appt.APPT_LOG_ID))
                            ELSE '' END) AS WAS_VERIFIED

                    FROM V_AGENT_CALL_LOG V

                        LEFT OUTER JOIN BNDNEW.EMPLOYEE E
                            ON V.EMPLOYEE_NUMBER = E.EMPLOYEE_NUMBER

                        LEFT OUTER JOIN BNDNEW.PATIENTS P
                            ON V.CLINIC_ID = P.CLINIC_ID AND V.UNIQUE_ID = P.UNIQUE_ID AND V.IS_LIBERTY = 0
                        LEFT OUTER JOIN BNDNEW.PATIENTS_EMAIL PE
                            ON P.CLINIC_ID = PE.CLINIC_ID AND P.UNIQUE_ID = PE.UNIQUE_ID AND V.IS_LIBERTY = 0

                        LEFT OUTER JOIN LIBERTY.PATIENT LP
                            ON V.TEMP_PATIENT_ID = LP.PATIENT_ID AND V.IS_LIBERTY = 1
                        LEFT OUTER JOIN LIBERTY.PATIENT_EMAIL LPE
                            ON LP.HOME_EMAIL_ID = LPE.EMAIL_ID AND LP.PATIENT_ID = LPE.PATIENT_ID AND LPE.EMAIL_ACTIVE = 1
                        LEFT OUTER JOIN LIBERTY.PATIENT_ADDRESS LPA
                            ON LP.PATIENT_ID = LPA.PATIENT_ID AND LP.HOME_ADDRESS_ID = LPA.ADDRESS_ID AND LPA.ADDRESS_ACTIVE = 1
                        LEFT OUTER JOIN LIBERTY.PATIENT_PHONE LPP1 --LIBERTY HOME PHONE
                            ON LP.HOME_PHONE_ID = LPP1.PHONE_ID AND LP.PATIENT_ID = LPP1.PATIENT_ID AND LPP1.PHONE_ACTIVE = 1
                        LEFT OUTER JOIN LIBERTY.PATIENT_PHONE LPP2 --LIBERTY CELL PHONE
                            ON LP.MOBILE_PHONE_ID = LPP2.PHONE_ID AND LP.PATIENT_ID = LPP2.PATIENT_ID AND LPP1.PHONE_ACTIVE = 1

                        LEFT OUTER JOIN BNDNEW.FACILITY F
                            ON V.FACILITY_ID = F.FACILITY_ID

                        LEFT OUTER JOIN BNDNEW.ACCOUNT_TYPES AC
                            ON P.CLINIC_ID = AC.CLINIC_ID AND P.ACCOUNT_TYPE = AC.ACCOUNT_TYPE

                        LEFT OUTER JOIN RPTOWN.REFERRAL_SOURCES RS
                            ON P.REFERRAL_SOURCE_ID = RS.REFERRAL_SOURCE_ID
                ) V
                WHERE '##DATE_QUERY##'
                '##AGENT_QUERY##'
                '##REPORT_QUERY##'
                '##ORDER_BY##'
            </value>
        </constructor-arg>
    </bean><bean id="GET_CALL_LOG_DETAIL_REPORT_BY_LATEST_APPT_DATE" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT * FROM (
                    SELECT V.CALL_LOG_ID,
                        V.SESSION_ID,
                        V.CALL_CENTER,
                        V.CALL_CENTER_TIME,
                        TO_CHAR(V.CALL_CENTER_TIME, 'MM/DD/YYYY HH:MI:SS PM') AS CALL_START_TIME,
                        TO_CHAR(V.CALL_CENTER_TIME + (V.CALL_DURATION / 86400), 'MM/DD/YYYY HH:MI:SS PM') AS CALL_END_TIME,
                        CASE V.EMAIL_CAPTURED WHEN '1' THEN 'Yes' ELSE ' ' END AS EMAIL_CAPTURED,
                        CASE V.WAITING_ON_INSURANCE WHEN '1' THEN 'Yes' ELSE ' ' END AS WAITING_ON_INSURANCE,
                        CASE V.IS_ORIGINAL WHEN 1 THEN 'Yes' ELSE ' ' END AS IS_ORIGINAL,
                        V.EMPLOYEE_NUMBER,
                        E.FIRST_NAME||' '||E.LAST_NAME AS AGENT_NAME,
                        V.AGENT_TYPE,
                        V.FACILITY_ID,
                        F.FACILITY_NAME,
                        (SELECT CASE CALL_CENTER_SUPPORTED WHEN 'Y' THEN 'Yes' ELSE ' ' END FROM BNDNEW.CALL_CENTER_SUPPORT WHERE FACILITY_ID = F.FACILITY_ID AND ROWNUM = 1) AS "SUPPORTED",
                        V.CLINIC_ID,
                        V.PATIENT_ID,
                        V.TEMP_PATIENT_ID,
                        CASE IS_LIBERTY
                            WHEN 0 THEN P.NAME_FIRST||' '||P.NAME_LAST
                            ELSE LP.FIRST_NAME||' '||LP.LAST_NAME
                        END AS PATIENT_NAME,

                        CASE IS_LIBERTY
                            WHEN 0 THEN P.PATIENT_ADDRESS1||' '||P.PATIENT_ADDRESS2
                            ELSE LPA.ADDRESS_LINE1||' '||LPA.ADDRESS_LINE2
                        END AS PATIENT_ADDRESS,

                        CASE IS_LIBERTY
                            WHEN 0 THEN
                                CASE WHEN P.PATIENT_CITY IS NULL THEN ' ' ELSE P.PATIENT_CITY||', '||P.PATIENT_STATE||' '||P.ZIPCODE END
                            ELSE CASE WHEN LPA.ADDRESS_CITY IS NULL THEN ' ' ELSE LPA.ADDRESS_CITY||', '||LPA.ADDRESS_STATE||' '||LPA.ZIP_CODE END
                        END AS CIT_ST_ZIP,

                        CASE IS_LIBERTY
                            WHEN 0 THEN P.PHONE_NUMBER1
                            ELSE LPP1.PHONE_NUMBER
                        END AS PHONE_NUMBER1,

                        CASE IS_LIBERTY
                            WHEN 0 THEN P.PHONE_NUMBER2
                            ELSE LPP2.PHONE_NUMBER
                        END AS PHONE_NUMBER2,

                        CASE IS_LIBERTY
                            WHEN 0 THEN PE.PATIENT_EMAIL
                            ELSE LPE.EMAIL_ADDRESS
                        END AS EMAIL_ADDRESS,

                        CASE IS_LIBERTY
                            WHEN 0 THEN P.DATE_OF_BIRTH
                            ELSE LP.DATE_OF_BIRTH
                        END AS DATE_OF_BIRTH,

                        CASE IS_LIBERTY
                            WHEN 0 THEN TRIM(P.GENDER)
                            ELSE TRIM(LP.GENDER)
                        END AS GENDER,

                        CASE IS_LIBERTY
                            WHEN 0 THEN
                                (CASE WHEN P.CLINIC_ID &lt; 1100 AND NVL(INSTR(P.ALERTS,'*', 1, 1),0) &gt; 0 THEN 'SPANISH'
                                    WHEN P.CLINIC_ID &gt; 1100 AND NVL(INSTR(P.ALERTS,'@', 1, 1),0) &gt; 0 THEN 'ENGLISH'
                                    WHEN P.CLINIC_ID IS NULL THEN ' '
                                ELSE 'ENGLISH' END)
                        ELSE LP.PATIENT_LANGUAGE_TYPE END AS PATIENT_LANGUAGE,

                        TRIM(NVL(AC.LIBERTY_PLAN_TYPE,' ')) AS PLAN_TYPE,
                        TRIM(NVL(AC.LIBERTY_ACCT_GROUP,' ')) AS PLAN_GROUP,
                        REPLACE(RS.REFERRAL_SOURCE_NAME, 'REF,', '') AS REFERRAL_SOURCE,
                        TRIM(V.PHONE_TYPE) AS PHONE_TYPE,
                        V.REASON_CODE,
                        V.RESOLUTION_CODE,
                        V.CALL_LOG_STATUS,
                        V.RING_TIME,
                        V.TALK_TIME,
                        V.HOLD_TIME,
                        V.WORK_TIME,
                        V.APPT_CLINIC_ID,
                        (CASE V.IS_LIBERTY WHEN 0 THEN (SELECT FACILITY_ID FROM BNDNEW.APPOINTMENTS WHERE CLINIC_ID = V.APPT_CLINIC_ID AND UNIQUE_ID = V.APPT_UNIQUE_ID AND APPT_DATE = V.APPT_DATE AND ROWNUM = 1)
                        ELSE (SELECT FACILITY_ID FROM LIBERTY.SCHEDULE_EVENT WHERE PATIENT_ID = V.PATIENT_ID AND START_DATETIME = V.APPT_DATE AND ROWNUM = 1)
                        END) AS APPT_FACILITY_ID,
                        V.APPT_PROVIDER_ID,
                        TRIM((CASE V.IS_LIBERTY WHEN 0 THEN (SELECT APPT_TYPE FROM BNDNEW.APPOINTMENTS WHERE CLINIC_ID = V.APPT_CLINIC_ID AND UNIQUE_ID = V.APPT_UNIQUE_ID AND APPT_DATE = V.APPT_DATE AND ROWNUM = 1)
                        ELSE NULL
                        END)) AS APPT_TYPE,
                        V.APPT_LOG_STATUS,
                        (CASE WHEN V.APPT_LOG_STATUS != 'NO_APPT_SCHEDULED'
                            THEN (SELECT appt.APPT_LOG_STATUS FROM APPT_CALL_LOG appt
                            WHERE appt.CALL_LOG_ID=v.CALL_LOG_ID AND appt.APPT_LOG_ID &lt; v.LAST_APPT_LOG_ID
                                AND NOT EXISTS (SELECT null FROM APPT_CALL_LOG appt2
                                    WHERE appt2.CALL_LOG_ID = appt.CALL_LOG_ID AND appt2.appt_log_id &gt; appt.APPT_LOG_ID AND appt2.APPT_LOG_ID &lt; v.LAST_APPT_LOG_ID)
                            )
                            ELSE null END) AS PREVIOUS_APPT_LOG_STATUS,
                        --TO_CHAR(V.APPT_DATE, 'MM/DD/YYYY HH:MI:SS PM') AS APPT_DATE,
                        V.APPT_DATE,
                        (CASE WHEN V.APPT_LOG_STATUS != 'NO_APPT_SCHEDULED'
                            THEN (SELECT appt.APPT_DATE --TO_CHAR(appt.APPT_DATE, 'MM/DD/YYYY HH:MI:SS PM')
                                    FROM APPT_CALL_LOG appt
                                    WHERE appt.CALL_LOG_ID = v.CALL_LOG_ID AND appt.APPT_LOG_ID &lt; v.LAST_APPT_LOG_ID AND appt.APPT_DATE IS NOT null
                                       AND NOT EXISTS (SELECT null FROM APPT_CALL_LOG appt2
                                            WHERE appt2.CALL_LOG_ID = appt.CALL_LOG_ID and appt2.APPT_LOG_ID &gt; appt.APPT_LOG_ID and appt2.APPT_LOG_ID &lt; v.LAST_APPT_LOG_ID
                                       AND appt2.APPT_DATE IS NOT null)
                            )
                            ELSE null END) AS LATEST_APPT_DATE,
                        V.TEAM_ID,
                        CASE V.SERVICE_LEVEL_MET WHEN 'Y' THEN 'Yes' ELSE '' END AS SLA,
                        CASE V.IS_ORTHO WHEN 1 THEN 'Yes' ELSE '' END AS IS_ORTHO,
                        CASE V.ALLOW_CREDIT WHEN 1 THEN 'Yes' ELSE '' END AS ALLOW_CREDIT,
                        (CASE WHEN V.APPT_LOG_STATUS != 'NO_APPT_SCHEDULED'
                            THEN (SELECT 'Yes' FROM APPT_CALL_LOG appt WHERE appt.CALL_LOG_ID = v.CALL_LOG_ID AND appt.APPT_LOG_STATUS='VERIFIED'
                                    AND NOT EXISTS (SELECT null FROM APPT_CALL_LOG appt2
                                        WHERE appt2.CALL_LOG_ID = appt.CALL_LOG_ID AND appt2.APPT_LOG_STATUS='VERIFIED' AND appt2.APPT_LOG_ID &gt; appt.APPT_LOG_ID))
                            ELSE '' END) AS WAS_VERIFIED

                    FROM V_AGENT_CALL_LOG V

                        JOIN BNDNEW.EMPLOYEE E
                            ON V.EMPLOYEE_NUMBER = E.EMPLOYEE_NUMBER

                        LEFT OUTER JOIN BNDNEW.PATIENTS P
                            ON V.CLINIC_ID = P.CLINIC_ID AND V.UNIQUE_ID = P.UNIQUE_ID AND V.IS_LIBERTY = 0
                        LEFT OUTER JOIN BNDNEW.PATIENTS_EMAIL PE
                            ON P.CLINIC_ID = PE.CLINIC_ID AND P.UNIQUE_ID = PE.UNIQUE_ID AND V.IS_LIBERTY = 0

                        LEFT OUTER JOIN LIBERTY.PATIENT LP
                            ON V.TEMP_PATIENT_ID = LP.PATIENT_ID AND V.IS_LIBERTY = 1
                        LEFT OUTER JOIN LIBERTY.PATIENT_EMAIL LPE
                            ON LP.HOME_EMAIL_ID = LPE.EMAIL_ID AND LP.PATIENT_ID = LPE.PATIENT_ID AND LPE.EMAIL_ACTIVE = 1
                        LEFT OUTER JOIN LIBERTY.PATIENT_ADDRESS LPA
                            ON LP.PATIENT_ID = LPA.PATIENT_ID AND LP.HOME_ADDRESS_ID = LPA.ADDRESS_ID AND LPA.ADDRESS_ACTIVE = 1
                        LEFT OUTER JOIN LIBERTY.PATIENT_PHONE LPP1 --LIBERTY HOME PHONE
                            ON LP.HOME_PHONE_ID = LPP1.PHONE_ID AND LP.PATIENT_ID = LPP1.PATIENT_ID AND LPP1.PHONE_ACTIVE = 1
                        LEFT OUTER JOIN LIBERTY.PATIENT_PHONE LPP2 --LIBERTY CELL PHONE
                            ON LP.MOBILE_PHONE_ID = LPP2.PHONE_ID AND LP.PATIENT_ID = LPP2.PATIENT_ID AND LPP1.PHONE_ACTIVE = 1

                        LEFT OUTER JOIN BNDNEW.FACILITY F
                            ON V.FACILITY_ID = F.FACILITY_ID

                        LEFT OUTER JOIN BNDNEW.ACCOUNT_TYPES AC
                            ON P.CLINIC_ID = AC.CLINIC_ID AND P.ACCOUNT_TYPE = AC.ACCOUNT_TYPE

                        LEFT OUTER JOIN RPTOWN.REFERRAL_SOURCES RS
                            ON P.REFERRAL_SOURCE_ID = RS.REFERRAL_SOURCE_ID
                    WHERE V.APPT_LOG_STATUS != 'NO_APPT_SCHEDULED'
                        AND EXISTS (SELECT null FROM APPT_CALL_LOG appt
                                        WHERE trunc(appt.APPT_DATE) between ? and ? AND appt.CALL_LOG_ID = v.CALL_LOG_ID AND appt.APPT_LOG_ID &lt; v.LAST_APPT_LOG_ID
                                            AND NOT EXISTS (SELECT null FROM APPT_CALL_LOG appt2
                                                                WHERE appt2.CALL_LOG_ID = appt.CALL_LOG_ID and appt2.APPT_LOG_ID &gt; appt.APPT_LOG_ID and appt2.APPT_LOG_ID &lt; v.LAST_APPT_LOG_ID
                                                                    AND appt2.APPT_DATE IS NOT null))
                    '##AGENT_QUERY##'
                ) V
                '##ORDER_BY##'
            </value>
        </constructor-arg>
    </bean>
    <bean id="GET_SUPERVISED_AGENT" class="java.lang.String">
        <constructor-arg>
            <value type="java.lang.String">
                SELECT EMP_SUPERVISOR_ID, cce.EMPLOYEE_NUMBER, emp.FIRST_NAME || ' ' || emp.LAST_NAME as EMPLOYEE_NAME,
                    cce.SUPERVISOR_EMPLOYEE_NUMBER, manager.FIRST_NAME || ' ' || manager.LAST_NAME as SUPERVISOR_EMPLOYEE_NAME,
                    CASE WHEN (emp.FACILITY_ID = 83000) THEN 'Plano' ELSE 'Irvine' END AS CALL_CENTER
                FROM CALL_CENTER_EMPLOYEE cce
                    JOIN EMPLOYEE emp on emp.EMPLOYEE_NUMBER = cce.EMPLOYEE_NUMBER AND emp.EMPLOYMENT_STATUS = 'Active'
                    JOIN EMPLOYEE manager on manager.EMPLOYEE_NUMBER = cce.SUPERVISOR_EMPLOYEE_NUMBER AND manager.EMPLOYMENT_STATUS = 'Active'
                WHERE cce.IS_ACTIVE = 1
                START WITH cce.SUPERVISOR_EMPLOYEE_NUMBER = ? AND cce.IS_ACTIVE = 1
                CONNECT BY PRIOR cce.EMPLOYEE_NUMBER = cce.SUPERVISOR_EMPLOYEE_NUMBER
                ORDER BY EMPLOYEE_NAME
            </value>
        </constructor-arg>
    </bean>
    <bean id="CHECK_IF_APPT_CALL_LOG_EXIST" class="java.lang.String">
        <constructor-arg>
            <value type="java.lang.String">
                SELECT * FROM V_AGENT_CALL_LOG v WHERE ALLOW_CREDIT = 1 AND TEMP_PATIENT_ID = ? AND FACILITY_ID = ? AND TO_CHAR(APPT_DATE,'MM-dd-YYYY HH24:MI') = ?
                '##APPT_LOG_ID##'
            </value>
        </constructor-arg>
    </bean>

    <bean id="GET_TEAM_HIERARCHY" class="java.lang.String">
        <constructor-arg>
            <value type="java.lang.String">
                SELECT DISTINCT EMP_SUPERVISOR_ID, cce.EMPLOYEE_NUMBER, emp.FIRST_NAME || ' ' || emp.LAST_NAME as EMPLOYEE_NAME,
                    cce.SUPERVISOR_EMPLOYEE_NUMBER, manager.FIRST_NAME || ' ' || manager.LAST_NAME as SUPERVISOR_EMPLOYEE_NAME,
                    CASE WHEN (emp.FACILITY_ID = 83000) THEN 'Plano' ELSE 'Irvine' END AS CALL_CENTER
                FROM CALL_CENTER_EMPLOYEE cce
                    JOIN EMPLOYEE emp on emp.EMPLOYEE_NUMBER = cce.EMPLOYEE_NUMBER AND emp.EMPLOYMENT_STATUS = 'Active'
                    JOIN EMPLOYEE manager on manager.EMPLOYEE_NUMBER = cce.SUPERVISOR_EMPLOYEE_NUMBER AND manager.EMPLOYMENT_STATUS = 'Active'
                WHERE cce.IS_ACTIVE = 1
                <!--START WITH manager.DEPARTMENT_DESC = 'Executive'-->
                CONNECT BY PRIOR cce.EMPLOYEE_NUMBER = cce.SUPERVISOR_EMPLOYEE_NUMBER
                ORDER SIBLINGS BY SUPERVISOR_EMPLOYEE_NAME, EMPLOYEE_NAME
            </value>
        </constructor-arg>
    </bean>
    <bean id="GET_CALL_CENTER_EMPLOYEE" class="java.lang.String">
        <constructor-arg>
            <value type="java.lang.String">
                SELECT em.EMPLOYEE_NUMBER, em.FIRST_NAME || ' ' || em.LAST_NAME as EMPLOYEE_NAME, em.STATE,
                    CASE WHEN (em.FACILITY_ID = 83000) THEN 'Plano Call Center' ELSE 'Irvine Call Center' END AS CALL_CENTER
                FROM CALL_CENTER_RESOURCE cr
                    JOIN EMPLOYEE em on em.EMPLOYEE_NUMBER = cr.RESOURCE_LOGIN_ID
                WHERE cr.DATE_INACTIVE IS NULL
                    AND em.DEPARTMENT_CODE IN ('CCR', 'CBO')
                    AND em.EMPLOYMENT_STATUS = 'Active'
                ORDER BY STATE, EMPLOYEE_NAME
            </value>
        </constructor-arg>
    </bean>
    <bean id="GET_CALL_CENTER_TEAM_LEAD" class="java.lang.String">
        <constructor-arg>
            <value type="java.lang.String">
                SELECT em.EMPLOYEE_NUMBER, em.FIRST_NAME || ' ' || em.LAST_NAME as EMPLOYEE_NAME, em.STATE,
                    CASE WHEN (em.FACILITY_ID = 83000) THEN 'Plano' ELSE 'Irvine' END AS CALL_CENTER
                FROM CALL_CENTER_SUPERVISOR sv
                JOIN EMPLOYEE em on em.EMPLOYEE_NUMBER = sv.RESOURCELOGINID
                WHERE sv.DATEINACTIVE IS NULL
                    AND em.DEPARTMENT_CODE in ('CCR', 'CBO')
                    AND em.EMPLOYMENT_STATUS = 'Active'
                ORDER BY STATE, EMPLOYEE_NAME
            </value>
        </constructor-arg>
    </bean>
    <bean id="GET_CALL_CENTER_EMPLOYEE_BY_NUMBER" class="java.lang.String">
        <constructor-arg>
            <value type="java.lang.String">
                SELECT cce.*, emp.FIRST_NAME || ' ' || emp.LAST_NAME as EMPLOYEE_NAME,
                    emp.FIRST_NAME, emp.LAST_NAME, emp.EMAIL_ADDRESS,
                    manager.FIRST_NAME || ' ' || manager.LAST_NAME as SUPERVISOR_EMPLOYEE_NAME,
                    CASE WHEN (emp.FACILITY_ID = 83000) THEN 'Plano' ELSE 'Irvine' END AS CALL_CENTER
                FROM CALL_CENTER_EMPLOYEE cce
                    JOIN EMPLOYEE emp on emp.EMPLOYEE_NUMBER = cce.EMPLOYEE_NUMBER AND emp.EMPLOYMENT_STATUS = 'Active'
                    JOIN EMPLOYEE manager on manager.EMPLOYEE_NUMBER = cce.SUPERVISOR_EMPLOYEE_NUMBER AND manager.EMPLOYMENT_STATUS = 'Active'
                WHERE IS_ACTIVE = 1
                    AND NVL(?, cce.EMPLOYEE_NUMBER) = cce.EMPLOYEE_NUMBER
            </value>
        </constructor-arg>
    </bean>
    <bean id="GET_EMPLOYEE_INFO_BY_NUMBER" class="java.lang.String">
        <constructor-arg>
            <value type="java.lang.String">
                SELECT emp.EMPLOYEE_NUMBER,
                    emp.FIRST_NAME || ' ' || emp.LAST_NAME as EMPLOYEE_NAME,
                    emp.FIRST_NAME, emp.LAST_NAME, emp.EMAIL_ADDRESS,
                    manager.EMPLOYEE_NUMBER as SUPERVISOR_EMPLOYEE_NUMBER,
                    manager.FIRST_NAME || ' ' || manager.LAST_NAME as SUPERVISOR_EMPLOYEE_NAME,
                    CASE WHEN (emp.FACILITY_ID = 83000) THEN 'Plano' ELSE 'Irvine' END AS CALL_CENTER
                FROM EMPLOYEE emp
                    JOIN EMPLOYEE manager on manager.EMPLOYEE_NUMBER = emp.MANAGER_EMPLOYEE_NUMBER AND manager.EMPLOYMENT_STATUS = 'Active'
                WHERE emp.EMPLOYMENT_STATUS = 'Active'
                    AND emp.EMPLOYEE_NUMBER = ?
            </value>
        </constructor-arg>
    </bean>
    <bean id="INACTIVATE_CALL_CENTER_EMPLOYEE" class="java.lang.String">
        <constructor-arg>
            <value type="java.lang.String">
                UPDATE CALL_CENTER_EMPLOYEE SET IS_ACTIVE = 0, UPDATE_DATETIME = SYSDATE WHERE EMP_SUPERVISOR_ID = ?
            </value>
        </constructor-arg>
    </bean>
    <bean id="INACTIVATE_ACTIVE_CALL_CENTER_EMPLOYEE" class="java.lang.String">
        <constructor-arg>
            <value type="java.lang.String">
                UPDATE CALL_CENTER_EMPLOYEE SET IS_ACTIVE = 0, UPDATE_DATETIME = SYSDATE WHERE EMPLOYEE_NUMBER = ? AND IS_ACTIVE = 1
            </value>
        </constructor-arg>
    </bean>
    <bean id="INSERT_CENTER_EMPLOYEE_BY_NUMBER" class="java.lang.String">
        <constructor-arg>
            <value type="java.lang.String">
                INSERT INTO CALL_CENTER_EMPLOYEE VALUES (CC_EMP_SEQ.NEXTVAL, ?, ?, 1, 0, SYSDATE, NULL)
            </value>
        </constructor-arg>
    </bean>
    <bean id="GET_CALL_LOG_EDITED" class="java.lang.String">
        <constructor-arg>
            <value type="java.lang.String">
                SELECT * FROM APPT_CALL_LOG appt WHERE appt.APPT_LOG_STATUS = 'UNVERIFIED' AND TO_CHAR(CREATE_DATETIME, 'MM-DD-YYYY') = ?
                    AND EXISTS (SELECT NULL FROM APPT_CALL_LOG appt2 where appt2.CALL_LOG_ID = appt.CALL_LOG_ID and appt2.APPT_LOG_STATUS = 'VERIFIED'
                        AND appt2.CREATE_DATETIME &lt; appt.CREATE_DATETIME)
            </value>
        </constructor-arg>
    </bean>

    <bean id="emailAccountSql" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT TRIM(FAC_ATTR_VALUE) as EMAIL
                FROM FACILITY_ATTR_VALUES
                WHERE FAC_ATTRIBUTE_ID = 8
                AND FACILITY_ID = ?
                AND INACTIVE_DATETIME IS NULL
            </value>
        </constructor-arg>
    </bean>

    <bean id="ccEmailAccountSql" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT TRIM(FAC_ATTR_VALUE) as EMAIL
                FROM FACILITY_ATTR_VALUES
                WHERE FAC_ATTRIBUTE_ID = 146
                AND FACILITY_ID = ?
                AND INACTIVE_DATETIME IS NULL
            </value>
        </constructor-arg>
    </bean>

    <bean id="GET_OPEN_BOOK_SUPPORTED_FACILITES" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT F.FACILITY_ID, F.FACILITY_NAME
                FROM FACILITY_ATTR_VALUES V
                JOIN FACILITY F ON V.FACILITY_ID = F.FACILITY_ID
                WHERE FAC_ATTRIBUTE_ID = 159782
                AND INACTIVE_DATETIME IS NULL
            </value>
        </constructor-arg>
    </bean>

    <bean id="CDW_FACILITY_SUPPORT" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                select facility_id,psr_supported,csr_supported, psr_queue_priority, csr_queue_priority
                from cdw.facility
            </value>
        </constructor-arg>
    </bean>

    <bean id="CDW_SPAM_PHONE_NUMBERS" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                select phone_number, date_added, description, is_spam, redirect_to_csr, redirect_queue
                from cisco.cis_spam_phone_numbers
            </value>
        </constructor-arg>
    </bean>

    <bean id="CDW_CISCO_AGENT_CALL_BY_DATE" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                select
                    acd.sessionid, acd.agentloginid
                    , (acd.startdatetime at time zone 'UTC') as startDate, (acd.enddatetime at time zone 'UTC') as endDate
                    , coalesce(ccd.originatordn, '0') ani
                    <!--, coalesce(csq.csqname, '') as queueName-->
                from cisco.cis_agentconnectiondetail acd
                    join cisco.cis_contactcalldetail ccd on acd.sessionid = ccd.sessionid and acd.sessionseqnum = ccd.sessionseqnum
                    <!--left join cisco.cis_contactqueuedetail cqd on cqd.sessionid = ccd.sessionid and cqd.sessionseqnum = ccd.sessionseqnum and cqd.nodeid = ccd.nodeid-->
                    <!--left join cisco.cis_contactservicequeue csq on csq.recordid = cqd.targetid and csq.queuetype = cqd.targettype-->
                where (acd.startdatetime at time zone 'UTC')::date = #DATE#
                    and acd.agentloginid in (#AGENT_IDS#)
                group by acd.sessionid, acd.agentloginid, startDate, endDate, ani
                    <!--, queueName-->
                order by startDate, endDate

            </value>
        </constructor-arg>
    </bean>
    <bean id="GET_EMAIL_FOR_REPORT" class="java.lang.String">
        <constructor-arg type="java.lang.String">
            <value>
                SELECT EMAIL_ADDRESS
                FROM dashboard.EMAIL_RECIPIENT
                WHERE REPORT_NAME = ?
                AND IS_ACTIVE = 1
            </value>
        </constructor-arg>
    </bean>

</beans>