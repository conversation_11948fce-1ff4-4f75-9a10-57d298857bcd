<?xml version="1.0" encoding="UTF-8"?>
<web-app version="2.5"
         xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd">
    <display-name>Call Center Support</display-name>

    <welcome-file-list>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>

    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>
            classpath:jndi-references.xml,
            classpath:spring-mongo.xml,
            classpath:spring-services.xml,
            classpath:spring-jdbc.xml,
            classpath:spring-jpa.xml
        </param-value>
    </context-param>
    <!-- initialization of the IoC container -->
    <listener>
        <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
    </listener>

    <!-- Security filter commented out for local development
    <filter>
        <filter-name>securityFilter</filter-name>
        <filter-class>com.smilebrands.security.web.filter.SecurityFilter</filter-class>
        <init-param>
            <param-name>ApplicationName</param-name>
            <param-value>CALL_CENTER_SUPPORT</param-value>
        </init-param>
        <init-param>
            <param-name>TimeOutInMinutes</param-name>
            <param-value>60</param-value>
        </init-param>
        <init-param>
            <param-name>IgnoreList</param-name>
            <param-value>.css,.js,.jpg,.gif,.png,pulse-check</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>securityFilter</filter-name>
        <url-pattern>/secure/*</url-pattern>
    </filter-mapping>
    -->

    <!-- JPA Filter -->
    <filter>
        <filter-name>JpaFilter</filter-name>
        <filter-class>org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>JpaFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- Incomplete Request Filter -->
    <filter>
        <filter-name>incompleteRequestFilter</filter-name>
        <filter-class>com.smilebrands.callcenter.web.filter.IncompleteRequestFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>incompleteRequestFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- Processes application requests -->
    <servlet>
        <servlet-name>appServlet</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>/WEB-INF/ctx/spring-mvc-context.xml</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet-mapping>
        <servlet-name>appServlet</servlet-name>
        <url-pattern>/</url-pattern>
    </servlet-mapping>

    <!-- Precompile JSP -->
    <servlet id="jsp">
        <servlet-name>jsp</servlet-name>
        <servlet-class>org.apache.jasper.servlet.JspServlet</servlet-class>
        <init-param>
            <param-name>development</param-name>
            <param-value>true</param-value>
        </init-param>
        <init-param>
            <param-name>keepgenerated</param-name>
            <param-value>true</param-value>
        </init-param>
        <init-param>
            <param-name>enablePooling</param-name>
            <param-value>true</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>jsp</servlet-name>
        <url-pattern>*.jsp</url-pattern>
    </servlet-mapping>

    <session-config>
        <session-timeout>30</session-timeout>
    </session-config>

    <!-- JSTL Tag Library Descriptors -->
    <jsp-config>
        <taglib>
            <taglib-uri>http://java.sun.com/jsp/jstl/core</taglib-uri>
            <taglib-location>/WEB-INF/lib/jstl-1.2.jar</taglib-location>
        </taglib>
        <taglib>
            <taglib-uri>http://java.sun.com/jsp/jstl/fmt</taglib-uri>
            <taglib-location>/WEB-INF/lib/jstl-1.2.jar</taglib-location>
        </taglib>
        <taglib>
            <taglib-uri>http://java.sun.com/jsp/jstl/functions</taglib-uri>
            <taglib-location>/WEB-INF/lib/jstl-1.2.jar</taglib-location>
        </taglib>
        <taglib>
            <taglib-uri>http://java.sun.com/jsp/jstl/sql</taglib-uri>
            <taglib-location>/WEB-INF/lib/jstl-1.2.jar</taglib-location>
        </taglib>
        <taglib>
            <taglib-uri>http://java.sun.com/jsp/jstl/xml</taglib-uri>
            <taglib-location>/WEB-INF/lib/jstl-1.2.jar</taglib-location>
        </taglib>
    </jsp-config>
    <!-- JSF Configuration -->
    <servlet>
        <servlet-name>Faces Servlet</servlet-name>
        <servlet-class>javax.faces.webapp.FacesServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>Faces Servlet</servlet-name>
        <url-pattern>*.jsf</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>Faces Servlet</servlet-name>
        <url-pattern>*.xhtml</url-pattern>
    </servlet-mapping>

    <!-- JSF Context Parameters -->
    <context-param>
        <param-name>javax.faces.DEFAULT_SUFFIX</param-name>
        <param-value>.xhtml</param-value>
    </context-param>
    <context-param>
        <param-name>javax.faces.PROJECT_STAGE</param-name>
        <param-value>Development</param-value>
    </context-param>

    <!-- Error Pages Configuration -->
    <error-page>
        <error-code>500</error-code>
        <location>/WEB-INF/views/error/500.jsp</location>
    </error-page>
    <error-page>
        <error-code>400</error-code>
        <location>/WEB-INF/views/error/400.jsp</location>
    </error-page>
    <error-page>
        <exception-type>java.lang.IllegalStateException</exception-type>
        <location>/WEB-INF/views/error/incomplete-request.jsp</location>
    </error-page>
    <error-page>
        <exception-type>org.eclipse.jetty.http.BadMessageException</exception-type>
        <location>/WEB-INF/views/error/bad-message.jsp</location>
    </error-page>

</web-app>