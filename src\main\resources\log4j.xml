<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">

<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/">

    <!-- Appenders -->
    <appender name="CONSOLE" class="org.apache.log4j.ConsoleAppender">
        <param name="Target" value="System.out" />
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d [%t] %-5p %c - %m%n" />
        </layout>
    </appender>

    <!-- Core Loggers. -->
    <logger name="com.smilebrands">
        <level value="DEBUG" />
        <appender-ref ref="CONSOLE"/>
    </logger>

    <!-- Cache Logging -->
    <logger name="com.hazelcast">
        <level value="INFO" />
        <appender-ref ref="CONSOLE"/>
    </logger>

    <!-- Hibernate Loggers -->
    <logger name="org.hibernate.SQL">
        <level value="INFO" />
    </logger>
    <logger name="org.hibernate.sql">
        <level value="INFO" />
    </logger>
    <logger name="org.hibernate.transaction">
        <level value="INFO" />
    </logger>
    <logger name="org.hibernate.mapping">
        <level value="WARN" />
    </logger>

    <!-- Package Specific Loggers -->
    <logger name="org.springframework">
        <level value="INFO" />
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="org.springframework.orm">
        <level value="WARN" />
    </logger>
    <logger name="org.springframework.web">
        <level value="INFO" />
    </logger>
    <logger name="org.springframework.data.rest.webmvc">
        <level value="INFO" />
    </logger>
    <logger name="org.springframework.security.ldap">
        <level value="WARN" />
    </logger>
    <logger name="com.mchange">
        <level value="WARN" />
    </logger>
    <logger name="org.apache.http">
        <level value="WARN" />
    </logger>

    <!-- Root Logger -->
    <root>
        <level value="INFO" />
    </root>

</log4j:configuration>