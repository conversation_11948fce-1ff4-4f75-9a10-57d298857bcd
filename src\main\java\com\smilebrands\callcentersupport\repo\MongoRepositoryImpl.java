package com.smilebrands.callcentersupport.repo;

import com.mongodb.*;
import com.smilebrands.callcentersupport.domain.*;
import com.smilebrands.callcentersupport.domain.helper.DistanceComparator;
import com.smilebrands.callcentersupport.domain.helper.MapToFacility;
import com.smilebrands.callcentersupport.domain.helper.SequenceId;
import com.smilebrands.callcentersupport.domain.partner.PartnerConfig;
import com.smilebrands.callcentersupport.domain.report.InboundCallSummary;
import com.smilebrands.callcentersupport.domain.schedule.Task;
import com.smilebrands.callcentersupport.service.ScheduleServiceImpl;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Repository;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Calendar;
import java.util.regex.Pattern;

/**
 * User: Marlin Clark
 * Date: 6/25/14
 */
@Repository
public class MongoRepositoryImpl implements MongoRepository {

    private final static String NEXT_ACQUIRED_ON_FORMAT = "yyyy/MM/dd HH:mm:ss";
    private final static int MAX_TRIAL = 3;

    static DateFormat dateFormat = new SimpleDateFormat("MM-dd-yyyy");
    static JavaMailSenderImpl mailSender  = new JavaMailSenderImpl();
    static{
        // -- initialize the email sender SMTP address.
        mailSender.setHost("smtprelay.bnd.corp");
    }

    final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private MongoOperations mongoOperations;

    @Autowired
    private FacilityRepository facilityRepository;

    @Autowired
    private JdbcRepository jdbcRepository;

    public static String SYNC_TOKEN = "98b2da3c05447caf160d4ec0f9abae0c";

    @Override
    public void rebuildMongoEnvironment() {
        // -- Load Facilities
        logger.info("Start Facility Collection rebuild.");
        Date start = new Date();

        List<Facility> facs = facilityRepository.findAllFacilities(true);
        for (Facility facility : facs) {
            Facility existing = findFacilityDetail(facility.getFacilityId());
            if(existing != null){
                facility.setMessage(existing.getMessage());
            }
            PhoneNumber phoneNumber = findPhoneNumberByFacility(facility.getFacilityId(), "ML");
            if(phoneNumber != null){
                facility.setPhoneNumber(phoneNumber.getPhoneNumber());
            }
            persistFacilityDetail(facility);
        }
        logger.info("Facility Collection load is complete loading " + facs.size() + " Facility records.");

        // -- Load Campaigns
        logger.info("Start Campaign Collection rebuild.");
        logger.info("Campaign Collection load is complete.");

        // -- Load Phone Numbers from Cisco
        logger.info("Start Phone Number Collection rebuild.");
        logger.info("Phone Number Collection load is complete.");

        // -- Call Reason & Resolution Codes
        logger.info("Start Call Reason & Resolution Collection rebuild.");
        logger.info("Call Reason & Resolution Collection load is complete loading.");

        // -- Load Zip to Facilities
        int z2f = rebuildZipCodeFacilityAssociations();

        int cce = rebuildCallCenterEmployees();

        // -- Sync Spam Numbers added to Postgres
        logger.debug("About to get spamNumbers");
        List<PhoneNumberRoutingRules> spamNumbers = jdbcRepository.getSpamPhoneNumbers();
        int size = spamNumbers.size();
        logger.debug("Got " + size + " spam numbers");
        int cnt = 0;
        for (PhoneNumberRoutingRules spamRule : spamNumbers) {
            mongoOperations.save(spamRule);
            cnt++;
            logger.debug("Saved " + cnt + " out of " + size + " spam numbers");
        }

        logger.debug("About to purge archived action logs");
        int purgedArchivedLog = purgeArchivedLog();
        logger.debug("Purged " + purgedArchivedLog + " archived logs..");

        logger.debug("About to get action logs");
        List<ActionLog> logs = getActionLogToArchive(new Date());
        size = logs.size();
        cnt = 0;
        for(ActionLog log : logs){
            archiveLog(log);
            cnt++;
            logger.debug("Archived " + cnt + " out of " + size + " action logs");
        }
        
        //THE JOB IS SCHEDULED TO RUN @2AM IN ORDER TO AVOID PULLING SAME-INACTIVE-DATE FACILITY ALERT
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -1);
        logger.debug("about to get agent calls");
        List<AgentCall> agentCalls = searchAgentCalls(VelocityUtils.getDateAsString(cal.getTime(), null), null, 0);
        int numberOfAgentCall = agentCalls.size();
        int numberOfCallLog = 0;
        int numberOfMissingCallLog = 0;
        int numberOfAppointments = 0;
        int numberOfMissingAppointments = 0;
        cnt = 0;
        size = agentCalls.size();
        logger.debug("got " + size + " agent calls");
        for(AgentCall agentCall : agentCalls){
            for(LoadedEmployee le : agentCall.getLoadedEmployees()){
                if(le.getCallLogId() != null){
                    numberOfCallLog++;
                }else{
                    numberOfMissingCallLog++;
                    logger.debug("missing call log for employee[" + le.getEmployeeNumber() + "] with UUID[" + agentCall.getUuid() + "]");
                }
            }
            for(Patient patient : agentCall.getPatients()){
                if(patient.getAppointmentCallLogId() != null){
                    numberOfAppointments++;
                }else{
                    numberOfMissingAppointments++;
                    logger.debug("missing appointment call log for UUID[" + agentCall.getUuid() + "] with Patient ID[" + patient.getTempPatientId() + "] " +
                            "and employee number[" + patient.getEmpEntered() + "] " +
                            "at " + patient.getFacilityId() + " on [" + patient.getNextAppointmentDate() + " " + patient.getNextAppointmentTime() + "]");
                }
            }
            cnt++;
            logger.debug("process " + cnt + " out of " + size + " agent calls");
        }

        logger.debug("about to get appointment call logs");
        List<AppointmentCallLog> editedApptLogs = jdbcRepository.getEditedAppointmentCallLog(new Date());
        Set<Long> callLogIds = new HashSet<Long>();
        cnt = 0;
        size = editedApptLogs.size();
        logger.debug("got " + size + " edited appointment call logs");
        for(AppointmentCallLog appt : editedApptLogs){
            callLogIds.add(appt.getCallLogId());
            cnt++;
            logger.debug("processed " + cnt + " out of " + size + " edited appointment call logs");
        }

        // -- Email the results.
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setTo(new String[]{"<EMAIL>"});
            helper.setFrom("<EMAIL>");
            helper.setSubject("Call Center Service Update -- " + new Date().toString());
            helper.setText(toEmail(facs.size(), 0, 0, z2f, numberOfAgentCall, numberOfCallLog, numberOfMissingCallLog, numberOfAppointments, numberOfMissingAppointments, callLogIds.size(), spamNumbers.size(), purgedArchivedLog), true);

            mailSender.send(message);
            logger.info("Summary email has been sent.");

        } catch (MessagingException e) {
            logger.warn("Unable to send summary email!");
            e.printStackTrace();
        }
        logger.debug("process start: " + start);
        logger.debug("process end: " + new Date());

    }

    @Override
    public int rebuildZipCodeFacilityAssociations() {
        Map<String, List<Facility>> zipFacilities = new HashMap<String, List<Facility>>();
        List<Map<String, Object>> zips = jdbcRepository.getZipCodeFacilityRecords();
        logger.debug("Loading " + zips.size() + " Zip 2 Facility associations.");

        for (Map<String, Object> record : zips) {
            String zip = zipToString(record.get("ZIP_CODE").toString());

            if (! zipFacilities.containsKey(zip)) {
                List<Facility> _facs = new ArrayList<Facility>();
                _facs.add(MapToFacility.convert(record));
                zipFacilities.put(zip, _facs);
            } else {
                zipFacilities.get(zip).add(MapToFacility.convert(record));
            }
        }

        logger.debug(zips.size() + " Zip Facility Entries have been converted into " + zipFacilities.size() + " unique Zip to Facilities.");
        int z2fcnt = 0;
        int keySize = zipFacilities.keySet().size();
        for (String key : zipFacilities.keySet()) {

            ZipFacility zf = new ZipFacility();
            zf.setZipCode(key);
            zf.setFacilities(zipFacilities.get(key));

            mongoOperations.save(zf);
            z2fcnt++;
            logger.debug("Processed " + z2fcnt + " out of " + keySize + " zip facilities");
        }

        return z2fcnt;
    }

    @Override
    public int rebuildCallCenterEmployees() {
        List<CallCenterEmployee> callCenterEmployees = jdbcRepository.getCallCenterEmployee(false);
        logger.debug("Loading " + callCenterEmployees.size() + " Call Center Employees");
        List<Object> currentEmployeeNumbers = getDistinctField("callCenterEmployee", "employeeNumber");
        int cnt = 0;
        for(CallCenterEmployee cce : callCenterEmployees){
            persistCallCenterEmployee(cce);
            cnt++;
            int idx = currentEmployeeNumbers.indexOf(cce.getEmployeeNumber());
            if(idx != -1){
                currentEmployeeNumbers.remove(idx);
            }
        }
        for(Object obj : currentEmployeeNumbers){
            CallCenterEmployee cce = findByEmployeeNumber((Integer)obj);
            if(cce != null){
                cce.setActive(false);
                cce.setInactiveDateTime(new Date());
                persistCallCenterEmployee(cce);
            }
        }
        return cnt;
    }

    @Override
    public Facility persistFacilityDetail(Facility facility) {
        mongoOperations.save(facility);
        return facility;
    }

    @Override
    public Facility findFacilityDetail(Integer facilityId) {
        return mongoOperations.findById(facilityId, Facility.class);
    }

    @Override
    public List<Facility> findFacilitiesByZipCode(String zipCode, Integer limit) {
        List<Facility> facilities = new ArrayList<Facility>();

        ZipFacility zf = mongoOperations.findById(zipCode, ZipFacility.class);
        if(zf != null && zf.getFacilities() != null){
            Collections.sort(zf.getFacilities(), new DistanceComparator());

            for (int x=0; x < limit && x < zf.getFacilities().size(); x++) {
                facilities.add(zf.getFacilities().get(x));
            }
        }
        return facilities;
    }

    @Override
    public List<Facility> findAllFacility() {
        return mongoOperations.findAll(Facility.class);
    }

    @Override
    public CallCenterEmployee findByEmployeeNumber(Integer employeeNumber) {
        List<CallCenterEmployee> cces =  mongoOperations.find(new Query(Criteria.where("isActive").is(true)
                                                                                        .and("employeeNumber").is(employeeNumber))
                                                                , CallCenterEmployee.class);
        return cces.size() > 0 ? cces.get(0) : null;
    }

    @Override
    public CallCenterEmployee persistCallCenterEmployee(CallCenterEmployee cce) {
        mongoOperations.save(cce);
        return cce;
    }

    @Override
    public PhoneNumber persistPhoneNumber(PhoneNumber number) {
        mongoOperations.save(number);
        return number;
    }

    @Override
    public PhoneNumber findPhoneNumber(Long number) {
        return mongoOperations.findById(number, PhoneNumber.class);
    }

    @Override
    public PhoneNumber findPhoneNumberByFacility(Integer facilityId) {
        List<PhoneNumber> numbers = mongoOperations.find(new Query(Criteria.where(("isActive")).is(true).and("facilityId").is(facilityId)), PhoneNumber.class);
        PhoneNumber result = null;
        for(PhoneNumber number : numbers){
            if(result == null || number.getIsDefault()){
                result = number;
            }
        }
        return result;
    }

    @Override
    public PhoneNumber findPhoneNumberByFacility(Integer facilityId, String phoneType) {
        List<PhoneNumber> numbers = mongoOperations.find(new Query(Criteria.where(("isActive")).is(true).and("facilityId").is(facilityId).and("phoneType").is(phoneType)), PhoneNumber.class);
        PhoneNumber result = null;
        for(PhoneNumber number : numbers){
            if(result == null || number.getIsDefault()){
                result = number;
            }
        }
        return result;
    }

    @Override
    public List<PhoneNumber> findAllPhoneNumbers() {
        return mongoOperations.findAll(PhoneNumber.class);
    }

    @Override
    public Campaign persistCampaign(Campaign campaign) {
        mongoOperations.save(campaign);
        return campaign;
    }

    @Override
    public Campaign findCampaign(Long number) {
        return mongoOperations.findById(number, Campaign.class);
    }

    @Override
    public List<Campaign> findAllCampaign() {
        return mongoOperations.findAll(Campaign.class);
    }

    @Override
    public Long countConfiguredPhoneNumbers() {
        return mongoOperations.count(new Query(), PhoneNumber.class);
    }

    @Override
    public Long countConfiguredFacilities() {
        return mongoOperations.count(new Query(), Facility.class);
    }

    @Override
    public AgentCall searchAgentCallByAni(String uuid, Long ani) {
        Query query = new Query(Criteria.where(("ani")).is(ani).and("uuid").ne(uuid));
        query.with(new Sort(Sort.Direction.DESC, "screenPopOpenDateTime"));

        List<AgentCall> agentCall = mongoOperations.find(query, AgentCall.class);
        if (agentCall.size() > 0) {
            return agentCall.get(0);
        }

        return null;
    }

    @Override
    public AgentCall registeredAgentCall(AgentCall agentCall) {
        mongoOperations.save(agentCall);
        return agentCall;
    }

    @Override
    public AgentCall updateAgentCall(AgentCall agentCall) {
        mongoOperations.save(agentCall);
        return agentCall;
    }

    @Override
    public AgentCall getAgentCall(String uuid) {
        return mongoOperations.findById(uuid, AgentCall.class);
    }

    @Override
    public int purgeAgentCall() {
        //Keep 18 months of agent calls
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, -19);
        return purgeInboundCall(cal.get(Calendar.MONTH) + 1, cal.get(Calendar.YEAR));
    }

    @Override
    public int purgeAgentCall(int month, int year) {
        Pattern timePeriodRegex = buildTimePeriodRegex(month, year);
        Criteria criteria = Criteria.where("callRegisteredDate").regex(timePeriodRegex);
        return purgeEntityByTimePeriod(criteria, AgentCall.class);
    }

    @Override
    public List<AgentCall> searchAgentCalls(String registeredDate, List<Integer> employeeNumbers, int numberOfTrial) {
        logger.debug("Search Agent Calls for date[" + registeredDate + "] and employee number[" + employeeNumbers + "]");
        List<AgentCall> agentCalls = null;
        try {
            Criteria criteria = Criteria.where("callRegisteredDate").is(registeredDate);
            if (employeeNumbers != null && employeeNumbers.size() > 0) {
                criteria.and("loadedEmployees.employeeNumber").in(employeeNumbers);
            }
            Query query = new Query(criteria);
            query.with(new Sort(Sort.Direction.ASC, "callRegisteredTime"));
            agentCalls = mongoOperations.find(query, AgentCall.class);
        } catch(Exception ex) {
            ex.printStackTrace();
            numberOfTrial++;
            if(numberOfTrial >= MAX_TRIAL){
                logger.debug("Fail to search agentCall.");
            }else{
                logger.debug("Try to search agentCalls from MongoDB " + numberOfTrial + " times.");
                agentCalls = searchAgentCalls(registeredDate, employeeNumbers, numberOfTrial);
            }
        }
        return agentCalls;
    }

    @Override
    public InboundCall getInboundCall(String uuid) {
        return mongoOperations.findById(uuid, InboundCall.class);
    }

    @Override
    public InboundCall getInboundCallBySessionId(String sessionId) {
        return mongoOperations.findOne(new Query(Criteria.where("sessionId").is(sessionId)), InboundCall.class);
    }

    @Override
    public int purgeInboundCall() {
        //Keep 18 months of inbound calls
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, -19);
        return purgeInboundCall(cal.get(Calendar.MONTH) + 1, cal.get(Calendar.YEAR));
    }

    @Override
    public int purgeInboundCall(int month, int year) {
        Pattern timePeriodRegex = buildTimePeriodRegex(month, year);
        Criteria criteria = Criteria.where("createDate").regex(timePeriodRegex);
        return purgeEntityByTimePeriod(criteria, InboundCall.class);
    }

    @Override
    public void updateInboundCall(InboundCall call) {
        mongoOperations.save(call);
    }

    @Override
    public InboundCall registerInboundCall(InboundCall inboundCall) {
        logger.debug("Attempting to persist InboundCall with UUID [" + inboundCall.getUuid() + "] ... ");
        mongoOperations.save(inboundCall);
        logger.debug("InboundCall with UUID [" + inboundCall.getUuid() + "] has been saved.");
        return inboundCall;
    }

    @Override
    public void addInboundCallEvent(String uuid, String eventId) {
        InboundCallEvent event = new InboundCallEvent();
        event.setEventDateTime(new Date());
        event.setEventId(eventId);

        InboundCall call = getInboundCall(uuid);
        if (call != null) {
            call.getInboundCallEvents().add(event);
            mongoOperations.save(call);
        } else {
            logger.error("An attempt to add a Call Event [" + eventId + "] a Call with UUID [" + uuid + "] that DOES NOT EXISTS !!! ");
        }

    }

    @Override
    public void addInboundCallLanguage(String uuid, String language) {
        InboundCall call = getInboundCall(uuid);
        if(call != null){
            call.setLanguage(language);
            mongoOperations.save(call);
        } else {
            logger.error("An attempt to add a Call Language [" + language + "] a Call with UUID [" + uuid + "] that DOES NOT EXISTS !!! ");
        }
    }

    @Override
    public void addInboundCallMainMenuNumberPress(String uuid, Integer mainMenuNumberPress) {
        InboundCall call = getInboundCall(uuid);
        if (call != null){
            call.setMainMenuNumberPress(mainMenuNumberPress);
            call.getMainMenuNumberPresses().add(mainMenuNumberPress);
            mongoOperations.save(call);
        } else {
            logger.error("An attempt to update a Call with UUID [" + uuid + "] that DOES NOT EXISTS !!! ");
        }
    }

    @Override
    public void addInboundCallSessionId(String uuid, String sessionId) {
        InboundCall call = getInboundCall(uuid);
        if(call != null){
            if(call.getSessionIds() == null){
                call.setSessionIds(new ArrayList<String>());
            }
            call.getSessionIds().add(call.getSessionId());
            call.setSessionId(sessionId);
            mongoOperations.save(call);
        } else {
            logger.error("An attempt to add a Call Session ID [" + sessionId + "] a Call with UUID [" + uuid + "] that DOES NOT EXISTS !!! ");
        }
    }

    @Override
    public CallResolutionCode getCallResolutionCode(String code) {
        return mongoOperations.findById(code, CallResolutionCode.class);
    }

    @Override
    public CallResolutionCode persistCallResolutionCode(CallResolutionCode code) {
        mongoOperations.save(code);
        return code;
    }

    @Override
    public List<CallResolutionCode> getCallReasonCodes(String screenType) {
        Criteria criteria = Criteria.where(("isActive")).is(true).and("isReason").is(true);
        if(screenType != null && !screenType.equalsIgnoreCase("ALL")){
            criteria.orOperator(Criteria.where("screenType").is(screenType), Criteria.where("screenType").is("ALL"));
        }
        Query query = new Query(criteria);
        query.with(new Sort(Sort.Direction.ASC, "displayOrder"));
        return mongoOperations.find(query, CallResolutionCode.class);
    }

    @Override
    public List<CallResolutionCode> getCallResolutionCodes(String screenType) {
        Criteria criteria = Criteria.where(("isActive")).is(true).and("isResolution").is(true);
        if(screenType != null && !screenType.equalsIgnoreCase("ALL")){
            criteria.orOperator(Criteria.where("screenType").is(screenType), Criteria.where("screenType").is("ALL"));
        }
        Query query = new Query(criteria);
        query.with(new Sort(Sort.Direction.ASC, "displayOrder"));
        return mongoOperations.find(query, CallResolutionCode.class);
    }

    @Override
    public List<CommunicationTemplate> getCommunicationTemplates() {
        Query query = new Query(Criteria.where("active").is(true));
        query.with(new Sort(Sort.Direction.ASC, "templateName"));
        return mongoOperations.find(query, CommunicationTemplate.class);
    }

    @Override
    public CommunicationTemplate getActiveCommunicationTemplateById(Long templateId) {
        CommunicationTemplate result = mongoOperations.findById(templateId, CommunicationTemplate.class);
        if(result != null && !result.isActive()){
            result = null;
        }
        return result;
    }

    @Override
    public List<Object> getDistinctField(String collection, String field) {
        return mongoOperations.getCollection(collection).distinct(field);
    }

    @Override
    public Npa getNpaByAni(Long ani) {
        String phoneNumber = ani != null ? ani.toString() : null;
        Npa result = null;
        if(phoneNumber != null && phoneNumber.length() == 10){
            String npa = phoneNumber.substring(0, 3);
            String nxx = phoneNumber.substring(3, 6);
            logger.debug("look up npa with " + npa + " and " + nxx);
            List<Npa> npas = mongoOperations.find(new Query(Criteria.where("npa").is(Integer.valueOf(npa)).and("nxx").is(Integer.valueOf(nxx))), Npa.class);
            if(npas.size() > 0){
                result = npas.get(0);
            }
        }
        return result;
    }

    @Override
    public PhoneNumberRoutingRules getPhoneNumberRoutingRules(Long ani) {
        List<PhoneNumberRoutingRules> rules = mongoOperations.find(new Query(Criteria.where("phoneNumber").is(ani)), PhoneNumberRoutingRules.class);
        if (rules.size() > 0) {
            return rules.get(0);
        }

        return null;
    }

    @Override
    public List<PhoneNumberRoutingRules> findAllRoutingRules() {
        return mongoOperations.find(new Query(), PhoneNumberRoutingRules.class);
    }

    @Override
    public PhoneNumberRoutingRules persistRoutingRules(PhoneNumberRoutingRules routingRules) {
        mongoOperations.save(routingRules);
        return routingRules;
    }

    @Override
    public List<InboundCallSummary> getInboundCallSummary(String callCenterName, String phoneType, String menuPress, Date processDate) {
        Criteria criteria = null;
        if(callCenterName != null && callCenterName.trim().length() > 0){
            criteria = Criteria.where("_id.callCenterName").is(callCenterName);
        }
        if(phoneType != null){
            if(criteria != null){
                criteria.and("_id.phoneType").is(phoneType);
            }else{
                criteria = Criteria.where("_id.phoneType").is(phoneType);
            }
        }
        if(menuPress != null){
            if(criteria != null){
                criteria.and("_id.menuPress").is(menuPress);
            }else{
                criteria = Criteria.where("_id.menuPress").is(menuPress);
            }
        }
        if(processDate != null){
            Calendar cal1 = Calendar.getInstance();
            Calendar cal2 = Calendar.getInstance();
            cal1.setTime(processDate);
            cal1.set(Calendar.HOUR_OF_DAY, 0);
            cal1.set(Calendar.MINUTE, 0);
            cal1.set(Calendar.SECOND, 0);
            cal1.set(Calendar.MILLISECOND, 0);

            cal2.setTime(processDate);
            cal2.set(Calendar.HOUR_OF_DAY, 23);
            cal2.set(Calendar.MINUTE, 59);
            cal2.set(Calendar.SECOND, 59);
            cal2.set(Calendar.MILLISECOND, 999);
            if(criteria != null){
                criteria.and("_id.processDate").gte(cal1.getTime()).lt(cal2.getTime());
            }else{
                criteria = Criteria.where("_id.processDate").gte(cal1.getTime()).lt(cal2.getTime());
            }
        }
        if(criteria != null){
            Query query = new Query(criteria);
            if(processDate == null){
                Sort sort = new Sort(Sort.Direction.DESC, "_id.processDate");
                sort = sort.and(new Sort(Sort.Direction.ASC, "_id.callCenterName"));
                if(phoneType == null){
                    sort = sort.and(new Sort(Sort.Direction.ASC, "_id.phoneType"));
                }else if(menuPress == null){
                    sort = sort.and(new Sort(Sort.Direction.ASC, "_id.menuPress"));
                }
                query.with(sort);
            }else{
                query.with(new Sort(Sort.Direction.DESC, "_id.callCenterName"));
            }

            query.limit(1000);
            logger.debug("query: {}", query.toString());
            return mongoOperations.find(query, InboundCallSummary.class);
        }else{
            return new ArrayList<InboundCallSummary>();
        }
    }

    @Override
    public CallCenter getCallCenterByName(String callCenterName) {
        return mongoOperations.findOne(new Query(Criteria.where("callCenterName").is(callCenterName)), CallCenter.class);
    }

    @Override
    public void inboundCallSummaryMapReduce(Date processDate) {
        // -- Date Params
        String _processDate = null;

        if (processDate == null) {
            Calendar start = Calendar.getInstance();
            start.add(Calendar.DATE, -1);
            _processDate = dateFormat.format(start.getTime());
        } else {
            _processDate = dateFormat.format(processDate);
        }

        Date begin = new Date();
        logger.info("\n\n-------------- Starting Map Reduce Job on " + begin.toString() + "-----------------");
        DBCollection dbCollection = mongoOperations.getCollection("inboundCall");

        // -- Map it
        String map = "function() {" +
                "        function getName(phone) { if (phone) { return phone.callCenterName; } else { return 'unknown'; } }" +
                "        function getPhoneType(phone) { if (phone) { return phone.phoneType; } else { return 'unknown'; } }" +
                "        function getSupported(phone) { if (phone) { return phone.callCenterSupported ? 1 : 0; } else { return 0; } }" +
                "        function getEventCount(events, eventId) { for (var x = 0; x < events.length; x++) { if (events[x].eventId == eventId) { return 1; } } return 0; }    " +
                "        function getPopCount(screenPops, popType) { " +
                "            var c = 0;" +
                "            for (var x = 0; x < screenPops.length; x++) { " +
                "                if (screenPops[x].loaded & screenPops[x].url.indexOf(popType) > 0) { " +
                "                    c++;" +
                "                } " +
                "            } " +
                "            return c; " +
                "        }    " +
                "       function getNumberPress(numberPress, numberPresses){"   +
                "           var result = \"\";" +
                "           if(numberPresses && numberPresses.length > 0) {"    +
                "               result = numberPresses.join(':');"   +
                "           }else{"  +
                "               result += numberPress;" +
                "           }"  +
                "           return result;" +
                "       }" +
                "        " +
                "        emit({ " +
                "                callCenterName  : getName(this.phoneNumber)," +
                "                phoneType       : getPhoneType(this.phoneNumber)," +
                "                menuPress       : getNumberPress(this.mainMenuNumberPress, this.mainMenuNumberPresses)," +
                "                processDate     : new Date(this.createDateTime.getFullYear(), this.createDateTime.getMonth(), this.createDateTime.getDate())" +
                "            }, { " +
                "                callCount              : 1," +
                "                supportedCount         : getSupported(this.phoneNumber)," +
                "                newPatCount            : getEventCount(this.inboundCallEvents, 'New Patient')," +
                "                extPatCount            : getEventCount(this.inboundCallEvents, 'Existing Patient')," +
                "                noPSRCount             : getEventCount(this.inboundCallEvents, 'No PSR')," +
                "                noCSRCount             : getEventCount(this.inboundCallEvents, 'No CSR')," +
                "                trnOffCount            : getEventCount(this.inboundCallEvents, 'Transfer to Office')," +
                "                aniRecCount            : getEventCount(this.inboundCallEvents, 'ANI Recognition')," +
                "                queryTimeoutCount      : getEventCount(this.inboundCallEvents, 'Query Timeout')," +
                "                searchNotFoundCount    : getEventCount(this.inboundCallEvents, 'Search not Found')," +
                "                offOffCount            : this.officeToOffice ? 1 : 0," +
                "                psrPopCount            : getPopCount(this.screenPopUrls, 'psr')," +
                "                csrPopCount            : getPopCount(this.screenPopUrls, 'csr')," +
                "                csrPsrCount            : ((getPopCount(this.screenPopUrls, 'csr') > 0 && getPopCount(this.screenPopUrls, 'psr') > 0) ? 1 : 0)," +
                "                transferToRDI          : getEventCount(this.inboundCallEvents, 'Transfer to RDI')," +
                "                receivedByRDI          : getEventCount(this.inboundCallEvents, 'Received by RDI')," +
                "                requestCallback        : getEventCount(this.inboundCallEvents, 'Request to call back')," +
                "                handledCallback        : getEventCount(this.inboundCallEvents, 'Handle call back request')" +
                "            }" +
                "        );" +
                "    }";

        // -- Reduce it
        String reduce = "function(methodName, values) {" +
                "        var a = values[0]; " +
                "        for (var i=1/*!*/; i < values.length; i++){" +
                "            var b = values[i]; " +
                "            a.callCount += b.callCount;" +
                "            a.supportedCount += b.supportedCount;" +
                "            a.newPatCount += b.newPatCount;" +
                "            a.extPatCount += b.extPatCount;" +
                "            a.noPSRCount += b.noPSRCount;" +
                "            a.noCSRCount += b.noCSRCount;" +
                "            a.trnOffCount += b.trnOffCount;" +
                "            a.aniRecCount += b.aniRecCount;" +
                "            a.offOffCount += b.offOffCount;" +
                "            a.psrPopCount += b.psrPopCount;" +
                "            a.csrPopCount += b.csrPopCount;" +
                "            a.csrPsrCount += b.csrPsrCount;" +
                "            a.queryTimeoutCount += b.queryTimeoutCount;" +
                "            a.searchNotFoundCount += b.searchNotFoundCount;" +
                "            a.transferToRDI += b.transferToRDI;" +
                "            a.receivedByRDI += b.receivedByRDI;" +
                "            a.requestCallback += b.requestCallback;" +
                "            a.handledCallback += b.handledCallback;" +
                "        }" +
                "        " +
                "        return a;" +
                "    }";
        String finalizeFn = "function(key, value {" +
                "           return {" +
                "               callCount: value.callCount," +
                "               events: [" +
                "                   {name: 'New Patient', count: value.newPatCount, order: 0}," +
                "                   {name: 'Existing Patient', count: value.extPatCount, order:1}," +
                "                   {name: 'No PSR', count: value.noPSRCount, order: 2}," +
                "                   {name: 'No CSR', count: value.noCSRCount, order:3}," +
                "                   {name: 'Transfer to Office', count: value.trnOffCount, order: 4}," +
                "                   {name: 'Office to Office', count: value.offOffCount, order: 5}," +
                "                   {name: 'PSR Pop', count: value.psrPopCount, order: 6}," +
                "                   {name: 'CSR Pop', count: value.csrPopCount, order: 7}," +
                "                   {name: 'ANI Recognition', count: value.aniRecCount, order: 8}" +
                "                   {name: 'Query Timeout', count: value.qtCount, order: 9}" +
                "                   {name: 'Search not Found', count: value.searchNFCount, order: 10}" +
                "                   {name: 'Transfer to RDI', count: value.transferToRDI, order: 10}" +
                "                   {name: 'Received by RDI', count: value.receivedByRDI, order: 10}" +
                "               ]" +
                "           };" +
                "       }";

        logger.info("......");
        logger.info("\tProcess Date: " + _processDate);
        logger.info(".....");

        BasicDBObject query = new BasicDBObject("createDate", _processDate);
//        query.append("_id", "{$not: /^test-*/}");

        // -- Map Reduce command to execute
        MapReduceCommand cmd = new MapReduceCommand(dbCollection,
                map,
                reduce,
                "inboundCallSummary",
                MapReduceCommand.OutputType.MERGE,
                query);
//        cmd.setFinalize(finalizeFn);

        // -- Execute it ...
        MapReduceOutput out = dbCollection.mapReduce(cmd);
        logger.info("Results from Map Reduce Job.");
//        logger.info("\t" + out.getCommandResult().get("counts"));

        // -- Calc runtime
        Date finish = new Date();
        long runtime = ((finish.getTime() - begin.getTime()) / 1000);
        int excMinutes = (int) (runtime / 60);
        int excSeconds = (int) (runtime / 60);

        logger.info("Runtime is " + excMinutes + " minutes and " + excSeconds + " seconds.\n");
        logger.info("-------------- Map Reduce Job complete @ " + finish.toString() + "-----------------");
    }

    @Override
    public ScreenPopTime getScreenPopTime(String uuid, Integer employeeNumber, String screenType) {
        List<ScreenPopTime> list = mongoOperations.find(new Query(Criteria.where("uuid").is(uuid).and("employeeNumber").is(employeeNumber).and("screenType").is(screenType)), ScreenPopTime.class);
        if(list.size() > 0){
            return list.get(0);
        }else{
            return null;
        }
    }

    @Override
    public ScreenPopTime persistScreenPopTime(ScreenPopTime screenPopTime) {
        mongoOperations.save(screenPopTime);
        return screenPopTime;
    }

    @Override
    public List<ScreenPopTime> getScreenPopTimeByDateRange(String start, String end) {

        Criteria criteria = null;
        if(start != null && start.trim().length() > 0){
            criteria = Criteria.where("callDate").gte(start);
        }
        if(end != null && end.trim().length() > 0){
            if(criteria != null){
                criteria = criteria.lte(end);
            }else{
                criteria = Criteria.where("callDate").lte(end);
            }
        }
        if(criteria != null){
            Query query = new Query(criteria);
            query.with(new Sort(Sort.Direction.ASC, "popDateTime"));
            return mongoOperations.find(query, ScreenPopTime.class);
        }else{
            return new ArrayList<ScreenPopTime>();
        }
    }

    @Override
    public void addLogAction(String uuid, String actionType, String message) {
        ActionLog actionLog = null;
        if(uuid == null){
            uuid = UUID.randomUUID().toString();
        }else{
            actionLog = this.getLogByUUID(uuid, actionType);
        }
        if(actionLog == null){
            actionLog = new ActionLog();
            actionLog.setUuid(uuid);
            actionLog.setCreateDate(VelocityUtils.getDateAsString(new Date(), null));
            if(actionType != null && actionType.trim().length() > 0){
                actionLog.setActionType(actionType);
            }
            actionLog.setId();
        }
        message = VelocityUtils.getDateAsString(new Date(), "MM-dd-yyyy HH:mm:ss") + " - " + message;
        actionLog.getLogs().add(message);
        this.persistLog(actionLog);
    }

    @Override
    public ActionLog getLogByUUID(String uuid, String actionType) {
        List<ActionLog> list = mongoOperations.find(new Query(Criteria.where("uuid").is(uuid).and("actionType").is(actionType)), ActionLog.class);
        if(list.size() > 0){
            return list.get(0);
        }else{
            return null;
        }
    }

    @Override
    public void persistLog(ActionLog log) {
        mongoOperations.save(log);
    }

    @Override
    public List<ActionLog> getActionLogToArchive(Date createDate) {
        String dateStr = VelocityUtils.getDateAsString(createDate, null);
        Query query = new Query(Criteria.where("createDate").lte(dateStr));

        return mongoOperations.find(query, ActionLog.class);
    }

    @Override
    public void archiveLog(ActionLog actionLog) {
        ActionLogArchive archiveLog = mongoOperations.findById(actionLog.getId(), ActionLogArchive.class);
        if(archiveLog == null){
            archiveLog = new ActionLogArchive(actionLog);
        }else{
            archiveLog.getLogs().addAll(actionLog.getLogs());
        }
        mongoOperations.save(archiveLog);
        mongoOperations.remove(actionLog);
    }

    @Override
    public int purgeArchivedLog() {
        //Keep last and current month archived action logs
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, -2);
        return purgeArchivedLogByTimePeriod(cal.get(Calendar.MONTH) + 1, cal.get(Calendar.YEAR));
    }

    @Override
    public int purgeArchivedLogByTimePeriod(int month, int year) {
        Pattern timePeriodRegex = buildTimePeriodRegex(month, year);
        Criteria criteria = Criteria.where("createDate").regex(timePeriodRegex);
        return purgeEntityByTimePeriod(criteria, ActionLogArchive.class);
    }

    private int purgeEntityByTimePeriod(Criteria criteria, Class clz){
        Query query = new Query(criteria);
        WriteResult wr = mongoOperations.remove(query, clz);
        return wr.getN();
    }

    private Pattern buildTimePeriodRegex(int month, int year){
        StringBuilder sb = new StringBuilder();
        if(month > 0){
            sb.append("^").append(month < 10 ? "0" : "").append(month).append(".*");
        }
        sb.append(year + "$");
        return Pattern.compile(sb.toString());
    }

    @Override
    public Long getNextSequenceValue(String sequenceName) {

        Query query = new Query(Criteria.where("sequenceName").is(sequenceName));

        Update update = new Update();
        update.inc("sequenceValue", 1);

        FindAndModifyOptions options = new FindAndModifyOptions();
        options.returnNew(true);
        options.upsert(true);

        SequenceId seqId = mongoOperations.findAndModify(query, update, options, SequenceId.class);

        return seqId.getSequenceValue();
    }

    @Override
    public Task claimOpenTask(String acquiredBy, String acquiredOn, Long taskId) {
        return claimOpenTask(acquiredBy, acquiredOn, null, taskId);
    }

    @Override
    public Task claimOpenTask(String acquiredBy, String acquiredOn, String taskName) {
        return claimOpenTask(acquiredBy, acquiredOn, taskName, null);
    }

    public Task claimOpenTask(String acquiredBy, String acquiredOn, String taskName, Long taskId){
        Criteria criteria = Criteria.where("acquiredBy").is(null).and("acquiredOn").is(null);
        if(taskId != null && taskId.compareTo(0l) != 0){
            criteria.and("_id").is(taskId);
        }else if(taskName != null && taskName.trim().length() > 0){
            criteria.and("taskName").is(taskName.trim());
        }

        List<Criteria> orCriteria = new ArrayList<Criteria>();
        orCriteria.add(Criteria.where("nextAcquiredOn").is(null));
        orCriteria.add(Criteria.where("nextAcquiredOn").lte(acquiredOn));
        criteria.orOperator(orCriteria.toArray(new Criteria[orCriteria.size()]));

        Query query = new Query(criteria).limit(1);

        Update update = new Update();
        update.set("acquiredBy", acquiredBy);
        update.set("acquiredOn", acquiredOn);

        FindAndModifyOptions options = new FindAndModifyOptions();
        options.returnNew(true);
        options.upsert(false);

        Task task = mongoOperations.findAndModify(query, update, options, Task.class);

        return task;
    }

    @Override
    public void releaseTask(Task task) {
        task.setLastAcquiredBy(task.getAcquiredBy());
        task.setLastAcquiredOn(task.getAcquiredOn());

        task.setAcquiredBy(null);
        task.setAcquiredOn(null);

        if(task.getDuration() > 0){
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.SECOND, task.getDuration());
            task.setNextAcquiredOn(VelocityUtils.getDateAsString(cal.getTime(), NEXT_ACQUIRED_ON_FORMAT));
        }

        mongoOperations.save(task);
    }

    @Override
    public List<Task> getAllClaimedTask(String nodeName) {
        Criteria criteria = Criteria.where("active").is(true).and("acquiredBy").regex(nodeName);
        return mongoOperations.find(new Query(criteria), Task.class);
    }

    @Override
    public List<Task> getAllTask() {
        Criteria criteria = Criteria.where("active").is(true);
        return mongoOperations.find(new Query(criteria), Task.class);
    }

    @Override
    public PartnerConfig findPartnerById(String partnerId) {
        PartnerConfig partnerConfig = mongoOperations.findOne(new Query(Criteria.where("partnerId").is(partnerId)), PartnerConfig.class);
        return partnerConfig;
    }

    @Override
    public ScreenPopHealthCheck getScreenPopHealthCheck(String id) {
        return mongoOperations.findById(id, ScreenPopHealthCheck.class);
    }

    @Override
    public ScreenPopHealthCheck persistScreenPopHealthCheck(ScreenPopHealthCheck record) {
        mongoOperations.save(record);
        return record;
    }

    @Override
    public List<ScreenPopHealthCheck> getScreenPopHealthCheckToClose(int limit) {
        Calendar cal = Calendar.getInstance();
        String processedOn = VelocityUtils.getDateAsString(cal.getTime(), ScreenPopHealthCheck.FORMAT_DATE_TIME);
        cal.add(Calendar.MINUTE, -10);
        String checkTime = VelocityUtils.getDateAsString(cal.getTime(), ScreenPopHealthCheck.FORMAT_DATE_TIME);

        Criteria criteria = Criteria.where("unloadDateTime").is(null).and("processedBy").is(null).and("pendingProcessedDateTime").is(null);
        criteria.and("checkDateTime").lte(checkTime);

        Query query = new Query(criteria);
        query.with(new Sort(Sort.Direction.ASC, "popDateTime"));
        if(limit > 0){
            query.limit(limit);
        }

        Update update = new Update();
        update.set("pendingProcessedDateTime", processedOn);
        update.set("pendingProcessedBy", ScheduleServiceImpl.NODE_NAME);
        mongoOperations.updateMulti(query, update, ScreenPopHealthCheck.class);

        criteria.and("pendingProcessedBy").is(ScheduleServiceImpl.NODE_NAME);
        criteria.and("pendingProcessedDateTime").is(processedOn);

        query = new Query(criteria);
        query.with(new Sort(Sort.Direction.ASC, "popDateTime"));
        if(limit > 0){
            query.limit(limit);
        }

        return mongoOperations.find(query, ScreenPopHealthCheck.class);
    }

    @Override
    public CallbackRequest getCallbackRequest(String uuid) {
        return mongoOperations.findById(uuid, CallbackRequest.class);
    }

    @Override
    public CallbackRequest persistCallbackRequest(CallbackRequest cbr) {
        mongoOperations.save(cbr);
        return cbr;
    }

    @Override
    public List<UpdateRequest> getRequestsToUpdateFacility() {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MINUTE, -5);
        String formattedDateTime = df.format(cal.getTime());
        formattedDateTime += ":59";
        Criteria criteria = Criteria.where("processDateTime").is(null);
        criteria.and("entityType").is("FACILITY");
        criteria.and("requestDateTime").lte(formattedDateTime);
        Query query = new Query(criteria);
        return mongoOperations.find(query, UpdateRequest.class);
    }

    @Override
    public void markUpdateRequestAsProcessed(UpdateRequest updateRequest) {
        List<UpdateRequest> check = mongoOperations.find(new Query(Criteria.where("entityType").is("FACILITY")
                                        .and("entityId").is(updateRequest.getEntityId())
                                        .and("requestDateTime").gte(updateRequest.getProcessDateTime())), UpdateRequest.class);
        if(check.size() == 0){
            mongoOperations.save(updateRequest);
        }
    }

    @Override
    public boolean doUpdateFacility(UpdateRequest updateRequest) {
        if(updateRequest.getEntityId() != null){
            Facility facility = facilityRepository.findFacilityByFacilityId(updateRequest.getEntityId().intValue());
            if(facility == null){
                return false;
            }
            Facility existing = findFacilityDetail(facility.getFacilityId());
            if(existing != null){
                facility.setMessage(existing.getMessage());
            }
            PhoneNumber phoneNumber = findPhoneNumberByFacility(facility.getFacilityId(), "ML");
            if(phoneNumber != null){
                facility.setPhoneNumber(phoneNumber.getPhoneNumber());
            }
            persistFacilityDetail(facility);
            return true;
        }
        return false;
    }

    @Override
    public List<CallResolutionCode> getCallReasonResolutionsCodes() {
        return mongoOperations.findAll(CallResolutionCode.class);
    }

    @Override
    public AgentCall updateAgentCallWithNewFacility(String uuid, Integer facilityId) {
        AgentCall agentCall = getAgentCall(uuid);

        LoadedFacility facility = new LoadedFacility();
        facility.setFacilityId(facilityId);
        facility.setLoadDateTime(new Date());
        agentCall.getLoadedFacilities().add(facility);
        agentCall.setCurrentFacilityId(facilityId);

        return updateAgentCall(agentCall);
    }

    @Override
    public void saveWebRequest(WebRequest webRequest) {
        mongoOperations.save(webRequest);
    }

    @Override
    public WebRequest findWebRequest(String uuid) {
        WebRequest wr = mongoOperations.findById(uuid, WebRequest.class);
        if(wr != null && wr.getPreferredRecallTime() != null && wr.getPreferredRecallTimeStr() == null){
            wr.setPreferredRecallTimeStr(null);
        }
        return wr;
    }

    @Override
    public List<WebRequest> getTodayWebRequest(Date appointmentDate) {
        Query query = new Query(Criteria.where("handledByAgent").is(false));

        return mongoOperations.find(query, WebRequest.class);
    }

    @Override
    public List<WebRequest> getWebRequestToProcess(Map<String, Object> conditions, int limit) {
        Query query = null;
        Criteria criteria = Criteria.where("processedDateTime").is(null);
        List<Criteria> nextProcessCriteria = new ArrayList<Criteria>();
        List<Criteria> pendingProcessCriteria = new ArrayList<Criteria>();

        // -- DISABLED PREFERRED APPOINTMENT DATE PRIORITIZATION MECHANISM IN FAVOR OF FIFO --//
        /*Calendar heldPendingProcess = Calendar.getInstance();
        heldPendingProcess.add(Calendar.HOUR_OF_DAY, -2);
        String heldPendingProcessDateTime = VelocityUtils.getDateAsString(heldPendingProcess.getTime(), "yyyy-MM-dd HH:mm:ss");

        if (conditions != null) { }

        nextProcessCriteria.add(Criteria.where("nextProcessDateTime").is(null));

        String pendingProcessedDateTime = VelocityUtils.getDateAsString(new Date(), "yyyy-MM-dd HH:mm:ss");
        nextProcessCriteria.add(Criteria.where("nextProcessDateTime").lte(pendingProcessedDateTime));

        pendingProcessCriteria.add(Criteria.where("pendingProcessedDateTime").is(null));
        pendingProcessCriteria.add(Criteria.where("pendingProcessedDateTime").lte(heldPendingProcessDateTime));
        criteria.andOperator(new Criteria().orOperator(nextProcessCriteria.toArray(new Criteria[nextProcessCriteria.size()]))
                            , new Criteria().orOperator(pendingProcessCriteria.toArray(new Criteria[pendingProcessCriteria.size()])));

        query = new Query(criteria);
        query.with(new Sort(Sort.Direction.ASC, new String[]{"appointmentDate", "createDatetime"}));

        if (limit > 0) { query.limit(limit); }
        logger.debug("WRP :: Update Query: {}", query);

        Update update = new Update();
        update.set("pendingProcessedDateTime", pendingProcessedDateTime);
        update.set("pendingProcessedBy", ScheduleServiceImpl.NODE_NAME);
        mongoOperations.updateMulti(query, update, WebRequest.class);*/

        // -- DISABLED PREFERRED APPOINTMENT DATE PRIORITIZATION MECHANISM IN FAVOR OF FIFO --//
        criteria = Criteria.where("processedDateTime").is(null);

        /*criteria.and("pendingProcessedDateTime").is(pendingProcessedDateTime);
        criteria.and("pendingProcessedBy").is(ScheduleServiceImpl.NODE_NAME);*/

        query = new Query(criteria);
        query.with(new Sort(Sort.Direction.ASC, new String[]{"createDatetime"}));

        // -- REMOVE APPOINTMENT SORT PER MARKETING/CALL CENTER DIRECTIVE
        // query.with(new Sort(Sort.Direction.ASC, new String[]{"appointmentDate", "createDatetime"}));

        if (limit > 0) { query.limit(limit); }
        logger.debug("WRP :: Select Query: {}", query);

        List<WebRequest> webRequests = mongoOperations.find(query, WebRequest.class);
        logger.debug("WRP :: Found {} Pending Web Request ready to process.", webRequests.size());

        return webRequests;
    }

    @Override
    public WebRequest findPendingWebRequestByPhoneNUmber(Long phoneNumber) {
        Query query = new Query(Criteria.where("phone").is(phoneNumber).and("processedDateTime").is(null));
        List<WebRequest> list = mongoOperations.find(query, WebRequest.class);

        return list.size() > 0 ? list.get(0) : null;
    }

    @Override
    public WebRequest findWebRequestByPhoneNUmberAndEmail(Long phoneNumber, String email) {
        Criteria criteria = Criteria.where("phone").is(phoneNumber)
                .and("emailAddress").is(email)
                .and("processedDateTime").is(null);

        logger.debug("findWebRequestByPhoneNUmberAndEmail.phone = " + phoneNumber);
        logger.debug("findWebRequestByPhoneNUmberAndEmail.email = " + email);

        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.HOUR, -1);
        Date minusOneHour = c.getTime();

        logger.debug("findWebRequestByPhoneNUmberAndEmail.minusOneHour = " + minusOneHour);

        criteria.and("createDateTime").gt(minusOneHour);

        Query query = new Query(criteria);
        List<WebRequest> list = mongoOperations.find(query, WebRequest.class);

        return list.size() > 0 ? list.get(0) : null;
    }

    @Override
    public List<WebRequest> findQueuedWebRequest(Map<String, Object> conditions) {
        Criteria criteria = Criteria.where("processedDateTime").is(null);
        Calendar current = Calendar.getInstance();
        current.add(Calendar.HOUR_OF_DAY, -1);
        criteria.and("createDateTime").lte(current.getTime());
        if(conditions != null){
            for(String key : conditions.keySet()){

            }
        }
        Query query = new Query(criteria);
        query.with(new Sort(Sort.Direction.ASC, new String[]{"createDatetime"}));
        return mongoOperations.find(query, WebRequest.class);
    }

    @Override
    public WebRequest updateRequestForEmail(String uuid, boolean pending, String confirmation, String emailFrom) {
        Criteria criteria = Criteria.where("_id").is(uuid).and("emailConfirmation").is(null);
        if(pending){
            criteria.orOperator(Criteria.where("emailPending").is(false), Criteria.where("emailPending").exists(false));
        }

        Update update = new Update();
        update.set("emailPending", pending);
        update.set("emailConfirmation", confirmation);
        update.set("emailFrom", emailFrom);
        if(confirmation != null && confirmation.trim().length() > 0){
            update.set("emailDateTime", VelocityUtils.getDateAsString(new Date(), "yyyy-MM-dd HH:mm:ss z"));
        }

        FindAndModifyOptions options = new FindAndModifyOptions();
        options.returnNew(false);
        options.upsert(false);

        Query query = new Query(criteria);
        return mongoOperations.findAndModify(query, update, options, WebRequest.class);
    }

    private String zipToString(String zip) {
        if (zip.length() == 5) {
            return zip;
        } else {
            StringBuilder sb = new StringBuilder();
            int x = (5 - zip.length());

            for (int z=0; z < x; z++) {
                sb.append("0");
            }

            sb.append(zip);
            return sb.toString();
        }
    }

    private String toEmail(int facCount, int campCount, int phCount, int z2fCount,
                           int agentCallCount, int callLogCount, int missingCallLogCount, int appointmentCount, int missingAppointmentCount,
                           int editApptLogCount, int spamPhoneNumbers, int purgedArchiveActionLog) {
        StringBuilder sb = new StringBuilder(
                "<html>"
                        + " <head> "
                        + " <style type='text/css'> "
                        + " body { "
                        + " background: #fff; "
                        + " color: #374A54; "
                        + " font: normal 12px verdana, arial, sans-serif; "
                        + " } "
                        + " h1 { "
                        + " font: bold 16px/18px verdana, arial, sans-serif; "
                        + " } "
                        + " h2 { "
                        + " color: #e01d1d; "
                        + " font: bold 14px/16px verdana, arial, sans-serif; "
                        + " } "
                        + " .line { "
                        + " font:  normal 12px verdana, arial, sans-serif; "
                        + " } "
                        + " .space { "
                        + " height: 6px; "
                        + " } "
                        + " table{ "
                        + " margin: 0 0 5px 5px; "
                        + " width: 470px;"
                        + " } "
                        + " </style>"
                        + " </head>"
                        + " <body>"
                        + " <h1>Call Center Service update into MongoDB</h1>"
                        + " <h2>Summary</h2>"
                        + " <table>"
                        + " <tr>"
                        + " <td width='50%' class='line'>Facility Update Count:</td>"
                        + " <td class='line'>" +  facCount + "</td>"
                        + " </tr>"
                        + " <tr>"
                        + " <td width='50%' class='line'>Campaign Count:</td>"
                        + " <td class='line'>" + campCount + "</td>"
                        + " </tr>"
                        + " <tr>"
                        + " <td width='50%' class='line'>Phone Number Count:</td>"
                        + " <td class='line'>" + phCount + "</td>"
                        + " </tr>"
                        + " <tr>"
                        + " <td width='50%' class='line'>Zip2Facility Count:</td>"
                        + " <td class='line'>" + z2fCount + "</td>"
                        + " </tr>"
                        + " <tr>"
                        + " <td width='50%' class='line'>Agent Call Count:</td>"
                        + " <td class='line'>" + agentCallCount + "</td>"
                        + " </tr>"
                        + " <tr>"
                        + " <td width='50%' class='line'>Call Log Count:</td>"
                        + " <td class='line'>" + callLogCount + "</td>"
                        + " </tr>"
                        + " <tr>"
                        + " <td width='50%' class='line'>Missing Call Log Count:</td>"
                        + " <td class='line'>" + missingCallLogCount + "</td>"
                        + " </tr>"
                        + " <tr>"
                        + " <td width='50%' class='line'>Appointment Count:</td>"
                        + " <td class='line'>" + appointmentCount + "</td>"
                        + " </tr>"
                        + " <tr>"
                        + " <td width='50%' class='line'>Missing Appointment Count:</td>"
                        + " <td class='line'>" + missingAppointmentCount + "</td>"
                        + " </tr>"
                        + " <tr>"
                        + " <td width='50%' class='line'>Edited Appointment Count:</td>"
                        + " <td class='line'>" + editApptLogCount + "</td>"
                        + " </tr>"
                        + " <tr>"
                        + " <td width='50%' class='line'>Spam Phone Numbers:</td>"
                        + " <td class='line'>" + spamPhoneNumbers + "</td>"
                        + " </tr>"
                        + " <tr>"
                        + " <td width='50%' class='line'>Purged Archive Action Log:</td>"
                        + " <td class='line'>" + purgedArchiveActionLog + "</td>"
                        + " </tr>"
                        + " </table>"
                        + " </body>"
                        + " </html>"
        );

        return sb.toString();
    }
}

