<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd" >
<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<%@ taglib uri="../customTag.tld" prefix="custom" %>

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Smile Brands Call Center - Phone Number Tester</title>
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/bootstrap.min.css" >
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/style/redmond/jquery-ui-1.8.20.custom.css" />
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/call-template.css" >
</head>
<body>
    <input type="text" style="display:none;" id="reportType" value="${reportType}"/>
    <input type="text" style="display:none;" id="agentId" value="${agentId}"/>
    <input type="text" style="display:none;" id="reportDate" value="${reportDate}"/>

    <jsp:include page="patientInfo.jsp"/>

    <div class="container main">
        <br/>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h1 class="panel-title gray">Agent Call Report

                </h1>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-1">Load For:</div>
                    <div class="col-md-3">
                        <input class="agent-call-date report-date" name="datepicker" maxDate="0"></input>
                    </div>
                    <div class="col-md-3">
                        <button id="exportBtn" class="btn btn-primary" from="local">Export</button>
                    </div>
                    <div class="progress report-progress" style="display:none; width: 40%;">
                        <div class="progress-bar progress-bar-striped active"  role="progressbar" aria-valuenow="45" aria-valuemin="0" aria-valuemax="100" style="width: 100%">
                            <span>Please wait while pulling report...</span>
                        </div>
                    </div>
                </div>
                <br/>
                <div class="row filter-cmp">
                    <div class="col-md-1">Filter By:</div>
                    <div class="col-md-3">
                        <select filterOptionFor="agentCallList" style="width:100%;">
                            <option value="uuid">UUID</option>
                            <option value="ani">ANI</option>
                            <option value="callCenterName">Call Center</option>
                            <option value="employeeNumber" selected>Employee Number</option>
                            <option value="loadedTime">Loaded Time</option>
                            <option value="unloadedTime">Unloaded Time</option>
                            <option value="screenType">Screen Type</option>
                            <option value="reasonCode">Reason</option>
                            <option value="resolutionCode">Resolution</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <input type="text" filterInputFor="agentCallList" style="width:90%;"/>
                        <span class="glyphicon glyphicon-remove pointer" removeFilterFor="agentCallList"></span>
                        <span class="" countFilterFor="agentCallList"></span>
                    </div>
                </div>
                <table id="agentCallList" class="table table-striped main">
                    <thead>
                        <tr>
                            <th sortField="uuid" class="pointer">UUID<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="ani" class="pointer">ANI<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="callCenterName" class="pointer">Call Center<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="employeeNumber" class="pointer">Employee Number<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="loadedTime" class="pointer">Loaded Time<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="unloadedTime" class="pointer">Unloaded Time<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="screenType" class="pointer">Screen Type<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="reasonCode" class="pointer">Reason<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="resolutionCode" class="pointer">Resolution<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="appointments" class="pointer">Number of booked appointments<span class="glyphicon padding-left-5"></span></th>
                        </tr>
                    </thead>
                    <tbody>
                        <c:forEach var="agentCall" items="${agentCallList}">
                            <c:forEach var="employee" items="${agentCall.loadedEmployees}">
                                <c:set var = "appointmentCnt" value = "0"/>
                                <tr>
                                    <td name="uuid" value="${agentCall.uuid}">${agentCall.uuid}</td>
                                    <td name="ani" value="${agentCall.ani}">${agentCall.ani}</td>
                                    <td name="callCenter" value="${agentCall.callCenterName}">${agentCall.callCenterName}</td>
                                    <td name="employeeNumber" value="${employee.employeeNumber}">${employee.employeeNumber}</td>
                                    <td name="loadedTime" value="${employee.loadedTime}">${employee.loadedTime}</td>
                                    <td name="unloadedTime" value="${employee.unloadedTime}">${employee.unloadedTime}</td>
                                    <td name="screenType" value="${employee.screenType}">${employee.screenType}</td>
                                    <td name="reasonCode" value="${employee.reasonCode}">${employee.reasonCode}</td>
                                    <td name="resolutionCode" value="${employee.resolutionCode}">${employee.resolutionCode}</td>
                                    <c:forEach var="appointment" items="${agentCall.patients}">
                                        <c:if test="${appointment.empEntered == employee.employeeNumber}">
                                            <c:set var="appointmentCnt" value="${appointmentCnt+1}"/>
                                        </c:if>
                                    </c:forEach>
                                    <td name="appointments" value="${appointmentCnt}" style="text-align: center;">
                                        <c:if test="${appointmentCnt > 0}">
                                            <a class="patient-schedule pointer">${appointmentCnt}</a>
                                        </c:if>
                                    </td>
                                </tr>

                            </c:forEach>
                        </c:forEach>
                    </tbody>
                </table>

            </div>
        </div>
    </div>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-1.7.2.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-ui-1.8.20.custom.min.js"></script>
    <script src="${pageContext.request.contextPath}/bootstrap/js/bootstrap.js"></script>

    <script type="text/javascript" src="${pageContext.request.contextPath}/script/sbi-script.js?_id=${buildNumber}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/reportActions.js?_id=${buildNumber}"></script>
</body>
</html>