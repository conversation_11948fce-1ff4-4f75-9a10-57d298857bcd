package com.smilebrands.callcentersupport.domain;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;

/**
 * Date: 5/18/12
 * Time: 10:29 AM
 */
@Document
public class CallResolutionCode {

    @Id
    private String code;
    private String description;
    private boolean isActive;
    private Integer displayOrder;
    private boolean isReason;
    private boolean isResolution;
    private String screenType = "ALL";

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(boolean active) {
        isActive = active;
    }

    public Integer getDisplayOrder() {
        return displayOrder;
    }

    public void setDisplayOrder(Integer displayOrder) {
        this.displayOrder = displayOrder;
    }

    public boolean getIsReason() {
        return isReason;
    }

    public void setIsReason(boolean reason) {
        isReason = reason;
    }

    public boolean getIsResolution() {
        return isResolution;
    }

    public void setIsResolution(boolean resolution) {
        isResolution = resolution;
    }

    public String getScreenType() {
        return screenType;
    }

    public void setScreenType(String screenType) {
        this.screenType = screenType;
    }
}
