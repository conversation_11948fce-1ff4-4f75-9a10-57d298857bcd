<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd" >
<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<%@ taglib uri="../customTag.tld" prefix="custom" %>

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Smile Brands Call Center - Call Log Management</title>
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/bootstrap.min.css" >
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/style/redmond/jquery-ui-1.8.20.custom.css" />
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/call-template.css" >
</head>
<body>
    <input type="text" style="display:none;" id="reportType" value="${reportType}"/>
    <input type="text" style="display:none;" id="agentId" value="${agentId}"/>
    <input type="text" style="display:none;" id="reportDate" value="${reportDate}"/>

    <div class="modal fade" id="agentCallLogPop" tabindex="-1" role="dialog" aria-labelledby="agentCallLogModalLabel" aria-hidden="true" style="top:100px;">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 class="modal-title" id="agentCallLogModalLabel"></h4>
                </div>
                <div class="modal-body" style="padding-bottom:0px;">
                    <form class="form-horizontal report-parameters-form" role="form">
                        <div class="form-group" action="edit">
                            <label for="uuid" class="col-sm-3 control-label">UUID
                            </label>
                            <div class="col-sm-9">
                                <input id="uuid" type="text" class="form-control input-sm" disabled/>
                                <input id="sessionId" type="text" disabled style="display: none;"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="employeeNumberInput" class="col-sm-3 control-label">Employee Number</label>
                            <div class="col-sm-9">
                                <c:choose>
                                    <c:when test="${hasTeam}">
                                        <div class="dropdown" selMode="SINGLE">
                                            <div class="input-group dropdown-toggle" id="agent-dropdown-menu" data-toggle="dropdown">
                                                <input id="employeeNumberInput" type="text" class="form-control" disabled>
                                                <span class="input-group-addon" style="font-weight: bold;">v</span>
                                            </div>
                                            <ul class="dropdown-menu" role="menu" aria-labelledby="agent-dropdown-menu" style="max-height: 300px; width: 100%; overflow-y: scroll;">
                                                <c:forEach var="agent" items="${teamList}">
                                                    <li role="presentation"><input role="menuitem" tabindex="-1" type="checkbox" class="agent-option" style="padding-left: 10px;" value="${agent.employeeNumber}">&nbsp;${agent.employeeNumber} - ${agent.employeeName}</input></li>
                                                </c:forEach>
                                            </ul>
                                        </div>
                                    </c:when>
                                    <c:otherwise>
                                        <input id="employeeNumberInput" class="form-control input-sm" disabled></input>
                                    </c:otherwise>
                                </c:choose>
                            </div>
                        </div>
                        <div class="form-group" action="add">
                            <label for="callListInput" class="col-sm-3 control-label">Call List</label>
                            <div class="col-sm-9">
                                <div class="dropdown" selMode="SINGLE">
                                    <div class="input-group dropdown-toggle" id="callList-dropdown-menu" data-toggle="dropdown">
                                        <input id="callListInput" type="text" class="form-control">
                                        <span class="input-group-addon" style="font-weight: bold;">v</span>
                                    </div>
                                    <ul class="dropdown-menu" role="menu" aria-labelledby="callList-dropdown-menu" style="max-height: 300px; width: 100%; overflow-y: scroll;" id="callListDDOptions">

                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="form-group" action="add">
                            <label for="callCenterInput" class="col-sm-3 control-label">Call Center</label>
                            <div class="col-sm-9">
                                <select id="callCenterInput" class="form-control input-sm">
                                    <option value="Irvine">Irvine</option>
                                    <option value="Plano">Plano</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="screenTypeInput" class="col-sm-3 control-label">Screen Type</label>
                            <div class="col-sm-9">
                                <select id="screenTypeInput" class="form-control input-sm">
                                    <option value="PSR">PSR</option>
                                    <option value="CSR">CSR</option>
                                    <option value="WR">WR</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group" action="add">
                            <label for="callDate" class="col-sm-3 control-label">Call Time</label>
                            <div class="col-sm-4">
                                <input id="callDate" type="text" class="form-control input-sm call-time-date" placeholder="Call Date" name="datepicker" disabled/>
                            </div>
                            <div class="col-sm-5 form-inline">
                                <select class="form-control input-sm call-time-hour">
                                    <c:forEach var="i" begin="1" end="12">
                                        <option value="${i}"
                                            <c:if test="${i==8}">selected</c:if>
                                        >
                                            ${i}
                                        </option>
                                    </c:forEach>
                                </select>
                                <select class="form-control input-sm call-time-minute">
                                    <c:forEach var="i" begin="0" end="59" step="5">
                                        <option value="${i}">
                                            <c:if test="${i<10}">0</c:if>${i}
                                        </option>
                                    </c:forEach>
                                </select>
                                <select class="form-control input-sm call-time-ampm">
                                    <option value="AM">AM</option>
                                    <option value="PM">PM</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group" action="add">
                            <label for="reasonInput" class="col-sm-3 control-label">Reason for Call</label>
                            <div class="col-sm-9">
                                <select id="reasonInput" class="form-control input-sm">
                                    <c:forEach var="reason" items="${reasonList}">
                                        <option value="${reason.code}" screenType="${reason.screenType}"> ${reason.description}</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </div>
                        <div class="form-group" action="add">
                            <label for="resolutionInput" class="col-sm-3 control-label">Call Resolution</label>
                            <div class="col-sm-9">
                                <select id="resolutionInput" class="form-control input-sm">
                                    <c:forEach var="resolutionCode" items="${resolutionCodeList}">
                                        <option value="${resolutionCode.code}" screenType="${resolutionCode.screenType}"> ${resolutionCode.description}</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </div>
                        <div class="form-group" action="edit">
                            <label for="downloadFileBtn" class="col-sm-3 control-label"></label>
                            <div class="col-sm-9">
                                <audio controls id="playbackCmp">
                                </audio controls>
                                <span id="downloadFileCmp" class="glyphicon glyphicon-download-alt fr pointer" data-toggle="tooltip" data-placement="left" title="Download"/>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading" style="font-weight:bold; font-size:15px;">
                                <span>Patient Information<span id="addPatientInfoBtn" style="cursor:pointer;" class="glyphicon glyphicon-plus fr" data-toggle="tooltip" data-placement="left" title="Add Patient"></span></span>
                            </div>
                            <div id="patientInfoBody" class="panel-body" style="max-height:400px; overflow:auto;">

                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button id="saveCallLog" type="button" class="btn btn-primary">Save</button>
                </div>
            </div>
        </div>
    </div>

    <div class="container main" style="width: 100%;">
        <select id="facilityIDs" style="display: none;">
            <c:forEach var="facilityId" items="${facilityList}">
                <option value="${facilityId}"> ${facilityId}</option>
            </c:forEach>
        </select>
        <select id="teamList" style="display: none;">
            <c:forEach var="cce" items="${teamList}">
                <option employeeNumber="${cce.employeeNumber}" employeeName="${cce.employeeName}">${cce.employeeName}</option>
            </c:forEach>
        </select>
        <select id="callList" style="display: none;">
            <c:forEach var="call" items="${callList}">
                <option sessionId="${call.sessionId}" employeeNumber="${call.agentLoginId}" ani="${call.ani}" startDate=<custom:fmtDateTime date="${call.startDate}" dateFormat="MM/dd/yyyy'T'HH:mm:ss"/>
                    endDate=<custom:fmtDateTime date="${call.endDate}" dateFormat="MM/dd/yyyy'T'HH:mm:ss"/>
                ></option>
            </c:forEach>
        </select>
        <br/>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h1 class="panel-title gray">Call Log Report
                    <span style="cursor:pointer;" class="add-call-log glyphicon glyphicon-plus fr" data-toggle="tooltip" data-placement="left" title="Add Call Log"></span>
                </h1>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-1">Load For:</div>
                    <div class="col-md-2">
                        <input class="agent-call-date report-date" name="datepicker" maxDate="0"></input>
                    </div>
                    <c:if test="${canSyncCallLog}">
                        <div class="col-md-2 sync-call-log">
                            <button type="button" class="btn btn-primary sync-call-log">Sync Call Log</button>
                        </div>
                    </c:if>
                    <div class="col-md-2">
                        <button id="exportBtn" class="btn btn-primary" from="local">Export</button>
                    </div>
                    <div class="progress report-progress" style="width: 40%;">
                        <div class="progress-bar progress-bar-striped active"  role="progressbar" aria-valuenow="45" aria-valuemin="0" aria-valuemax="100" style="width: 100%">
                            <span class="progress-message">Please wait while pulling report...</span>
                        </div>
                    </div>
                </div>
                <br/>
                <div class="row filter-cmp">
                    <div class="col-md-1">Filter By:</div>
                    <div class="col-md-3">
                        <select filterOptionFor="agentCallList" style="width:100%;">
                            <option value="callCenter">Call Center</option>
                            <option value="employeeNumber">Employee Number</option>
                            <option value="employeeName">Employee Name</option>
                            <option value="logStatus">Call Log Status</option>
                            <option value="facilityId">Facility ID</option>
                            <option value="tempPatientId" selected>Temp. Patient ID</option>
                            <option value="woi">WOI</option>
                            <option value="ortho">SCA Ortho</option>
                            <option value="agentType">Agent Type</option>
                            <option value="resolutionCode">Resolution</option>
                            <option value="appointmentDateTime">Appointment Date Time</option>
                            <option value="appointmentStatus">Appointment Status</option>
                            <option value="original">Is Original</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <input type="text" filterInputFor="agentCallList" style="width:90%;"/>
                        <span class="glyphicon glyphicon-remove pointer" removeFilterFor="agentCallList"></span>
                        <span class="" countFilterFor="agentCallList"></span>
                    </div>
                </div>
                <table id="agentCallList" class="table table-striped main">
                    <thead>
                        <th style="display:none;"></th>
                        <th sortField="uuid" class="pointer" style="width:275px; display: none;">UUID<span class="glyphicon padding-left-5"></span></th>
                        <th sortField="callCenter" class="pointer">Call Center<span class="glyphicon padding-left-5"></span></th>
                        <th sortField="employeeNumber" class="pointer">Employee Number<span class="glyphicon padding-left-5"></span></th>
                        <th sortField="employeeName" class="pointer">Employee Name<span class="glyphicon padding-left-5"></span></th>
                        <th sortField="callTime" class="pointer">Call Time<span class="glyphicon padding-left-5"></span></th>
                        <th sortField="callDuration" class="pointer">Duration<span class="glyphicon padding-left-5"></span></th>
                        <th sortField="resolutionCode" class="pointer">Resolution<span class="glyphicon padding-left-5"></span></th>
                        <th sortField="woi" class="pointer">WOI<span class="glyphicon padding-left-5"></span></th>
                        <th sortField="logStatus" class="pointer">Call Log Status<span class="glyphicon padding-left-5"></span></th>
                        <th sortField="facilityId" class="pointer">Facility ID<span class="glyphicon padding-left-5"></span></th>
                        <th sortField="patientId" class="pointer">Patient ID<span class="glyphicon padding-left-5"></span></th>
                        <th sortField="tempPatientId" class="pointer">Temp. Patient ID<span class="glyphicon padding-left-5"></span></th>
                        <th sortField="ortho" class="pointer">SCA Ortho<span class="glyphicon padding-left-5"></span></th>
                        <th sortField="agentType" class="pointer">Agent Type<span class="glyphicon padding-left-5"></span></th>
                        <th sortField="appointmentDateTime" class="pointer">Appointment Date Time<span class="glyphicon padding-left-5"></span></th>
                        <th sortField="appointmentStatus" class="pointer" style="width:180px;">Appointment Status<span class="glyphicon padding-left-5"></span></th>
                        <th sortField="original" class="pointer">Is Original?<span class="glyphicon padding-left-5"></span></th>
                        <th style="display:none;"></th>
                        <th style="display:none;"></th>
                        <th style="display:none;"></th>
                        <th style="display:none;"></th>
                        <th style="display:none;"></th>
                        <th style="display:none;"></th>
                        <th style="display:none;"></th>
                    </thead>
                    <tbody>
                        <c:forEach var="callLog" items="${callLogList}">
                                <tr uuid="${callLog.agentCallLog.uuid}" agentType="${callLog.agentCallLog.agentType}" employeeNumber="${callLog.agentCallLog.employeeNumber}"
                                    callCenter="${callLog.agentCallLog.callCenterName}" reason="${callLog.agentCallLog.reasonCode}" resolution="${callLog.agentCallLog.resolutionCode}">
                                    <td name="callLogId" value="${callLog.agentCallLog.callLogId}" style="display:none;">${callLog.agentCallLog.callLogId}</td>
                                    <td name="uuid" value="${callLog.agentCallLog.uuid}" style="display: none;">${callLog.agentCallLog.uuid}</td>
                                    <td name="callCenter" value="${callLog.agentCallLog.callCenterName}">${callLog.agentCallLog.callCenterName}</td>
                                    <td name="employeeNumber" value="${callLog.agentCallLog.employeeNumber}">${callLog.agentCallLog.employeeNumber}</td>
                                    <td name="employeeName" value="${callLog.agentCallLog.employeeName}">${callLog.agentCallLog.employeeName}</td>
                                    <td name="callTime" value="${callLog.agentCallLog.callTime}">${callLog.agentCallLog.callTime}</td>
                                    <td name="callDuration" value="${callLog.agentCallLog.callDuration}">${callLog.agentCallLog.callDuration}</td>
                                    <td name="resolutionCode" value="${callLog.agentCallLog.resolutionCode}">${callLog.agentCallLog.resolutionCode}</td>
                                    <td name="woi" value="${callLog.agentCallLog.insuranceWaiting}">${callLog.agentCallLog.insuranceWaiting}</td>
                                    <td name="logStatus" value="${callLog.agentCallLog.logStatus}">${callLog.agentCallLog.logStatus}</td>
                                    <td name="facilityId" value="${callLog.agentCallLog.facilityId}">${callLog.agentCallLog.facilityId}</td>
                                    <td name="patientId" value="${callLog.agentCallLog.patientId}">${callLog.agentCallLog.patientId}</td>
                                    <td name="tempPatientId" value="${callLog.agentCallLog.tempPatientId}">${callLog.agentCallLog.tempPatientId}</td>
                                    <td name="ortho" value="${callLog.appointmentCallLog.isOrtho}">${callLog.appointmentCallLog.isOrtho}</td>
                                    <td name="agentType" value="${callLog.agentCallLog.agentType}">${callLog.agentCallLog.agentType}</td>
                                    <td name="appointmentDateTime" value="${callLog.appointmentCallLog.appointmentDateTimeStr}">${callLog.appointmentCallLog.appointmentDateTimeStr}</td>
                                    <td name="appointmentStatus" value="${callLog.appointmentCallLog.appointmentStatus}">${callLog.appointmentCallLog.appointmentStatus}
                                        <c:if test="${canEditCallLog || (callLog.agentCallLog.editable && callLog.appointmentCallLog.editable)}">
                                            <span class="glyphicon glyphicon-pencil padding-left-5 pointer edit-call-log" data-toggle="tooltip" data-original-title="Edit"></span>
                                        </c:if>
                                        <c:if test="${callLog.callLogFile != null && callLog.callLogFile.callLogFileId > 0}">
                                            <span class="glyphicon glyphicon-paperclip padding-left-5 pointer edit-call-log" data-toggle="tooltip" data-original-title="Attachment"></span>
                                        </c:if>
                                    </td>
                                    <td name="original" value="${callLog.original}">${callLog.original}</td>
                                    <td name="appointmentCallLogId" value="${callLog.appointmentCallLog.appointmentCallLogId}" style="display:none;">${callLog.appointmentCallLog.appointmentCallLogId}</td>
                                    <td name="emailCaptured" value="${callLog.agentCallLog.emailCaptured}" style="display:none;">${callLog.agentCallLog.emailCaptured}</td>
                                    <td name="insuranceWaiting" value="${callLog.agentCallLog.insuranceWaiting}" style="display:none;">${callLog.agentCallLog.insuranceWaiting}</td>
                                    <td name="allowCredit" value="${callLog.agentCallLog.allowCredit}" style="display:none;">${callLog.agentCallLog.allowCredit}</td>
                                    <td name="fileId" value="${callLog.callLogFile.callLogFileId}" style="display:none;">${callLog.callLogFile.callLogFileId}</td>
                                    <td name="editable" value="${(callLog.agentCallLog.editable && callLog.appointmentCallLog.editable)}" style="display:none;">${(callLog.agentCallLog.editable && callLog.appointmentCallLog.editable)}r</td>
                                    <td name="sessionId" value="${callLog.agentCallLog.sessionId}" style="display:none;">${callLog.agentCallLog.sessionId}</td>
                                </tr>
                        </c:forEach>
                    </tbody>
                </table>

            </div>
        </div>
    </div>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-1.7.2.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-ui-1.8.20.custom.min.js"></script>
    <script src="${pageContext.request.contextPath}/bootstrap/js/bootstrap.js"></script>

    <script type="text/javascript" src="${pageContext.request.contextPath}/script/sbi-script.js?_id=${buildNumber}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/reportActions.js?_id=${buildNumber}"></script>
</body>
</html>