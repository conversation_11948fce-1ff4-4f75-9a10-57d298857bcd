package com.smilebrands.callcentersupport.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Utility class for exporting Excel files to HTTP response
 */
public class ExcelExporter {

    private static final Logger logger = LoggerFactory.getLogger(ExcelExporter.class);

    /**
     * Writes Excel file content to HTTP response output stream
     *
     * @param response HttpServletResponse to write to
     * @param filePath Path to the Excel file to export
     * @param fileName Name for the downloaded file
     * @throws IOException if there's an error reading the file or writing to response
     */
    public static void writeExcelToResponse(HttpServletResponse response, String filePath, String fileName) throws IOException {
        if (filePath == null || filePath.trim().isEmpty()) {
            logger.warn("File path is null or empty, cannot export Excel file");
            sendError(response, "File path is null or empty");
            return;
        }

        File file = new File(filePath);

        // Enhanced file validation with detailed logging
        logger.debug("Attempting to export file at absolute path: {}", file.getAbsolutePath());

        if (!file.exists()) {
            logger.error("File does not exist at path: {} (absolute: {})", filePath, file.getAbsolutePath());
            sendError(response, "Export file not found: " + file.getName());
            return;
        }

        if (!file.isFile()) {
            logger.error("Path exists but is not a file: {} (absolute: {})", filePath, file.getAbsolutePath());
            sendError(response, "Export path is not a valid file: " + file.getName());
            return;
        }

        if (!file.canRead()) {
            logger.error("File exists but is not readable: {} (absolute: {})", filePath, file.getAbsolutePath());
            sendError(response, "Export file is not readable: " + file.getName());
            return;
        }

        if (file.length() == 0) {
            logger.warn("File exists but is empty: {} (absolute: {})", filePath, file.getAbsolutePath());
            sendError(response, "Export file is empty: " + file.getName());
            return;
        }

        // Check if response is already committed before proceeding
        if (response.isCommitted()) {
            logger.warn("Response already committed, cannot write Excel file");
            return;
        }

        FileInputStream in = null;
        ServletOutputStream out = null;
        boolean downloadSuccess = false;

        try {
            // Set response headers before getting output stream
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment; filename=" + (fileName != null ? fileName : file.getName()));
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Cache-Control", "no-cache");
            response.setContentLength((int) file.length());

            // Get output stream AFTER setting headers
            out = response.getOutputStream();
            in = new FileInputStream(file);

            // Copy file content to response
            IOUtils.copy(in, out);
            out.flush();

            // Mark as successful only after flush completes
            downloadSuccess = true;
            logger.debug("Successfully exported Excel file: {} (size: {} bytes)", fileName, file.length());

        } catch (IOException e) {
            logger.error("Error exporting Excel file: {}", e.getMessage(), e);
            // Only try to send error message if response isn't committed
            // Note: Don't call sendError if we already got the OutputStream - it will cause conflicts
            if (!response.isCommitted() && out == null) {
                try {
                    sendError(response, "Error exporting file: " + e.getMessage());
                } catch (Exception errorEx) {
                    logger.warn("Could not send error message due to response state: {}", errorEx.getMessage());
                }
            } else {
                logger.warn("Cannot send error message - response already committed or stream already obtained");
            }
            throw e;
        } finally {
            // Clean up resources
            IOUtils.closeQuietly(in);
            IOUtils.closeQuietly(out);

            // Only delete temporary file if download was successful
            // This prevents deleting files that failed to stream properly
            if (downloadSuccess && file.exists()) {
                boolean deleted = file.delete();
                if (deleted) {
                    logger.debug("Successfully deleted temporary file: {}", filePath);
                } else {
                    logger.warn("Could not delete temporary file after successful download: {}", filePath);
                }
            } else if (!downloadSuccess && file.exists()) {
                logger.info("Preserving file after failed download for debugging: {}", filePath);
            }
        }
    }

    /**
     * Writes Excel file content to HTTP response output stream with default filename
     *
     * @param response HttpServletResponse to write to
     * @param filePath Path to the Excel file to export
     * @throws IOException if there's an error reading the file or writing to response
     */
    public static void writeExcelToResponse(HttpServletResponse response, String filePath) throws IOException {
        writeExcelToResponse(response, filePath, null);
    }

    /**
     * Sends an error message using the response output stream (not writer)
     * This ensures consistency when getOutputStream() has already been called
     *
     * @param response HttpServletResponse to write to
     * @param message Error message to send
     */
    public static void sendError(HttpServletResponse response, String message) {
        try {
            if (!response.isCommitted()) {
                response.reset(); // clear previous buffer if safe
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.setContentType("text/plain; charset=UTF-8");

                try (ServletOutputStream out = response.getOutputStream()) {
                    out.write(message.getBytes(StandardCharsets.UTF_8));
                    out.flush();
                    logger.debug("Sent error message via output stream: {}", message);
                }
            } else {
                logger.warn("Cannot send error message - response already committed: {}", message);
            }
        } catch (IOException | IllegalStateException e) {
            logger.error("Unable to send error message: {}", e.getMessage(), e);
            // Do nothing else. Don't attempt getWriter() - Jetty doesn't allow stream switching
        }
    }
}
