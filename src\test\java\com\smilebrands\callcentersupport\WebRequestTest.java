package com.smilebrands.callcentersupport;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.*;

import com.smilebrands.callcentersupport.domain.*;
import com.smilebrands.callcentersupport.domain.constants.PreferredRecallTime;
import com.smilebrands.callcentersupport.repo.FacilityEmailRepository;
import com.smilebrands.callcentersupport.service.CommunicationService;
import com.smilebrands.callcentersupport.service.WebRequestService;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import jxl.write.*;
import jxl.write.Number;
import org.apache.commons.lang.StringEscapeUtils;
import org.codehaus.jackson.map.DeserializationConfig;
import org.codehaus.jackson.map.ObjectMapper;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.smilebrands.callcentersupport.repo.MongoRepository;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * User: Alban Moreau
 * Date: 11/03/14
 */
public class WebRequestTest extends BaseTest {

    @Autowired
    MongoRepository mongoRepository;

    @Autowired
    MongoOperations mongoOperations;

    @Autowired
    protected CommunicationService communicationService;

    @Autowired
    protected FacilityEmailRepository facilityEmailRepository;

    @Autowired
    protected WebRequestService webRequestService;

    @Autowired
    @Qualifier("postgresTemplate")
    protected JdbcTemplate postgresTemplate;

    @Test
    public void helloWorld() {
        logger.debug("Hello World");
        logger.debug(UUID.randomUUID().toString());

        SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss a");
        logger.debug(sdf.format(new Date()));
    }

    @Test
    public void saveWebRequest() {
        logger.debug("BEGIN: saveWebRequest");
        WebRequest wr = new WebRequest();

        wr.setState("CA");
        wr.setCity("Temecula");
        wr.setOffice(10230);

        wr.setIsNewPatient(true);
        wr.setReasonForAppointment("New Appointment");

        wr.setFirstName("Alban");
        wr.setLastName("Moreau");
        wr.setZipCode("92618");
        wr.setPhone(9093629774L);
        wr.setPhoneType("Cell");
        wr.setEmailAddress("<EMAIL>");

        wr.setAppointmentDate(VelocityUtils.getDateAsString(new Date(), null));
        wr.setPreferredTime("Morning");


        wr.setUuid(UUID.randomUUID().toString());

        mongoRepository.saveWebRequest(wr);
        logger.debug("END: saveWebRequest");
    }

    @Test
    public void findTodayWebRequest() {
        logger.debug("BEGIN: findTodayWebRequest");

        List<WebRequest> todayRequest = mongoRepository.getTodayWebRequest(new Date());

        logger.info("number of requested appointment: {}", todayRequest.size());

        for (WebRequest wr : todayRequest) {
            logger.info("appointmentDate: {}", wr.getAppointmentDate());
        }

        logger.debug("END: findTodayWebRequest");
    }

    @Test
    public void testGetWebRequestToProcess(){
        List<WebRequest> list = mongoRepository.getWebRequestToProcess(null,0);
        logger.debug("list size: {}", list.size());
        for(WebRequest wr : list){
            logger.debug(wr.getUuid() + ":::" + wr.getAppointmentDate());
        }
    }

    @Test
    public void resetWebRequest(){
        String[] ids = new String[]{"d13fbcdc-17ff-473d-bd1c-a9879159044c", "154082fd-**************-9e1d65f7183e"};
        for(String id : ids){
            WebRequest wr = mongoRepository.findWebRequest(id);
            if(wr != null){
                logger.debug("for [" + id + "] pending process at " + wr.getPendingProcessedDateTime() + " by " + wr.getPendingProcessedBy());
                wr.setPendingProcessedBy(null);
                wr.setPendingProcessedDateTime(null);
                mongoRepository.saveWebRequest(wr);
            }
        }
    }

    @Test
    public void funWithPreferredRecallTime(){
        PreferredRecallTime preferredRecallTime = PreferredRecallTime.getByOrder(0);
        logger.debug("preferred recall time: {}", preferredRecallTime);
        logger.debug("is ASAP: {}", (preferredRecallTime == PreferredRecallTime.ASAP));
        logger.debug("is Tomorrow: {}", (preferredRecallTime == PreferredRecallTime.TOMORROW));
        logger.debug("is Next week: {}", (preferredRecallTime == PreferredRecallTime.NEXT_WEEK));
    }

    @Test
    public void testFindWebRequestByPhoneNUmberAndEmail(){
        logger.debug("BEGIN: testFindWebRequestByPhoneNUmberAndEmail");
        WebRequest wr = new WebRequest();

        wr.setState("CA");
        wr.setCity("Temecula");
        wr.setOffice(10230);

        wr.setIsNewPatient(true);
        wr.setReasonForAppointment("New Appointment");

        wr.setFirstName("Alban");
        wr.setLastName("Moreau");
        wr.setZipCode("92618");
        wr.setPhone(**********L);
        wr.setPhoneType("Cell");
        wr.setEmailAddress("<EMAIL>");

        wr.setAppointmentDate(VelocityUtils.getDateAsString(new Date(), null));
        wr.setPreferredTime("Morning");
        wr.setCreateDateTime(new Date());


        wr.setUuid(UUID.randomUUID().toString());

        mongoRepository.saveWebRequest(wr);

        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        if (mongoRepository.findWebRequestByPhoneNUmberAndEmail(**********L, "<EMAIL>") != null) {
            logger.debug("WebRequest saved within the last hour");
        } else {
            logger.debug("Test Failed!");
        }

        mongoOperations.remove(wr);
        logger.debug("END: testFindWebRequestByPhoneNUmberAndEmail");
    }

    @Test
    public void testJson() throws UnsupportedEncodingException {
//        String json = "office=10560&lastName=Moreau&appointmentDate=2014-12-24&phone=**********&reasonForAppointment=New+appointment&phoneType=Land&preferredTime=Morning&emailAddress=alban.moreau%40smilebrands.com&isNewPatient=true&originWebSite=www.brightnow.com&officeName=Bright+Now%21+Dental&zipCode=23123&officeAddress=280+South+Mountain+Avenue%2C+Upland%2C++91786&firstName=Alban&comment=test+%0A%0Arrestea%0Asd%0A212";
        String json = "office%7E%5E%7C10560%7C%5E%7ElastName%7E%5E%7CMoreau%7C%5E%7EappointmentDate%7E%5E%7C2014-12-24%7C%5E%7Ephone%7E%5E%7C**********%7C%5E%7EreasonForAppointment%7E%5E%7CNew+appointment%7C%5E%7EphoneType%7E%5E%7CLand%7C%5E%7EpreferredTime%7E%5E%7CMorning%7C%5E%7EemailAddress%7E%5E%7Calban.moreau%40smilebrands.com%7C%5E%7EisNewPatient%7E%5E%7Ctrue%7C%5E%7EoriginWebSite%7E%5E%7Cwww.brightnow.com%7C%5E%7EofficeName%7E%5E%7CBright+Now%21+Dental%7C%5E%7EzipCode%7E%5E%7C23123%7C%5E%7EofficeAddress%7E%5E%7C280+South+Mountain+Avenue%2C+Upland%2C++91786%7C%5E%7EfirstName%7E%5E%7CAlban%7C%5E%7Ecomment%7E%5E%7Ctest+%0A%0Arrestea%0Asd%0A212";

        json = json.replace("%0A", " ");
        json = json.replace("%7E%5E%7C", "\":\"");  // ~^|
        json = json.replace("%7C%5E%7E", "\",\"");  // |^~

        logger.debug("json: {}", json);

        String decodedUrl = URLDecoder.decode(json, "UTF-8");

        decodedUrl = "{\"" + decodedUrl + "\"}";
        decodedUrl = decodedUrl.replaceAll("=", "\":\"");
        decodedUrl = decodedUrl.replaceAll("&", "\",\"");

        logger.debug("decodedUrl : {}", decodedUrl);

        WebRequest wr  = (WebRequest) convertJson(decodedUrl, WebRequest.class);
        logger.debug("comment: {}", wr.getComment());
        logger.debug("WR: {}", wr);
        logger.debug("End testJson");
    }

    private Object convertJson(String json, Class clazz) {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationConfig.Feature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        try {
            return mapper.readValue(json, clazz);

        } catch (Exception e) {
            logger.error("JsonConversionException " + e.getMessage());
            e.printStackTrace();

            throw new RuntimeException("Unable to convert Json [" + json + "] to Class [" + clazz.getName() + "]<br/> Msg: " + e.getMessage()+ "!");
        }
    }

    @Test
    public void testSendEmail(){
        logger.debug("BEGIN: testSendEmail");
        WebRequest wr = new WebRequest();

        wr.setState("CA");
        wr.setCity("Temecula");
        wr.setOffice(10230);

        wr.setIsNewPatient(true);
        wr.setReasonForAppointment("New Appointment");

        wr.setFirstName("Alban");
        wr.setLastName("Moreau");
        wr.setZipCode("92618");
        wr.setPhone(**********L);
        wr.setPhoneType("Cell");
        wr.setEmailAddress("<EMAIL>");

        wr.setAppointmentDate(VelocityUtils.getDateAsString(new Date(), null));
        wr.setPreferredTime("Morning");
        wr.setCreateDateTime(new Date());


        wr.setUuid(UUID.randomUUID().toString());

        Map model = new HashMap();

        Date appDate = VelocityUtils.parseDate(wr.getAppointmentDate(), "yyyy-MM-dd");

        // Web request Info
        model.put("createDatetime", VelocityUtils.getDateAsString(new Date(), "EEEE MM-dd-yyyy HH:mm:ss a"));

        // Patient Info
        model.put("firstName", wr.getFirstName());
        model.put("lastName", wr.getLastName());
        model.put("phone", VelocityUtils.phoneFormatter(wr.getPhone().toString()));
        model.put("phoneType", wr.getPhoneType());
        model.put("emailAddress", wr.getEmailAddress());
        model.put("zipCode", wr.getZipCode());

        // Appointment Info
        model.put("reason", wr.getReasonForAppointment());
        model.put("day", VelocityUtils.getDateAsString(appDate, "EEEE MM-dd-yyyy"));
        model.put("time", wr.getPreferredTime());
        model.put("newPatient", (wr.getIsNewPatient() ? "Yes" : "No"));
        model.put("comment", wr.getComment());

        // Facility Info
        model.put("facilityId", wr.getOffice());
        model.put("facilityName", wr.getOfficeName());
        model.put("facilityAddress", wr.getOfficeAddress());

        communicationService.sendNotificationViaEmail("<EMAIL>", "<EMAIL>", model, "appointment_template_3.0.vm", "Appointment Request - JUnit Test");

        logger.debug("END: testSendEmail");
    }

    @Test
    public void testGetLongQueuedWebRequest(){
        List<WebRequest> webRequests = mongoRepository.findQueuedWebRequest(null);
        int count = 1;
        String msg = "";
        for(WebRequest wr : webRequests){
            logger.debug(count + "/ " + wr + "\nNumber of Process: " + wr.getNumberOfProcess());
            msg +=  count + "/ " + wr + "\nNumber of Process: " + wr.getNumberOfProcess() + "\n\n\n";
            count++;
        }
        //String subject = webRequests.size() + " web request(s) pending.";
        //communicationService.sendNotificationViaEmailAsPlainText("<EMAIL>", msg, subject);
    }

    @Test
    public void getWebRequestReport(){
        Query query = new Query();
        query.with(new Sort(Sort.Direction.ASC, "createDateTime"));
        List<WebRequest> webRequests = mongoOperations.find(query, WebRequest.class);
        logger.debug("webRequest size: {}", webRequests.size());
        LinkedHashMap<String, LinkedHashMap> map = new LinkedHashMap<String, LinkedHashMap>();
        int totalCnt = 0, emailCnt = 0, callCenterCnt = 0;
        double duration = 0;
        try{
            WritableWorkbook workbook = Workbook.createWorkbook(new File("output_as of " + VelocityUtils.getDateAsString(new Date(), "yyyy-MM-dd HH:mm:ss")+".xls"));
            WritableSheet summarySheet = workbook.createSheet("Summary", 0);
            Label dateLbl = new Label(0, 0, "Date");
            summarySheet.addCell(dateLbl);
            Label totalLbl = new Label(1, 0, "Total");
            summarySheet.addCell(totalLbl);
            Label emailLbl = new Label(2, 0, "Email");
            summarySheet.addCell(emailLbl);
            Label ccLbl = new Label(3, 0, "Call Center");
            summarySheet.addCell(ccLbl);
            Label durationLbl = new Label(4, 0, "Duration in Seconds");
            summarySheet.addCell(durationLbl);
            Label durationInMinLbl = new Label(5, 0, "Duration in Minutes");
            summarySheet.addCell(durationInMinLbl);

            WritableSheet detailSheet = workbook.createSheet("Detail", 1);
            Label detailDateLbl = new Label(0, 0, "Date");
            detailSheet.addCell(detailDateLbl);
            Label nameLbl = new Label(1, 0, "Name");
            detailSheet.addCell(nameLbl);
            Label emailDetailLbl = new Label(2, 0, "Email");
            detailSheet.addCell(emailDetailLbl);
            Label phoneLbl = new Label(3, 0, "Phone");
            detailSheet.addCell(phoneLbl);
            Label appointmentLbl = new Label(4, 0, "Appointment Request");
            detailSheet.addCell(appointmentLbl);
            Label createLbl = new Label(5, 0, "Created On");
            detailSheet.addCell(createLbl);
            Label processLbl = new Label(6, 0, "Processed On");
            detailSheet.addCell(processLbl);
            Label durationDetailLbl = new Label(7, 0, "Duration in Seconds");
            detailSheet.addCell(durationDetailLbl);
            Label durationInMinDetailLbl = new Label(8, 0, "Duration in Minutes");
            detailSheet.addCell(durationInMinDetailLbl);
            Label officeLbl = new Label(9, 0, "Office");
            detailSheet.addCell(officeLbl);
            Label commentLbl = new Label(10, 0, "Comment");
            detailSheet.addCell(commentLbl);

            int count = 1;
            for(WebRequest wr : webRequests){
                String formattedDate = VelocityUtils.getDateAsString(wr.getCreateDateTime(), "EEE. MMM dd yyyy");
                LinkedHashMap<String, Object> dateMap = (LinkedHashMap)map.get(formattedDate);
                if(dateMap == null){
                    dateMap = new LinkedHashMap<String, Object>();
                    map.put(formattedDate, dateMap);
                    dateMap.put("Date", formattedDate);
                    dateMap.put("Total", 0);
                    dateMap.put("Email", 0);
                    dateMap.put("Call Center", 0);
                    dateMap.put("Duration", 0D);
                }
                Date created = wr.getCreateDateTime();
                Date processed = wr.getProcessedDateTime() != null
                                ? VelocityUtils.parseDate(wr.getProcessedDateTime(), "yyyy-MM-dd HH:mm:ss")
                                : new Date();

                dateMap.put("Total", (Integer)dateMap.get("Total") + 1);
                if(wr.getIsNewPatient()){
                    PhoneNumber pn = mongoRepository.findPhoneNumberByFacility(wr.getOffice(), "ML");
                    if(pn != null && pn.isCallCenterSupported()){
                        dateMap.put("Call Center", (Integer)dateMap.get("Call Center") + 1);
                        duration = processed.getTime() - created.getTime();
                        dateMap.put("Duration", (Double)dateMap.get("Duration") + duration);
                        Label dateValLbl = new Label(0, count, formattedDate);
                        detailSheet.addCell(dateValLbl);
                        Label nameValLbl = new Label(1, count, wr.getFirstName() + " " + wr.getLastName());
                        detailSheet.addCell(nameValLbl);
                        Label emailValLbl = new Label(2, count, wr.getEmailAddress());
                        detailSheet.addCell(emailValLbl);
                        Label phoneValLbl = new Label(3, count, wr.getPhone() + (wr.getPhoneType() != null ? " (" + wr.getPhoneType() + ")" : ""));
                        detailSheet.addCell(phoneValLbl);
                        Label apptValLbl = new Label(4, count, wr.getAppointmentDate() + (wr.getPreferredTime() != null ? " (" + wr.getPreferredTime() + ")" : ""));
                        detailSheet.addCell(apptValLbl);
                        Label createValLbl = new Label(5, count, VelocityUtils.getDateAsString(wr.getCreateDateTime(), "yyyy-MM-dd HH:mm:ss"));
                        detailSheet.addCell(createValLbl);
                        Label processedValLbl = new Label(6, count, wr.getProcessedDateTime() != null ? wr.getProcessedDateTime() : "");
                        detailSheet.addCell(processedValLbl);
                        Number durationValLbl = new Number(7, count, new BigDecimal(duration/1000).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue());
                        detailSheet.addCell(durationValLbl);
                        Number durationInMinValLbl = new Number(8, count, new BigDecimal(duration/1000/60).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue());
                        detailSheet.addCell(durationInMinValLbl);
                        Label officeValLbl = new Label(9, count, wr.getOffice() + " - " + wr.getOfficeName() + " (" + wr.getOfficeAddress() + ")");
                        detailSheet.addCell(officeValLbl);
                        Label commentValLbl = new Label(10, count, wr.getComment());
                        detailSheet.addCell(commentValLbl);


                        count++;
                    }else{
                        dateMap.put("Email", (Integer)dateMap.get("Email") + 1);
                    }
                }else{
                    dateMap.put("Email", (Integer)dateMap.get("Email") + 1);
                }
            }
            count = 1;
            for(String key : map.keySet()){
                LinkedHashMap dateMap = (LinkedHashMap)map.get(key);
                totalCnt = (Integer)dateMap.get("Total");
                emailCnt = (Integer)dateMap.get("Email");
                callCenterCnt = (Integer)dateMap.get("Call Center");
                duration = (Double)dateMap.get("Duration");
                duration = duration/callCenterCnt;
                String msg = "\n\nDate: " + key;
                msg += "\nTotal Count: " + totalCnt;
                msg += "\nEmail Count: " + emailCnt;
                msg += "\nCall Center Count: " + callCenterCnt;
                msg += "\nDuration: " + duration;
                msg += "\nAverage Duration: " + new BigDecimal(duration/1000).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                msg += "\n\n";
                logger.debug("{}", msg);

                Label dateValLbl = new Label(0, count, key);
                summarySheet.addCell(dateValLbl);
                Number totalValLbl = new Number(1, count, totalCnt);
                summarySheet.addCell(totalValLbl);
                Number emailValLbl = new Number(2, count, emailCnt);
                summarySheet.addCell(emailValLbl);
                Number ccValLbl = new Number(3, count, callCenterCnt);
                summarySheet.addCell(ccValLbl);
                Number durationValLbl = new Number(4, count, new BigDecimal(duration/1000).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue());
                summarySheet.addCell(durationValLbl);
                Number durationMinValLbl = new Number(5, count, new BigDecimal(duration/1000/60).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue());
                summarySheet.addCell(durationMinValLbl);
                count++;

//                LinkedHashMap<String, String> contentRow = new LinkedHashMap<String, String>();
//                contentRow.put("Date", key);
//                contentRow.put("Total", totalCnt + "");
//                contentRow.put("Email", emailCnt + "");
//                contentRow.put("Call Center", callCenterCnt + "");
//                contentRow.put("Duration (in second)", (duration/1000) + "");
//                mapContent.add(contentRow);
            }

//            VelocityUtils.convertListMapToCSV(mapContent, "test.csv");
            workbook.write();
            workbook.close();

        }catch (Exception ex){

        }
    }


    @Test
    public void testCheckWebRequestForCloseOffice(){
        Query query = new Query();
        query.with(new Sort(Sort.Direction.ASC, "createDateTime"));
        List<WebRequest> webRequests = mongoOperations.find(query, WebRequest.class);
        logger.debug("webRequest size: {}", webRequests.size());
        for(WebRequest wr : webRequests){
            Facility facility = mongoRepository.findFacilityDetail(wr.getOffice());
            if(facility == null){
                logger.debug(wr.getUuid() + " set to close office[" + wr.getOffice() + "]");
            }
        }
    }

    @Test
    public void reSendWebRequest() {
//        String uuid = "6f095cc1-270e-4500-94d6-f22cde74afe7";
//        String uuid = "51ed4c90-a089-4e11-985d-d3fef8ebad86";
//        String uuid = "81dc69b3-dc68-4a01-ab49-7973ea80d17e";
//        String uuid = "fc3730f0-e707-43a7-a323-a212abc73a42";
        String uuid = "9eebd5cd-5c30-4de2-a168-6c15871772e2";
        WebRequest wr = mongoRepository.findWebRequest(uuid);
        Integer facilityId = wr.getOffice();

        // Email message model variables
        Map model = new HashMap();

        Date appDate = VelocityUtils.parseDate(wr.getAppointmentDate(), "yyyy-MM-dd");

        // Web request Info
        model.put("createDatetime", VelocityUtils.getDateAsString(new Date(), "EEEE MM-dd-yyyy HH:mm:ss a"));

        // Patient Info
        model.put("firstName", wr.getFirstName());
        model.put("lastName", wr.getLastName());
        model.put("phone", wr.getPhone() != null ? VelocityUtils.phoneFormatter(wr.getPhone().toString()) : "");
        model.put("phoneType", wr.getPhoneType());
        model.put("emailAddress", wr.getEmailAddress());
        model.put("zipCode", wr.getZipCode());

        // Appointment Info
        model.put("reason", wr.getReasonForAppointment());
        model.put("day", VelocityUtils.getDateAsString(appDate, "EEEE MM-dd-yyyy"));
        model.put("time", wr.getPreferredTime());
        model.put("newPatient", (wr.getIsNewPatient() ? "Yes" : "No"));
        model.put("comment", wr.getComment());

        // Facility Info
        model.put("facilityId", facilityId);
        model.put("facilityName", wr.getOfficeName());
        model.put("facilityAddress", wr.getOfficeAddress());

        // Find facility call center web appointment emails.
        List<String> callCenterEmails = facilityEmailRepository.getCallCenterEmail(facilityId.longValue());

        // Find facility manager emails and web appointment emails.
        List <String> managerEmails = facilityEmailRepository.getApptEmail(facilityId.longValue());

        // This is who we'll email ...
        List <String> emails = null;

        List <EmailSuccess> es = new ArrayList();
        boolean success = true;

        /**
         *  If this is a new patient appointment request and the office has ccEmails configured, send the ccEmails collection
         *  which is the supporting call center.
         *
         *  If this is not a new patient appointment request, send to the managerEmails collection.
         *
         */
        if (wr.getIsNewPatient() && callCenterEmails.size() > 0) {
            logger.info("Appt request for a new patient in a Call Center supported office.");
            emails = callCenterEmails;
        } else {
            emails = managerEmails;
        }

        if (emails == null || emails.size() == 0) {
            emails = new ArrayList<String>();
            emails.add(facilityId + "-<EMAIL>");
        }

        String recipient = "";

        // Send email to managers (Plain Text)
        for (String mgrEmail : emails) {
            boolean rez = false;
            rez = communicationService.sendNotificationViaEmail(wr.getNoReplyEmailAddress(), mgrEmail, model, "appointment_template_3.0.vm", "Appointment Request - " + wr.getWebSiteHostName());

            recipient += mgrEmail + ", ";
            logger.info("Appointment Email sent to:  " + mgrEmail + " was successful: " + rez);

            if (success && !rez) {
                logger.warn("An invalid Email [" + mgrEmail + "] has been found!  Sending the <NAME_EMAIL>");
                success = false;
            }

            EmailSuccess _es = new EmailSuccess();
            _es.setEmail(mgrEmail);
            _es.setSuccess(rez);
            es.add(_es);
        }
        wr.setRecipient(recipient);

        mongoRepository.saveWebRequest(wr);

        if (!success) {
            logger.info("Sent a failure email containing " + es.size() + " entries.");
        }
    }

    @Test
    public void testWebRequestRecipient() {
        Integer facilityId = 10690;

        // Find facility call center web appointment emails.
        List<String> ccEmails = facilityEmailRepository.getCallCenterEmail(facilityId.longValue());

        // Find facility manager emails and web appointment emails.
        List <String> managerEmails = facilityEmailRepository.getApptEmail(facilityId.longValue());

        // This is who we'll email ...
        List <String> emails = null;

        List <EmailSuccess> es = new ArrayList();
        boolean success = true;

        /**
         *  If this is a new patient appointment request and the office has ccEmails configured, send the ccEmails collection
         *  which is the supporting call center.
         *
         *  If this is not a new patient appointment request, send to the managerEmails collection.
         *
         */
        if (ccEmails.size() > 0) {
            logger.info("Appt request for a new patient in a Call Center supported office.");
            emails = ccEmails;
        } else {
            emails = managerEmails;
        }

        if (emails == null || emails.size() == 0) {
            emails = new ArrayList<String>();
            emails.add(facilityId + "-<EMAIL>");
        }
        String recipient = "";

        for (String mgrEmail : emails) {
            recipient += mgrEmail + ", ";
        }

        logger.debug("Email will be sent to: {}", recipient);
    }

    @Test
    public void testDecodeUrl() {
        String url = "alban &amp; moreau";
        String result = StringEscapeUtils.unescapeHtml(url);

        logger.debug("Result: {}", result);
    }

    @Test
    public void releasePendingWebRequest(){
        List<WebRequest> webRequests = mongoRepository.findQueuedWebRequest(null);
//        List<WebRequest> webRequests = new ArrayList<WebRequest>();
        int numberOfTry = 0;
//        do{
//            webRequests = mongoRepository.getWebRequestToProcess(null, 0);
            numberOfTry++;
            int count = 1;
            for(WebRequest wr : webRequests){
                logger.debug(count + "/{}", wr);
                wr.setPendingProcessedBy(null);
                wr.setPendingProcessedDateTime(null);
                mongoRepository.saveWebRequest(wr);
                count++;
            }
//        }while(webRequests.size() > 0 && numberOfTry <= 100);
        logger.debug("number of trial: {}", numberOfTry);
        logger.debug("error occurs: {}", webRequests.size() == 0);
    }

    @Test
    public void testSendEmailForNonContact(){
//        webRequestService.sendEmailToNonContactWebRequest("2ec3020b-4558-4ebc-bf4a-b9cad88bdf41", null);
//        String str = "<EMAIL>";
//        logger.debug("substr: {}", str.substring(0, str.indexOf("@")));
        String[] uuids = new String[]{
                "dff88b12-650c-4bba-bb26-725d397945ac"
//                "2ab5b769-24fd-4d3d-ab85-2504b3b4aea2",
//                "980c45fc-4f97-4086-8536-439c9ca6b82e",
//                "a8d3e1ba-06ce-4b00-a47e-2ca11f6b5033",
//                "b93465cf-81d8-458e-befc-e7074d661ec6",
//                "b628d1c6-9d23-4e0a-b5f1-6901a7acc247",
//                "345b531e-4f54-429c-bb7a-264c30164cf3",
//                "2f1fd24b-bb97-435e-af67-ef6334e118ea",
//                "dd7fd344-17f8-40c1-841a-0f9e1aae52e7",
//                "2ea4afca-5cbd-4fb2-a1de-303153ca7b18",
//                "d25002e7-889c-483c-8643-0d74de396da9",
//                "8f2d67df-3e11-4d48-ad42-720b07c5135d",
//                "eb3e482c-73b7-4700-aa6f-118c94055da1",
//                "9df8a1a8-6218-4991-a67a-c7e812bb9bb6",
//                "93e3e09f-2cad-422f-9bcf-69e548174a6e",
//                "17a7adba-e05b-403a-9cf0-8b1851b7ace8",
//                "0fc5c354-cfd5-4349-b61e-1075a8b48cf4",
//                "6795101c-d19f-44c9-80bc-71316b60aa38",
//                "635d7726-33bf-451f-8e5c-46c9c3034bb4",
//                "a84c8de1-ba92-4df8-adbf-d7ead3cce8a5",
//                "3cfed335-dcec-4d7e-97eb-7e68b968e01f",
//                "4e6e07dc-4599-4047-ac5a-042915dcea42"
        };
        int count = 1;
        for(String uuid : uuids){
            WebRequest wr = mongoRepository.findWebRequest(uuid);
            if(wr != null && wr.getHandledByAgent() != null && wr.getHandledByAgent() > 0){
                webRequestService.sendEmailToNonContactWebRequest(uuid, wr.getHandledByAgent());
                logger.debug(count + "/resend email for [" + uuid + "]");
                count++;
            }
        }
    }

    @Test
    public void testGetNeighboringOffices(){
        List<Facility> facilities = mongoOperations.findAll(Facility.class);
        try{
            WritableWorkbook workbook = Workbook.createWorkbook(new File("Neighboring Offices.xls"));
            WritableSheet dataSheet = workbook.createSheet("Data", 0);
            Label idLbl = new Label(0, 0, "Facility ID");
            dataSheet.addCell(idLbl);
            Label nameLbl = new Label(1, 0, "Facility Name");
            dataSheet.addCell(nameLbl);
            Label neighboringLbl = new Label(2, 0, "Neighboring Office");
            dataSheet.addCell(neighboringLbl);
            Label distanceLbl = new Label(3, 0, "Distance");
            dataSheet.addCell(distanceLbl);
            Label timeLbl = new Label(4, 0, "Time");
            dataSheet.addCell(timeLbl);
            int count = 1;
            for(Facility facility : facilities){
                Number idVal = new Number(0, count, facility.getFacilityId());
                dataSheet.addCell(idVal);
                Label nameVal = new Label(1, count, facility.getName());
                dataSheet.addCell(nameVal);
                List<Facility> neighboring = mongoRepository.findFacilitiesByZipCode(facility.getZip(), 8);
                for(Facility nf : neighboring){
                    if(nf.getFacilityId().equals(facility.getFacilityId())){
                        continue;
                    }
                    Label name = new Label(2, count, nf.getFacilityId() + " - " + nf.getName());
                    dataSheet.addCell(name);
                    Number distance = new Number(3, count, nf.getDistance());
                    dataSheet.addCell(distance);
                    Number time = new Number(4, count, nf.getTravelTime());
                    dataSheet.addCell(time);
                    count++;
                }
                count++;
            }
            workbook.write();
            workbook.close();
        }catch(Exception ex){
            ex.printStackTrace();
        }
    }

    @Test
    public void testToLoadPriorityFromXcel(){
        try{
//            URL url = this.getClass().getResource("/Queue_priority_routing.xls");
            URL url = this.getClass().getResource("/Banding_on_12-09-15.xls");
            String fileName = url.getFile();

            Workbook workbook = Workbook.getWorkbook(new File(fileName));
            Map<Integer, Integer> summary = new HashMap<Integer, Integer>(){{
                put(1, 0);
                put(2, 0);
                put(3, 0);
                put(4, 0);
                put(5, 0);
            }};
            Set<Integer> facilityIds = new HashSet<Integer>();
            for(int i=0; i<workbook.getNumberOfSheets(); i++){
                Sheet sheet = workbook.getSheet(i);
                if(!sheet.getName().equals("New Banding")){
                    continue;
                }
                Cell band = sheet.findCell("Banding");
//                Cell newBand = sheet.findCell("New band");
//                Cell facility = sheet.findCell("target_facility_id");
                Cell facility = sheet.findCell("Site Code");
                if(facility == null || band == null
//                        || newBand == null
                    ){
                    logger.debug("missing column");
                    return;
                }
                int bandIdx = band.getColumn();
//                int newBandIdx = newBand.getColumn();
                int facilityIdx = facility.getColumn();
                for(int r=facility.getRow()+1; r<sheet.getRows(); r++){
                    Cell facilityCell = sheet.getCell(facilityIdx, r);
                    Cell bandCell = sheet.getCell(bandIdx, r);
//                    Cell newBandCell = sheet.getCell(newBandIdx, r);
                    String facilityStr = facilityCell.getContents();
                    if(facilityStr == null || facilityStr.trim().length() == 0){
                        continue;
                    }
                    Integer facilityId = Integer.valueOf(facilityStr);
                    Integer bandValue = bandCell.getContents() != null && bandCell.getContents().trim().length() > 0 ? Integer.valueOf(bandCell.getContents()) : null;
//                    Integer newBandValue = newBandCell.getContents() != null && newBandCell.getContents().trim().length() > 0 ? Integer.valueOf(newBandCell.getContents()) : null;
//                    Integer priority = newBandValue != null ? newBandValue : bandValue;
                    Integer priority = bandValue;
                    logger.debug("facility ID[" + facilityId + "] has band as " + bandValue + ".");
                    if(priority != null && facilityId != null){
                        facilityIds.add(facilityId);
                        String sql = "update cdw.facility1 set psr_queue_priority=" + priority + ", csr_queue_priority=" + priority + " where facility_id = " + facilityId;
                        logger.debug(sql);
                        postgresTemplate.execute(sql);
                        summary.put(priority, summary.get(priority)+1);
                    }else{
                        logger.debug(facilityId + " does not have priority value.");
                    }
                }
                if(facilityIds.size() > 0){
                    logger.debug("facilityIds.size() = " + facilityIds.size());
                    String idStr = "";
                    int cnt = 1;
                    for(Integer id : facilityIds){
                        idStr += id;
                        if(cnt < facilityIds.size()){
                            idStr += ",";
                        }
                        cnt++;
                    }
                    String sql = "update cdw.facility1 set psr_queue_priority=2, csr_queue_priority=2 where facility_id not in (" + idStr + ")";
                    postgresTemplate.execute(sql);
                    logger.debug(sql);
                }
            }
            logger.debug("summary: {}", summary);
        }catch(Exception ex){
            ex.printStackTrace();
        }
    }
}
