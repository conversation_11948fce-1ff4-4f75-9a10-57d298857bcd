package com.smilebrands.callcentersupport;

import com.smilebrands.callcentersupport.service.CallService;
import com.smilebrands.callcentersupport.util.BaseJsonConverter;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Map;

/**
 * User: <PERSON><PERSON>
 * Date: 7/24/14
 */
public class ExternalServiceTest extends BaseTest {

    @Autowired
    private CallService callService;

    @Test
    public void testAreaCodeSearch() {
        logger.debug(callService.getStateFromAni(7144281200L));
    }

    @Test
    public void testGoogleTranslate() {
        logger.debug(callService.translate("This is an English message translated into French.", "fr"));
    }
}
