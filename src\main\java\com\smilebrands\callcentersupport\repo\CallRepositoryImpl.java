package com.smilebrands.callcentersupport.repo;

import com.smilebrands.callcentersupport.domain.Appointment;
import com.smilebrands.callcentersupport.domain.Facility;
import com.smilebrands.callcentersupport.domain.Patient;
import com.smilebrands.callcentersupport.domain.V_CallLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * User: <PERSON><PERSON>
 * Date: 6/26/14
 */
@Repository
public class CallRepositoryImpl extends JdbcRepository implements CallRepository {

    @Autowired
    @Qualifier("LOOK_UP_QSI_PATIENT")
    protected String FIND_QSI_PATIENT;

    @Autowired
    @Qualifier("LOOK_UP_LIBERTY_PATIENT")
    protected String FIND_LIBERTY_PATIENT;

    @Override
    public boolean queueConfirmationEmail(Integer facilityId, Long patientId, String firstName, String lastName, String emailAddress, String appointmentTime, String language) {
        String insertStmt = QUEUE_EMAIL_CONFIRM
                .replaceFirst("##APPT_DATETIME##", toApptDate(appointmentTime))
                .replaceFirst("##PATIENT_ID##", String.valueOf(patientId))
                .replaceFirst("##EMAIL_ADDRESS##", emailAddress)
                .replaceFirst("##FIRST_NAME##", firstName)
                .replaceFirst("##LAST_NAME##", lastName)
                .replaceFirst("##LANGUAGE##", language);

        logger.debug("Converted SQL: " + insertStmt);
        int insertCnt = jdbcTemplate.update(insertStmt, new Object[]{ facilityId });

        return insertCnt > 0;
    }

    @Override
    public Appointment findAppointmentByPatientPhoneNumber(Long phoneNumber, Integer facilityId, String dob) {
        List<Appointment> opts = getQsiPatientAppointments(phoneNumber, facilityId, dob);

        if (opts.size() > 0) {
            return opts.get(0);
        }

        return null;
    }

    @Override
    public Appointment findLibertyEventByPatientPhoneNumber(Long phoneNumber, Integer facilityId, String dob) {
        Appointment result = null;
        List<Appointment> opts = getLibertyPatientAppointments(phoneNumber, facilityId, dob);
        if(opts.size() > 0){
            result = opts.get(0);
        }
        return result;
    }

    @Override
    public List<Appointment> findAppointmentsByPatientPhoneNumber(Long phoneNumber, Integer facilityId, String dob) {
        return getQsiPatientAppointments(phoneNumber, facilityId, dob);
    }

    @Override
    public List<Appointment> findLibertyEventsByPatientPhoneNumber(Long phoneNumber, Integer facilityId, String dob) {
        return getLibertyPatientAppointments(phoneNumber, facilityId, dob);
    }

    @Override
    public Patient findPatientByIdAndFacility(Long patientId, Integer facilityId, String systemSource) {
        Patient result = null;
        String query = null;
        Object[] params = null;
        if(systemSource.equalsIgnoreCase("QSI")){
            query = FIND_QSI_PATIENT;
            params = new Object[]{patientId, facilityId};
        }else if(systemSource.equalsIgnoreCase("LIBERTY")){
            query = FIND_LIBERTY_PATIENT;
            params = new Object[]{patientId};
        }
        if(query != null){

            List<Patient> patients = jdbcTemplate.query(query, params, new RowMapper<Patient>() {
                @Override
                public Patient mapRow(ResultSet resultSet, int i) throws SQLException {
                    Patient patient = new Patient();
                    patient.setPatientFirstName(resultSet.getString("FIRST_NAME"));
                    patient.setPatientLastName(resultSet.getString("LAST_NAME"));
                    patient.setDateOfBirth(resultSet.getDate("DATE_OF_BIRTH"));
                    return patient;
                }
            });
            if(patients.size() > 0){
                result = patients.get(0);
            }
        }
        return result;
    }

    @Override
    public List<V_CallLog> findAgentCallLogs(Integer agentId, Date callDate) {
        return getAgentCallLog(agentId, callDate);
    }

    /** Format Appointment Date. **/
    private String toApptDate(String date) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'hh:mmaa");
        DateFormat sf = new SimpleDateFormat("yyyy/MM/dd  HH:mm");

        try {
            Date in = df.parse(date);
            return sf.format(in);

        } catch (ParseException px) {
            px.printStackTrace();
            return date;
        }
    }

}
