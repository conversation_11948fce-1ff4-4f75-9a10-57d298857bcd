package com.smilebrands.callcentersupport.service;

import com.smilebrands.callcentersupport.domain.CommunicationTemplate;
import com.smilebrands.callcentersupport.domain.Facility;
import com.smilebrands.callcentersupport.domain.InboundCall;
import com.smilebrands.callcentersupport.domain.mandrill.SendRequest;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * Created by phongpham on 7/14/14.
 */
public interface CommunicationService {

    public boolean sendNotificationViaEmail(String emailAddress, String message, String subject);
    public boolean sendNotificationViaEmailAsPlainText(String emailAddress, String message, String subject);
    public boolean sendNotificationViaEmail(String from, String emailAddress, Map model, String template, String subject);
    public boolean sendNotificationViaEmail(String emailAddress, Map model, String template, String subject, String attachment, boolean deleteAttachment);
    public boolean sendNotificationViaEmail(String[] emailAddresses, Map model, String template, String subject, String attachment, boolean deleteAttachment);

    public String sendSMS(String to, String message, Integer facilityId, Long replyTo);

    public String sendNotification(String from, String fromName, String recipient, Map model, String template, String subject, String token);
    public String sendNotificationViaMandrill(SendRequest sendRequest);
    public String sendNotificationViaAmazon(SendRequest sendRequest);

    public void sendApplicationErrorToDeveloper(Integer employeeNumber, Exception exception, HttpServletRequest request, String body, String subject);

    public void updateEmailRecipients();

    public CommunicationTemplate getCommunicationTemplateById(Long templateId);
}
