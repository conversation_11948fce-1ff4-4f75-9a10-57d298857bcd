package com.smilebrands.callcenter.config;

import org.eclipse.jetty.server.handler.ErrorHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.smilebrands.callcenter.web.error.CustomErrorHandler;

/**
 * Configuration class to customize Jetty server settings
 * for better handling of incomplete requests.
 *
 * Note: We're not using Spring Boot's JettyServletWebServerFactory
 * because this project uses traditional Jetty configuration.
 */
@Configuration
public class JettyCustomConfiguration {

    /**
     * Creates a custom error handler bean that can be referenced
     * in web.xml or other configuration files.
     */
    @Bean
    public ErrorHandler errorHandler() {
        return new CustomErrorHandler();
    }
}
