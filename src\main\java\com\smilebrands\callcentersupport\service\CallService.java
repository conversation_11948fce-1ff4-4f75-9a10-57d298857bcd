package com.smilebrands.callcentersupport.service;

import com.smilebrands.callcentersupport.domain.*;
import com.smilebrands.callcentersupport.domain.partner.PartnerConfig;
import com.smilebrands.callcentersupport.domain.report.InboundCallSummary;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * User: <PERSON><PERSON>
 * Date: 6/26/14
 *
 * Service interface used to record InboundCall with events as well as AgentCalls with it's associated agent actions.
 *
 */
public interface CallService {

    public String generateUUID();

    public String getStateFromAni(Long ani);

    public Facility findFacilityByFacilityId(Integer facilityId);

    public Campaign findCampaign(Long dnis);

    public List<Facility> findFacilitiesByZipCode(String zipCode);

    public InboundCall registerCall(Long dnis, Long ani, String sessionId);

    public InboundCall copyInboundCall(String oldSessionId, String newSessionId);

    public InboundCall findInboundCall(String uuid);

    public InboundCall findInboundCallBySessionId(String sessionId);

    public void updateInboundCall(InboundCall call);

    public void updateInboundCallScreenPopResult(String uuid, Integer agentId, boolean result);

    public PhoneNumber findPhoneNumberByFacilityId(Integer facilityId);

    public PhoneNumber findPhoneNumberByFacilityId(Integer facilityId, String uuid);

    public PhoneNumber findPhoneNumberByFacilityAndPhoneType(Integer facilityId, String phoneType);

    public Appointment findAppointmentByPatientPhoneNumber(Long phoneNumber, Integer facilityId, String dob);

    public List<Appointment> findAppointmentsByPatientPhoneNumber(Long phoneNumber, Integer facilityId, String dob);

    public List<Appointment> findAppointments(Long patientId, Long phoneNumber, Integer facilityId);

    public Patient findPatientByIdAndFacility(Long patientId, Integer facilityId, String sourceSystem);

    public PhoneNumber findFacilityByAni(Long phoneNumber);

    public void registerCallEvent(String uuid, String eventId);

    public void registerCallLanguage(String uuid, String language);

    public void registerCallMainMenuNumberPress(String uuid, Integer mainMenuNumberPress);

    public void registerSessionId(String uuid, String sessionId);

    public AgentCall startAgentCall(String uuid, Integer employeeNumber, Integer facilityId, String screenType, String sessionId);

    public AgentCall findAgentCall(String uuid);

    public AgentCall findAgentCallByAni(String uuid, Long ani);

    public AgentCall updateAgentCallWithFacility(String uuid, Integer facilityId);

    public AgentCall updateAgentCallWithEmployeeNumber(String uuid, Integer employeeNumber, String screenType);

    public AgentCall updateAgentCallWithScreenType(String uuid, String screenType);

    @Deprecated
    public AgentCall updateAgentCallWithPatientDetails(String uuid, Long patientId, String firstName, String lastName, String emailAddress, String appointmentTime, String language, boolean emailImmediately, String sourceSystem);

    @Deprecated
    public AgentCall updateAgentCallWithResolutionCode(String uuid, String resolutionCode, String reasonCode);

    @Deprecated
    public AgentCall updateAgentCallWithUnloadEvent(String uuid);

    public AgentCall updateAgentCallWithUnloadEvent(String uuid, Integer employeeNumber, String screenType, Date unloadDateTime);

    @Deprecated
    public AgentCall updateAgentCallWithUnloadEventAndResolution(String uuid, String resolutionCode, String reasonCode);

    @Deprecated
    public AgentCall updateAgentCallWithPatientPhone(String uuid, Long phoneNumber);

    public AgentCall updateAgentCall(AgentCall agentCall, boolean modifyExisting);

    public List<AgentCall> searchAgentCall(String date, List<Integer> employeeNumbers);

    public boolean queueConfirmationEmail(Integer facilityId, Long patientId, String firstName, String lastName, String emailAddress, String appointmentTime, String language);

    public LoadedEmployee findLoadedEmployee(AgentCall call, Integer employeeNumber, String screenType);

    public String translate(String message, String language);

    public List<Object> getDistinctFieldFromCollection(String collectionName, String fieldName);

    public List<InboundCallSummary> getInboundCallSummary(String callCenterName, String phoneType, String menuPress, Date processDate);

    public Map<String, Object> getInboundCallSummaryReport(String callCenterName, String phoneType, String menuPress, String eventCount);

    public void persistScreenPopTime(String uuid, Integer employeeNumber, String screenType, String popDateTime, String openDateTime);

    public List<ScreenPopTime> getScreenPops(String startDate, String endDate);

    public List<Provider> getProviderInsurance(Integer facilityId);

    public String getCallCenterName(Integer employeeNumber, String uuid, String screenType, boolean byFacility);

    public CallCenter getCallCenterByName(String callCenterName);

    public void updateLoadedEmployeeSession(String uuid, String sessionId, Integer employeeNumber, String screenType);

    public PartnerConfig findPartnerConfigById(String partnerId);

    public void linkAudioFilesWithCallLog(String source);

    public CallLogFile findCallLogFileById(Long fileId);

    public String convertWavToMp3(String filePath);

    public ScreenPopHealthCheck doScreenPopHealthCheck(String uuid, Integer employeeNumber, String screenType, String popDateTime, String unloadDateTime);

    public void processScreenPopHealthCheck(int limit);

    public List<Map<String, Object>> getCiscoAgentCall(boolean canEditCallLog, List<CallCenterEmployee> employees, Date dt);

    public boolean isOldScreenPop(Date callDateTime, String uuid, int checkType);

    public boolean checkIfCallbackEnable(String callCenter);

    public List<Map> getOpenBookSupportedFacilities();
}
