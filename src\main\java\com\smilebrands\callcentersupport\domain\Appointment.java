package com.smilebrands.callcentersupport.domain;

import com.smilebrands.callcentersupport.util.TranslateUtil;
import com.smilebrands.callcentersupport.util.VelocityUtils;

import javax.xml.bind.annotation.XmlElement;
import java.text.DateFormat;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

/**
 * User: <PERSON><PERSON>
 * Date: 7/10/14
 */
public class Appointment {

    private final static Format dateFormat = new SimpleDateFormat("h:mm a EEE, MMM d, ''yy");

    private Date appointmentDateTime;
    private Facility facility;
    private Patient patient;

    public Date getAppointmentDateTime() {
        return appointmentDateTime;
    }

    public void setAppointmentDateTime(Date appointmentDateTime) {
        this.appointmentDateTime = appointmentDateTime;
    }

    public Facility getFacility() {
        return facility;
    }

    public void setFacility(Facility facility) {
        this.facility = facility;
    }

    public Patient getPatient() {
        return patient;
    }

    public void setPatient(Patient patient) {
        this.patient = patient;
    }

    public String getFormattedDateTime(){
        String result = "";
        if(appointmentDateTime != null){
            result = dateFormat.format(appointmentDateTime);
        }
        return result;
    }

    @XmlElement(name = "ttsAppointment")
    public String getTTSAppointment(String language){
        String result = "";
        if(patient != null){
            result += TranslateUtil.translatePhrase("We have found an appointment for ", language) + " " + patient.getPatientFullName();
            Calendar cal = Calendar.getInstance();
            cal.setTime(appointmentDateTime);
            int hour = cal.get(Calendar.HOUR_OF_DAY);
            hour = hour > 12 ? hour - 12 : hour;
            result += " " + TranslateUtil.translateDate(cal.get(Calendar.DAY_OF_MONTH), cal.get(Calendar.MONTH), cal.get(Calendar.YEAR), language, "for ");
            result += ", " + TranslateUtil.translateTime(hour, cal.get(Calendar.MINUTE), language, "at ");
            result += " " + TranslateUtil.translateAMPM(cal.getDisplayName(Calendar.AM_PM, Calendar.LONG, Locale.US), language);
            if(facility != null){
                result += (language.equalsIgnoreCase("SP") ? ", en " : " at " )+ facility.getTTSFacility();
            }
            result += ".";
        }
        return result;
    }
}
