CREATE OR REPLACE PROCEDURE "CISCOADM"."SP_UPDATE_AGENT_CALL_LOG"

/*---------------------------------------------------------------------------------*/
/* Procedure: SP_UPDATE_AGENT_CALL_LOG                                             */
/*                                                                                 */
/* Author: <PERSON>da                                                          */
/* Date Written: 08/08/2014                                                        */
/*                                                                                 */
/* Purpose: This stored procedure updates the Agent Call Log and the Agent         */
/*          appts associated with the Agent Call Log                               */ 
/*                                                                                 */ 
/* Input Variables:                                                                */
/*                                                                                 */
/*                                                                                 */
/*                                                                                 */
/*                                                                                 */
/* Returns:                                                                        */
/*                                                                                 */
/*                                                                                 */
/* Revisions                                                                       */
/*                                                                                 */
/*                                                                                 */
/*                                                                                 */
/*---------------------------------------------------------------------------------*/

AS

V_UNIQUE_ID                     PATIENTS.UNIQUE_ID%TYPE;
V_PATIENT_ID                    PATIENTS.PATIENT_ID%TYPE;
V_CURRENT_DATE                  DATE := TRUNC(SYSDATE);
V_FACILITY_ID                   NUMBER;
V_CALL_LOG_STATUS               VARCHAR2(15);
V_CLINIC_ID                     APPT_CALL_LOG.CLINIC_ID%TYPE;
V_APPT_LOG_ID                   APPT_CALL_LOG.APPT_LOG_ID%TYPE;
V_APPT_LOG_STATUS               APPT_CALL_LOG.APPT_LOG_STATUS%TYPE;
V_ADD_APPT_LOG                  NUMBER(1,0);
V_UPDATE_AGENT_LOG              NUMBER(1,0);
V_UPDATE_APPT_LOG               NUMBER(1,0);
V_TERMINAL_EVENT                NUMBER(1,0);
V_APPT_CHANGED                  NUMBER(1,0);

V_CLOSE_DATE                    DATE;

V_PROVIDER_ID                   APPOINTMENTS.PROVIDER_ID%TYPE;
V_APPT_DATE                     APPOINTMENTS.APPT_DATE%TYPE;
V_FUTURE_PROVIDER_ID            APPOINTMENTS.PROVIDER_ID%TYPE;
V_FUTURE_APPT_DATE              APPOINTMENTS.APPT_DATE%TYPE;
V_APPT_VALUE                    NUMBER(9,2);
V_APPT_FOUND                    NUMBER(1,0);
V_FUTURE_APPT_FOUND             NUMBER(1,0);
V_APPT_MATCHED                  NUMBER(1,0);
V_PATIENT_VISIT                 NUMBER(5,0);
V_CONTINUE_PROCESSING           NUMBER(1,0);

ERR_NUM                         NUMBER;
ERR_MSG                         VARCHAR2(512); 
V_STORED_PROCEDURE              VARCHAR2(30) := $$PLSQL_UNIT;  


BEGIN
/*---------------------------------------------------------------------------*/
/* Set Patient Info                                                          */
/*---------------------------------------------------------------------------*/
  
    SP_UPDATE_PATIENT_INFO();
 
/*---------------------------------------------------------------------------*/
/* Set Read Loop                                                             */
/*---------------------------------------------------------------------------*/

    FOR R IN (SELECT CL.CALL_LOG_ID, 
                     CL.SESSION_ID, 
                     CL.EMPLOYEE_NUMBER, 
                     CL.FACILITY_ID, 
                     CL.TEMP_PATIENT_ID,
                     CL.CLINIC_ID,
                     CL.PATIENT_ID,
                     CL.UNIQUE_ID,
                     CL.CALL_CENTER_TIME,
                     TRUNC(CL.CALL_TIME) AS CALL_TIME,
                     TRIM(CL.CALL_LOG_STATUS) AS CALL_LOG_STATUS,
                     TRIM(CL.APPT_LOG_STATUS) AS APPT_LOG_STATUS,
                     UPPER(NVL(FV.FAC_ATTR_VALUE,'FALSE')) AS IS_LIBERTY,
                     CL.APPT_DATE AS APPT_DATE,
                     NVL(TRUNC(CL.APPT_DATE) + 2,TRUNC(CL.CALL_CENTER_TIME) + 3) as CLOSE_DATE,
                     (TRUNC(CL.CREATE_DATETIME) + 2)  AS MISSING_DATE,
                     CL.CREATE_DATETIME,
                     CL.IS_ORTHO

            FROM V_AGENT_CALL_LOG CL
            
            LEFT OUTER JOIN FACILITY_ATTR_VALUES FV
                ON FV.FACILITY_ID = CL.FACILITY_ID AND
                   FV.FAC_ATTRIBUTE_ID = 69 
            WHERE UPPER(CALL_LOG_STATUS) = 'ACTIVE' AND
                  UNIQUE_ID IS NOT NULL)
  
    LOOP

/*---------------------------------------------------------------------------*/
/* Get Clinic Id                                                             */
/*---------------------------------------------------------------------------*/

    V_CALL_LOG_STATUS := R.CALL_LOG_STATUS;
    V_APPT_LOG_STATUS := R.APPT_LOG_STATUS;

    V_CLINIC_ID := R.CLINIC_ID;
    V_UNIQUE_ID := R.UNIQUE_ID;  
    V_CONTINUE_PROCESSING := 1;
    V_CLOSE_DATE := R.CLOSE_DATE;
    V_APPT_DATE := R.APPT_DATE;

/*---------------------------------------------------------------------------*/
/* Process Non Liberty Offices                                               */
/*---------------------------------------------------------------------------*/

    IF R.IS_LIBERTY = 'FALSE' THEN
/*---------------------------------------------------------------------------*/
/* Test for reschedule of existing appt                                      */
/*---------------------------------------------------------------------------*/
 
    IF R.APPT_LOG_STATUS = 'VERIFIED' OR
       R.APPT_LOG_STATUS = 'RESCHEDULED' THEN

    BEGIN
     
    SP_RESCHEDULE_APPOINTMENTS(
              P_CALL_LOG_ID               => R.CALL_LOG_ID,
              P_FACILITY_ID               => R.FACILITY_ID,
              P_CLINIC_ID                 => R.CLINIC_ID,
              P_UNIQUE_ID                 => R.UNIQUE_ID,
              P_CALL_CENTER_TIME          => R.CALL_CENTER_TIME,
              P_CLOSE_DATE                => V_CLOSE_DATE,
              P_APPT_DATE                 => V_APPT_DATE,
              P_IS_ORTHO                  => R.IS_ORTHO,
              IO_APPT_LOG_STATUS          => V_APPT_LOG_STATUS,
              IO_CALL_LOG_STATUS          => V_CALL_LOG_STATUS,
              IO_CONTINUE_PROCESSING      => V_CONTINUE_PROCESSING);

    END;          
    END IF;

/*---------------------------------------------------------------------------*/
/* Process Unverified/ Missing                                               */
/*---------------------------------------------------------------------------*/
 
    IF R.APPT_LOG_STATUS IN ('UNVERIFIED','MISSING') AND 
       V_CONTINUE_PROCESSING = 1 THEN

    BEGIN

    
     
    SP_VERIFY_APPOINTMENTS(
              P_CALL_LOG_ID               => R.CALL_LOG_ID,
              P_FACILITY_ID               => R.FACILITY_ID,
              P_CLINIC_ID                 => R.CLINIC_ID,
              P_UNIQUE_ID                 => R.UNIQUE_ID,
              P_CALL_CENTER_TIME          => R.CALL_CENTER_TIME,
              P_CREATE_DATETIME           => R.CREATE_DATETIME, 
              P_CLOSE_DATE                => V_CLOSE_DATE,
              P_IS_ORTHO                  => R.IS_ORTHO,
              IO_APPT_LOG_STATUS          => V_APPT_LOG_STATUS,
              IO_CALL_LOG_STATUS          => V_CALL_LOG_STATUS,
              IO_CONTINUE_PROCESSING      => V_CONTINUE_PROCESSING);

    END;          
    END IF;

/*---------------------------------------------------------------------------*/
/* Test for rescheduled or Missing                                           */
/*---------------------------------------------------------------------------*/
 
    IF R.APPT_LOG_STATUS = 'VERIFIED' OR
       R.APPT_LOG_STATUS = 'RESCHEDULED' AND
       V_CONTINUE_PROCESSING = 1 AND
       R.APPT_DATE <= SYSDATE THEN 

    BEGIN

    SP_PROCESS_VERIFIED_APPTS(
              P_CALL_LOG_ID               => R.CALL_LOG_ID,
              P_FACILITY_ID               => R.FACILITY_ID,
              P_CLINIC_ID                 => R.CLINIC_ID,
              P_UNIQUE_ID                 => R.UNIQUE_ID,
              P_CALL_CENTER_TIME          => R.CALL_CENTER_TIME,
              P_CLOSE_DATE                => V_CLOSE_DATE,
              P_APPT_DATE                 => V_APPT_DATE,
              P_IS_ORTHO                  => R.IS_ORTHO,
              IO_APPT_LOG_STATUS          => V_APPT_LOG_STATUS,
              IO_CALL_LOG_STATUS          => V_CALL_LOG_STATUS,
              IO_CONTINUE_PROCESSING      => V_CONTINUE_PROCESSING);

    END;          
    END IF;

/*---------------------------------------------------------------------------*/
/* End of Non Liberty Office Processing                                      */
/*---------------------------------------------------------------------------*/  

    END IF;
/*---------------------------------------------------------------------------*/
/* Process Non Liberty Offices                                               */
/*---------------------------------------------------------------------------*/

    IF R.IS_LIBERTY <> 'FALSE' THEN

/*---------------------------------------------------------------------------*/
/* Process Unverified/ Missing                                               */
/*---------------------------------------------------------------------------*/
 
    IF R.APPT_LOG_STATUS IN ('UNVERIFIED','MISSING') AND 
       V_CONTINUE_PROCESSING = 1 THEN

    BEGIN

    SL_VERIFY_APPOINTMENTS(
              P_CALL_LOG_ID               => R.CALL_LOG_ID,
              P_FACILITY_ID               => R.FACILITY_ID,
              P_CLINIC_ID                 => R.CLINIC_ID,
              P_UNIQUE_ID                 => R.UNIQUE_ID,
              P_CALL_CENTER_TIME          => R.CALL_CENTER_TIME,
              P_CLOSE_DATE                => V_CLOSE_DATE,
              P_IS_ORTHO                  => R.IS_ORTHO,
              P_CREATE_DATETIME           => R.CREATE_DATETIME, 
              IO_APPT_LOG_STATUS          => V_APPT_LOG_STATUS,
              IO_CALL_LOG_STATUS          => V_CALL_LOG_STATUS,
              IO_CONTINUE_PROCESSING      => V_CONTINUE_PROCESSING);

    END;          
    END IF;
/*---------------------------------------------------------------------------*/
/* Test for rescheduled or Missing                                           */
/*---------------------------------------------------------------------------*/
 
    IF V_APPT_LOG_STATUS = 'VERIFIED' OR
       V_APPT_LOG_STATUS = 'RESCHEDULED' AND
       V_CONTINUE_PROCESSING = 1 AND
       R.APPT_DATE <= SYSDATE THEN 

    BEGIN

    SL_PROCESS_VERIFIED_APPTS(
              P_CALL_LOG_ID               => R.CALL_LOG_ID,
              P_FACILITY_ID               => R.FACILITY_ID,
              P_CLINIC_ID                 => R.CLINIC_ID,
              P_UNIQUE_ID                 => R.UNIQUE_ID,
              P_CALL_CENTER_TIME          => R.CALL_CENTER_TIME,
              P_CLOSE_DATE                => V_CLOSE_DATE,
              P_APPT_DATE                 => V_APPT_DATE,
              P_IS_ORTHO                  => R.IS_ORTHO,
              IO_APPT_LOG_STATUS          => V_APPT_LOG_STATUS,
              IO_CALL_LOG_STATUS          => V_CALL_LOG_STATUS,
              IO_CONTINUE_PROCESSING      => V_CONTINUE_PROCESSING);

    END;          
    END IF;

/*---------------------------------------------------------------------------*/
/* End of Liberty Office Processing                                          */
/*---------------------------------------------------------------------------*/  

    END IF;
/*---------------------------------------------------------------------------*/
/* End of  Loop                                                              */
/*---------------------------------------------------------------------------*/  
    END LOOP;

/*---------------------------------------------------------------------------*/
/* Error Occured - Add error to log                                          */
/*---------------------------------------------------------------------------*/  
    EXCEPTION WHEN OTHERS THEN
    
    ERR_NUM := SQLCODE;
    ERR_MSG := SUBSTR(SQLERRM, 1, 512);
             
    INSERT INTO APPLICATION_ERROR_LOG(
                ERROR_ID, 
                STORED_PROCEDURE,   
                ERROR_NUMBER, 
                ERROR_MSG, 
                ERROR_DATE) 
      VALUES(
               APPLICATION_ERROR_LOG_SEQ.NEXTVAL,
               V_STORED_PROCEDURE,
               ERR_NUM,
               ERR_MSG,
               SYSDATE);    
END;