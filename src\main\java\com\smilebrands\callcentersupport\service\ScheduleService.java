package com.smilebrands.callcentersupport.service;

import com.smilebrands.callcentersupport.domain.schedule.Task;

import java.util.Map;

/**
 * Created by phongpham on 11/6/14.
 */
public interface ScheduleService {

    public Task claimOpenTask(String taskName);
    public Task claimOpenTask(Long taskId);
    public boolean processTask(Task task);
    public void processTaskAsync(Task task);
    public void releaseTask(Task task);
    public void releaseAllTask();

    public void processWebRequest(Map<String, Object> mongoOptions, int mongoLimit);
    public void checkLongQueuedWebRequest(Map<String, Object> mongoOptions, int mongoLimit);
    public void generateCallLogReport(Task task);
}
