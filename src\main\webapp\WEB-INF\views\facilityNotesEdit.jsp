<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<%@ taglib uri="../customTag.tld" prefix="custom" %>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Smile Brands Call Center</title>
    <link type="text/css" rel="stylesheet" media="all" href="../style/style.css" />
    <link type="text/css" href="../style/redmond/jquery-ui-1.8.20.custom.css" rel="stylesheet" />
    <script type="text/javascript" src="../script/jquery-1.7.2.min.js"></script>
    <script type="text/javascript" src="../script/jquery-ui-1.8.20.custom.min.js"></script>
    <script type="text/javascript" src="../script/ckeditor/ckeditor.js"></script>
    <script type="text/javascript" src="../script/security.js"></script>
    <script type="text/javascript" src="../script/editFacilityAction.js"></script>


</head>
<body>
    <div id="dialog_box">
        <div id='dialog_message'></div>
    </div>
    <div class="main">
           <div class="link-container">
               <form class="search-form" >
                   <input id="fid" type="text" name="name" placeholder="Enter Site ID" />
                   <input id="searchNotesByFIDBtn" type="submit" value="Search" style="float:none"/><span class="pipe" id="pipeBeforeMongo"></span>
                   <input id="updateDBBtn" type="submit" value="Update Mongo DB" style="float:none"/>
                   <img id="progressBar" src="../images/loading.gif" style="display:none"/>

               </form>
           </div>
           <br/>
           <div id="facilityNotes" class="block" style="height:100%; display: none">
               <div class="left fl">
                      <ul class="info-list">
                          <li>
                              <span class="label fl">Site name:</span>
                              <span  class="content title">
                                  <span id="siteNameLbl"></span>
                              </span>
                          </li>
                          <li>
                              <span class="label fl">Site ID:</span>
                              <span id="siteIDlbl" class="content "></span>
                          </li>
                          <li>
                              <span class="label fl">Address:</span>
                              <span id="addresslbl" class="content "></span>
                          </li>
                      </ul>
               </div>
               <div class="clear"/>
               <div id="outer" style="width=100%">
                  <div id="editor1"/>
               </div>


               <div class="clear"/>
               <div class="clear"/>
               <span class="content fl search-form"><br/><input id="saveNoteBtn" type="submit" value="Update"/></span>
           </div>
    </div>
</body>
</html>