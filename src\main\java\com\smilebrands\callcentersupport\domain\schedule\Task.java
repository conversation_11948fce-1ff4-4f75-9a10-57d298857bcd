package com.smilebrands.callcentersupport.domain.schedule;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Map;

/**
 * Created by phongpham on 11/6/14.
 */
@Document
public class Task {

    @Id
    private Long taskId;
    private String taskName;
    private String description;
    private Map<String, Object> mongoConditions;
    private Integer mongoLimit = -1;
    private boolean active = true;
    private int duration;   //in second;
    private boolean parallel = false;
    private List<String> scheduledTimes;
    private String scheduledTimeZone;

    private String acquiredBy;
    private String acquiredOn;
    private String lastAcquiredBy;
    private String lastAcquiredOn;
    private String nextAcquiredOn;


    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Map<String, Object> getMongoConditions() {
        return mongoConditions;
    }

    public void setMongoConditions(Map<String, Object> mongoConditions) {
        this.mongoConditions = mongoConditions;
    }

    public Integer getMongoLimit() {
        return mongoLimit;
    }

    public void setMongoLimit(Integer mongoLimit) {
        this.mongoLimit = mongoLimit;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public boolean isParallel() {
        return parallel;
    }

    public void setParallel(boolean parallel) {
        this.parallel = parallel;
    }

    public List<String> getScheduledTimes() {
        return scheduledTimes;
    }

    public void setScheduledTimes(List<String> scheduledTimes) {
        this.scheduledTimes = scheduledTimes;
    }

    public String getScheduledTimeZone() {
        return scheduledTimeZone;
    }

    public void setScheduledTimeZone(String scheduledTimeZone) {
        this.scheduledTimeZone = scheduledTimeZone;
    }

    public String getAcquiredBy() {
        return acquiredBy;
    }

    public void setAcquiredBy(String acquiredBy) {
        this.acquiredBy = acquiredBy;
    }

    public String getAcquiredOn() {
        return acquiredOn;
    }

    public void setAcquiredOn(String acquiredOn) {
        this.acquiredOn = acquiredOn;
    }

    public String getLastAcquiredBy() {
        return lastAcquiredBy;
    }

    public void setLastAcquiredBy(String lastAcquiredBy) {
        this.lastAcquiredBy = lastAcquiredBy;
    }

    public String getLastAcquiredOn() {
        return lastAcquiredOn;
    }

    public void setLastAcquiredOn(String lastAcquiredOn) {
        this.lastAcquiredOn = lastAcquiredOn;
    }

    public String getNextAcquiredOn() {
        return nextAcquiredOn;
    }

    public void setNextAcquiredOn(String nextAcquiredOn) {
        this.nextAcquiredOn = nextAcquiredOn;
    }

    public String toString(){
        return this.taskId + ":::" + this.taskName;
    }
}
