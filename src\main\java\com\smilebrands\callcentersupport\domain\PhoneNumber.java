package com.smilebrands.callcentersupport.domain;

import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.Date;

/**
 * User: <PERSON><PERSON>
 * Date: 6/24/14
 */
@Document
@XmlRootElement
public class PhoneNumber implements Serializable {

    @Id
    private Long phoneNumber;
    private Integer facilityId = 0;
    private Long transferNumber;
    private Long csrTransferNumber;
    private String phoneType;
    private String brandName;
    private String campaignName;
    private boolean isCampaignLimited;
    private String ivrPromptFile;
    private String ivrName = "";
    private boolean searchOnAni;
    private String language = "en";
    private boolean callCenterSupported;
    private boolean csrSupported;
    private boolean psrSupported;
    private String callCenterName;
    private String callCenterQueueName;
    private String voiceMailRoute;
    private boolean isDefault;
    private boolean isActive;
    private Date inactivatedOn;
    private Integer inactivatedBy;
    private Date createdOn;
    private Integer createdBy;
    private Date updatedOn;
    private Integer updatedBy;


    @Transient
    private String facilityName;
    @Transient
    private String facilityAddress;

    public Long getPhoneNumber() { return phoneNumber; }

    public void setPhoneNumber(Long phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getPhoneType() {
        return phoneType;
    }

    public void setPhoneType(String phoneType) {
        this.phoneType = phoneType;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getCampaignName() {
        return campaignName;
    }

    public void setCampaignName(String campaignName) {
        this.campaignName = campaignName;
    }

    public String getIvrPromptFile() {
        return ivrPromptFile;
    }

    public void setIvrPromptFile(String ivrPromptFile) {
        this.ivrPromptFile = ivrPromptFile;
    }

    public String getIvrName() {
        return ivrName;
    }

    public void setIvrName(String ivrName) {
        this.ivrName = ivrName;
    }

    public boolean isCallCenterSupported() {
        return callCenterSupported;
    }

    public void setCallCenterSupported(boolean callCenterSupported) {
        this.callCenterSupported = callCenterSupported;
    }

    public boolean isCsrSupported() {
        return csrSupported;
    }

    public void setCsrSupported(boolean csrSupported) {
        this.csrSupported = csrSupported;
    }

    public boolean isPsrSupported() {
        return psrSupported;
    }

    public void setPsrSupported(boolean psrSupported) {
        this.psrSupported = psrSupported;
    }

    public String getCallCenterName() {
        return callCenterName;
    }

    public void setCallCenterName(String callCenterName) {
        this.callCenterName = callCenterName;
    }

    public String getCallCenterQueueName() {
        return callCenterQueueName;
    }

    public void setCallCenterQueueName(String callCenterQueueName) {
        this.callCenterQueueName = callCenterQueueName;
    }

    public String getVoiceMailRoute() {
        return voiceMailRoute;
    }

    public void setVoiceMailRoute(String voiceMailRoute) {
        this.voiceMailRoute = voiceMailRoute;
    }

    public Integer getFacilityId() {
        return facilityId;
    }

    public void setFacilityId(Integer facilityId) {
        this.facilityId = facilityId;
    }

    public Long getTransferNumber() {
        return transferNumber;
    }

    public void setTransferNumber(Long transferNumber) {
        this.transferNumber = transferNumber;
        this.csrTransferNumber = transferNumber;
    }

    public boolean isCampaignLimited() {
        return isCampaignLimited;
    }

    public void setCampaignLimited(boolean isCampaignLimited) {
        this.isCampaignLimited = isCampaignLimited;
    }

    public boolean isSearchOnAni() {
        return searchOnAni;
    }

    public void setSearchOnAni(boolean searchOnAni) {
        this.searchOnAni = searchOnAni;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getFacilityName() {
        return facilityName;
    }

    public void setFacilityName(String facilityName) {
        this.facilityName = facilityName;
    }

    public String getFacilityAddress() {
        return facilityAddress;
    }

    public void setFacilityAddress(String facilityAddress) {
        this.facilityAddress = facilityAddress;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean isActive) {
        this.isActive = isActive;
    }

    public boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(boolean isDefault) {
        this.isDefault = isDefault;
    }

    public void setInactivatedOn(Date inactivatedOn){
        this.inactivatedOn = inactivatedOn;
    }

    public Date getInactivatedOn(){
        return inactivatedOn;
    }

    public void setInactivatedBy(Integer inactivatedBy){
        this.inactivatedBy = inactivatedBy;
    }

    public Integer getInactivatedBy(){
        return this.inactivatedBy;
    }

    public Date getCreatedOn() {
        return createdOn;
    }

    public void setCreatedOn(Date createdOn) {
        this.createdOn = createdOn;
    }

    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedOn() {
        return updatedOn;
    }

    public void setUpdatedOn(Date updatedOn) {
        this.updatedOn = updatedOn;
    }

    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Long getCsrTransferNumber() {
        //return csrTransferNumber;
        return transferNumber;
    }

    public void setCsrTransferNumber(Long csrTransferNumber) {
        this.csrTransferNumber = csrTransferNumber;
    }
}
