package com.smilebrands.callcentersupport.service;

import com.smilebrands.callcentersupport.domain.AgentCall;
import com.smilebrands.callcentersupport.domain.CallLog;
import com.smilebrands.callcentersupport.domain.LoadedEmployee;
import com.smilebrands.callcentersupport.domain.V_CallLog;

import java.util.*;

/**
 * Created by phongpham on 11/10/14.
 */
public interface CallLogService {

    public List<CallLog> populateCallLogForAgentCall(AgentCall agentCall, Integer employeeNumber, Integer facilityId, String screenType, boolean sendExceptionEmail);

    public List<CallLog> doPopulateCallLogForAgentCall(AgentCall agentCall, Integer employeeNumber, Integer facilityId, String screenType, boolean sendExceptionEmail, String entrySource);

    public List<V_CallLog> getCallLogsByDateAndSupervisor(Integer reportForAgentId, Date callDate, boolean checkForEdit, Integer agentId);

    public Set<Integer> getSupervisedAgentIds(Integer supervisorId);

    public String updateCallLog(Map<String, Object> map, Integer createEmployeeNumber);

    public String addCallLog(Map<String, Object> map, Integer createEmployeeNumber);

    public List<LinkedHashMap<String, String>> getCallLogDetailReport(String startDate, String endDate, List<Integer> agentIds, String dateType, String reportQuery);

    public CallLog updateCallLogWithPartnerInfo(Long callLogId, LoadedEmployee loadedEmployee);

//    public String getCallCenterName(Integer employeeNumber, String uuid, String screenType);
}
