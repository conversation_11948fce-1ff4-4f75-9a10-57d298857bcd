package com.smilebrands.callcentersupport.controllers;


import com.smilebrands.callcentersupport.service.CallService;
import com.smilebrands.callcentersupport.service.WebRequestService;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import com.smilebrands.callcentersupport.domain.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import org.springframework.web.util.UriComponents;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;

/**
 * User: <PERSON><PERSON>
 * Date: 11/04/14
 */
@Controller
public class WebRequestController extends AbstractController {

    @Autowired
    private WebRequestService webRequestService;

    @Autowired
    private CallService callService;

    @ResponseBody
    @RequestMapping(value = "web-request", method = RequestMethod.POST, produces = "application/json")
    public ModelMap saveWebRequest(@RequestBody String json) throws UnsupportedEncodingException {

        logger.debug("json: {}", json);
        logAction(null, ActionLog.WEB_REQUEST, "Request to create web request with json [" + json + "]");

        json = json.replace("%0A", " ");
        json = json.replace("%0D", " ");
        json = json.replace("%7E%5E%7C", "\":\"");  // ~^|
        json = json.replace("%7C%5E%7E", "\",\"");  // |^~

        json = json.replace("%26quot%3B", "'");

        String decodedUrl = URLDecoder.decode(json, "UTF-8");

        decodedUrl = StringEscapeUtils.unescapeHtml(decodedUrl);

        decodedUrl = "{\"" + decodedUrl + "\"}";

        logger.debug("decodedUrl : {}", decodedUrl);

        WebRequest wr  = (WebRequest) convertJson(decodedUrl, WebRequest.class);
        wr = webRequestService.saveThenSendEmailOrScreenPopWebRequest(wr);

        if (wr != null) {
            logAction(wr.getUuid(), ActionLog.WEB_REQUEST, "Create new web request [" + wr + "]");
        }
        ModelMap map = new ModelMap();

        map.addAttribute("success", true);
        map.addAttribute("data", wr);
        map.addAttribute("Access-Control-Allow-Origin", "http://localhost");

        return map;
    }

    @ResponseBody
    @RequestMapping(value = "web-appointment-request", method = RequestMethod.POST, produces = "application/json")
    public String saveWebAppointmentRequest(@RequestBody String json) throws UnsupportedEncodingException, IOException {

        logger.debug("json: {}", json);
        logAction(null, ActionLog.WEB_REQUEST, "Request to create web request with json [" + json + "]");

        try {
            WebRequest wr = (WebRequest) convertJson(json, WebRequest.class);
            wr = webRequestService.saveThenSendEmailOrScreenPopWebRequest(wr);
            if (wr != null) {
                logAction(wr.getUuid(), ActionLog.WEB_REQUEST, "Create new web request [" + wr + "]");
                return "true";
            } else {
                return "false";
            }

        } catch (Exception ex) {
            logger.error("Something went wrong while trying to process this Web Appointment Request!  Exception Message --> [" + ex.getMessage() + "].");
            return "false";
        }
    }

    /**
     *  Service used to generate a PSR Screen Pop URL for the IVR to use.
     */
    @ResponseBody
    @RequestMapping(value="wr/generate-url.xml", method= RequestMethod.GET, produces = "application/xml")
    public IvrResponseWrapper generatePsrScreenPopUrl(@RequestParam String uuid,
                                                      @RequestParam Integer agentId,
                                                      HttpServletRequest request) {

        IvrResponseWrapper wrapper = new IvrResponseWrapper();
        wrapper.setSuccess(true);

        WebRequest wr = webRequestService.findWebRequest(uuid);

        // -- register the URL generation
        UriComponents ucb = ServletUriComponentsBuilder.fromContextPath(request)
                .path("/load-screen-pop/")
                .path("wr/")
                .path(uuid + "/")
                .path(agentId.toString() + "/")
                .path("en/")
                .path(wr.getOffice() + "")
                .build();

        String url = ucb.toUriString();
        logger.debug(url);

        ScreenPopUrl pop = new ScreenPopUrl();
        pop.setUrl(url);
        pop.setAgentEmployeeNumber(agentId);
        pop.setGenerationDate(new Date());

        wr.getScreenPopUrls().add(pop);
        webRequestService.saveWebRequest(wr, false);

        wrapper.setMessage(url);

        return wrapper;
    }

    @ResponseStatus(HttpStatus.OK)
    @RequestMapping(value="wr/url-response.xml", method= RequestMethod.GET)
    public void registerWrScreenPopResult(@RequestParam String uuid, @RequestParam Integer agentId, boolean connected){
        logger.debug("Register WR Screen Pop Result with UUID[" + uuid + "], agentId [" + agentId + "] and connected [" + connected + "].");
        logAction(uuid, ActionLog.INBOUND_CALL_ACTION, "Register WR Screen Pop Result with UUID[" + uuid + "], agentId [" + agentId + "] and connected [" + connected + "].");
        webRequestService.updateWebRequestScreenResult(uuid, agentId, connected);
    }

    @ResponseBody
    @RequestMapping(value = "response-from-cisco/{uuid}", method = RequestMethod.GET, produces = "application/json")
    public ResponseWrapper responseFromCisco(@PathVariable String uuid,
                                             @RequestParam Integer status,
                                             @RequestParam(required = false) Integer agentsReady,
                                             @RequestParam(required = false) Integer agentsTalking,
                                             @RequestParam(required = false) Integer agent,
                                             @RequestParam(required = false) String sessionId){
        WebRequest webRequest = webRequestService.findWebRequest(uuid);
        ResponseWrapper responseWrapper = new ResponseWrapper();
        responseWrapper.setMessage("response from cisco");
        String message = "Response from cisco for UUID[" + uuid + "] with session ID[" + sessionId + "], status[" + status + "], agentsReady[" + agentsReady + "], agentsTalking[" + agentsTalking + "] and agent[" + agent + "]";
        logger.debug(message);
        logAction(uuid, ActionLog.WEB_REQUEST, message);
        if(webRequest != null) {
//            if(agentsReady != null && agentsTalking != null){
//                agentsReady += agentsTalking;
//            }else if ((agentsReady == null || agentsReady.equals(0)) && agentsTalking != null && !agentsTalking.equals(0)) {
//                agentsReady = agentsTalking;
//            }
            Map<String, Object> data = new HashMap<String, Object>();
            data.put("agentsTalking", agentsTalking);
            data.put("agentsReady", agentsReady);
            data.put("uuid", uuid);

            responseWrapper.setData(data);

            if (status == 0 && agent != null && agent > 0) {
                webRequest.setProcessedDateTime(VelocityUtils.getDateAsString(new Date(), "yyyy-MM-dd HH:mm:ss"));
                webRequest.setHandledByAgent(agent);
                responseWrapper.setSuccess(true);
                callService.updateLoadedEmployeeSession(uuid, sessionId, agent, "wr");
                if(agentsReady == null || agentsReady.equals(0)){
                    data.put("agentsReady", 1);
                }
            } else {
                //IF WEB REQUEST IS PICKED UP TO PROCESS 5 TIMES IN A ROW, SET NEXT PROCESS TO 15 MINUTES INSTEAD OF 1 MINUTE
//                if ((webRequest.getNumberOfProcess() % 5) == 0) {
//                    Calendar cal = Calendar.getInstance();
//                    cal.add(Calendar.MINUTE, 15);
//                    webRequest.setNextProcessDateTime(VelocityUtils.getDateAsString(cal.getTime(), "yyyy-MM-dd HH:mm:ss"));
//                }
                webRequest.setPendingProcessedDateTime(null);
                webRequest.setPendingProcessedBy(null);
                responseWrapper.setSuccess(false);
            }
            webRequestService.saveWebRequest(webRequest, false);
        }else{
            responseWrapper.setSuccess(false);
            responseWrapper.setMessage("Web Request Not Found For UUID[" + uuid + "]");
        }

        return  responseWrapper;
    }
}
