<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<%@ taglib uri="../customTag.tld" prefix="custom" %>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Smile Brands Call Center</title>
    <link type="text/css" rel="stylesheet" media="all" href="../style/style.css" />
    <link type="text/css" href="../style/redmond/jquery-ui-1.8.20.custom.css" rel="stylesheet" />
    <script type="text/javascript" src="../script/jquery-1.7.2.min.js"></script>
    <script type="text/javascript" src="../script/jquery-ui-1.8.20.custom.min.js"></script>
    <script type="text/javascript" src="../script/security.js?_id=${buildNumber}"></script>
    <script type="text/javascript" src="../script/editFacilityAction.js?_id=${buildNumber}"></script>
</head>
<body>
    <div id="dialog_box">
        <div id='dialog_message'></div>
    </div>
    <div class="main">
           <div class="link-container">
               <form class="search-form" >
                   <input id="fid" type="text" name="name" placeholder="Enter Site ID" />
                   <input id="searchByFIDBtn" type="submit" value="Search" style="float:none"/><span class="pipe"></span>
                   <input id="addNewFacilityBtn" type="submit" value="Add new" style="float:none"/><span class="pipe" id="pipeBeforeMongo"></span>
                   <input id="updateDBBtn" type="submit" value="Update Mongo DB" style="float:none"/>
                   <img id="progressBar" src="../images/loading.gif" style="display:none"/>

               </form>
           </div>

            <br/>
           <div id="facilityDetail" class="block" style="height:100%; display: none">
                   <div class="left fl">
                       <ul class="info-list">
                           <li id="siteNameLI">
                               <span class="label fl">Site name:</span>
                               <span  class="content title">
                                   <span id="siteNameLbl"></span>
                               </span>
                           </li>
                           <li id="siteIDLI">
                               <span class="label fl">Site ID:</span>
                               <span id="siteIDlbl" class="content "></span>
                           </li>
                           <li id="addressLI">
                               <span class="label fl">Address:</span>
                               <span id="addresslbl" class="content "></span>
                           </li>
                           <li id="dnisLI">
                                <span class="label fl">DNIS:</span>
                                <span class="content">
                                    <select id="dnisOptions"></select>
                                    <a id='addDNISLink' href=''> Add</a><span class="pipe"></span><a id='deleteDNISLink' href=''> Delete</a></span>
                           </li>
                           <li></li>
                           <li id="fidInputLI" style="display:none">
                                <span class="labelEdit fl">Site ID:</span>
                                <span class="content"><input id="fidInput"/></span>
                           </li>
                           <li id="dnisInputLI" style="display:none">
                                <span class="labelEdit fl">DNIS:</span>
                                <span class="content "><input id="dnisInput" /></span>
                           </li>
                           <li>
                              <span class="labelEdit fl">Main Number:</span>
                              <span class="content "><input id="phoneNum" /></span>
                           </li>
                           <li>
                              <span class="labelEdit fl">Fax Number:</span>
                              <span class="content "><input id="faxNum" /></span>
                           </li>

                            <li>
                              <span class="labelEdit fl">Transfer Number:</span>
                              <span  class="content "><input id="transferNum" /></span>
                            </li>
                           <li>
                              <span class="labelEdit fl">Call Center:</span>
                              <span class="content "><input id="callCenter" /></span>
                           </li>
                           <li>
                              <span class="labelEdit fl">Call Center Queue:</span>
                              <span class="content "><input id="callCenterQueue" /></span>
                           </li>
                           <li>
                               <span class="labelEdit fl">Call Center Supported:</span>

                               <input id="yesCCSupported" type="radio" name="ccSupported" value="Yes"> Yes </input>
                               <input id="noCCSupported" type="radio" name="ccSupported" value="No"> No </input>

                           </li>

                           <li>
                               <span class="content fr search-form">
                                    <br/>
                                    <input id="saveBtn" type="submit" value="Update" style="float:none"/>
                                    <input id="cancelBtn" type="submit" value="Cancel" style="float:none;display:none"/>
                               </span>
                           </li>

                       </ul>
                   </div>
           </div>
    </div>
</body>
</html>