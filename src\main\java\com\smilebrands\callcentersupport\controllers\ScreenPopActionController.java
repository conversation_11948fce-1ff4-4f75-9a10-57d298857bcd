package com.smilebrands.callcentersupport.controllers;

import com.smilebrands.callcentersupport.async.BackgroundExecutor;
import com.smilebrands.callcentersupport.domain.*;
import com.smilebrands.callcentersupport.domain.partner.PartnerConfig;
import com.smilebrands.callcentersupport.service.CallLogService;
import com.smilebrands.callcentersupport.service.CallService;
import com.smilebrands.callcentersupport.service.CommunicationService;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * User: <PERSON><PERSON>
 * Date: 6/24/14
 */
@Controller
public class ScreenPopActionController extends AbstractController {

    @Autowired
    private CallService callService;
    @Autowired
    private CallLogService callLogService;
    @Autowired
    private CommunicationService communicationService;

    @Autowired
    private BackgroundExecutor backgroundExecutor;

    @ResponseBody
    @RequestMapping(value="secure/facility.json", method= RequestMethod.GET, produces = "application/json")
    public ResponseWrapper findClosestFacilities(@RequestParam Integer facilityId,
                                                 @RequestParam String uuid,
                                                 @RequestParam Integer employeeNumber) {

        logger.debug("Facility Load using Facility ID[" + facilityId + "] UUI[" + uuid + "].");
        logAction(uuid, ActionLog.SCREEN_POP_ACTION, "Request to load facility using Facility ID[" + facilityId + "] and UUID[" + uuid + "].");
        Facility facility = callService.findFacilityByFacilityId(facilityId);

        ResponseWrapper wrapper = new ResponseWrapper();
        wrapper.setSuccess(true);
        wrapper.setCount(facility != null ? 1 : 0);
        wrapper.setData(facility);
        wrapper.setMessage("Complete");

        return wrapper;
    }

    @ResponseBody
    @RequestMapping(value="closest-facilities.json", method= RequestMethod.GET, produces = "application/json")
    public ResponseWrapper findClosestFacilities(@RequestParam String zip,
                                                 @RequestParam String uuid,
                                                 @RequestParam Integer employeeNumber) {

        logger.debug("Neighbor Offices search using ZIP[" + zip + "] UUI[" + uuid + "].");
        logAction(uuid, ActionLog.SCREEN_POP_ACTION, "Neighbor Offices search using ZIP[" + zip + "] UUI[" + uuid + "].");
        List<Facility> facilities = callService.findFacilitiesByZipCode(zip);

        ResponseWrapper wrapper = new ResponseWrapper();
        wrapper.setSuccess(true);
        wrapper.setCount(facilities.size());
        wrapper.setData(facilities);
        wrapper.setMessage("Complete");

        return wrapper;
    }

    @ResponseBody
    @RequestMapping(value = "facility-detail.json", method = RequestMethod.GET, produces = "application/json")
    public ResponseWrapper getFacilityDetail(@RequestParam Integer facilityId){
        ResponseWrapper wrapper = new ResponseWrapper();
        Facility facility = callService.findFacilityByFacilityId(facilityId);
        if(facility != null){
            facility.setAssociatedPhoneNumber(callService.findPhoneNumberByFacilityAndPhoneType(facility.getFacilityId(), "ML"));
            if(facility.getNeighborFacilities() != null){
                for(Facility nf : facility.getNeighborFacilities()){
                    Facility detail = callService.findFacilityByFacilityId(nf.getFacilityId());
                    if(detail != null){
                        nf.setEmployees(detail.getEmployees());
                    }
                }
            }
            wrapper.setData(facility);
            wrapper.setCount(1);
            wrapper.setSuccess(true);
        }else{
            wrapper.setSuccess(false);
        }

        return wrapper;
    }

    @ResponseBody
    @RequestMapping(value="csr-call-update.json", method= RequestMethod.GET, produces = "application/json")
    public ResponseWrapper updateInboundCall(@RequestParam String uuid,
                                             @RequestParam Integer menuNumberPress,
                                             @RequestParam Integer facilityId) {

        logger.debug("Request to update Call UUI[" + uuid + "] with Menu Number Press[" + menuNumberPress + "] and Facility ID[" + facilityId + "].");
        logAction(uuid, ActionLog.SCREEN_POP_ACTION, "Request to update Call UUI[" + uuid + "] with Menu Number Press[" + menuNumberPress + "] and Facility ID[" + facilityId + "].");

        InboundCall call = callService.findInboundCall(uuid);
        if(!menuNumberPress.equals(call.getMainMenuNumberPress())){
            call.setMainMenuNumberPressOverride(menuNumberPress);
        }
        call.setTargetFacilityId(facilityId);

        PhoneNumber number = callService.findPhoneNumberByFacilityId(facilityId, uuid);
        if(number != null
                && (call.getPhoneNumber() == null || !number.getPhoneNumber().equals(call.getPhoneNumber().getPhoneNumber()))){
            call.getPreviousPhoneNumbers().add(call.getPhoneNumber());
            call.setPhoneNumber(number);
            call.setTargetFacilityId(facilityId);
            call.setMatched(number.getFacilityId() != null && !number.getFacilityId().equals(0));
        }

        callService.updateInboundCall(call);

        ResponseWrapper wrapper = new ResponseWrapper();
        wrapper.setSuccess(true);
        wrapper.setMessage("Update Complete.");

        return wrapper;
    }

    @ResponseBody
    @RequestMapping(value = "appointment-search.json", method = RequestMethod.GET, produces = "application/json")
    public ResponseWrapper searchAppointment(@RequestParam Long patientId,
                                             @RequestParam Long phoneNumber,
                                             @RequestParam Integer facilityId){
        ResponseWrapper wrapper = new ResponseWrapper();

        List<Appointment> appointments = callService.findAppointments(patientId, phoneNumber, facilityId);
        wrapper.setData(appointments);
        wrapper.setCount(appointments.size());
        wrapper.setSuccess(true);

        return wrapper;
    }

    @ResponseBody
    @RequestMapping(value = "update-call-detail", method = RequestMethod.POST, produces = "application/json")
    public ResponseWrapper updateCallDetail(@RequestBody String json){
        ResponseWrapper wrapper = new ResponseWrapper();

        logger.debug("json: {}", json);

        AgentCall agentCall  = (AgentCall) convertJson(json, AgentCall.class);
        LoadedEmployee employee = agentCall.getLoadedEmployees().get(0);
        logAction(agentCall.getUuid(), ActionLog.SCREEN_POP_ACTION, "Request to update call detail with json: " + json);

        logger.debug("reason date time: {}", employee.getReasonDateTime());
        logger.debug("resolution date time: {}", employee.getResolutionDateTime());
        this.setRequestData(json);

        agentCall = callService.updateAgentCall(agentCall, true);

        wrapper.setData(agentCall);
        wrapper.setSuccess(true);
        wrapper.setCount(1);

        return wrapper;
    }

    @ResponseBody
    @RequestMapping(value = "get-agent-call", method = RequestMethod.GET, produces = "application/json")
    public ResponseWrapper getAgentCall(@RequestParam String uuid){
        ResponseWrapper wrapper = new ResponseWrapper();

        logger.debug("get agent call for UUID[" + uuid + "]");
        logAction(uuid, ActionLog.SCREEN_POP_ACTION, "Request to get agent call for UUID[" + uuid + "]");

        AgentCall agentCall = callService.findAgentCall(uuid);
        wrapper.setData(agentCall);
        wrapper.setCount(agentCall != null ? 1 : 0);
        wrapper.setSuccess(agentCall != null ? true : false);

        return wrapper;
    }

    @ResponseStatus(HttpStatus.OK)
    @RequestMapping(value = "unload-screen-pop", method = RequestMethod.GET)
    public void unloadScreenPop(@RequestParam String uuid,
                                @RequestParam Integer employeeNumber,
                                @RequestParam (required = false) Integer facilityId,
                                @RequestParam String screenType,
                                @RequestParam (required = false) @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss") DateTime unloadDateTime){
        logger.debug("Unload screen pop [" + screenType + "] for uuid [" + uuid + "] with employee number [" + employeeNumber + "] @ [" + (unloadDateTime != null ? unloadDateTime.toDate() : new Date() + "]"));
        logAction(uuid, ActionLog.SCREEN_POP_ACTION, "Unload screen pop [" + screenType + "] for uuid [" + uuid + "] with employee number [" + employeeNumber + "] @ [" + (unloadDateTime != null ? unloadDateTime.toDate() : new Date() + "]"));
        if(!callService.isOldScreenPop(null, uuid, 0)){
            AgentCall agentCall = callService.updateAgentCallWithUnloadEvent(uuid, employeeNumber, screenType, unloadDateTime != null ? unloadDateTime.toDate() : new Date());
            List<CallLog> callLogs = callLogService.populateCallLogForAgentCall(agentCall, employeeNumber, facilityId, screenType, false);
            logger.debug("Adding/Updating " + callLogs.size() + " call log(s) for employee[" + employeeNumber + "] on [" + screenType + "] screen.");
            callService.doScreenPopHealthCheck(uuid, employeeNumber, screenType, null, VelocityUtils.getDateAsString(unloadDateTime.toDate(), ScreenPopHealthCheck.FORMAT_DATE_TIME));
        }else{
            logger.debug(screenType + " screen pop for employee[" + employeeNumber + "] with UUID[" + uuid + "] is old.");
        }
    }

    @ResponseStatus(HttpStatus.OK)
    @RequestMapping(value = "screen-open-interval", method = RequestMethod.GET)
    public void reportScreenOpenInterval(@RequestParam String uuid,
                                         @RequestParam Integer employeeNumber,
                                         @RequestParam String screenType,
                                         @RequestParam String popDateTime,
                                         @RequestParam String currentDateTime){
        String msg = "Screen pop " + screenType + " for employee number[" + employeeNumber + "] with UUID[" + uuid + "] is still open @[" + currentDateTime + "] since " + popDateTime;
//        communicationService.sendNotificationViaEmail(CommunicationServiceImpl.DEVELOPER_EMAIL, msg, "Screen pop open for long time");
        callService.persistScreenPopTime(uuid, employeeNumber, screenType, popDateTime, currentDateTime);
    }

    @ResponseBody
    @RequestMapping(value = "provider-insurance", method = RequestMethod.GET, produces = "application/json")
    public ResponseWrapper getProviderInsurance(@RequestParam Integer facilityId){
        List<Provider> providers = callService.getProviderInsurance(facilityId);
        ResponseWrapper result = new ResponseWrapper();
        result.setSuccess(providers != null);
        result.setData(providers);
        result.setCount(providers != null ? providers.size() : 0);
        return result;
    }

    @ResponseBody
    @RequestMapping(value = "register-call-info.xml", method = RequestMethod.GET, produces = "application/xml")
    public IvrResponseWrapper registerCallInformation(@RequestParam String uuid,
                                                      @RequestParam Integer employeeNumber,
                                                      @RequestParam(required = false) String sessionId,
                                                      @RequestParam String screenType,
                                                      @RequestParam Long startDateTimeMilli,
                                                      @RequestParam String timezone,
                                                      @RequestParam Integer ringTime,
                                                      @RequestParam Integer talkTime,
                                                      @RequestParam Integer holdTime,
                                                      @RequestParam Integer workTime,
                                                      @RequestParam Long calledNumber,
                                                      @RequestParam Integer assignedTeamId,
                                                      @RequestParam Integer metServiceLevel,
                                                      @RequestParam String partnerId) {


        AgentCall agentCall = callService.findAgentCall(uuid);
        PartnerConfig partnerConfig = callService.findPartnerConfigById(partnerId);

        logAction(uuid, ActionLog.SCREEN_POP_ACTION, "Register Session Information for [" + partnerId + "] using UUID[" + uuid + "] with " +
                " employeeNumber[" + employeeNumber + "]" +
                " sessionId[" + sessionId + "]" +
                " screenType[" + screenType + "]" +
                " startDateTime[" + startDateTimeMilli + "]" +
                " timezone[" + timezone + "]" +
                " ringTime[" + ringTime + "]" +
                " talkTime[" + talkTime + "]" +
                " holdTime[" + holdTime + "]" +
                " workTime[" + workTime + "]" +
                " calledNumber[" + calledNumber + "]" +
                ".");
        if(partnerConfig != null && (employeeNumber == null || employeeNumber.equals(0))){
            employeeNumber = partnerConfig.getAgentStart();
        }

        IvrResponseWrapper wrapper = new IvrResponseWrapper();

        if(agentCall == null){
            InboundCall inboundCall = callService.findInboundCall(uuid);
            if(inboundCall != null && partnerConfig != null){
                agentCall = callService.startAgentCall(uuid, employeeNumber, inboundCall.getTargetFacilityId(), screenType, null);
            }
        }

        if (agentCall != null && partnerConfig != null) {

            LoadedEmployee loadedEmployee = callService.findLoadedEmployee(agentCall, employeeNumber, screenType);
            if(loadedEmployee == null){
                loadedEmployee = new LoadedEmployee(employeeNumber, new Date(), screenType);
                loadedEmployee.setSequence(agentCall.getLoadedEmployees().size() + 1);
                agentCall.getLoadedEmployees().add(loadedEmployee);
            }
            loadedEmployee.setPartnerId(partnerId);
            loadedEmployee.setPartnerSessionId(sessionId);
            loadedEmployee.setScreenType(screenType);
            loadedEmployee.setPartnerStartDateTime(startDateTimeMilli);
            loadedEmployee.setPartnerTimezone(timezone);
            loadedEmployee.setPartnerRingTime(ringTime);
            loadedEmployee.setPartnerTalkTime(talkTime);
            loadedEmployee.setPartnerHoldTime(holdTime);
            loadedEmployee.setPartnerWorkTime(workTime);
            loadedEmployee.setPartnerCalledNumber(calledNumber);
            loadedEmployee.setPartnerTeamId(assignedTeamId);
            loadedEmployee.setPartnerMetServiceLevel(metServiceLevel);

            callService.updateAgentCall(agentCall, false);

            if(loadedEmployee.getCallLogId() != null && loadedEmployee.getCallLogId() > 0){
                callLogService.updateCallLogWithPartnerInfo(loadedEmployee.getCallLogId(), loadedEmployee);
            }
            wrapper.setSuccess(true);
        } else {
            wrapper.setSuccess(false);
            wrapper.setMessage("Unable to find an InboundCall record using the UUID [" + uuid + "] provided!");
            logAction(uuid, ActionLog.SCREEN_POP_ACTION, "Fail to register session information for [" + partnerId + "] because there is no AgentCall.");
        }

        return wrapper;
    }

    @ResponseStatus(HttpStatus.OK)
    @RequestMapping(value = "link-file", method = RequestMethod.GET)
    public void linkFile(@RequestParam String source){
        backgroundExecutor.doLinkFileToCallLog(source);
    }

    @ResponseStatus(HttpStatus.OK)
    @RequestMapping(value = "pulse-check-screen-pop", method = RequestMethod.GET)
    public void checkScreenPopOnline(@RequestParam String uuid,
                                     @RequestParam Integer employeeNumber,
                                     @RequestParam String screenType,
                                     @RequestParam String popDateTime){
        backgroundExecutor.checkScreenPopOnline(uuid, employeeNumber, screenType, popDateTime);
    }

    @ResponseStatus(HttpStatus.OK)
    @RequestMapping(value = "register-ip", method = RequestMethod.GET)
    public void registerIp(@RequestParam String ip,
                            @RequestParam String uuid,
                            @RequestParam Integer employeeNumber,
                            @RequestParam String screenType){
        String message = "Register ip[" + ip + "] for " + screenType + " screen with UUID[" + uuid + "] and employee[" + employeeNumber + "]";
        logger.debug(message);
        logAction(uuid, ActionLog.SCREEN_POP_ACTION, message);
    }

    @ResponseBody
    @RequestMapping(value = "send-sms", method = RequestMethod.POST, produces = "application/json")
    public ResponseWrapper sendSMS(@RequestBody String json
                                    , @RequestParam Integer employeeNumber
                                    , @RequestParam Integer facilityId
                                    , @RequestParam Long templateId){
        ResponseWrapper wrapper = new ResponseWrapper();
        Map<String, String> smsData = (Map)convertJson(json, HashMap.class);
        String uuid = smsData.get("uuid");
        String to = smsData.get("phoneNumber");
        String msg = smsData.get("msg");
        String screenType = smsData.get("screenType");
        CommunicationTemplate template = communicationService.getCommunicationTemplateById(templateId);
        String logMsg = "Request to send SMS to [" + to + "] with msg [" + msg + "] by employee [" + employeeNumber + "]";
        boolean smsResult = false;
        InboundCall inboundCall = callService.findInboundCall(uuid);
        AgentCall agentCall = callService.findAgentCall(uuid);
        if(agentCall != null && template != null){
            String sids = communicationService.sendSMS(to, msg, facilityId, template.getReplyToAsPhoneNumber());
            smsResult = sids != null && sids.length() > 0;
            LoadedEmployee loadedEmployee = agentCall.findLoadedEmployee(employeeNumber, screenType);
            loadedEmployee.captureSMSEvent(to, msg, sids, facilityId);
            callService.updateAgentCall(agentCall, false);
        }
        logMsg += " and result [" + smsResult + "]";
        logAction(uuid, ActionLog.SCREEN_POP_ACTION, logMsg);
        wrapper.setSuccess(smsResult);
        return wrapper;
    }
}
