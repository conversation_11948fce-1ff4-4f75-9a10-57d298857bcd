package com.smilebrands.callcentersupport.domain;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.smilebrands.callcentersupport.domain.constants.PreferredRecallTime;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * User: Alban <PERSON>
 * Date: 11/03/14
 */
@Document
public class WebRequest {

    @Id
    private String uuid;

    private String state;
    private String city;
    private Integer office;
    private String officeName;
    private String officeAddress;

    private boolean isNewPatient;
    private String reasonForAppointment;

    private String firstName;
    private String lastName;

    private String gender;
    private String dateOfBirth;

    private String zipCode;

    private String patientStreet;
    private String patientStreet2;
    private String patientCity;
    private String patientState;
    private String patientZipCode;

    private boolean hasInsurance;
    private String anyDentalOffice;
    private String insuranceCompany;
    private String insuranceType;
    private String subscriberId;
    private String relationship;
    private String employerName;
    private String subscriberFirstName;
    private String subscriberLastName;
    private String subscriberGender;
    private String subscriberDateOfBirth;

    private Long phone;
    private String phoneType;

    private String emailAddress;

    private String appointmentDate;

    private String preferredTime;

    private Integer handledByAgent;

    private Date createDateTime;
    private Date updateDateTime;

    private Integer preferredRecallTime;
    @Transient
    private String preferredRecallTimeStr;

    private String comment;

    private String processedDateTime;
    private String pendingProcessedDateTime;
    private String pendingProcessedBy;
    private Integer numberOfRequest = 1;
    private Integer numberOfProcess = 0;
    private String nextProcessDateTime;

    private String originWebSite;

    private String resolution;

    private String recipient;

    private String campaign;
    private String utmCampaign;
    private String offer;
    private String keyword;

    private boolean emailPending;
    private String emailConfirmation;
    private String emailDateTime;
    private String emailFrom;

    private List<ScreenPopUrl> screenPopUrls = new ArrayList<ScreenPopUrl>();

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public Integer getOffice() {
        return office;
    }

    public void setOffice(Integer office) {
        this.office = office;
    }

    public String getOfficeName() {
        return officeName;
    }

    public void setOfficeName(String officeName) {
        this.officeName = officeName;
    }

    public String getOfficeAddress() {
        return officeAddress;
    }

    public void setOfficeAddress(String officeAddress) {
        this.officeAddress = officeAddress;
    }

    public boolean getIsNewPatient() {
        return isNewPatient;
    }

    public void setIsNewPatient(boolean isNewPatient) {
        this.isNewPatient = isNewPatient;
    }

    public String getReasonForAppointment() {
        return reasonForAppointment;
    }

    public void setReasonForAppointment(String reasonForAppointment) {
        this.reasonForAppointment = reasonForAppointment;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public Long getPhone() {
        return phone;
    }

    public void setPhone(Long phone) {
        this.phone = phone;
    }

    public String getPhoneType() {
        return phoneType;
    }

    public void setPhoneType(String phoneType) {
        this.phoneType = phoneType;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getAppointmentDate() {
        return appointmentDate;
    }

    public void setAppointmentDate(String appointmentDate) {
        this.appointmentDate = appointmentDate;
    }

    public String getPreferredTime() {
        return preferredTime;
    }

    public void setPreferredTime(String preferredTime) {
        this.preferredTime = preferredTime;
    }

    public Integer getHandledByAgent() {
        return handledByAgent;
    }

    public void setHandledByAgent(Integer handledByAgent) {
        this.handledByAgent = handledByAgent;
    }

    public Date getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(Date createDateTime) {
        this.createDateTime = createDateTime;
    }

    public Date getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(Date updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    public Integer getPreferredRecallTime() {
        return preferredRecallTime;
    }

    public void setPreferredRecallTime(Integer preferredRecallTime) {
        this.preferredRecallTime = preferredRecallTime;
    }

    public String getPreferredRecallTimeStr() {
        return preferredRecallTimeStr;
    }

    public void setPreferredRecallTimeStr(String preferredRecallTimeStr) {
        if(preferredRecallTimeStr != null && preferredRecallTimeStr.trim().length() > 0){
            this.preferredRecallTimeStr = preferredRecallTimeStr;
        }else{
            PreferredRecallTime prt = PreferredRecallTime.getByOrder(preferredRecallTime != null ? preferredRecallTime: 0);
            if(prt != null){
                this.preferredRecallTimeStr = prt.getDisplayName();
            }
        }
    }

    public String getProcessedDateTime() {
        return processedDateTime;
    }

    public void setProcessedDateTime(String processedDateTime) {
        this.processedDateTime = processedDateTime;
    }

    public String getPendingProcessedDateTime() {
        return pendingProcessedDateTime;
    }

    public void setPendingProcessedDateTime(String pendingProcessedDateTime) {
        this.pendingProcessedDateTime = pendingProcessedDateTime;
    }

    public String getPendingProcessedBy() {
        return pendingProcessedBy;
    }

    public void setPendingProcessedBy(String pendingProcessedBy) {
        this.pendingProcessedBy = pendingProcessedBy;
    }

    public Integer getNumberOfRequest() {
        return numberOfRequest;
    }

    public void setNumberOfRequest(Integer numberOfRequest) {
        this.numberOfRequest = numberOfRequest;
    }

    public Integer getNumberOfProcess() {
        return numberOfProcess;
    }

    public void setNumberOfProcess(Integer numberOfProcess) {
        this.numberOfProcess = numberOfProcess;
    }

    public String getNextProcessDateTime() {
        return nextProcessDateTime;
    }

    public void setNextProcessDateTime(String nextProcessDateTime) {
        this.nextProcessDateTime = nextProcessDateTime;
    }

    public List<ScreenPopUrl> getScreenPopUrls() {
        return screenPopUrls;
    }

    public void setScreenPopUrls(List<ScreenPopUrl> screenPopUrls) {
        this.screenPopUrls = screenPopUrls;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getOriginWebSite() {
        return originWebSite;
    }

    public void setOriginWebSite(String originWebSite) {
        this.originWebSite = originWebSite;
    }

    public String getResolution() {
        return resolution;
    }

    public void setResolution(String resolution) {
        this.resolution = resolution;
    }

    public String getRecipient() {
        return recipient;
    }

    public void setRecipient(String recipient) {
        this.recipient = recipient;
    }

    public String getCampaign() {
        return campaign;
    }

    public void setCampaign(String campaign) {
        this.campaign = campaign;
    }

    public String getUtmCampaign() {
        return utmCampaign;
    }

    public void setUtmCampaign(String utmCampaign) {
        this.utmCampaign = utmCampaign;
    }

    public String getOffer() {
        return offer;
    }

    public void setOffer(String offer) {
        this.offer = offer;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public boolean getEmailPending() {
        return emailPending;
    }

    public void setEmailPending(boolean emailPending) {
        this.emailPending = emailPending;
    }

    public String getEmailConfirmation() {
        return emailConfirmation;
    }

    public void setEmailConfirmation(String emailConfirmation) {
        this.emailConfirmation = emailConfirmation;
    }

    public String getEmailDateTime() {
        return emailDateTime;
    }

    public void setEmailDateTime(String emailDateTime) {
        this.emailDateTime = emailDateTime;
    }

    public String getEmailFrom() {
        return emailFrom;
    }

    public void setEmailFrom(String emailFrom) {
        this.emailFrom = emailFrom;
    }

    public String getNoReplyEmailAddress() {
        String result = "BrightNow.com <<EMAIL>>";

        if (this.getOriginWebSite() != null) {
            if (this.getOriginWebSite().indexOf("www.castledental.com") != -1) {
                result = "CastleDental.com <<EMAIL>>";
            } else if (this.getOriginWebSite().indexOf("www.monarchdental.com") != -1) {
                result = "MonarchDental.com <<EMAIL>>";
            }
        }

        return result;
    }

    public String getWebSiteHostName() {
        String result = "BrightNow.com";

        if (this.getOriginWebSite() != null) {
            if (this.getOriginWebSite().indexOf("www.castledental.com") != -1) {
                result = "CastleDental.com";
            } else if (this.getOriginWebSite().indexOf("www.monarchdental.com") != -1) {
                result = "MonarchDental.com";
            }
        }

        return result;
    }

    public String toString(){
        String result = "";
        result += this.getUuid() + " - " + this.getFirstName() + " " + this.getLastName() + " (" + this.getIsNewPatient() + ")";
        result += "\nEmail: " + this.getEmailAddress();
        result += "\nPhone: " + this.getPhone() + " (" + this.getPhoneType() + ")";
        result += "\nAppointment Date: " + this.getAppointmentDate() + " (" + this.getPreferredTime() + ")";
        result += "\nFacility: " + this.getOffice() + " - " + this.getOfficeName() + " (" + this.getOfficeAddress() + ")";
        result += "\nCreated On: " + VelocityUtils.getDateAsString(this.getCreateDateTime(), "MM-dd-yyyy hh:mm:ss a (z)");
        result += "\nComment: " + this.getComment();
        return result;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(String dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getPatientStreet() {
        return patientStreet;
    }

    public void setPatientStreet(String patientStreet) {
        this.patientStreet = patientStreet;
    }

    public String getPatientStreet2() {
        return patientStreet2;
    }

    public void setPatientStreet2(String patientStreet2) {
        this.patientStreet2 = patientStreet2;
    }

    public String getPatientCity() {
        return patientCity;
    }

    public void setPatientCity(String patientCity) {
        this.patientCity = patientCity;
    }

    public String getPatientState() {
        return patientState;
    }

    public void setPatientState(String patientState) {
        this.patientState = patientState;
    }

    public String getPatientZipCode() {
        return patientZipCode;
    }

    public void setPatientZipCode(String patientZipCode) {
        this.patientZipCode = patientZipCode;
    }

    public boolean getHasInsurance() {
        return hasInsurance;
    }

    public void setHasInsurance(boolean hasInsurance) {
        this.hasInsurance = hasInsurance;
    }

    public String getAnyDentalOffice() {
        return anyDentalOffice;
    }

    public void setAnyDentalOffice(String anyDentalOffice) {
        this.anyDentalOffice = anyDentalOffice;
    }

    public String getInsuranceCompany() {
        return insuranceCompany;
    }

    public void setInsuranceCompany(String insuranceCompany) {
        this.insuranceCompany = insuranceCompany;
    }

    public String getInsuranceType() {
        return insuranceType;
    }

    public void setInsuranceType(String insuranceType) {
        this.insuranceType = insuranceType;
    }

    public String getSubscriberId() {
        return subscriberId;
    }

    public void setSubscriberId(String subscriberId) {
        this.subscriberId = subscriberId;
    }

    public String getRelationship() {
        return relationship;
    }

    public void setRelationship(String relationship) {
        this.relationship = relationship;
    }

    public String getEmployerName() {
        return employerName;
    }

    public void setEmployerName(String employerName) {
        this.employerName = employerName;
    }

    public String getSubscriberFirstName() {
        return subscriberFirstName;
    }

    public void setSubscriberFirstName(String subscriberFirstName) {
        this.subscriberFirstName = subscriberFirstName;
    }

    public String getSubscriberLastName() {
        return subscriberLastName;
    }

    public void setSubscriberLastName(String subscriberLastName) {
        this.subscriberLastName = subscriberLastName;
    }

    public String getSubscriberGender() {
        return subscriberGender;
    }

    public void setSubscriberGender(String subscriberGender) {
        this.subscriberGender = subscriberGender;
    }

    public String getSubscriberDateOfBirth() {
        return subscriberDateOfBirth;
    }

    public void setSubscriberDateOfBirth(String subscriberDateOfBirth) {
        this.subscriberDateOfBirth = subscriberDateOfBirth;
    }
}
