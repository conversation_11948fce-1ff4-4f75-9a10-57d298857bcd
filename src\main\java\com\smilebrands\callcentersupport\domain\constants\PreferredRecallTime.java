package com.smilebrands.callcentersupport.domain.constants;

/**
 * Created by phong<PERSON>m on 11/17/14.
 */
public enum PreferredRecallTime {
    UNKNOWN(9999, ""),
    ASAP(0, "ASAP"),
    TOMORROW(1, "Tomorrow"),
    NEXT_WEEK(2, "Next Week")
    ;

    private Integer order;
    private String displayName;

    private PreferredRecallTime(Integer order, String displayName){
        this.order = order;
        this.displayName = displayName;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public static PreferredRecallTime getByOrder(Integer order){
        PreferredRecallTime value = UNKNOWN;
        switch (order){
            case 0: value = ASAP; break;
            case 1: value = TOMORROW; break;
            case 2: value = NEXT_WEEK; break;
            default: break;
        }

        return value;
    }

    public String getDisplayNameByOrder(Integer order){
        PreferredRecallTime value = getByOrder(order);
        return value.displayName;
    }

    public static PreferredRecallTime getByDisplayName(String displayName){
        PreferredRecallTime value = UNKNOWN;
        if(displayName != null && displayName.trim().length() > 0){
            displayName = displayName.trim();
            if(displayName.equalsIgnoreCase(ASAP.displayName)){
                value = ASAP;
            }else if(displayName.equalsIgnoreCase(TOMORROW.displayName)){
                value = TOMORROW;
            }else if(displayName.equalsIgnoreCase(NEXT_WEEK.displayName)){
                value = NEXT_WEEK;
            }
        }
        return value;
    }

    public Integer getOrderByDisplayName(String displayName){
        PreferredRecallTime value = getByDisplayName(displayName);
        return value.order;
    }

    @Override
    public String toString(){
        return this.order + " - " + this.displayName;
    }
}
