<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<%@ taglib uri="../customTag.tld" prefix="custom" %>
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="${pageContext.request.contextPath}/favicon.ico">

    <title>${languageText} Call for ${brandName}</title>

    <!-- Bootstrap core CSS -->
        <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/bootstrap.min.css" >

        <!-- Custom styles for this template -->
        <link href="${pageContext.request.contextPath}/bootstrap/css/call-template.css" rel="stylesheet">

  </head>

  <body>

        <input type="text" style="display:none;" id="uuid" value="${uniqueCallId}"/>
        <input type="text" style="display:none;" id="employeeNumber" value="${employeeNumber}"/>
        <input type="text" style="display:none;" id="language" value="${language}"/>
        <input type="text" style="display:none;" id="numberPress" value="${numberPress}"/>
        <input type="text" style="display:none;" id="zipCode" value="${zipCode}"/>
        <input type="text" style="display:none;" id="screenType" value="${screenType}"/>


        <div class="modal fade" id="callDetail" tabindex="-1" role="dialog" aria-labelledby="callDetailModalLabel" aria-hidden="true" style="top:100px;">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                        <h4 class="modal-title" id="callDetailModalLabel">Set Call Detail</h4>
                    </div>
                    <div class="modal-body" style="padding-bottom:0px;">
                        <form class="form-horizontal call-info-form" role="form">
                            <div class="form-group">
                                <label for="aniInput" class="col-sm-3 control-label">Phone Number
                                    <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="Phone Number" data-placement="right">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <input id="aniInput" type="text" class="form-control input-sm required" placeholder="Phone Number" data-type="number" length=10 />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="reasonInput" class="col-sm-3 control-label">Reason for Call</label>
                                <div class="col-sm-9">
                                    <select id="reasonInput" class="form-control input-sm">
                                        <c:forEach var="reason" items="${reasonList}">
                                            <option value="${reason.code}"> ${reason.description}</option>
                                        </c:forEach>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="resolutionInput" class="col-sm-3 control-label">Call Resolution</label>
                                <div class="col-sm-9">
                                    <select id="resolutionInput" class="form-control input-sm">
                                        <c:forEach var="resolutionCode" items="${resolutionCodeList}">
                                            <option value="${resolutionCode.code}"> ${resolutionCode.description}</option>
                                        </c:forEach>
                                    </select>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                        <button id="saveCallDetailBtn" type="button" class="btn btn-primary">Save</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="panel panel-${langButton}">
                <div class="panel-heading">
                    <h3 class="panel-title">This is a ${languageText} Call</h3>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-7">
                            <h2 class="blue top">${brandName}
                                <small>${extraBrandNameInfo}</small>
                                <c:if test="${isCampaign}">
                                    <small>
                                        (${campaignName})
                                    </small>
                                </c:if>
                            </h2>
                            <div class="alert alert-success" role="alert">
                                <div class="row">
                                    <div class="col-md-3"><strong>Menu Selection - ${numberPress}</strong></div>
                                    <div class="col-md-8">${numberPressText}</div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3"><strong>Number Dialed - ${phoneType}</strong></div>
                                    <div class="col-md-8">${phoneTypeText}.</div>
                                </div>
                                <c:if test="${zipCode != null}">
                                    <div class="row">
                                        <div class="col-md-3"><strong>Zip Code Entered </strong></div>
                                        <div class="col-md-8">${zipCode}.</div>
                                    </div>
                                </c:if>
                            </div>
                        </div>
                        <div class="col-md-offset-1 ctl-btn">
                            <div class="btn-group">
                                <button id="callDetailsBtn" type="button" class="btn btn-${langButton} btn">Record Call Details</button>
                            </div>
                        </div>
                    </div>

                    <div id="facilitySearchByZipCode" class="panel panel-default">
                        <div class="panel-heading">
                          <h3 class="panel-title gray">Search Office by Zip Code</h3>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="input-group">
                                        <span class="input-group-addon">Enter zip code:</span>
                                        <input id="fsbzInput" type="text" class="form-control" placeholder="5-digit zip code">
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div id="display_list" class="list-group" display-position="right">

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div><!-- /.container -->
    <!-- Bootstrap core JavaScript
    ================================================== -->
    <!-- Placed at the end of the document so the pages load faster -->
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-1.7.2.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-ui-1.8.20.custom.min.js"></script>
    <script src="${pageContext.request.contextPath}/bootstrap/js/bootstrap.js"></script>

    <script type="text/javascript" src="${pageContext.request.contextPath}/script/screenPopActions.js?_id=20171030"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/sbi-script.js?_id=20170822"></script>
  </body>
</html>