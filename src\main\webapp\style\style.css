/* =============================================================================
   HTML5 display definitions
   ========================================================================== */

article, aside, details, figcaption, figure, footer, header, hgroup, nav, section { display: block; }
audio, canvas, video { display: inline-block; *display: inline; *zoom: 1; }
audio:not([controls]) { display: none; }
[hidden] { display: none; }


/* =============================================================================
   Base
   ========================================================================== */

/*
 * 1. Correct text resizing oddly in IE6/7 when body font-size is set using em units
 * 2. Prevent iOS text size adjust on device orientation change, without disabling user zoom: h5bp.com/g
 */

html { font-size: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }

html, button, input, select, textarea { font-family: sans-serif; color: #222; }

body { margin: 0; font-size: 1em; line-height: 1.4; }

input:-moz-placeholder {
    color:    #999;
}

/*
 * Remove text-shadow in selection highlight: h5bp.com/i
 * These selection declarations have to be separate
 * Also: hot pink! (or customize the background color to match your design)
 */

::-moz-selection { background: #fe57a1; color: #fff; text-shadow: none; }
::selection { background: #fe57a1; color: #fff; text-shadow: none; }


/* =============================================================================
   Links
   ========================================================================== */

a { color: #00e; text-decoration: none;}
a:visited { color: #551a8b; }
a:hover { color: #06e; }
a:focus { outline: thin dotted; }

/* Improve readability when focused and hovered in all browsers: h5bp.com/h */
a:hover, a:active { outline: 0; }


/* =============================================================================
   Typography
   ========================================================================== */

abbr[title] { border-bottom: 1px dotted; }

b, strong { font-weight: bold; }

blockquote { margin: 1em 40px; }

dfn { font-style: italic; }

hr { display: block; height: 1px; border: 0; border-top: 1px solid #ccc; margin: 1em 0; padding: 0; }

ins { background: #ff9; color: #000; text-decoration: none; }

mark { background: #ff0; color: #000; font-style: italic; font-weight: bold; }

/* Redeclare monospace font family: h5bp.com/j */
pre, code, kbd, samp { font-family: monospace, serif; _font-family: 'courier new', monospace; font-size: 1em; }

/* Improve readability of pre-formatted text in all browsers */
pre { white-space: pre; white-space: pre-wrap; word-wrap: break-word; }

q { quotes: none; }
q:before, q:after { content: ""; content: none; }

small { font-size: 85%; }

/* Position subscript and superscript content without affecting line-height: h5bp.com/k */
sub, sup { font-size: 75%; line-height: 0; position: relative; vertical-align: baseline; }
sup { top: -0.5em; }
sub { bottom: -0.25em; }


/* =============================================================================
   Lists
   ========================================================================== */

ul, ol { margin: 1em 0; padding: 0 0 0 40px; }
dd { margin: 0 0 0 40px; }
nav ul, nav ol { list-style: none; list-style-image: none; margin: 0; padding: 0; }


/* =============================================================================
   Embedded content
   ========================================================================== */

/*
 * 1. Improve image quality when scaled in IE7: h5bp.com/d
 * 2. Remove the gap between images and borders on image containers: h5bp.com/i/440
 */

img { border: 0; -ms-interpolation-mode: bicubic; vertical-align: middle; }

/*
 * Correct overflow not hidden in IE9
 */

svg:not(:root) { overflow: hidden; }


/* =============================================================================
   Figures
   ========================================================================== */

figure { margin: 0; }


/* =============================================================================
   Forms
   ========================================================================== */

form { margin: 0; }
fieldset { border: 0; margin: 0; padding: 0; }

/* Indicate that 'label' will shift focus to the associated form element */
label { cursor: pointer; }

/*
 * 1. Correct color not inheriting in IE6/7/8/9
 * 2. Correct alignment displayed oddly in IE6/7
 */

legend { border: 0; *margin-left: -7px; padding: 0; white-space: normal; }

/*
 * 1. Correct font-size not inheriting in all browsers
 * 2. Remove margins in FF3/4 S5 Chrome
 * 3. Define consistent vertical alignment display in all browsers
 */

button, input, select, textarea { font-size: 100%; margin: 0; vertical-align: baseline; *vertical-align: middle; }

/*
 * 1. Define line-height as normal to match FF3/4 (set using !important in the UA stylesheet)
 */

button, input { line-height: normal; }

/*
 * 1. Display hand cursor for clickable form elements
 * 2. Allow styling of clickable form elements in iOS
 * 3. Correct inner spacing displayed oddly in IE7 (doesn't effect IE6)
 */

button, input[type="button"], input[type="reset"], input[type="submit"] { cursor: pointer; -webkit-appearance: button; *overflow: visible; }

/*
 * Re-set default cursor for disabled elements
 */

button[disabled], input[disabled] { cursor: default; }

/*
 * Consistent box sizing and appearance
 */

input[type="checkbox"], input[type="radio"] { box-sizing: border-box; padding: 0; *width: 13px; *height: 13px; }
input[type="search"] { -webkit-appearance: textfield; -moz-box-sizing: content-box; -webkit-box-sizing: content-box; box-sizing: content-box; }
input[type="search"]::-webkit-search-decoration, input[type="search"]::-webkit-search-cancel-button { -webkit-appearance: none; }

/*
 * Remove inner padding and border in FF3/4: h5bp.com/l
 */

button::-moz-focus-inner, input::-moz-focus-inner { border: 0; padding: 0; }

/*
 * 1. Remove default vertical scrollbar in IE6/7/8/9
 * 2. Allow only vertical resizing
 */

textarea { overflow: auto; vertical-align: top; resize: vertical; }

/* Colors for form validity */
input:valid, textarea:valid {  }
input:invalid, textarea:invalid { background-color: #f0dddd; }


/* =============================================================================
   Tables
   ========================================================================== */

table { border-collapse: collapse; border-spacing: 0; }
td { vertical-align: top; }


/* =============================================================================
   Chrome Frame Prompt
   ========================================================================== */

.chromeframe { margin: 0.2em 0; background: #ccc; color: black; padding: 0.2em 0; }


/* ==|== primary styles =====================================================
   Author:
   ========================================================================== */
















/* ==|== media queries ======================================================
   EXAMPLE Media Query for Responsive Design.
   This example overrides the primary ('mobile first') styles
   Modify as content requires.
   ========================================================================== */

@media only screen and (min-width: 35em) {
  /* Style adjustments for viewports that meet the condition */
}



/* ==|== non-semantic helper classes ========================================
   Please define your styles before this section.
   ========================================================================== */

/* For image replacement */
.ir { display: block; border: 0; text-indent: -999em; overflow: hidden; background-color: transparent; background-repeat: no-repeat; text-align: left; direction: ltr; *line-height: 0; }
.ir br { display: none; }

/* Hide from both screenreaders and browsers: h5bp.com/u */
.hidden { display: none !important; visibility: hidden; }

/* Hide only visually, but have it available for screenreaders: h5bp.com/v */
.visuallyhidden { border: 0; clip: rect(0 0 0 0); height: 1px; margin: -1px; overflow: hidden; padding: 0; position: absolute; width: 1px; }

/* Extends the .visuallyhidden class to allow the element to be focusable when navigated to via the keyboard: h5bp.com/p */
.visuallyhidden.focusable:active, .visuallyhidden.focusable:focus { clip: auto; height: auto; margin: 0; overflow: visible; position: static; width: auto; }

/* Hide visually and from screenreaders, but maintain layout */
.invisible { visibility: hidden; }

/* Contain floats: h5bp.com/q */
.clearfix:before, .clearfix:after { content: ""; display: table; }
.clearfix:after { clear: both; }
.clearfix { *zoom: 1; }



/* ==|== print styles =======================================================
   Print styles.
   Inlined to avoid required HTTP connection: h5bp.com/r
   ========================================================================== */

@media print {
  * { background: transparent !important; color: black !important; box-shadow:none !important; text-shadow: none !important; filter:none !important; -ms-filter: none !important; } /* Black prints faster: h5bp.com/s */
  a, a:visited { text-decoration: underline; }
  a[href]:after { content: " (" attr(href) ")"; }
  abbr[title]:after { content: " (" attr(title) ")"; }
  .ir a:after, a[href^="javascript:"]:after, a[href^="#"]:after { content: ""; }  /* Don't show links for images, or javascript/internal links */
  pre, blockquote { border: 1px solid #999; page-break-inside: avoid; }
  thead { display: table-header-group; } /* h5bp.com/t */
  tr, img { page-break-inside: avoid; }
  img { max-width: 100% !important; }
  @page { margin: 0.5cm; }
  p, h2, h3 { orphans: 3; widows: 3; }
  h2, h3 { page-break-after: avoid; }
}

/*** BRIGHT NOW ***/

.clear {
	clear: both;
}
.fl {
	float: left;
}
.fr {
	float: right;
}

/**p {
	margin: 18px 0;	
}**/

body {
	background:#123558;
	text-align: center;
	color: #252525;
	font: 13px/18px Arial, Helvetica, sans-serif;
}
.main {
	padding: 36px 45px 25px;
	width: 1050px;
	background: #fff;
	-moz-border-radius: 5px; /* Firefox */
	-webkit-border-radius: 5px; /* Safari, Chrome */
	border-radius: 5px; /* CSS3 */
	margin: 26px auto;
	text-align: left;
}
h1, h2, h3, h4, h5, h6 {
	color: #123558;	
	font-weight: normal;
}
h1 {font-size: 18px;}
h2 {font-size: 16px; margin: 0 0 19px;}
h3 {font-size: 14px;}
h4 {font-size: 13px;}
h5 {font-size: 12px;}

h1.title {
	margin: 0 0 3px;
}

h4.group {
	margin: 0 0 21px;	
}

.block {
	padding: 20px 0 27px;
	border-top: 1px solid #bdbdbd;
}

.pipe {

    border: 1px solid #BDBDBD;
    margin-left: 5px;
    margin-right: 2px
}

.left {
    width: 500px;
    padding: 10px 17px 0 0;
	/*width: 320px;
	padding: 10px 77px 0 0; */
}

.right {
    width: 300px;
    padding: 11px 100px 0 0;
	/*width: 482px;
	padding: 11px 170px 0 0; */
}

ul.info-list {
	list-style: none;
	padding: 0;
	margin: 0;
    width: 600px;
	/*width: 320px;*/
}

ul.info-list li {
	float: left;
	margin: 0 0 6px;
	display: block;
	clear: both;
    width: 600px;
	/*width: 320px;*/
}

span.label {
	width: 100px;
	margin: 0 10px 0 0;
	display: block;
	color: #6a6a6a;
	clear: left;
}

.labelEdit{
    width: 140px;
    margin: 0 10px 0 0;
    display: block;
    color: #6a6a6a;
    clear: left;

}

span.day {
	width: 40px;
	display: block;
}

ul.info-list span.content {
	display: block;
    width: 490px;
	/*width: 210px;*/
}

ul.info-list span.title {
	color: #165b9c;	
	font-weight: bold;
}

ul.info-list span.phone {
	color: #123558;
}

ul.info-list span.phone-sp {
    color: #2894db;
}

ul.staff-list {
	list-style: none;
	padding: 0;
	margin: 0 0 24px;
}

ul.staff-list li {
	float: left;
	margin: 0 0 12px;
	display: block;
	clear: both;
}

ul.staff-list-last {
	margin-bottom: 0;
}

span.name {
	display: block;
	width: 450px;
	font-weight: bold;
}

/*** TOP ACTIONS BAR ***/
.top {
	background: #e1e1e1;
	/*height: 23px;*/
	line-height: 20px;
	padding: 11px;
	color: #202020;
	margin: -2px 0 0 0;
}
.top-no-background{
	/*height: 23px;*/
	line-height: 20px;
	padding: 11px;
	color: #202020;
	margin: -2px 0 0 0;

}

.actions {
		
}

.actions p {
	margin: 0 0 0 10px;
	line-height: 23px;
}
.actions-list {
	margin: 1px 0 0 50px;
	padding: 0;
	min-width: 100px;
	*width: 100px;
}
.actions-list li {
	float: left;
	list-style: none;
	display: block;
	width: 32px;
	height: 18px;
	background: url('../images/icon-bg.jpg') repeat-x;
	border: 1px solid #a7aaad;
	border-left: none;
}
.actions-list li:first-child {
	border-left: 1px solid #a7aaad;	
}
.actions-list li a {
	display: block;
	width: 32px;
	height: 18px;
}
.actions-list li a.link {
	background: url('../images/link.png') no-repeat center center;
}
.actions-list li a.email {
	background: url('../images/email.png') no-repeat center center;
}
.actions-list li a.phone {
	background: url('../images/phone.png') no-repeat center center;
}
.actions-list li a.search {
	background: url('../images/search-icon.png') no-repeat center center;
}

.search-form input[type="text"] {
	border: 1px solid #abaeb1;
	height: 21px;
	padding: 5px 5px 2px;
	margin: 0 10px 0 0;
	line-height: 15px;
	font-size: 12px;
	color: #202020;
	background: url('../images/input-bg.jpg');
	float: left;
}

.search-form input[type="submit"] {
	background: url('../images/icon-bg.jpg') repeat-x;
	border: 1px solid #a7aaad;
	height: 20px;
	line-height: 18px;
	padding: 0 6px;
	text-transform: uppercase;
	font-size: 11px;
	color: #165B9C;
	float: right;
	margin: 0;
}

.email-form input[type="submit"] {
	background: url('../images/icon-bg.jpg') repeat-x;
	border: 1px solid #a7aaad;
	height: 20px;
	line-height: 18px;
	padding: 0 6px;
	text-transform: uppercase;
	font-size: 11px;
    color: #165B9C;
	float: right;
	margin: 0;
}

/* New */
.neighbor-office {
    width: 480px;
    padding: 10px 0 0 0;
    /*width: 320px;
     padding: 10px 77px 0 0; */
}

ul.neighbor-office-info-list {
    list-style: none outside none;
    margin: 0;
    padding: 0;
    width: 480px;
}

ul.neighbor-office-info-list li {
    clear: both;
    display: block;
    float: left;
    margin: 0 0 6px;
    width: 480px;
}

ul.neighbor-office-info-list span.content {
    display: block;
    width: 370px;
}

ul.neighbor-office-info-list span.title {
    color: #165b9c;
    font-weight: bold;
}

ul.neighbor-office-info-list span.phone {
    color: #123558;
}

ul.neighbor-office-info-list span.phone-sp {
    color: #2894db;
}

.map-link {
    /*background: url("../images/google-map-logo2.png") no-repeat scroll 0 0 transparent;*/
    height: 18px;
    width: 20px;
    /*float: right;*/
}

.body-background-sp {
    background:#2894DB;
}

.green {
    color: green;
}

.linked {
	color: #165b9c;
	font-weight: bold;
}

.email-label {
	width: 130px;
	margin: 0 10px 0 0;
	color: #6a6a6a;
}

.email-padding {
	padding-right: 20px;
}

.email-div {
	margin: 13px 0;
}

.lib-btn {
    background: #0095cd;
    background: linear-gradient(top,  #458DC1, #1F527F);
    border: solid 1px #0076a3;
    background: -moz-linear-gradient(top,  #458DC1, #1F527F);
    background: -webkit-linear-gradient(top,  #458DC1, #1F527F);
}
.x-btn-center {
    background: none repeat scroll 0 0 transparent;
    font-size: 0.917em;
    height: 30px;
}
.x-btn-inner {
    color: #EFEFEF !important;
    font-size: 1.1em;
    padding: 0 25px;
    text-shadow: 1px 1px 1px #222222;
}