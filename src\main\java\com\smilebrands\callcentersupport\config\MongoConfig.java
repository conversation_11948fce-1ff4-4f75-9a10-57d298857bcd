package com.smilebrands.callcentersupport.config;

import com.mongodb.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.MongoDbFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoDbFactory;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * User: <PERSON><PERSON>
 * Date: 5/9/16
 */
@Configuration
public class MongoConfig {

    private static final Logger LOGGER = LoggerFactory.getLogger(MongoConfig.class);

    /** MongoDB Custom Configs **/
    @Autowired
    @Qualifier("mongoDBReplicaSet")
    private String mongoServersAndPort;

    @Autowired
    @Qualifier("mongoDBName")
    private String mongoDatabase;

    @Autowired
    @Qualifier("mongoAuthDB")
    private String mongoAuthDatabase;

    @Autowired
    @Qualifier("mongoDBUser")
    private String mongoUser;

    @Autowired
    @Qualifier("mongoDBPass")
    private String mongoPassword;

    @Bean(name = "mongoTemplate")
    public MongoTemplate getMongoTemplate() throws Exception {
        LOGGER.debug("Initializing a mongoTemplate...");
        return new MongoTemplate(getMongoDbFactory());
    }

    @Bean
    public MongoDbFactory getMongoDbFactory() throws Exception {
        return new SimpleMongoDbFactory(getMongoClient(), mongoDatabase);
    }

    @Bean
    public MongoClient getMongoClient() throws Exception {
        LOGGER.info("Launching MongoDB using these Servers [" + mongoServersAndPort + "].");

        List<ServerAddress> mongos = new ArrayList<ServerAddress>();
        String[] servers = StringUtils.delimitedListToStringArray(mongoServersAndPort, ";");
        if (servers.length == 0) {
            throw new RuntimeException("Unable to build a MongoClient using Mongo Servers " + mongoServersAndPort);
        }

        for (String server : servers) {
            String[] sp = StringUtils.delimitedListToStringArray(server, ":");
            if (sp.length == 2) {
                mongos.add(new ServerAddress(sp[0], new Integer(sp[1])));
            } else {
                throw new RuntimeException("Unable to split this server:port value [" + sp + "] into a legitimate Mongo Server and Port.");
            }
        }

        MongoClientOptions.Builder opts = new MongoClientOptions.Builder();
        opts.maxWaitTime(90000);
        opts.minConnectionsPerHost(25);
        opts.threadsAllowedToBlockForConnectionMultiplier(4);
        opts.socketTimeout(90000);
        opts.maxConnectionIdleTime(300000);
        opts.maxConnectionLifeTime(900000);
        opts.writeConcern(WriteConcern.ACKNOWLEDGED);
        opts.readPreference(ReadPreference.primaryPreferred());

        MongoClient mc;
        if ((mongoUser.length() > 0 && ! mongoUser.equalsIgnoreCase("EMPTY")) && (mongoPassword.length() > 0 && ! mongoPassword.equalsIgnoreCase("EMPTY"))) {
            mc = new MongoClient(mongos,
                    MongoCredential.createScramSha1Credential(mongoUser, mongoAuthDatabase, mongoPassword.toCharArray()),
                    opts.build());
        } else {
            mc = new MongoClient(mongos,
                    opts.build());
        }

        return mc;
    }

}
