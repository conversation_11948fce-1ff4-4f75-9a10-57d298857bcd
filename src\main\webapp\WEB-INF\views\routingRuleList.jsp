<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd" >
<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<%@ taglib uri="../customTag.tld" prefix="custom" %>

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Smile Brands Call Center - Reason - Resolution List</title>
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/bootstrap.min.css" >
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/style/redmond/jquery-ui-1.8.20.custom.css" />

    <!-- Custom styles for this template -->
    <link href="${pageContext.request.contextPath}/bootstrap/css/call-template.css" rel="stylesheet">
</head>
<body>
    <div class="modal fade" id="routingRuleDetail" tabindex="-1" role="dialog" aria-labelledby="routingRuleModalLabel" aria-hidden="true" style="top:100px;">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 class="modal-title" id="routingRuleModalLabel">Update Routing Rule</h4>
                </div>
                <div class="modal-body" style="padding-bottom:0px;">
                    <form class="form-horizontal call-info-form" role="form">
                        <div class="form-group">
                            <label for="phoneNumberInput" class="col-sm-4 control-label">Phone Number
                                <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="Phone Number" data-placement="right">*</span>
                            </label>
                            <div class="col-sm-7">
                                <input id="phoneNumberInput" type="text" class="form-control input-sm required" placeholder="Enter Phone Number" dataField="phoneNumber" style="display:none;"/>
                                <div style="padding-top:5px;">
                                    <span id="phoneNumberLbl" style="font-size:14px;"></span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="descriptionInput" class="col-sm-4 control-label">Description
                                <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="Description" data-placement="right">*</span>
                            </label>
                            <div class="col-sm-7">
                                <input id="descriptionInput" type="text" class="form-control input-sm required" placeholder="Enter Descriptions" dataField="description"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="directQueueInput" class="col-sm-4 control-label">Redirect Queue Name
                                <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="Redirect Queue Name" data-placement="right">*</span>
                            </label>
                            <div class="col-sm-4">
                                <input id="directQueueInput" type="text" class="form-control input-sm" placeholder="Redirect Queue Name" dataField="redirectQueueName"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="directCSRInput" class="col-sm-4 control-label">Direct to CSR?
                            </label>
                            <div class="col-sm-7">
                                <input id="directCSRInput" type="checkbox" class="input-sm" dataField="redirectCsr" style="width:50px;" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="isSpamInput" class="col-sm-4 control-label">Is Spam?
                            </label>
                            <div class="col-sm-7">
                                <input id="isSpamInput" type="checkbox" class="input-sm" dataField="spam" style="width:50px;" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="isActiveInput" class="col-sm-4 control-label">Is Active?
                            </label>
                            <div class="col-sm-7">
                                <input id="isActiveInput" type="checkbox" class="input-sm" dataField="isActive" style="width:50px;" />
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button id="saveRoutingRuleDetailBtn" type="button" class="btn btn-primary">Save</button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <br/>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h1 class="panel-title gray">Routing Rules
                    <span class="glyphicon glyphicon-plus fr pointer add-routing-rule" data-toggle="tooltip" title="Add New"></span>
                </h1>
            </div>
            <div class="panel-body">
                <div class="row filter-cmp">
                    <div class="col-md-1">Filter By:</div>
                    <div class="col-md-3">
                        <select filterOptionFor="routingRuleList" style="width:100%;">
                            <option value="phoneNumber">Phone Number</option>
                            <option value="description">Description</option>
                            <option value="redirectQueueName">Direct Queue Name</option>
                            <option value="spam">Is Spam?</option>
                            <option value="redirectCsr">Direct to CSR?</option>
                            <option value="isActive">Is Active?</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <input type="text" filterInputFor="routingRuleList" style="width:90%;"/>
                        <span class="glyphicon glyphicon-remove pointer" removeFilterFor="reasonResolutionList"/>
                    </div>
                </div>
                <table id="routingRuleList" class="table table-striped">
                    <thead>
                        <tr>
                            <th sortField="phoneNumber" class="pointer">Phone Number<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="description" class="pointer">Description<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="redirectQueueName" class="pointer">Direct Queue Name<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="spam" class="pointer">Is Spam?<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="redirectCsr" class="pointer">Direct to CSR?<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="isActive" class="pointer">Is Active?<span class="glyphicon padding-left-5"></span></th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <c:forEach var="rule" items="${routingRuleList}">

                            <tr>
                                <td name="phoneNumber" value="${rule.phoneNumber}">${rule.phoneNumber}</td>
                                <td name="description" value="${rule.description}">${rule.description}</td>
                                <td name="redirectQueueName" value="${rule.redirectQueueName}">${rule.redirectQueueName}</td>
                                <td name="spam" value="${rule.spam}">${rule.spam}</td>
                                <td name="redirectCsr" value="${rule.redirectCsr}">${rule.redirectCsr}</td>
                                <td name="isActive" value="true">true</td>
                                <td><span class="glyphicon glyphicon-pencil pointer edit-routing-rule" data-toggle="tooltip" title="Edit"></span></td>
                            </tr>

                        </c:forEach>
                    </tbody>
                </table>

            </div>
        </div>
    </div>

    <!-- Placed at the end of the document so the pages load faster -->
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-1.7.2.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-ui-1.8.20.custom.min.js"></script>
    <script src="${pageContext.request.contextPath}/bootstrap/js/bootstrap.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/configurationActions.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/sbi-script.js"></script>
</body>
</html>