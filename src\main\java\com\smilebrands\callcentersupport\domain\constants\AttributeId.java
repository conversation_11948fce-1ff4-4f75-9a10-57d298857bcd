package com.smilebrands.callcentersupport.domain.constants;

/**
 * Created by phong<PERSON>m on 10/24/14.
 */
public enum AttributeId {
    UNKNOWN(0, ""),
    GROUP_SPECIALTY_SERVICES(179, "FACILITY"),
    <PERSON><PERSON><PERSON>_ALERT(180, "FACILITY"),
    ATTRIBUTE_IMMEDIATE_ALERT(26458, "FACILITY"),
    ATTRIBUTE_GENERAL_ALERT(26459, "FACILITY"),
    ATTRIBUTE_QSI_ID(6, "FACILITY"),
    ATTRIBUTE_MONDAY_HOUR(10, "FACILITY"),
    ATTRIBUTE_TUESDAY_HOUR(11, "FACILITY"),
    ATTRIBUTE_WEDNESDAY_HOUR(12, "FACILITY"),
    ATTRIBUTE_THURSDAY_HOUR(13, "FACILITY"),
    ATTRIBUTE_FRIDAY_HOUR(14, "FACILITY"),
    ATTRIBUTE_SATURDAY_HOUR(15, "FACILITY"),
    ATTRIBUTE_SUNDAY_HOUR(16, "FACILITY"),
    ATTRIBUTE_CROSS_STREETS(26432, "FACILITY"),
    ATTRIBUTE_SPECIAL_NOTES(26452, "FACILITY"),
    ATTRIBUTE_ON_CALL_NAME(26453, "FACILITY"),
    ATTRIBUTE_ON_CALL_NUMBER(26454, "FACILITY"),
    ATTRIBUTE_LANGUAGES_SPOKEN(26455, "FACILITY"),
    ATTRIBUTE_ACCEPTED_STATE_PLAN(26473, "FACILITY"),
    ATTRIBUTE_AGE_LIMITED(26492, "FACILITY"),
    ATTRIBUTE_NITROUS_OFFERED(26493, "FACILITY"),
    ATTRIBUTE_IV_SEDATION(31472, "FACILITY"),
    ATTRIBUTE_OFFICE_HOURS_NOTES(32109, "FACILITY"),
    ATTRIBUTE_OPEN_BOOK_SCHEDULING_ENABLED(159782, "FACILITY"),

    ATTRIBUTE_PROVIDER_LANGUAGES_SPOKEN(1, "PROVIDER"),
    ATTRIBUTE_PROVIDER_SCHEDULE_NOTES(2, "PROVIDER");


//    public static final Integer GROUP_SPECIALTY_SERVICES = 179;
//
//    public static final Integer GROUP_ALERT = 180;
//    public static final Integer ATTRIBUTE_IMMEDIATE_ALERT = 26458;
//    public static final Integer ATTRIBUTE_GENERAL_ALERT = 26459;
//
//    public static final Integer ATTRIBUTE_QSI_ID = 6;

    private int value;
    private String attributeType;

    private AttributeId(int value, String attributeType) {
        this.value = value;
        this.attributeType = attributeType;
    }

    public int getValue() {
        return value;
    }

    public String getAttributeType() {
        return this.attributeType;
    }

    public static AttributeId getById(int id, String attributeType){
        AttributeId result = UNKNOWN;
        if(attributeType != null && attributeType.equalsIgnoreCase("facility")){
            if(id == GROUP_ALERT.getValue()){
                result = GROUP_ALERT;
            }else if(id == GROUP_SPECIALTY_SERVICES.getValue()){
                result = GROUP_SPECIALTY_SERVICES;
            }else if(id == ATTRIBUTE_IMMEDIATE_ALERT.getValue()){
                result = ATTRIBUTE_IMMEDIATE_ALERT;
            }else if(id == ATTRIBUTE_GENERAL_ALERT.getValue()){
                result = ATTRIBUTE_GENERAL_ALERT;
            }else if(id == ATTRIBUTE_QSI_ID.getValue()){
                result = ATTRIBUTE_QSI_ID;
            }else if(id == ATTRIBUTE_MONDAY_HOUR.getValue()){
                result = ATTRIBUTE_MONDAY_HOUR;
            }else if(id == ATTRIBUTE_TUESDAY_HOUR.getValue()){
                result = ATTRIBUTE_TUESDAY_HOUR;
            }else if(id == ATTRIBUTE_WEDNESDAY_HOUR.getValue()){
                result = ATTRIBUTE_WEDNESDAY_HOUR;
            }else if(id == ATTRIBUTE_THURSDAY_HOUR.getValue()){
                result = ATTRIBUTE_THURSDAY_HOUR;
            }else if(id == ATTRIBUTE_FRIDAY_HOUR.getValue()){
                result = ATTRIBUTE_FRIDAY_HOUR;
            }else if(id == ATTRIBUTE_SATURDAY_HOUR.getValue()){
                result = ATTRIBUTE_SATURDAY_HOUR;
            }else if(id == ATTRIBUTE_SUNDAY_HOUR.getValue()){
                result = ATTRIBUTE_SUNDAY_HOUR;
            }else if(id == ATTRIBUTE_CROSS_STREETS.getValue()){
                result = ATTRIBUTE_CROSS_STREETS;
            }else if(id == ATTRIBUTE_SPECIAL_NOTES.getValue()){
                result = ATTRIBUTE_SPECIAL_NOTES;
            }else if(id == ATTRIBUTE_ON_CALL_NAME.getValue()){
                result = ATTRIBUTE_ON_CALL_NAME;
            }else if(id == ATTRIBUTE_ON_CALL_NUMBER.getValue()){
                result = ATTRIBUTE_ON_CALL_NUMBER;
            }else if(id == ATTRIBUTE_LANGUAGES_SPOKEN.getValue()){
                result = ATTRIBUTE_LANGUAGES_SPOKEN;
            }else if(id == ATTRIBUTE_ACCEPTED_STATE_PLAN.getValue()){
                result = ATTRIBUTE_ACCEPTED_STATE_PLAN;
            }else if(id == ATTRIBUTE_AGE_LIMITED.getValue()){
                result = ATTRIBUTE_AGE_LIMITED;
            }else if(id == ATTRIBUTE_NITROUS_OFFERED.getValue()){
                result = ATTRIBUTE_NITROUS_OFFERED;
            }else if(id == ATTRIBUTE_IV_SEDATION.getValue()){
                result = ATTRIBUTE_IV_SEDATION;
            }else if(id == ATTRIBUTE_OFFICE_HOURS_NOTES.getValue()){
                result = ATTRIBUTE_OFFICE_HOURS_NOTES;
            }else if(id == ATTRIBUTE_OPEN_BOOK_SCHEDULING_ENABLED.getValue()){
                result = ATTRIBUTE_OPEN_BOOK_SCHEDULING_ENABLED;
            }
        }else if(attributeType != null && attributeType.equalsIgnoreCase("provider")){
            if(id == ATTRIBUTE_PROVIDER_LANGUAGES_SPOKEN.getValue()){
                result = ATTRIBUTE_PROVIDER_LANGUAGES_SPOKEN;
            }else if(id == ATTRIBUTE_PROVIDER_SCHEDULE_NOTES.getValue()){
                result = ATTRIBUTE_PROVIDER_SCHEDULE_NOTES;
            }
        }

        return result;
    }

}
