package com.smilebrands.callcentersupport.repo;

import com.smilebrands.callcentersupport.domain.Appointment;
import com.smilebrands.callcentersupport.domain.Patient;
import com.smilebrands.callcentersupport.domain.V_CallLog;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * User: <PERSON><PERSON>
 * Date: 6/26/14
 */
public interface CallRepository {

    public boolean queueConfirmationEmail(Integer facilityId, Long patientId, String firstName, String lastName, String emailAddress, String appointmentTime, String language);

    public Appointment findAppointmentByPatientPhoneNumber(Long phoneNumber, Integer facilityId, String dob);

    public Appointment findLibertyEventByPatientPhoneNumber(Long phoneNumber, Integer facilityId, String dob);

    public List<Appointment> findAppointmentsByPatientPhoneNumber(Long phoneNumber, Integer facilityId, String dob);

    public List<Appointment> findLibertyEventsByPatientPhoneNumber(Long phoneNumber, Integer facilityId, String dob);

    public Patient findPatientByIdAndFacility(Long patientId, Integer facilityId, String systemSource);

    public List<V_CallLog> findAgentCallLogs(Integer agentId, Date callDate);

}
