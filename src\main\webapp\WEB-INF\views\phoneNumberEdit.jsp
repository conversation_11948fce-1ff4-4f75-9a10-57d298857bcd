<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd" >
<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<%@ taglib uri="../customTag.tld" prefix="custom" %>

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Smile Brands Call Center - Phone Number Editor</title>
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/bootstrap.min.css" >
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/style/redmond/jquery-ui-1.8.20.custom.css" />
    <!-- Custom styles for this template -->
    <link href="${pageContext.request.contextPath}/bootstrap/css/call-template.css" rel="stylesheet">

</head>
<body>
    <div class="modal fade" id="facilitySearchById" tabindex="-1" role="dialog" aria-labelledby="facilitySearchModalLabel" aria-hidden="true" style="top:100px;">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 class="modal-title" id="facilitySearchModalLabel">Search Facility by ID</h4>
                </div>
                <div class="modal-body" style="padding-bottom:0px;">
                    <div class="input-group">
                        <span class="input-group-addon">Enter Facility ID:</span>
                        <input id="fsInput" type="text" class="form-control" placeholder="5-digit facility ID">
                    </div>
                    <div style="padding:20px 10px; display:inline-flex;">
                        <span class="input-group panel-header" style="padding-right:20px; font-size:14px; font-weight:bold;">Facility Found</span>
                        <div>
                            <span id="foundFacilityNameLbl" style="font-size:14px;" dataField="facilityName"></span><br/>
                            <span id="foundFacilityAddressLbl" style="font-size:14px;" dataField="facilityAddress"></span>
                            <span id="foundFacilityIdLbl" dataField="facilityId" style="display:none;"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button id="setOfficeBtn" type="button" class="btn btn-primary">Set Facility</button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <br/>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h1 class="panel-title gray">Phone Number Management</h1>
            </div>
            <div class="panel-body">
                <form class="form-inline" role="form">
                    <div class="input-group">
                        <div class="input-group-addon">Phone Number
                            <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="Phone Number" data-placement="right">*</span>
                        </div>
                        <input class="form-control" id="searchPhoneNumberInput" placeholder="Enter Phone Number">
                    </div>
                    <button id="searchPhoneNumberBtn" type="submit" class="btn btn-primary">Search</button>
                    <button id="addPhoneNumberBtn" type="submit" class="btn btn-primary">Add New</button>
                </form>
                <hr/>
                <div id="phoneNumberAction" class="panel panel-default" style="display:none;">
                    <div class="panel-heading">
                        <h3 class="panel-title">Panel title</h3>
                    </div>
                    <div class="panel-body">
                        <form id="phoneNumberDetailForm" class="form-horizontal call-info-form" role="form">

                            <div class="form-group">
                                <label for="phoneNumberInput" class="col-sm-3 control-label">Phone Number
                                    <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="Phone Number" data-placement="right">*</span>
                                </label>
                                <div class="col-sm-7">
                                    <input id="phoneNumberInput" type="text" class="form-control input-sm required" placeholder="Enter Phone Number" data-type="number" minLength="4" dataField="phoneNumber" style="display:none;"/>
                                    <div style="padding-top:5px;">
                                        <span id="phoneNumberLbl" style="font-size:14px;" dataField="phoneNumber"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">Phone Type</label>
                                <div class="col-sm-7">
                                    <select id="phoneTypeOptions" dataField="phoneType" class="form-control input-sm">
                                        <option value="ML">Main Line</option>
                                        <option value="TF">Toll Free</option>
                                        <option value="CM">Campaign</option>
                                        <option value="TX">Transfer</option>
                                        <option value="CB">Call Center Back Line</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="defaultNumberInput" class="col-sm-3 control-label">Is default?
                                </label>
                                <div class="col-sm-7">
                                    <input id="defaultNumberInput" type="checkbox" class="input-sm" dataField="isDefault" style="width:50px;" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="campaignNameInput" class="col-sm-3 control-label">Campaign Name
                                    <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="Campaign Name" data-placement="right">*</span>
                                </label>
                                <div class="col-sm-7">
                                    <input id="campaignNameInput" type="text" class="form-control input-sm required" placeholder="Enter Campaign Name" dataField="campaignName" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">Target Facility
                                    <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="Associated Facility" data-placement="right">*</span>
                                </label>
                                <div class="col-sm-7">
                                    <span id="facilityIdLbl" dataField="facilityId" style="display:none;"></span>
                                    <span id="facilityNameLbl" style="font-size:14px;" dataField="facilityName"></span>
                                    <span id="editFacilityBtn" class="glyphicon glyphicon-edit fr btn-link" data-toggle="tooltip" title="Edit Facility" data-placement="right"></span><br/>
                                    <span id="facilityAddressLbl" style="font-size:14px;" dataField="facilityAddress"></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="brandNameInput" class="col-sm-3 control-label">Brand Name
                                </label>
                                <div class="col-sm-7">
                                    <select id="brandNameInput" class="form-control input-sm required" dataField="brandName">
                                        <option>Aspire Dental</option>
                                        <option>Bright Now! Dental</option>
                                        <option>Castle Dental</option>
                                        <option>Monarch Dental</option>
                                        <option>Newport Dental</option>
                                        <option>Smile Brands</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="callCenterNameInput" class="col-sm-3 control-label">Call Center Name
                                    <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="Call Center Name" data-placement="right">*</span>
                                </label>
                                <div class="col-sm-7">
                                    <select id="callCenterNameInput" class="form-control input-sm required" dataField="callCenterName">
                                        <option>Irvine</option>
                                        <option>Plano</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="callCenterQueueInput" class="col-sm-3 control-label">Call Center Queue
                                    <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="Call Center Queue" data-placement="right">*</span>
                                </label>
                                <div class="col-sm-7">
                                    <input id="callCenterQueueInput" type="text" class="form-control input-sm required" placeholder="Enter Call Center Queue" dataField="callCenterQueueName" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="callCenterSupportInput" class="col-sm-3 control-label">Is Call Center Supported?
                                </label>
                                <div class="col-sm-7">
                                    <input id="callCenterSupportInput" type="checkbox" class="input-sm" dataField="callCenterSupported" style="width:50px;" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="csrSupportInput" class="col-sm-3 control-label">Is CSR Supported?
                                </label>
                                <div class="col-sm-7">
                                    <input id="csrSupportInput" type="checkbox" class="input-sm" dataField="csrSupported" style="width:50px;" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="psrSupportInput" class="col-sm-3 control-label">Is PSR Supported?
                                </label>
                                <div class="col-sm-7">
                                    <input id="psrSupportInput" type="checkbox" class="input-sm" dataField="psrSupported" style="width:50px;" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="searchOnAniInput" class="col-sm-3 control-label">Search on ANI?
                                </label>
                                <div class="col-sm-7">
                                    <input id="searchOnAniInput" type="checkbox" class="input-sm" dataField="searchOnAni" style="width:50px;" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="transferNumberInput" class="col-sm-3 control-label">Transfer Number
                                    <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="Transfer Number" data-placement="right">*</span>
                                </label>
                                <div class="col-sm-7">
                                    <input id="transferNumberInput" type="text" class="form-control input-sm" placeholder="Enter Transfer Number" dataField="transferNumber" data-type="number" length=10 />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="voiceMailRouteInput" class="col-sm-3 control-label">Voice Mail Route
                                    <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="Voice Mail Route" data-placement="right">*</span>
                                </label>
                                <div class="col-sm-7">
                                    <select id="voiceMailRouteInput" class="form-control input-sm" dataField="voiceMailRoute">
                                        <option>aspire</option>
                                        <option>brightnow</option>
                                        <option>castle</option>
                                        <option>monarch</option>
                                        <option>newport</option>
                                        <option>smilebrands</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="languageOptions" class="col-sm-3 control-label">Language
                                    <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="language" data-placement="right">*</span>
                                </label>
                                <div class="col-sm-7">
                                    <select id="languageOptions" class="form-control input-sm" dataField="language">
                                        <option value="en">English</option>
                                        <option value="sp">Spanish</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="ivrPromptInput" class="col-sm-3 control-label">IVR Prompt File
                                    <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="IVR Prompt File" data-placement="right">*</span>
                                </label>
                                <div class="col-sm-7">
                                    <input id="ivrPromptInput" type="text" class="form-control input-sm" placeholder="IVR Prompt File" dataField="ivrPromptFile" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="ivrNameInput" class="col-sm-3 control-label">IVR Name
                                    <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="IVR Name" data-placement="right">*</span>
                                </label>
                                <div class="col-sm-7">
                                    <input id="ivrNameInput" type="text" class="form-control input-sm" placeholder="IVR Name" dataField="ivrName" />
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="panel-footer">
                        <button id="updatePhoneNumberBtn" type="button" class="btn btn-primary">Update Phone Number</button>
                        <button id="inactivatePhoneNumberBtn" type="button" class="btn btn-danger" style="display:none;">Inactivate Phone Number</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript
    ================================================== -->
    <!-- Placed at the end of the document so the pages load faster -->
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-1.7.2.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-ui-1.8.20.custom.min.js"></script>
    <script src="${pageContext.request.contextPath}/bootstrap/js/bootstrap.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/configurationActions.js?_id=${buildNumber}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/sbi-script.js?_id=${buildNumber}"></script>

</body>
</html>