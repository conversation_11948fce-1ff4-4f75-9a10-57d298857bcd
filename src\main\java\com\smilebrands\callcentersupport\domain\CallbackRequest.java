package com.smilebrands.callcentersupport.domain;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Created by phongpham on 10/20/15.
 */
@Document
public class CallbackRequest {

    @Id
    private String uuid;
    private String sessionId;
    private Long phoneNumber;
    private String preferredTime;
    private Integer preferredTimeVal = 0;

    private String agentType;
    private String callCenter;
    private String language;
    private String queueName;

    private String requestDateTime;

    private Integer handledByAgent;
    private String handledByUUID;

    private String processedDateTime;
    private String pendingProcessedDateTime;
    private String pendingProcessedBy;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Long getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(Long phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getPreferredTime() {
        return preferredTime;
    }

    public void setPreferredTime(String preferredTime) {
        this.preferredTime = preferredTime;
    }

    public Integer getPreferredTimeVal() {
        return preferredTimeVal;
    }

    public void setPreferredTimeVal(Integer preferredTimeVal) {
        this.preferredTimeVal = preferredTimeVal;
    }

    public String getAgentType() {
        return agentType;
    }

    public void setAgentType(String agentType) {
        this.agentType = agentType;
    }

    public String getCallCenter() {
        return callCenter;
    }

    public void setCallCenter(String callCenter) {
        this.callCenter = callCenter;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getQueueName() {
        return queueName;
    }

    public void setQueueName(String queueName) {
        this.queueName = queueName;
    }

    public String getRequestDateTime() {
        return requestDateTime;
    }

    public void setRequestDateTime(String requestDateTime) {
        this.requestDateTime = requestDateTime;
    }

    public Integer getHandledByAgent() {
        return handledByAgent;
    }

    public void setHandledByAgent(Integer handledByAgent) {
        this.handledByAgent = handledByAgent;
    }

    public String getHandledByUUID() {
        return handledByUUID;
    }

    public void setHandledByUUID(String handledByUUID) {
        this.handledByUUID = handledByUUID;
    }

    public String getProcessedDateTime() {
        return processedDateTime;
    }

    public void setProcessedDateTime(String processedDateTime) {
        this.processedDateTime = processedDateTime;
    }

    public String getPendingProcessedDateTime() {
        return pendingProcessedDateTime;
    }

    public void setPendingProcessedDateTime(String pendingProcessedDateTime) {
        this.pendingProcessedDateTime = pendingProcessedDateTime;
    }

    public String getPendingProcessedBy() {
        return pendingProcessedBy;
    }

    public void setPendingProcessedBy(String pendingProcessedBy) {
        this.pendingProcessedBy = pendingProcessedBy;
    }
}
