package com.smilebrands.callcentersupport.domain;

import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

/**
 * User: <PERSON><PERSON>
 * Date: 6/26/14
 */
@Document
public class InboundCallEvent implements Serializable {

    private String eventId;
    private Date eventDateTime;

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public Date getEventDateTime() {
        return eventDateTime;
    }

    public void setEventDateTime(Date eventDateTime) {
        this.eventDateTime = eventDateTime;
    }
}
