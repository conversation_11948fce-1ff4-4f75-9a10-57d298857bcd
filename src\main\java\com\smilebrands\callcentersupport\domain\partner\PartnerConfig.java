package com.smilebrands.callcentersupport.domain.partner;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by phongpham on 3/4/15.
 */

@Document
public class PartnerConfig {

    @Id
    private String partnerId;
    private String partnerName;
    private Integer agentStart;
    private Integer agentEnd;
    private Date createdOn;
    private Integer createdBy;
    private Date updatedOn;
    private Integer updatedBy;
    private boolean active = true;
    private List<PartnerAgent> agents = new ArrayList<PartnerAgent>();

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public Integer getAgentStart() {
        return agentStart;
    }

    public void setAgentStart(Integer agentStart) {
        this.agentStart = agentStart;
    }

    public Integer getAgentEnd() {
        return agentEnd;
    }

    public void setAgentEnd(Integer agentEnd) {
        this.agentEnd = agentEnd;
    }

    public Date getCreatedOn() {
        return createdOn;
    }

    public void setCreatedOn(Date createdOn) {
        this.createdOn = createdOn;
    }

    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedOn() {
        return updatedOn;
    }

    public void setUpdatedOn(Date updatedOn) {
        this.updatedOn = updatedOn;
    }

    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    public List<PartnerAgent> getAgents() {
        return agents;
    }

    public void setAgents(List<PartnerAgent> agents) {
        this.agents = agents;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }
}
