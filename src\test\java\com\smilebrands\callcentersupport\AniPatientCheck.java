package com.smilebrands.callcentersupport;

import com.smilebrands.callcentersupport.domain.InboundCall;
import com.smilebrands.callcentersupport.domain.PhoneNumber;
import com.smilebrands.callcentersupport.domain.ScreenPopTime;
import com.smilebrands.callcentersupport.repo.MongoRepository;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * User: <PERSON><PERSON>
 * Date: 8/13/14
 */
public class AniPatientCheck extends BaseTest {

    @Autowired
    private MongoOperations mongoOperations;
    @Autowired
    private MongoRepository mongoRepository;
    @Autowired
    protected JdbcTemplate jdbcTemplate;

    static String sql = "SELECT * FROM PATIENTS PAT WHERE FACILITY_ID = ?"
                      + "AND (PAT.PHONE_NUMBER1 = ? OR PAT.PHONE_NUMBER2 = ?)";

    static String sql2 = "SELECT /*+ index(APPT) index(PAT,PATIENT_DOB_FIDX) */\n" +
            "                    'X' AS N_X\n" +
            "                FROM APPOINTMENTS APPT\n" +
            "                    JOIN PATIENTS PAT ON PAT.CLINIC_ID=APPT.CLINIC_ID AND PAT.UNIQUE_ID=APPT.UNIQUE_ID\n" +
            "                    JOIN FACILITY FAC ON FAC.QSI_CLINIC_ID=APPT.CLINIC_ID\n" +
            "                WHERE\n" +
            "                    (APPT_PHONE = ? OR PAT.PHONE_NUMBER1 = ? OR PAT.PHONE_NUMBER2 = ?)\n" +
            "                    AND (APPT.CLINIC_ID < 1100 OR APPT.LOCATION_ID=PAT.LOCATION_ID)\n" +
            "                    AND (APPT.CLINIC_ID < 1100 OR APPT.LOCATION_ID=FAC.QSI_LOCATION_ID)\n" +
            "                    AND fac.FACILITY_ID = ?                    \n" +
            "                    AND TRUNC(APPT_DATE) BETWEEN TRUNC(SYSDATE-365) AND TRUNC(SYSDATE)\n" +
            "                    AND ROWNUM = 1 " +
            "                ORDER BY APPT_DATE";

    @Test
    public void check() {
        List<InboundCall> calls = mongoOperations.find(new Query(Criteria.where(("createDate")).is("08-13-2014")), InboundCall.class);
        logger.debug("Inbound Calls: " + calls.size());

        int found = 0;
        int found1 = 0;
        int found2 = 0;
        int process = 0;
        int check = 0;
        int o2oCount = 0;
        int press1 = 0;
        int press2 = 0;

        long qAvg = 0L;
        for (InboundCall call : calls) {
            if (call.getAni() != null && call.getAni().toString().length() == 10) {
                PhoneNumber officeNumber = mongoRepository.findPhoneNumber(call.getAni());
                check++;

                if (officeNumber == null) {
                    if (call.getPhoneType() != null &&
                            call.getPhoneType().equalsIgnoreCase("ML") &&
                            call.getPhoneNumber().isCallCenterSupported()) {

                        Long ani = call.getAni();
                        Integer facility = call.getTargetFacilityId();
                        String phone = p(ani.toString());

                        if (call.getMainMenuNumberPress() > 0 && call.getMainMenuNumberPress() < 6) {
                            process++;
                            logger.debug("Searching for Patient by ANI [" + ani + "] Phone [" + phone + "] in Facility [" + facility
                                    + "] & Menu [" + call.getMainMenuNumberPress() + "].");


                            if (call.getMainMenuNumberPress() == 1) { press1++; }
                            if (call.getMainMenuNumberPress() == 2) { press2++; }

                            try {
                                Long start = new Date().getTime();

                                jdbcTemplate.setQueryTimeout(3);
                                List<Map<String, Object>> pats = jdbcTemplate.queryForList(sql2, new Object[]{phone, phone, phone, facility});

                                if (pats.size() > 0) {
                                    found++;
                                    if (call.getMainMenuNumberPress() == 1) {
                                        found1++;
                                    }
                                    if (call.getMainMenuNumberPress() == 2) {
                                        found2++;
                                    }
                                }

                                Long end = new Date().getTime();
                                Long qTime = (end - start);
                                qAvg += qTime;

                                logger.debug("Query took " + qTime + " millis to process.");
                            } catch (Exception x) {
                                logger.warn("Query time out! " + x.getMessage());
                            }
                        }
                    }
                } else {
                    o2oCount++;
                }

                if (found > 10) {
                    logger.debug("Out of " + check + " calls checked (excluding " + o2oCount + " were office to office) " + process
                            + " calls searched (" + press1 + " press 1 calls & " + press2 + " press 2 calls), " + found
                            + " (" + found1 + " press 1 found & " + found2 + " press 2 found) patients were found using the ANI.");

                    logger.debug("Avg process time " + (qAvg / process));
                    System.exit(1);
                } else {
                    logger.debug(found + " patients found.");
                }
            }
        }

        logger.debug("Out of " + calls.size() + " calls, " + found + " patients were found using the ANI.");
    }

    static String p(String phone) {
        return new StringBuilder().append("(").append(phone.substring(0,3)).append(")").append(phone.substring(3,6)).append("-").append(phone.substring(6)).toString();
    }

    @Test
    public void testGetScreenPopTime(){
        ScreenPopTime screenPopTime = mongoRepository.getScreenPopTime("test-8c825e67-9550-446c-af52-70221003d2b9", 102029, "psr");
        logger.debug("screenPopTime: {}", screenPopTime);
    }
}
