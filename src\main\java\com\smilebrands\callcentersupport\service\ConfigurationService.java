package com.smilebrands.callcentersupport.service;

import com.smilebrands.callcentersupport.domain.*;

import java.util.List;
import java.util.Map;

/**
 * User: <PERSON><PERSON>
 * Date: 6/25/14
 */
public interface ConfigurationService {

    public void rebuildMongoEnvironment();

    public Facility updateFacilityFacilityMessage(Integer facilityID, String message);

    public List<PhoneNumber> getAllPhoneNumbers();

    public PhoneNumber persistPhoneNumber(PhoneNumber phoneNumber);

    public PhoneNumber getPhoneNumber(Long number);

    public Campaign persistCampaign(Campaign campaign);

    public Campaign getCampaign(Long number);

    public CallResolutionCode getCallResolutionCode(String code);

    public CallResolutionCode persistCallResolutionCode(CallResolutionCode code);

    public List<CallResolutionCode> getCallReasonResolutionsCodes();

    public List<CallResolutionCode> getCallReasonCodes(String screenType);

    public List<CallResolutionCode> getCallResolutionCodes(String screenType);

    public List<Campaign> findAllCampaign();

    public List<Facility> findAllFacility();

    public List<CallCenterEmployee> getSupervisedAgents(Integer supervisorId);

    public List<Map<String, Object>> getTeamHierarchy();

    public List<CallCenterEmployee> getCallCenterResource(boolean supervisor);

    public List<PhoneNumberRoutingRules> findAllRoutingRules();

    public PhoneNumberRoutingRules getRoutingRuleByNumber(Long phoneNumber);

    public PhoneNumberRoutingRules persistRoutingRule(PhoneNumberRoutingRules routingRules);

    public boolean updateCallCenterEmployee(Integer supervisorId, String agentsInTeam);

    public void processUpdateFacilityRequest(Integer facilityId);

    public List<CommunicationTemplate> getCommunicationTemplates();

}
