package com.smilebrands.callcentersupport.domain.constants;

/**
 * Created by phongpham on 7/28/14.
 */
public enum  State {
    AL{
        @Override
        public String getStateName() {
            return "Alabama";
        }
        @Override
        public int getOffset(){
            return -6;
        }
    },
    AK{
        @Override
        public String getStateName() {
            return "Alaska";
        }
        @Override
        public int getOffset(){
            return -9;
        }
    },
    AZ{
        @Override
        public String getStateName() {
            return "Arizona";
        }
        @Override
        public int getOffset(){
            return -7;
        }
    },
    AR{
        @Override
        public String getStateName() {
            return "Arkansas";
        }
        @Override
        public int getOffset(){
            return -6;
        }
    },
    CA{
        @Override
        public String getStateName() {
            return "California";
        }
        @Override
        public int getOffset(){
            return -8;
        }
    },
    CO{
        @Override
        public String getStateName() {
            return "Colorado";
        }
        @Override
        public int getOffset(){
            return -7;
        }
    },
    CT{
        @Override
        public String getStateName() {
            return "Connecticut";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    DE{
        @Override
        public String getStateName() {
            return "Delaware";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    DC{
        @Override
        public String getStateName() {
            return "District of Columbia";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    FL{
        @Override
        public String getStateName() {
            return "Florida";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    GA{
        @Override
        public String getStateName() {
            return "Georgia";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    HI{
        @Override
        public String getStateName() {
            return "Hawaii";
        }
        @Override
        public int getOffset(){
            return -10;
        }
    },
    ID{
        @Override
        public String getStateName() {
            return "Idaho";
        }
        @Override
        public int getOffset(){
            return -7;
        }
    },
    IL{
        @Override
        public String getStateName() {
            return "Illinois";
        }
        @Override
        public int getOffset(){
            return -6;
        }
    },
    IN{
        @Override
        public String getStateName() {
            return "Indiana";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    IA{
        @Override
        public String getStateName() {
            return "Iowa";
        }
        @Override
        public int getOffset(){
            return -6;
        }
    },
    KS{
        @Override
        public String getStateName() {
            return "Kansas";
        }
        @Override
        public int getOffset(){
            return -6;
        }
    },
    KY{
        @Override
        public String getStateName() {
            return "Kentucky";
        }
        @Override
        public int getOffset(){
            return -5;  //partial
        }
    },
    LA{
        @Override
        public String getStateName() {
            return "Louisiana";
        }
        @Override
        public int getOffset(){
            return -6;
        }
    },
    ME{
        @Override
        public String getStateName() {
            return "Maine";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    MD{
        @Override
        public String getStateName() {
            return "Maryland";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    MA{
        @Override
        public String getStateName() {
            return "Massachusetts";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    MI{
        @Override
        public String getStateName() {
            return "Michigan";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    MN{
        @Override
        public String getStateName() {
            return "Minnesota";
        }
        @Override
        public int getOffset(){
            return -6;
        }
    },
    MS{
        @Override
        public String getStateName() {
            return "Mississippi";
        }
        @Override
        public int getOffset(){
            return -6;
        }
    },
    MO{
        @Override
        public String getStateName() {
            return "Missouri";
        }
        @Override
        public int getOffset(){
            return -6;
        }
    },
    MT{
        @Override
        public String getStateName() {
            return "Montana";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    NE{
        @Override
        public String getStateName() {
            return "Nebraska";
        }
        @Override
        public int getOffset(){
            return -6;
        }
    },
    NV{
        @Override
        public String getStateName() {
            return "Nevada";
        }
        @Override
        public int getOffset(){
            return -8;
        }
    },
    NH{
        @Override
        public String getStateName() {
            return "New Hampshire";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    NJ{
        @Override
        public String getStateName() {
            return "New Jersey";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    NM{
        @Override
        public String getStateName() {
            return "New Mexico";
        }
        @Override
        public int getOffset(){
            return -7;
        }
    },
    NY{
        @Override
        public String getStateName() {
            return "New York";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    NC{
        @Override
        public String getStateName() {
            return "North Carolina";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    ND{
        @Override
        public String getStateName() {
            return "North Dakota";
        }
        @Override
        public int getOffset(){
            return -6;
        }
    },
    OH{
        @Override
        public String getStateName() {
            return "Ohio";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    OK{
        @Override
        public String getStateName() {
            return "Oklahoma";
        }
        @Override
        public int getOffset(){
            return -6;
        }
    },
    OR{
        @Override
        public String getStateName() {
            return "Oregon";
        }
        @Override
        public int getOffset(){
            return -8;
        }
    },
    PA{
        @Override
        public String getStateName() {
            return "Pennsylvania";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    RI{
        @Override
        public String getStateName() {
            return "Rhode Island";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    SC{
        @Override
        public String getStateName() {
            return "South Carolina";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    SD{
        @Override
        public String getStateName() {
            return "South Dakota";
        }
        @Override
        public int getOffset(){
            return -6;
        }
    },
    TN{
        @Override
        public String getStateName() {
            return "Tennessee";
        }
        @Override
        public int getOffset(){
            return -6;
        }
    },
    TX{
        @Override
        public String getStateName() {
            return "Texas";
        }
        @Override
        public int getOffset(){
            return -6;
        }
    },
    UT{
        @Override
        public String getStateName() {
            return "Utah";
        }
        @Override
        public int getOffset(){
            return -7;
        }
    },
    VT{
        @Override
        public String getStateName() {
            return "Vermont";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    VA{
        @Override
        public String getStateName() {
            return "Virginia";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    WA{
        @Override
        public String getStateName() {
            return "Washington";
        }
        @Override
        public int getOffset(){
            return -8;
        }
    },
    WV{
        @Override
        public String getStateName() {
            return "West Virginia";
        }
        @Override
        public int getOffset(){
            return -5;
        }
    },
    WI{
        @Override
        public String getStateName() {
            return "Wisconsin";
        }
        @Override
        public int getOffset(){
            return -6;
        }
    },
    WY{
        @Override
        public String getStateName() {
            return "Wyoming";
        }
        @Override
        public int getOffset(){
            return -7;
        }
    };
    private String stateName;
    private int offset;

    public String getStateName() {
        return stateName;
    }

    public void setStateName(String stateName) {
        this.stateName = stateName;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }
}
