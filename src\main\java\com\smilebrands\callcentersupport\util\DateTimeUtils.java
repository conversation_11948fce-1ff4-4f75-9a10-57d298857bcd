package com.smilebrands.callcentersupport.util;

import com.smilebrands.callcentersupport.domain.CallCenter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.TimeZone;

/**
 * Created by phongpham on 12/14/14.
 */
public class DateTimeUtils {

    private final static Logger logger = LoggerFactory.getLogger(DateTimeUtils.class);

    public static Date getLoadedTimeByLocationOffset(Integer locationOffset){
        Calendar cal = Calendar.getInstance();
        if(locationOffset != null){
            TimeZone timeZone = cal.getTimeZone();
            Integer currentOffset = timeZone.getRawOffset() / (1000 * 60 * 60);

            Integer offset = locationOffset - currentOffset;
            cal.add(Calendar.HOUR_OF_DAY, offset);
        }
        return cal.getTime();
    }

    public static Date getTimeByLocationOffset(Date date, Integer locationOffset){
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        if(locationOffset != null){
            TimeZone timeZone = cal.getTimeZone();
            Integer currentOffset = timeZone.getRawOffset() / (1000 * 60 * 60);

            Integer offset = locationOffset - currentOffset;
            cal.add(Calendar.HOUR_OF_DAY, offset);
        }
        return cal.getTime();
    }

    public static Date getGMTTimeFromLocationOffset(Date date, Integer locationOffset){
        Calendar cal = Calendar.getInstance();
        if(locationOffset != null){
            cal.setTime(date);

            int dstOffset = cal.get(Calendar.DST_OFFSET);
//            logger.debug("DST_OFFSET for " + cal.getTime() + ": {}", cal.get(Calendar.DST_OFFSET));
            if(dstOffset != 0){
                locationOffset += 1;
            }
            Integer offset = 0 - locationOffset;
            cal.add(Calendar.HOUR_OF_DAY, offset);
        }
        return cal.getTime();
    }

    public static Date getBusinessDateByIncrement(int increment){
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, increment);
//        if(cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY){
//            cal.add(Calendar.DAY_OF_YEAR, 1);
//        }
        return cal.getTime();
    }

    public static Date adjustDateByOffset(Date current, int offset, Integer[] excludeDays){
        Calendar cal = Calendar.getInstance();
        if(current != null){
            cal.setTime(current);
        }
        int multiplier = offset < 0 ? -1 : 1;
        offset = Math.abs(offset);
        Set<Integer> excludeDaySet = new HashSet<Integer>();
        for(int i=0; excludeDays != null && i<excludeDays.length; i++){
            excludeDaySet.add(excludeDays[i]);
        }

        if(excludeDaySet.size() > 0){
            if(excludeDaySet.size() == 7){
                return null;
            }
            for(int i=0; i<offset; i++){
                cal.add(Calendar.DAY_OF_MONTH, multiplier);
                if(excludeDaySet.contains(cal.get(Calendar.DAY_OF_WEEK))){
                    i--;
                }
            }
            if(cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY){
                cal.add(Calendar.DAY_OF_MONTH, multiplier);
            }
        }else{
            cal.add(Calendar.DAY_OF_MONTH, multiplier * offset);
        }
        return cal.getTime();
    }
}
