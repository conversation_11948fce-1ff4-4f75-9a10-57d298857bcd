package com.smilebrands.callcentersupport.domain.helper;

import org.codehaus.jackson.JsonParser;
import org.codehaus.jackson.JsonProcessingException;
import org.codehaus.jackson.map.DeserializationContext;
import org.codehaus.jackson.map.JsonDeserializer;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.ISODateTimeFormat;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Date;

/**
 * Used to deserialize Java.util.Date
 *
 * <AUTHOR>
 * Date: 6/27/11
 */
@Component
public class DateIsoDeSerializer extends JsonDeserializer<Date>{

    private static final DateTimeFormatter dateFormatter = ISODateTimeFormat.dateHourMinuteSecond();

    @Override
    public Date deserialize(JsonParser parser, DeserializationContext context) throws IOException, JsonProcessingException {
	    String dateInStr = parser.getText();

//        System.out.println("dateInStr = " + dateInStr);
//        System.out.println("dateFormatter.parseDateTime(dateInStr).toDate() = " + dateFormatter.parseDateTime(dateInStr).toDate().toString());

	    return dateFormatter.parseDateTime(dateInStr).toDate();
    }
}