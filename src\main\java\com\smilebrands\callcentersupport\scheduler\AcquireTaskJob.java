package com.smilebrands.callcentersupport.scheduler;

import com.smilebrands.callcentersupport.domain.schedule.Task;
import com.smilebrands.callcentersupport.repo.MongoRepository;
import com.smilebrands.callcentersupport.service.ScheduleService;
import com.smilebrands.callcentersupport.service.ScheduleServiceImpl;
import com.smilebrands.callcentersupport.util.DateTimeUtils;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.net.Inet4Address;
import java.util.Date;
import java.util.List;

/**
 * Created by phongpham on 11/6/14.
 */
public class AcquireTaskJob extends QuartzJobBean{

    final Logger logger = LoggerFactory.getLogger(this.getClass());


    private ScheduleService scheduleService;


    public ScheduleService getScheduleService() {
        return scheduleService;
    }

    public void setScheduleService(ScheduleService scheduleService) {
        this.scheduleService = scheduleService;
    }

    private List<Task> taskList;

    private boolean running;

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
//        logger.debug("running acquire task job @" + new Date() + " with running[" + running + "].");
        if(scheduleService == null && context.getJobDetail().getJobDataMap().get("scheduleService") != null){
            scheduleService = (ScheduleServiceImpl) context.getJobDetail().getJobDataMap().get("scheduleService");
            logger.debug("set scheduleService");
        }
        if(taskList == null){
            taskList = ScheduleServiceImpl.taskList;
        }
        if(!running){
            running = true;
        }else{
            return;
        }
        for(Task t : taskList) {
            Task task = null;
            try {
                task = scheduleService.claimOpenTask(t.getTaskName());
                if (task != null) {
                    if(task.isParallel()) {
                        scheduleService.processTaskAsync(task);
                    }else{
                        scheduleService.processTask(task);
                    }
                } else {
                    logger.debug("task[" + t.getTaskName() + "] is not available...");
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            } finally {
                if (task != null) {
                    scheduleService.releaseTask(task);
                }
            }
        }
        running = false;
    }
}
