<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd" >
<%@ page import="java.util.Date" %>
<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Smile Brands Call Center</title>

    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/bootstrap.min.css" >
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/style/redmond/jquery-ui-1.8.20.custom.css" />
    <!-- Custom styles for this template -->
    <link href="${pageContext.request.contextPath}/bootstrap/css/call-template.css" rel="stylesheet">


    <!--script type="text/javascript" src="../script/jquery-1.7.2.min.js"></script-->
    <script type="text/javascript">
        function doOnload()
        {
            var canAccessReport = SBI.getCookies('CALL_CENTER_SUPPORT', 'CAN_ACCESS_CALL_CENTER_REPORT'),
                agentId         = SBI.getCookies('APPTRACKER', 'EMPLOYEE_NUMBER');
            if(canAccessReport == 'false' || !canAccessReport){
                $('#callCenterReportPanel').remove();
                if(agentId){
                    $('#agentActivityLink').attr('href', $('#agentActivityLink').attr('href') + '?agentId=' + agentId);
                }
            }else{
                var canSyncCallLog  = SBI.getCookies('CALL_CENTER_SUPPORT', 'CAN_SYNC_CALL_LOG'),
                    canEditCallLog  = SBI.getCookies('CALL_CENTER_SUPPORT', 'CAN_EDIT_CALL_LOG');
                canSyncCallLog = canSyncCallLog && canSyncCallLog == 'true';
                if(!canSyncCallLog
                    && (!canEditCallLog || canEditCallLog == 'false')){
                    //$('#callLogManagement').remove();
                }else if(false && !canSyncCallLog){
                    var linkRef = $('#callLogManagement').find('a');
                    linkRef.attr('href', linkRef.attr('href') + '?agentId=' + agentId);
                }
            }
        }
    </script>
</head>
<body onload="doOnload()">
    <div class="modal fade" id="agentReportPop" tabindex="-1" role="dialog" aria-labelledby="agentReportModalLabel" aria-hidden="true" style="top:100px;">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 class="modal-title" id="agentReportModalLabel"></h4>
                </div>
                <div class="modal-body" style="padding-bottom:0px;">
                    <form class="form-horizontal report-parameters-form" role="form">
                        <div class="form-group">
                            <label for="agentId" class="col-sm-3 control-label">Agent ID
                                <span style="display:none;" class="text-danger validation-cmp" data-toggle="tooltip" field-name="Agent ID" data-placement="right">*</span>
                            </label>
                            <div class="col-sm-9">
                                <c:choose>
                                    <c:when test="${hasAgentList == false}">
                                        <input id="agentId" type="text" class="form-control input-sm required" placeholder="Agent ID" data-type="number"/>
                                    </c:when>
                                    <c:otherwise>
                                        <div class="dropdown" selMode="MULTI">
                                            <div class="input-group dropdown-toggle" id="agent-dropdown-menu" data-toggle="dropdown">
                                                <input id="agentId" type="text" class="form-control">
                                                <span class="input-group-addon" style="font-weight: bold;">v</span>
                                            </div>
                                            <ul class="dropdown-menu" role="menu" aria-labelledby="agent-dropdown-menu" style="max-height: 300px; width: 100%; overflow-y: scroll;">
                                                <li role="presentation"><input role="menuitem" tabindex="-1" type="checkbox" class="agent-option" style="padding-left: 10px;" value="0">&nbsp;All</input></li>
                                                <c:forEach var="agent" items="${agentList}">
                                                    <li role="presentation"><input role="menuitem" tabindex="-1" type="checkbox" class="agent-option" style="padding-left: 10px;" value="${agent.employeeNumber}">&nbsp;${agent.employeeNumber} - ${agent.employeeName}</input></li>
                                                </c:forEach>
                                            </ul>
                                        </div>
                                    </c:otherwise>
                                </c:choose>
                            </div>
                        </div>
                        <div class="form-group" style="display: none;">
                            <label class="col-sm-3 control-label dateType">Date Type</label>
                            <div class="col-sm-9">
                                <label class="col-sm-3" style="text-align: left; bottom: -5px; padding-left: 0px; font-weight: normal;">Call<input type="radio" value="call" name="dateType" style="width: 20px; margin-left: 10px;" checked></input></label>
                                <label class="col-sm-4" style="text-align: left; bottom: -5px; padding-left: 0px; font-weight: normal;">Appointment<input type="radio" value="appt" name="dateType" style="width: 20px; margin-left: 10px;"></input></label>
                                <label class="col-sm-4" style="text-align: left; bottom: -5px; padding-left: 0px; font-weight: normal;">Latest<input type="radio" value="latest" name="dateType" style="width: 20px; margin-left: 10px;"></input></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="fromInput" class="col-sm-3 control-label">From</label>
                            <div class="col-sm-9">
                                <input class="fromInput" name="datepicker" maxDate="0"></input>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="toInput" class="col-sm-3 control-label">To</label>
                            <div class="col-sm-9">
                                <input class="toInput" name="datepicker" maxDate="0"></input>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="callMonth" class="col-sm-3 control-label">Call Month</label>
                            <div class="col-sm-3">
                                <select class="callMonth">
                                    <c:forEach var="i" begin="1" end="12">
                                        <option
                                            <c:if test="${i==currentMonth}">
                                                selected
                                            </c:if>
                                        >
                                            ${i}
                                        </option>
                                    </c:forEach>
                                </select>
                                <select class="callYear">
                                    <c:forEach var="i" begin="2014" end="${currentYear}">
                                        <option
                                            <c:if test="${i==currentYear}">
                                                selected
                                            </c:if>
                                        >
                                            ${i}
                                        </option>
                                    </c:forEach>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="callMonthCompared" class="col-sm-3 control-label">Compared with</label>
                            <div class="col-sm-3">
                                <select class="callMonthCompared">
                                    <c:forEach var="i" begin="1" end="12">
                                        <option
                                            <c:if test="${i==currentMonth}">
                                                selected
                                            </c:if>
                                        >
                                            ${i}
                                        </option>
                                    </c:forEach>
                                </select>
                                <select class="callYearCompared">
                                    <c:forEach var="i" begin="2014" end="${currentYear}">
                                        <option
                                            <c:if test="${i==currentYear}">
                                                selected
                                            </c:if>
                                        >
                                            ${i}
                                        </option>
                                    </c:forEach>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button id="getReportBtn" type="button" class="btn btn-primary">Get Report</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="webRequestPop" tabindex="-1" role="dialog" aria-labelledby="agentReportModalLabel" aria-hidden="true" style="top:100px;">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 class="modal-title" id="agentReportModalLabel"></h4>
                </div>
                <div class="modal-body" style="padding-bottom:0px;">
                    <form class="form-horizontal report-parameters-form" role="form">
                        <div class="form-group">
                            <label for="state" class="col-sm-3 control-label">State</label>
                            <div class="col-sm-9">
                                <input id="state" type="text" class="form-control input-sm required" placeholder="State" data-type="string"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="city" class="col-sm-3 control-label">City</label>
                            <div class="col-sm-9">
                                <input id="city" type="text" class="form-control input-sm required" placeholder="City" data-type="string"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="office" class="col-sm-3 control-label">Office</label>
                            <div class="col-sm-9">
                                <input id="office" type="text" class="form-control input-sm required" placeholder="Office" data-type="string"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="isNewPatient" class="col-sm-3 control-label">New Patient?</label>
                            <div class="col-sm-9">
                                <select id="isNewPatient" filterOptionFor="isNewPatient" style="width:100%;">
                                    <option value="true">New Patient</option>
                                    <option value="false">Current Patient</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="reasonForAppointment" class="col-sm-3 control-label">Reason For Appointment</label>
                            <div class="col-sm-9">
                                <select id="reasonForAppointment" filterOptionFor="reasonForAppointment" style="width:100%;">
                                    <option value="newAppointment">New Appointment</option>
                                    <option value="dentalCheckUp">Dental Check Up</option>
                                    <option value="emergencyAppointment">Emergency Appointment</option>
                                    <option value="orthodonticConsultation">Orthodontic Consultation</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="firstName" class="col-sm-3 control-label">First Name</label>
                            <div class="col-sm-9">
                                <input id="firstName" type="text" class="form-control input-sm required" placeholder="First Name" data-type="string"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="lastName" class="col-sm-3 control-label">Last Name</label>
                            <div class="col-sm-9">
                                <input id="lastName" type="text" class="form-control input-sm required" placeholder="Last Name" data-type="string"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="zipCode" class="col-sm-3 control-label">Zip Code</label>
                            <div class="col-sm-9">
                                <input id="zipCode" type="text" class="form-control input-sm required" placeholder="Zip Code" data-type="number"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="phone" class="col-sm-3 control-label">Phone</label>
                            <div class="col-sm-9">
                                <input id="phone" type="text" class="form-control input-sm required" placeholder="Phone" data-type="number"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="phoneType" class="col-sm-3 control-label">Phone Type</label>
                            <div class="col-sm-9">
                                <select id="phoneType" filterOptionFor="phoneType" style="width:100%;">
                                    <option value="Cell">Cell</option>
                                    <option value="Land">Land</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="emailAddress" class="col-sm-3 control-label">Email Address</label>
                            <div class="col-sm-9">
                                <input id="emailAddress" type="text" class="form-control input-sm required" placeholder="Email Address" data-type="string"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="appointmentDay" class="col-sm-3 control-label">Day</label>
                            <div class="col-sm-9">
                                <input id="appointmentDay" class="appointmentDay" name="datepicker" maxDate="0"></input>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="timeOfDay" class="col-sm-3 control-label">Preferred Time Of Day</label>
                            <div class="col-sm-9">
                                <select id="timeOfDay" filterOptionFor="timeOfDay" style="width:100%;">
                                    <option value="Morning">Morning</option>
                                    <option value="Afternoon">Afternoon</option>
                                    <option value="Evening">Evening</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="preferredRecallTime" class="col-sm-3 control-label">When would you like to be contacted?</label>
                            <div class="col-sm-9">
                                <select id="preferredRecallTime" filterOptionFor="preferredRecallTime" style="width:100%;">
                                    <option value="0">ASAP</option>
                                    <option value="1">Tomorrow</option>
                                    <option value="2">Next Week</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button id="submitWebRequestBtn" type="button" class="btn btn-primary">Submit</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="agentReportView" tabindex="-1" role="dialog" aria-labelledby="agentReportViewLabel" aria-hidden="true" style="top:100px;">
        <div class="modal-dialog">
            <div class="modal-content" style="height: 100%">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 class="modal-title" id="agentReportViewLabel"></h4>
                </div>
                <div class="modal-body" style="padding-bottom:0px; height: 85%">
                    <iframe seamless="yes" id="reportIframe" style="width: 100%; height: 100%;" frameborder="no"></iframe>
                </div>
                <div class="modal-footer" style="padding: 10px;">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="teamManagementView" tabindex="-1" role="dialog" aria-labelledby="teamManagementViewLabel" aria-hidden="true">
        <div class="modal-dialog" style="width: 60%;">
            <div class="modal-content" style="height: 100%;">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 class="modal-title" id="teamManagementViewLabel">Team Management</h4>
                </div>
                <div class="modal-body" style="height: 100%">
                    <div class="row">
                        <div id="teamTree" class="menu nav-collapse collapse in width col-md-4">
                            <div style="overflow-y: scroll; overflow-x: hidden; max-height: 500px; padding: 15px; border: 1px solid;">
                                <ul class="nav nav-list top-level">
                                    <jsp:include page="teamTree.jsp"/>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="panel panel-default" style="height: 500px;">
                                <div class="panel-heading" style="font-weight:bold; font-size:15px;">
                                    <span>Team Detail</span>
                                </div>
                                <div id="teamDetailBody" class="panel-body" style="height: 450px; overflow:auto;">
                                    <form class="form-horizontal report-parameters-form" role="form">
                                        <div class="form-group">
                                            <label for="fromInput" class="col-sm-3 control-label">Team Lead</label>
                                            <div class="col-sm-9">
                                                <div class="dropdown" selMode="SINGLE">
                                                    <div class="input-group dropdown-toggle" id="supervisor-dropdown-menu" data-toggle="dropdown">
                                                        <input id="supervisorId" type="text" class="form-control">
                                                        <span class="input-group-addon" style="font-weight: bold;">v</span>
                                                    </div>
                                                    <ul class="dropdown-menu" role="menu" aria-labelledby="supervisor-dropdown-menu" style="max-height: 300px; width: 100%; overflow-y: scroll;">
                                                        <c:forEach var="supervisor" items="${supervisorList}">
                                                            <li role="presentation"><input role="menuitem" tabindex="-1" type="checkbox" class="agent-option" style="padding-left: 10px;" value="${supervisor.employeeNumber}">&nbsp;${supervisor.employeeNumber} - ${supervisor.employeeName} (${supervisor.state})</input></li>
                                                        </c:forEach>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="fromInput" class="col-sm-3 control-label">Agents</label>
                                            <div class="col-sm-9">
                                                <div class="dropdown" selMode="MULTI" id="call-center-employee-dropdown">
                                                    <div class="input-group dropdown-toggle" id="call-center-employee-dropdown-menu" data-toggle="dropdown">
                                                        <input id="employeeInput" type="text" class="form-control">
                                                        <span class="input-group-addon" style="font-weight: bold;">v</span>
                                                    </div>
                                                    <ul class="dropdown-menu" role="menu" aria-labelledby="call-center-employee-dropdown-menu" style="max-height: 300px; width: 100%; overflow-y: scroll;">
                                                        <c:forEach var="employee" items="${callCenterEmployeeList}">
                                                            <li role="presentation" employeeNumber="${employee.employeeNumber}">
                                                                <input role="menuitem" tabindex="-1" type="checkbox" class="agent-option" style="padding-left: 10px;" value="${employee.employeeNumber}"></input>
                                                                <label style="font-weight: normal;">&nbsp;${employee.employeeNumber} - ${employee.employeeName} (${employee.state})</label>
                                                            </li>
                                                        </c:forEach>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                    <table id="agentsInTeam" class="table table-striped" style="height: 300px; overflow-y: scroll;">
                                    </table>
                                </div>
                                <div class="panel-footer" style="padding: 5px 15px;">
                                    <button id="cancelTeamBtn" type="button" class="btn btn-default btn-sm">Cancel</button>
                                    <button id="saveTeamBtn" type="button" class="btn btn-primary btn-sm">Save</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="padding: 10px;">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">

        <div class="page-header">
            <h2>Call Center CTI Tools
                <div class="pull-right" style="font-size: 14px;">
                    <a id="logoutLink" href="http://security.bnd.corp/at/logout.jsp?name=CALL_CENTER_SUPPORT">Logout</a>
                </div>
            </h2>
        </div>
        <c:if test="${canUpdateMongoDB || canUpdateReasonResolution || canEditTeam}">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h1 class="panel-title gray">Configuration</h1>
                </div>
                <div class="panel-body">
                    <c:if test="${canUpdateReasonResolution}">
                        <div class="row">
                            <div class="col-md-8"><a id='reasonResolutionLink' href="${pageContext.request.contextPath}/secure/tester?entity=reasonResolutions" target="_blank">Reason / Resolutions List</a></div>
                        </div>
                    </c:if>
                    <c:if test="${canUpdateMongoDB}">
                        <div class="row">
                            <div class="col-md-8"><a id='mongoSyncLink' href="#" target="_blank">Manual Sync</a>
                                <div id="syncProgress" class="progress" style="display:none;">
                                  <div class="progress-bar progress-bar-striped active"  role="progressbar" aria-valuenow="45" aria-valuemin="0" aria-valuemax="100" style="width: 100%">
                                    <span>Please wait while rebuilding...</span>
                                  </div>
                                </div>
                            </div>
                        </div>
                    </c:if>
                    <c:if test="${canEditTeam}">
                        <div class="row">
                            <div class="col-md-8"><a id='teamManagementLink' href="javascript:void(0);" target="_blank">Team Management</a></div>
                        </div>
                    </c:if>
                </div>
            </div>
        </c:if>

        <c:if test="${canEditFacility}">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h1 class="panel-title gray">Facility Management</h1>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div id="editFacilityLink" class="col-md-8"><a href="${pageContext.request.contextPath}/secure/facilityMessageEdit" target="_blank">Add / Update Facility Message</a></div>
                    </div>
                    <div class="row">
                        <div class="col-md-8"><a id='testerFacilityLink' href="${pageContext.request.contextPath}/secure/tester?entity=facilityInfo" target="_blank">Facility Tester</a></div>
                    </div>
                </div>
            </div>
        </c:if>

        <c:if test="${canEditPhoneNumber}">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h1 class="panel-title gray">Phone Numbers Management</h1>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-8"><a id="phoneNumberLink" href="${pageContext.request.contextPath}/secure/phoneNumberEdit" target="_blank">Add / Update Phone Numbers</a></div>
                    </div>
                    <div class="row">
                        <div class="col-md-8"><a id='testerPhoneNumberLink' href="${pageContext.request.contextPath}/secure/tester?entity=phoneNumber" target="_blank">Phone Number List</a></div>
                    </div>
                    <div class="row">
                        <div class="col-md-8"><a id='routingRuleLink' href="${pageContext.request.contextPath}/secure/tester?entity=routingRule" target="_blank">Phone Number Routing Rules</a></div>
                    </div>
                </div>
            </div>
        </c:if>

        <div class="panel panel-default" id="callCenterReportPanel">
            <div class="panel-heading">
                <h1 class="panel-title gray">Call Center Reports</h1>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-8"><a href="${pageContext.request.contextPath}/secure/report/1" target="_blank">Call Volume</a></div>
                </div>
                <div class="row">
                    <div class="col-md-8"><a id="callLogLink" href="javascript:void(0);" target="_blank">Call Log Report</a></div>
                </div>
                <div id="screenPopReport" class="row">
                    <div class="col-md-8"><a href="${pageContext.request.contextPath}/secure/report/5" target="_blank">Screen Pop Report</a></div>
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h1 class="panel-title gray">Call Center Agent Reports</h1>
            </div>
            <div class="panel-body">
                <div class="row" style="display: none;">
                    <div class="col-md-8"><a id='agentActivityLink' href="${pageContext.request.contextPath}/secure/report/3" target="_blank">Agent Activities</a></div>
                </div>
                <div class="row">
                    <div class="col-md-8"><a id='agentCallLogLink' href="javascript:void(0);" target="_blank">Agent Call Log</a></div>
                </div>
                <div id="callLogManagement" class="row">
                    <div class="col-md-8"><a href="${pageContext.request.contextPath}/secure/report/2" target="_blank">Call Log Management</a></div>
                </div>
                <c:if test="${hasAgentList == true || canAccessReport == true}">
                    <div class="row">
                        <div class="col-md-8"><a id='agentScorecardLink' href="#" target="_blank">PSR Scorecard</a></div>
                    </div>
                    <div class="row">
                        <div class="col-md-8"><a id='csrScorecardLink' href="#" target="_blank">CSR Scorecard</a></div>
                    </div>
                    <div class="row">
                        <div class="col-md-8"><a id='agentScorecardCompareLink' href="#" target="_blank">Agent Scorecard Month Comparison</a></div>
                    </div>
                    <div class="row">
                        <div class="col-md-8"><a id='teamScorecardLink' href="#" target="_blank">Team Scorecard</a></div>
                    </div>
                    <div class="row">
                        <div class="col-md-8"><a id='abandonRateLink' href="#" target="_blank">Abandon Rates by Interval</a></div>
                    </div>
                    <div class="row">
                        <div class="col-md-8"><a id='notReadyUsageLink' href="#" target="_blank">Not Ready Usage</a></div>
                    </div>
                    <div class="row">
                        <div class="col-md-8"><a id='averageHandleTimeLink' href="#" target="_blank">Average Handle Time</a></div>
                    </div>
                </c:if>
            </div>
        </div>
    </div>

    <!-- Placed at the end of the document so the pages load faster -->
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-1.7.2.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-ui-1.8.20.custom.min.js"></script>
    <script src="${pageContext.request.contextPath}/bootstrap/js/bootstrap.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/configurationActions.js?_id=${buildNumber}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/reportActions.js?_id=${buildNumber}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/sbi-script.js?_id=${buildNumber}"></script>

</body>

</html>