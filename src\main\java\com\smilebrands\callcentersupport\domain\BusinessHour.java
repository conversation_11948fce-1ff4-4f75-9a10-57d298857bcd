package com.smilebrands.callcentersupport.domain;

import com.smilebrands.callcentersupport.domain.constants.HourType;

import java.util.Date;

/**
 * Created by phongpham on 1/21/16.
 */
public class BusinessHour {

    private HourType hourType;

    private int dayOfWeek;
    private int fromHour;
    private int fromMinute;
    private int toHour;
    private int toMinute;

    private int createdBy;
    private Date createdOn = new Date();
    private int updatedBy;
    private Date updatedOn;

    public BusinessHour(){}

    /*
    *
    * @param ht {@link com.smilebrands.callcentersupport.domain.constants.HourType}
     */
    public BusinessHour(HourType ht, int dow, int fh, int fm, int th, int tm){
        this.hourType = ht;
        this.dayOfWeek = dow;
        this.fromHour = fh;
        this.fromMinute = fm;
        this.toHour = th;
        this.toMinute = tm;
    }

    public HourType getHourType() {
        return hourType;
    }

    public void setHourType(HourType hourType) {
        this.hourType = hourType;
    }

    public int getDayOfWeek() {
        return dayOfWeek;
    }

    public void setDayOfWeek(int dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    public int getFromHour() {
        return fromHour;
    }

    public void setFromHour(int fromHour) {
        this.fromHour = fromHour;
    }

    public int getFromMinute() {
        return fromMinute;
    }

    public void setFromMinute(int fromMinute) {
        this.fromMinute = fromMinute;
    }

    public int getToHour() {
        return toHour;
    }

    public void setToHour(int toHour) {
        this.toHour = toHour;
    }

    public int getToMinute() {
        return toMinute;
    }

    public void setToMinute(int toMinute) {
        this.toMinute = toMinute;
    }

    public int getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(int createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreatedOn() {
        return createdOn;
    }

    public void setCreatedOn(Date createdOn) {
        this.createdOn = createdOn;
    }

    public int getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(int updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getUpdatedOn() {
        return updatedOn;
    }

    public void setUpdatedOn(Date updatedOn) {
        this.updatedOn = updatedOn;
    }
}
