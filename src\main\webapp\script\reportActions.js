$(document).ready(function() {
    var dt = new Date();
    var canSynCallLog = SBI.getCookies('CALL_CENTER_SUPPORT', 'CAN_SYNC_CALL_LOG');
    if(canSynCallLog == null || canSynCallLog == 'false'){
//        $('.sync-call-log').detach();
    }else{
        $('button.sync-call-log').click(function(){
            var btn     = $(this),
                date    = $('.report-date[name=datepicker]').datepicker('getDate'),
                dateFormat  = SBI.formatDateTime(date, 'm-d-Y');
            if(dateFormat){
                SBI.showDialog('Confirm', 'Are you sure you would like to sync call log for ' + dateFormat + '?', SBI.SBI_ALERT_LEVEL_MESSAGE, 'No', null, 'Yes', doSyncCallLog);
            }

        });
    }
    var doSyncCallLog = function(){
        var btn         = $('button.sync-call-log'),
            date        = $('.report-date[name=datepicker]').datepicker('getDate'),
            dateFormat  = SBI.formatDateTime(date, 'm-d-Y');
        btn.attr('disabled', true);
        $('.report-progress').css('display', 'block');
        $('.report-progress').find('span.progress-message').html('Please wait while syncing Agent Call with Call Log...');
        $.getJSON(
            '/callcenter/secure/sync-call-log',
            {
                date: dateFormat
            },
            function(json){
                if(json.success){
                    SBI.showDialog('Message', json.message, SBI.SBI_ALERT_LEVEL_MESSAGE, 'Close', reloadPage);
                }else{
                    SBI.showDialog('ERROR', json.message, SBI.SBI_ALERT_LEVEL_ERROR, 'Close', reloadPage);
                }
            }
        );
    };
    var reloadPage = function(){
        location.href = location.href;
    };

    var reportDate = $('#reportDate').val();
    $('[name=datepicker]').datepicker();
    $('[name=datepicker]').datepicker('option', 'maxDate', 0);
    $('[name=datepicker]').datepicker('setDate', reportDate ? new Date(reportDate) : new Date());
    var endDate = $('#toDate').val();
    $('[name=datepicker].toDate').datepicker('setDate', endDate ? new Date(endDate) : new Date());

    var mainTable = $('table.main');
    if(mainTable.length > 0){
        var mainHeader = $('.container.main').find('.panel-title');
        if(mainHeader.length > 0){
            mainHeader.html(mainHeader.html() + ' (' + mainTable.find('tbody tr').length + ' records)');
        }
    }

    $('.agent-call-date').change(function(){
        console.log('data change: ' + $(this).val());
        if($('.toDate').length > 0){
            return;
        }
        $('.report-progress').css('display', 'block');
        var cmp             = $(this),
            selectedDate    = cmp.datepicker('getDate'),
            formatDate      = SBI.formatDateTime(selectedDate, 'm-d-Y'),
            canAccessReport = SBI.getCookies('CALL_CENTER_SUPPORT', 'CAN_ACCESS_CALL_CENTER_REPORT'),
            agentId         = SBI.getCookies('APPTRACKER', 'EMPLOYEE_NUMBER');
        if(formatDate){
            if(canAccessReport){
                agentId = '';
            }else if(agentId){
                agentId = 'agentId=' + agentId;
            }else{
                agentId = '';
            }
            location.href = location.pathname + '?date=' + formatDate + '&' + agentId;
        }
    });

    $('#loadBtn').click(function(){
        $('.report-progress').css('display', 'block');
        var fromDate    = $('.fromDate'),
            fromDateStr = '',
            toDate      = $('.toDate'),
            toDateStr   = '';
        if(fromDate && fromDate.length > 0){
            fromDate = fromDate.datepicker('getDate');
            fromDateStr = fromDate ? SBI.formatDateTime(fromDate, 'm-d-Y') : '';
        }
        if(toDate && toDate.length > 0){
            toDate = toDate.datepicker('getDate');
            toDateStr = toDate ? SBI.formatDateTime(toDate, 'm-d-Y') : '';
        }
        location.href = location.pathname + '?date=' + fromDateStr + '&endDate=' + toDateStr;
    });

    $('body').on('click', '.patient-schedule', function(){
        var cmp     = $(this),
            tr      = cmp.parents('tr'),
            emp     = tr.find('td[name=employeeNumber]'),
            agentId = emp.attr('value'),
            uuid    = tr.find('td[name=uuid]'),
            ani     = tr.find('td[name=ani]');
        console.log('about to load patient appointments for %o with uuid [%o]', emp.attr('value'), uuid.attr('value'));
        $.getJSON(
            '/callcenter/secure/agent-appointment-search',
            {
                agentId : agentId,
                uuid    : uuid.attr('value')
            },
            function(json){
                if(json.success){
                    var list    = json.data,
                        result  = '';
                    for(var i=0; i<list.length; i++){
                        var appointment = list[i];
                        result += "<a href='#' class='list-group-item' style='padding: 5px 15px;'><div>";
                        result += "<span class='fl'>Patient:</span><span class='content fr title'>" + appointment.patient.patientFullName + " (" + appointment.patient.dateOfBirth + ")</span><br/>";
                        result += "<span class='fl'>  Appointment:</span><span class='content fr'>" + appointment.formattedDateTime + " at "+ appointment.facility.name +"</span><br/>";
                        result += "</div></a>";
                    }
                    $('#appointmentSearch #appointment-display_list').html(result);
                    $('#appointmentSearchModalLabel').text('Appointment(s) for Agent ' + agentId + ' with ANI ' + SBI.formatPhoneNumber(ani.attr('value')));
                    $('#appointmentSearch').modal();
                }
            }
        );
    });

    var inboundCallSummaryList  = $('#inboundCallSummaryList'),
        inboundCallSummary      = {},
        volumeChart             = null,
        callCenterChart         = null;
    if(inboundCallSummaryList && inboundCallSummaryList.length > 0){
        var rows            = inboundCallSummaryList.find('tr'),
            summaryBody     = $('.call-center-volume').find('tbody');

        var populateExtraInfo = function(row, extraInfo, extraReportField, extraReportKey, extraReportValue){
            var eventCounts = row.find('td[eventCount=true]');
            extraInfo = extraInfo || {};
                for(var i=0; i<eventCounts.length; i++){
                    var event       = $(eventCounts[i]),
                        displayName = event.attr('displayName'),
                        countValue  = +event.attr('value');
                    countValue = isNaN(countValue) ? 0 : countValue;
                    if(extraInfo[event.attr('displayName')] == undefined){
                        extraInfo[event.attr('displayName')] = countValue
                    }else{
                        extraInfo[event.attr('displayName')] += countValue
                    }
                }
                if(!extraInfo[extraReportField]){
                    extraInfo[extraReportField] = {};
                    extraInfo[extraReportField][extraReportKey] = extraReportValue;
                }else{
                    if(extraInfo[extraReportField][extraReportKey]){
                        extraInfo[extraReportField][extraReportKey] += extraReportValue
                    }else{
                        extraInfo[extraReportField][extraReportKey] = extraReportValue;
                    }
                }
           return extraInfo;
        };
        for(var i=0; i<rows.length; i++){
            var row             = $(rows[i]),
                phoneTypeCmp    = row.find('td[name=phoneType]'),
                phoneType       = phoneTypeCmp.attr('value'),
                menuPressCmp    = row.find('td[name=menuPress]'),
                menuPress       = menuPressCmp.attr('value'),
                callCountCmp    = row.find('td[name=callCount]'),
                callCount       = +callCountCmp.attr('value'),
                callCenterName  = row.attr('callCenterName'),
                callCenterObj   = inboundCallSummary[callCenterName];
            if(callCenterName){
                if(!callCenterObj){
                    callCenterObj = {
                        count       : callCount,
                        phoneType   : {},
                        numberPress : {},
                        isCombined  : callCenterName == 'Combined'
                    };
                }else{
                    callCenterObj.count += callCount;
                }
                if(callCenterObj.phoneType[phoneType]){
                    callCenterObj.phoneType[phoneType].count += callCount;
                    callCenterObj.phoneType[phoneType].extra = populateExtraInfo(row, callCenterObj.phoneType[phoneType].extra, 'Number Press', menuPress, callCount);
                }else{
                    callCenterObj.phoneType[phoneType] = {
                        count   : callCount,
                        extra   : populateExtraInfo(row, null, 'Number Press', menuPress, callCount)
                    }
                }

                if(callCenterObj.numberPress[menuPress]){
                    callCenterObj.numberPress[menuPress].count  += callCount;
                    callCenterObj.numberPress[menuPress].extra  = populateExtraInfo(row, callCenterObj.numberPress[menuPress].extra, 'Phone Type', phoneType, callCount);
                }else{
                    callCenterObj.numberPress[menuPress] = {
                        count   : callCount,
                        extra   : populateExtraInfo(row, null, 'Phone Type', phoneType, callCount)
                    }
                }

                inboundCallSummary[callCenterName] = callCenterObj;
            }
        }
        console.log('inboundCallSummary: %o', inboundCallSummary);
        var callCenterNames = Object.keys(inboundCallSummary);
        callCenterNames =callCenterNames.sort(function(n1, n2){
            if(n1 == 'Combined' && n2 != 'Combined'){
                return 1;
            }else if(n1 != 'Combined' && n2 == 'Combined'){
                return -1;
            }else if(n1 == 'Combined' && n2 == 'Combined'){
                return 0;
            }else{
                if(n1 > n2){
                    return 1;
                }else if(n1 < n2){
                    return -1;
                }else{
                    return 0;
                }
            }
        });
        var newBody     = '',
            chartData   = [];
        for(var i=0; i<callCenterNames.length; i++){
            var name    = callCenterNames[i],
                tr      = '<tr value="' + name + '" class="call-center-name pointer ';
            if(name == 'Combined'){
                tr += 'bold h4">';
            }else{
                tr += '">';
                var color = SBI.pieColors[i%SBI.pieColors.length];
                chartData.push({
                    label       : name,
                    value       : inboundCallSummary[name].count,
                    color       : color.color,
                    highlight   : color.highlight
                });
            }
            tr += '<td>' + name + '</td>';
            tr += '<td class="text-align-right">' + inboundCallSummary[name].count + '</td>';
            tr += '</tr>';
            newBody += tr;
        }
        if(!newBody){
            newBody = '<tr><td colspan="2" style="text-align: center;">No Data</td></tr>';
        }
        summaryBody.html(newBody);
        inboundCallSummaryList.detach();
        if(chartData.length > 0){
            var chartCanvas = $('.call-center-volume-chart').find('canvas')[0];
            if(chartCanvas){
                var ctx = chartCanvas.getContext('2d');
                window.pieChart = new Chart(ctx).Pie(chartData);
            }
        }
    }
    $('body').on('click', '.call-center-name', function(){
        var tr              = $(this),
            name            = tr.attr('value'),
            selectedReport  = $('.call-center-report-control button.btn-primary').attr('reportType');
        hideVisiblePopover();
        tr.parents('tbody').find('tr').removeClass('info');
        tr.addClass('info');
        console.log('display detail for call center ' + name);
        populateReportDetail(name, selectedReport);
    });
    $('button.report').click(function(){
        var selectedCallCenter = $('.call-center-name.info').attr('value');
        hideVisiblePopover();
        populateReportDetail(selectedCallCenter, $(this).attr('reportType'));
    });

    var hideVisiblePopover = function(){
        if($('.popover.in').length > 0){
            var detailView  = $('.call-center-report-detail'),
                tr          = detailView.find('table tr.info'),
                cmp         = tr.find('.line-report-detail');
            cmp.triggerHandler('click');
        }
    };

    var populateReportDetail = function(callCenter, reportType){
        var reportControls  = $('.call-center-report-control button'),
            selectedReport  = $('.call-center-report-control button[reportType=' + reportType + ']'),
            reportView      = $('.call-center-report-detail table'),
            detailValue     = '',
            callCenterData  = inboundCallSummary ? inboundCallSummary[callCenter] : null,
            hasSelectedReport = selectedReport.length > 0,
            extraField      = '',
            reportField     = '';

        reportControls.removeClass('btn-primary');
        if(hasSelectedReport){
            selectedReport.addClass('btn-primary');
        }else{
            reportType = $(reportControls[0]).attr('reportType');
            if(callCenterData && callCenterData[reportType]){
                $(reportControls[0]).addClass('btn-primary');
                hasSelectedReport = true;
            }
        }

        if(callCenterData && hasSelectedReport){
            $('.call-center-report-control').css('display', 'block');
            if(reportType == 'phoneType'){
                extraField = 'Number Press';
                reportField = 'Phone Type - ';
            }else if(reportType == 'numberPress'){
                extraField = 'Phone Type';
                reportField = 'Number Press - ';
            }
            var data        = callCenterData[reportType],
                keys        = Object.keys(data),
                sum         = 0,
                count       = 0,
                extra       = null,
                chartData   = [],
                color       = null;
            keys = keys.sort();
            reportView.attr('callCenterName', callCenter);

            for(var i=0; i<keys.length; i++){
                color = SBI.pieColors[i%SBI.pieColors.length];
                count = data[keys[i]].count;
                extra = data[keys[i]].extra;
                detailValue += '<tr reportType="' + reportType +'" key="'+ keys[i] +'">' ;
                detailValue += '<td>' + keys[i] + '</td>';
                detailValue += '<td class="text-align-right">' + data[keys[i]].count + '</td>';
                detailValue += '<td width="30"><span class="pointer line-report-compare glyphicon glyphicon-stats" data-toggle="tooltip" data-title="Show Trend"></span></td>';
                chartData.push({
                    label       : reportField + keys[i],
                    value       : data[keys[i]].count,
                    color       : color.color,
                    highlight   : color.highlight
                });
                if(extra){
                    var extraInfo   = '<div class=\'row\'>',
                        extraKeys   = [],
                        extraKey    = '',
                        extraData   = null;
                    if(extra[extraField]){
                        extraData   = extra[extraField];
                        extraKeys   = Object.keys(extraData);
                        extraKeys = extraKeys.sort();
                        if(extraKeys.length > 0){
                            extraInfo += '<div class=\'col-md-3 width-200\' style=\'overflow-x: scroll; padding-top: 5px;\'><table class=\'table table-striped\' style=\'font-family: sans-serif;\'>';
                            extraInfo += '<thead><th>' + extraField + '</th><th>Count</th></thead><tbody>';
                            for(var j=0; j<extraKeys.length; j++){
                                extraKey = extraKeys[j];
                                extraInfo += '<tr reportType=\'' + extraField + '\' key=\'' + extraKey + '\'><td>' + extraKey + '</td><td class=\'text-align-right\'>' + extraData[extraKey] + '</td>';
                                extraInfo += '<td width=\'30\'><span class=\'pointer line-report-compare glyphicon glyphicon-stats\' data-toggle=\'tooltip\' data-title=\'Show Trend\'></span></td></tr>';
                            }
                            extraInfo += '</tbody></table></div>';
                        }
                    }
                    extraKeys = Object.keys(extra);
                    if(extraKeys.length > 0){
                        extraInfo += '<div class=\'col-md-3 width-250\' style=\'overflow-x: scroll; padding-top: 5px;\'><table class=\'table table-striped\' style=\'font-family: sans-serif;\'>';
                        extraInfo += '<thead><th>Event</th><th>Count</th></thead><tbody>';
                        for(var j=0; j<extraKeys.length; j++){
                            extraKey = extraKeys[j];
                            if(extraKey != extraField){
                                extraInfo += '<tr eventCount=\'' + extraKey+ '\'><td>' + extraKey + '</td><td class=\'text-align-right\'>' + extra[extraKey] + '</td>';
                                extraInfo += '<td width=\'30\'><span class=\'pointer line-report-compare glyphicon glyphicon-stats\' data-toggle=\'tooltip\' data-title=\'Show Trend\'></span></td></tr>';
                            }
                        }
                        extraInfo += '</tbody></table></div>';
                        extraInfo += '</div>';
                    }
                    detailValue += '<td style="width: 30px;"><span class="pointer fr padding-left-5 glyphicon glyphicon-arrow-right line-report-detail" data-toggle="popover" data-html=true data-content="' + extraInfo +'" data-container="body"></span></td>';
                }
                detailValue += '</tr>';
                sum += count;
            }
            if(keys.length > 0){
                detailValue += '<tr><td></td><td class="bold h5 text-align-right">' + sum + '</td><td colspan="2"></td></tr>';
            }
            if(chartData.length > 0){
                if(callCenterChart){
                    callCenterChart.segments.length = 0;
                    for(var i=0; i<chartData.length; i++){
                        callCenterChart.addData(chartData[i], 0, true);
                    }
                    callCenterChart.reflow();
                    callCenterChart.update();
                }else{
                    var canvas = $('.call-center-detail-chart').find('canvas')[0];
                    if(canvas){
                        callCenterChart = new Chart(canvas.getContext('2d')).Pie(chartData);
                    }
                }
            }else if(callCenterChart){
                callCenterChart.segments.length = 0;
                callCenterChart.reflow();
                callCenterChart.update();
            }
        }
        reportView.html(detailValue);
    };

    var loadingComparison = false;
    $('body').on('click', '.line-report-detail', function(){
        if(loadingComparison){
            return;
        }

        var cmp         = $(this),
            tr          = cmp.parents('tr'),
            existingPop = $('.line-report-detail').not(this);
        if(tr.hasClass('info')){
            tr.removeClass('info');
            $(this).popover('hide');
        }else{
            tr.parents('tbody').find('tr').removeClass('info');
            tr.addClass('info');
            if(existingPop.length > 0){
                existingPop.popover('hide');
            }
            $(this).popover('show');
        }
    });

    $('body').on('click', '.line-report-compare', function(){
        loadingComparison = true;
        var cmp         = $(this),
            tr          = cmp.parents('tr'),
            existingPop = $('.line-report-detail').not(this),
            reportView  = $('.call-center-report-detail table'),
            compareType = tr.attr('reportType'),
            eventCount  = tr.attr('eventCount'),
            key         = tr.attr('key'),
            params      = {},
            chartView   = $('#compareCallVolume'),
            title       = [],
            isFromPop   = cmp.parents('.popover').length > 0;

        if(compareType == 'phoneType' || compareType == 'Phone Type'){
            params.phoneType = key;
            compareType = 'Menu Press';
            title.push('Phone Type [' + key + ']');
        }else if(compareType == 'numberPress' || compareType == 'Number Press'){
            params.numberPress = key;
            compareType = 'Phone Type';
            title.push('Menu Press [' + key + ']');
        }
        if(eventCount){
            params.eventName = eventCount;
            title.push('Event [' + eventCount + ']');
        }
        if(isFromPop){
            var selectedTr = reportView.find('tr.info');
            if(selectedTr.length > 0){
                var rt = selectedTr.attr('reportType'),
                    rk = selectedTr.attr('key');
                if(rt == 'phoneType'){
                    params.phoneType = rk;
                    title.unshift('Phone Type [' + rk + ']');
                }else if(rt == 'numberPress'){
                    params.numberPress = rk;
                    title.unshift('Menu Press [' + rk + ']');
                }
            }
        }
        title = 'Call Volume Trend for ' + title.join(' - ');
        if(!SBI.isEmpty(params)){

            chartView.find('.progress').css('display', 'block');
            chartView.find('#compareChartDiv').css('display', 'none');

            if(reportView.attr('callCenterName') != 'Combined'){
                params.callCenterName = reportView.attr('callCenterName');
                title += ' at ' + params.callCenterName;
            }
            chartView.find('#compareCallVolumeModalLabel').html(title);
            chartView.modal();
            $('.popover').detach();

            $.getJSON(
                'compare-call-volume',
                params,
                function(json){
                    loadingComparison = false;
                    if(json.success){
                        renderComparisonChart(chartView, compareType, json.data, key);
                    }
                }
            )
        }else{
            loadingComparison = false;
        }

    });

    var compareDataChart = null;
    var renderComparisonChart = function(chartView, reportType, data, key){
        var keys        = data.keys,
            labels      = data.labels,
            dataValue   = data.data,
            chartData   = {
                labels  : labels,
                datasets: []
            },
            color = SBI.pieColors[0],
            chartDiv    = $('#compareChartDiv');
        keys = keys && keys.length > 0 ? keys : Object.keys(dataValue);
        if(keys.length == 1){
            keys.push(keys[0]);
        }
        keys.sort();
        chartData.labels = keys;
        chartData.datasets.push({
            label                   : reportType + ' ' + key,
            fillColor               : color.color,
            strokeColor             : color.color,
            pointColor              : color.color,
            pointStrokeColor        : "#fff",
            pointHighlightFill      : "#fff",
            pointHighlightStroke    : color.color,
            data                    : []
        })
        chartView.find('.progress').css('display', 'none');
        chartView.find('#compareChartDiv').css('display', 'block');

        if(keys.length > 0){
            for(var i=0; i<keys.length; i++){
                var key = keys[i];
                chartData.datasets[0].data.push(dataValue[key]);
            }
            if(compareDataChart){
                chartDiv.find('canvas').detach();
                chartDiv.append('<canvas width="800" height="400"></canvas>');
            }

            var canvas = chartDiv.find('canvas')[0];
            if(canvas){
                compareDataChart = new Chart(canvas.getContext('2d')).Line(chartData,{
//                    responsive: true,
                    bezierCurve: false,
                    datasetFill: false
                });
            }
        }else if(compareDataChart){
            chartDiv.find('canvas').detach();
        }
    };

    $('.dropdown li').live('click', function(e){
        var cmp         = $(this),
            current     = cmp.find('input')[0],
            currentVal  = current.value,
            btn         = cmp.parents('.dropdown'),//$('#agent-dropdown-menu'),
            dd          = btn.find('.dropdown-menu'),//$('#agent-dropdown-menu + .dropdown-menu'),
            selMode     = dd.parents('.dropdown').attr('selMode'),
            items       = dd.find('li input[type=checkbox'),
            selected    = [];
        if(selMode == 'MULTI'){
            e.stopPropagation();
            for(var i=0; i<items.length; i++){
                if(currentVal == '0'){
                    if(current.checked){
                        items[i].checked = true;
                        if(items[i].value != '0'){
                            selected.push(items[i].value);
                        }
                    }else{
                        items[i].checked = false;
                    }
                }else{
                    if(items[i].value == '0'){
                        items[i].checked = false
                    }else if(items[i].checked){
                        selected.push(items[i].value);
                    }
                }
            }
        }else{
            for(var i=0; i<items.length; i++){
                if(items[i].checked){
                    if(items[i] != current){
                        items[i].checked = false;
                    }else{
                        selected = [items[i].value];
                    }
                }
            }
        }
        btn.find('input[type=text]').changeVal(selected.join(','));
    });

    $('#agentCallLogLink').click(function(){
        var reportPop       = $('#agentReportPop'),
            reportTile      = $('#agentReportModalLabel'),
            agentInput      = reportPop.find('#agentId'),
            canAccessReport = SBI.getCookies('CALL_CENTER_SUPPORT', 'CAN_ACCESS_CALL_CENTER_REPORT'),
            agentId         = SBI.getCookies('APPTRACKER', 'EMPLOYEE_NUMBER');
        reportTile.html('Agent Call Log Report');
        reportPop.attr('reportType', 'agentCallLog');
        manageReportControl(reportPop, 'block', 'block', 'block', 'none', 'MULTI', 'none', 'none');
        if(canAccessReport == null || canAccessReport == 'false'){
            agentInput.attr('disabled', true);
            agentInput.val(agentId);
        }else if(!agentInput.val()){
            agentInput.val(agentId);
        }
        reportPop.modal();
        return false;
    });

    $('#webRequestFormLink').click(function(){
        var reportPop       = $('#webRequestPop'),
            reportTile      = $('#agentReportModalLabel'),
            agentInput      = reportPop.find('#agentId'),
            canAccessReport = SBI.getCookies('CALL_CENTER_SUPPORT', 'CAN_ACCESS_CALL_CENTER_REPORT'),
            agentId         = SBI.getCookies('APPTRACKER', 'EMPLOYEE_NUMBER');

        reportTile.html('Agent Call Log Report');
        reportPop.attr('reportType', 'agentCallLog');
        manageReportControl(reportPop, 'block', 'block', 'block', 'none', 'MULTI', 'none');

        agentInput.val(agentId);

        reportPop.find('#firstName').val(SBI.getCookies('APPTRACKER', 'FIRST_NAME'));
        reportPop.find('#lastName').val(SBI.getCookies('APPTRACKER', 'LAST_NAME'));
        reportPop.find('#emailAddress').val(SBI.getCookies('APPTRACKER', 'EMAIL'));
        reportPop.find('#city').val('Temecula');
        reportPop.find('#state').val('California');
        reportPop.find('#office').val('10230');
        reportPop.find('#zipCode').val('92618');
        reportPop.find('#phone').val('7148245019');

        reportPop.modal();
        return false;
    });

    $('#submitWebRequestBtn').click(function () {
        var reportPop   = $('#webRequestPop'),
            appDate     = reportPop.find('.appointmentDay').datepicker('getDate'),
            url         = '',
            wr;
        console.info('Appointment Date = ' + appDate);

        wr = {
            firstName               : reportPop.find('#firstName').val(),
            lastName                : reportPop.find('#lastName').val(),
            emailAddress            : reportPop.find('#emailAddress').val(),
            newPatient              : true,
            reasonForAppointment    : reportPop.find('#reasonForAppointment').val(),
            city                    : reportPop.find('#city').val(),
            state                   : reportPop.find('#state').val(),
            office                  : reportPop.find('#office').val(),
            zipCode                 : reportPop.find('#zipCode').val(),
            phone                   : reportPop.find('#phone').val(),
            phoneType               : reportPop.find('#phoneType').val(),
            appointmentDate         : new Date(reportPop.find('#appointmentDay').val()),
            preferredTime           : reportPop.find('#timeOfDay').val(),
            preferredRecallTime     : reportPop.find('#preferredRecallTime').val(),
            createDatetime          : new Date()
        };
        $.postJSON(
            '/callcenter/web-request',
             wr,
             function (json) {
                console.info('Web Request saved');
             }
        );
    });

    $('#webRequestScreenPopLink').click(function(){
        var facilityId = 10230;
        var url = '/callcenter/load-screen-pop/wr/a7a82f32-3b54-461d-97a1-6bcf788b6712/' + SBI.getCookies('APPTRACKER', 'EMPLOYEE_NUMBER') + '/en/10230';
        window.open(url, '_blank');
    });

    $('#callLogLink').click(function(){
        var reportPop       = $('#agentReportPop'),
            reportTile      = $('#agentReportModalLabel'),
            agentInput      = reportPop.find('#agentId');
        manageReportControl(reportPop, 'block', 'block', 'block', 'none', 'MULTI', 'block', 'none');
        reportTile.html('Call Log Report');
        reportPop.attr('reportType', 'callLog');
        reportPop.modal();
        return false;
    });

    $('#agentScorecardLink').click(function(){
        agentScorecardClickHandler('PSR');
        return false;
    });

    $('#csrScorecardLink').click(function(){
        agentScorecardClickHandler('CSR');
        return false;
    });

    var agentScorecardClickHandler = function(agentType){
        var reportPop       = $('#agentReportPop'),
            reportTile      = $('#agentReportModalLabel'),
            agentInput      = reportPop.find('#agentId'),
            canAccessReport = SBI.getCookies('CALL_CENTER_SUPPORT', 'CAN_ACCESS_CALL_CENTER_REPORT'),
            agentId         = SBI.getCookies('APPTRACKER', 'EMPLOYEE_NUMBER');
        reportTile.html(agentType + ' Scorecard');
        reportPop.attr('reportType', 'agentScorecard');
        reportPop.attr('agentType', agentType);
        manageReportControl(reportPop, 'block', 'none', 'none', 'block', 'SINGLE', 'none', 'none');
        if(canAccessReport == null || canAccessReport == 'false'){
            agentInput.attr('disabled', true);
            agentInput.val(agentId);
        }else if(!agentInput.val()){
            agentInput.val(agentId);
        }
        reportPop.modal();
    };

    $('#agentScorecardCompareLink').click(function(){
        var reportPop       = $('#agentReportPop'),
            reportTile      = $('#agentReportModalLabel'),
            agentInput      = reportPop.find('#agentId'),
            canAccessReport = SBI.getCookies('CALL_CENTER_SUPPORT', 'CAN_ACCESS_CALL_CENTER_REPORT'),
            agentId         = SBI.getCookies('APPTRACKER', 'EMPLOYEE_NUMBER');
        reportTile.html('Agent Scorecard');
        reportPop.attr('reportType', 'agentScorecardCompare');
        manageReportControl(reportPop, 'block', 'none', 'none', 'block', 'SINGLE', 'none', 'block');
        if(canAccessReport == null || canAccessReport == 'false'){
            agentInput.attr('disabled', true);
            agentInput.val(agentId);
        }else if(!agentInput.val()){
            agentInput.val(agentId);
        }
        reportPop.modal();
        return false;
    });

    $('#teamScorecardLink').click(function(){
        var reportPop       = $('#agentReportPop'),
            reportTile      = $('#agentReportModalLabel'),
            agentInput      = reportPop.find('#agentId'),
            canAccessReport = SBI.getCookies('CALL_CENTER_SUPPORT', 'CAN_ACCESS_CALL_CENTER_REPORT'),
            agentId         = SBI.getCookies('APPTRACKER', 'EMPLOYEE_NUMBER');
        reportTile.html('Team Scorecard');
        reportPop.attr('reportType', 'teamScorecard');
        manageReportControl(reportPop, 'block', 'none', 'none', 'block', 'MULTI', 'none', 'none');
        if(canAccessReport == null || canAccessReport == 'false'){
            agentInput.attr('disabled', true);
            agentInput.val(agentId);
        }else if(!agentInput.val()){
            agentInput.val(agentId);
        }
        reportPop.modal();
        return false;
    });

    $('#abandonRateLink').click(function(){
        var reportPop       = $('#agentReportPop'),
            reportTile      = $('#agentReportModalLabel');
        reportTile.html('Abandon Rates by Interval');
        reportPop.attr('reportType', 'abandonRate');
        manageReportControl(reportPop, 'none', 'block', 'block', 'none', 'SINGLE', 'none', 'none');
        reportPop.modal();
        return false;
    });

    $('#notReadyUsageLink').click(function(){
        var reportPop       = $('#agentReportPop'),
            reportTile      = $('#agentReportModalLabel');
        reportTile.html('Not Ready Usage');
        reportPop.attr('reportType', 'notReadyUsage');
        manageReportControl(reportPop, 'none', 'block', 'block', 'none', 'SINGLE', 'none', 'none');
        reportPop.modal();
        return false;
    });

    $('#averageHandleTimeLink').click(function(){
        var reportPop       = $('#agentReportPop'),
            reportTile      = $('#agentReportModalLabel');
        reportTile.html('Average Handle Time');
        reportPop.attr('reportType', 'averageHandleTime');
        manageReportControl(reportPop, 'none', 'none', 'none', 'block', 'SINGLE', 'none', 'none');
        reportPop.modal();
        return false;
    });

    var manageReportControl = function(reportPop, displayAgentInput, displayFrom, displayTo, displayCallMonth, dropDownSelMode, displayDateType, displayCallMonthCompared){
        var agentInput          = reportPop.find('#agentId'),
            agentDD             = reportPop.find('.dropdown'),
            toDate              = reportPop.find('.toInput'),
            fromDate            = reportPop.find('.fromInput'),
            callMonth           = reportPop.find('.callMonth'),
            dateType            = reportPop.find('.dateType'),
            callMonthCompared   = reportPop.find('.callMonthCompared');
        agentInput.parents('.form-group').css('display', displayAgentInput);
        agentDD.attr('selMode', dropDownSelMode);
        agentDD.find('input[type=checkbox][value=0]').parents('li').css('display', dropDownSelMode == 'SINGLE' ? 'none' : 'block');

        $('#agent-dropdown-menu + .dropdown-menu li input[type=checkbox]').attr('checked', false);
        $('#agent-dropdown-menu input[type=text]').val('');


        fromDate.parents('.form-group').css('display', displayFrom);
        toDate.parents('.form-group').css('display', displayTo);
        callMonth.parents('.form-group').css('display', displayCallMonth);
        callMonthCompared.parents('.form-group').css('display', displayCallMonthCompared);
        dateType.parents('.form-group').css('display', displayDateType);

        if(displayDateType == 'block'){
            fromDate.datepicker('option', 'maxDate', null);
            toDate.datepicker('option', 'maxDate', null);
        }else{
            fromDate.datepicker('option', 'maxDate', 0);
            toDate.datepicker('option', 'maxDate', 0);
        }
    }

    $('#getReportBtn').click(function(){
        var reportPop   = $('#agentReportPop'),
            agentId     = reportPop.find('#agentId').val(),
            from        = reportPop.find('.fromInput').datepicker('getDate'),
            to          = reportPop.find('.toInput').datepicker('getDate'),
            callMonth   = reportPop.find('.callMonth').val(),
            callYear    = reportPop.find('.callYear').val(),
            dateTypeCmp = reportPop.find('.dateType'),
            url         = '',
            selectMonth = callYear + '-' + ('0' + callMonth).slice(-2),
            reportType  = reportPop.attr('reportType');
        if(reportPop.attr('reportType') == 'agentCallLog' && agentId){
            url = 'http://liberty.bnd.corp/pentaho/content/reporting/reportviewer/report.html?solution=SB+Reporting&path=&name=Agent+Call+Log.prpt&locale=en_US&userid=joe&password=password&showParameters=false&renderMode=PARAMETER';
            url += '&p_agent_id=' + agentId;
            $('#agentReportView').find('.modal-title').html('Agent Call Log Report for ' + agentId);
            url += '&p_start_date=' + SBI.formatDateTime(from, 'd-m-Y');
            url += '&p_end_date=' + SBI.formatDateTime(to, 'd-m-Y');
            window.open(url, '_blank');
        }else if(reportPop.attr('reportType') == 'callLog'){
//            url = 'http://liberty.bnd.corp/pentaho/content/reporting/reportviewer/report.html?solution=SB+Reporting&path=&name=Call+Log+Detail.prpt&locale=en_US&userid=joe&password=password&showParameters=false&renderMode=PARAMETER';
            url = '/callcenter/secure/export/4?downloadId=4-' + new Date().getTime();
            url += '&startDate=' + SBI.formatDateTime(from, 'd-m-Y');
            url += '&endDate=' + SBI.formatDateTime(to, 'd-m-Y');
            url += '&requestAgentIdStr=' + agentId;
            if(dateTypeCmp.parents('.form-group').css('display') != 'none'){
                var dateType = dateTypeCmp.parents('.form-group').find('input[name=dateType]:checked').val();
                if(dateType){
                    url += '&dateType=' + dateType;
                }
            }
            downloadURL(url);
        }else if(reportPop.attr('reportType') == 'agentScorecard' && agentId){
            if(isValidMonthForReport(selectMonth, false)){
                var agentType   = reportPop.attr('agentType'),
                    reportName  = agentType == 'CSR' ? 'CSR+Scorecard.prpt' : 'Agent+Scorecard.prpt';
                url = 'http://liberty.bnd.corp/pentaho/content/reporting/reportviewer/report.html?solution=SB+Reporting&path=&name=' + reportName + '&locale=en_US&userid=joe&password=password&showParameters=false&renderMode=PARAMETER';
                url += '&p_agent_id=' + agentId;
                url += '&p_month=' + callYear + '-' + ('0' + callMonth).slice(-2);
                window.open(url, '_blank');
            }
        }else if(reportPop.attr('reportType') == 'agentScorecardCompare' && agentId){

            var callMonthCompared   = reportPop.find('.callMonthCompared').val(),
                callYearCompared    = reportPop.find('.callYearCompared').val(),
                selectMonthCompared = callYearCompared + '-' + ('0' + callMonthCompared).slice(-2);
            if(isValidMonthForReport(selectMonth, false) && isValidMonthForReport(selectMonthCompared, false)){
                url = 'http://liberty.bnd.corp/pentaho/content/reporting/reportviewer/report.html?solution=SB+Reporting&path=&name=Agent+Scorecard-Month+Comparison.prpt&locale=en_US&userid=joe&password=password&showParameters=false&renderMode=PARAMETER';
                url += '&p_agent_id=' + agentId;
                url += '&p_current_month=' + selectMonth;
                url += '&p_compare_month=' + selectMonthCompared;
                window.open(url, '_blank');
            }
        }else if(reportPop.attr('reportType') == 'teamScorecard' && agentId){
            if(isValidMonthForReport(selectMonth, false)){
                url = 'http://liberty.bnd.corp/pentaho/content/reporting/reportviewer/report.html?solution=SB+Reporting&path=&name=Team+Scorecard.prpt&locale=en_US&userid=joe&password=password&showParameters=false&renderMode=PARAMETER';
                url += '&p_agent_id=' + agentId;
                url += '&p_month=' + callYear + '-' + ('0' + callMonth).slice(-2);
                window.open(url, '_blank');
            }
        }else if(reportType === 'abandonRate'){
            url = 'http://liberty.bnd.corp/pentaho/content/reporting/reportviewer/report.html?solution=SB+Reporting&path=&name=Abandon+Rates+by+Interval.prpt&locale=en_US&userid=joe&password=password&showParameters=false&renderMode=PARAMETER';
            url += '&p_start_date=' + SBI.formatDateTime(from, 'd-m-Y');
            url += '&p_end_date=' + SBI.formatDateTime(to, 'd-m-Y');
            window.open(url, '_blank');
        }else if(reportType === 'notReadyUsage'){
            url = 'http://liberty.bnd.corp/pentaho/content/reporting/reportviewer/report.html?solution=SB+Reporting&path=&name=Not+Ready+Usage.prpt&locale=en_US&userid=joe&password=password&showParameters=false&renderMode=PARAMETER';
            url += '&p_start_date=' + SBI.formatDateTime(from, 'd-m-Y');
            url += '&p_end_date=' + SBI.formatDateTime(to, 'd-m-Y');
            window.open(url, '_blank');
        }else if(reportType === 'averageHandleTime'){
            url = 'http://liberty.bnd.corp/pentaho/content/reporting/reportviewer/report.html?solution=SB+Reporting&path=&name=Average+Handle+Time.prpt&locale=en_US&userid=joe&password=password&showParameters=false&renderMode=PARAMETER';
            url += '&p_month=' + callYear + '-' + ('0' + callMonth).slice(-2);
            window.open(url, '_blank');
        }
    });
    var iframe = $('#reportIframe');
    iframe.on('load', function(){
        console.log('on iframe load ' +  this.src);
        if(this.src){
            $('#agentReportView').modal();
        }
    });
    var isValidMonthForReport = function(selectMonth, silent){
        var valid       = false,
            minMonth    = '2014-08',
            maxMonth    = SBI.formatDateTime(new Date(), 'Y-m');
        if(selectMonth){
            valid = selectMonth >= minMonth && selectMonth <= maxMonth;
        }
        if(!valid && !silent){
            SBI.showDialog('Error', 'Selected month must be from ' + minMonth + ' to ' + maxMonth, SBI.SBI_ALERT_LEVEL_ERROR);
        }
        return valid;
    };
    var reportView = $('#agentReportView').find('.modal-dialog');


    var downloadURL = function downloadURL(url) {
//        var iframe = $('#reportIframe');
//        if(url){
//            iframe.attr('src', url);
//        }
        var hiddenIFrameID = 'reportIframe',
            iframe = $('#reportIframe');
        if(iframe.length == 0){
            iframe = $('#' + hiddenIFrameID);
        }
        if (iframe.length == 0) {
            iframe = document.createElement('iframe');
            iframe.id = hiddenIFrameID;
            iframe.style.display = 'none';
            document.body.appendChild(iframe);
            iframe.src = url;
        }else if(url){
            iframe.attr('src', url);
        }

    };

    /********************************************************************************************************************************************
                    BEGIN CALL LOG ACTION
    **********************************************************************************************************************************************/
    var facilityIdSelect = $('#facilityIDs');
    SBI.facilityIdsList = [];
    SBI.reasonResolutionCode = [];
    if(facilityIdSelect.length > 0){
        var facIds = facilityIdSelect.find('option');
        for(var i=0; i<facIds.length; i++){
            SBI.facilityIdsList.push(facIds[i].value);
        };
        facilityIdSelect.remove();
    }
    var reasonList = $('#reasonInput');
    if(reasonList.length > 0){
        var reasonOptions = reasonList.find('option');
        for(var i=0; i<reasonOptions.length; i++){
            var option = $(reasonOptions[i]);
            SBI.reasonResolutionCode.push({id: option.attr('value'), description: option.text(), screenType: option.attr('screenType'), type: 'reason'});
        }
        $('#callDate').datepicker('setDate', reportDate ? new Date(reportDate) : new Date());
    }
    var resolutionList = $('#resolutionInput');
    if(resolutionList.length > 0){
        var resolutionOptions = resolutionList.find('option');
        for(var i=0; i<resolutionOptions.length; i++){
            var option = $(resolutionOptions[i]);
            SBI.reasonResolutionCode.push({id: option.attr('value'), description: option.text(), screenType: option.attr('screenType'), type: 'resolution'});
        }
    }
    var callListCmp = $('#callList');
    SBI.callList = {};
    if(callListCmp.length > 0){
        var callListOptions = callListCmp.find('option'),
            callListTable   = $('#agentCallList'),
            sessionIdCmps   = callListTable.find('td[name=sessionId][value]'),
            sessionIds      = [],
            sessionId;
        for(var i=0; i<sessionIdCmps.length; i++){
            sessionId = $(sessionIdCmps[i]).attr('value');
            if(sessionIds.indexOf(sessionId) == -1){
                sessionIds.push(sessionId);
            }
        }
        for(var i=0; i<callListOptions.length; i++){
            var option = $(callListOptions[i]);
            sessionId = option.attr('sessionId');
            if(!SBI.callList.hasOwnProperty(option.attr('employeeNumber'))){
                SBI.callList[option.attr('employeeNumber')] = []
            }
            SBI.callList[option.attr('employeeNumber')].push({
                sessionId: sessionId,
                ani: option.attr('ani'),
                startDate: option.attr('startDate'),
                endDate: option.attr('endDate'),
//                queueName: option.attr('queueName'),
                hasCallLog: sessionIds.indexOf(sessionId) != -1
            })
        }
        callListCmp.remove();
    }

    $('.add-call-log').click(function(){
        doOpenCallLogDetail(this, 'Add');
    });
    $('.edit-call-log').live('click', function(){
        doOpenCallLogDetail(this, 'Edit');
    });

    $('#screenTypeInput').change(function(){
        var cmp             = $(this),
            screenType      = cmp.val(),
            reasonCmp       = $('#reasonInput'),
            reasonValue     = '',
            resolutionCmp   = $('#resolutionInput'),
            resolutionValue = '',
            patientPanel    = $('#patientInfoBody').parents('.panel');
        SBI.reasonResolutionCode.forEach(function(option){
            if(option.screenType == 'ALL' || option.screenType == screenType){
                if(option.type == 'reason'){
                    reasonValue += '<option value="' + option.id + '">' + option.description + '</option>';
                }else if(option.type == 'resolution'){
                    resolutionValue += '<option value="' + option.id + '">' + option.description + '</option>';
                }
            }
        });
        reasonCmp.html(reasonValue);
        resolutionCmp.html(resolutionValue);
//        patientPanel.css('display', screenType == 'PSR' ? 'block' : 'none');
    });

    $('#screenTypeInput').triggerHandler('change');

    $('#employeeNumberInput').change(function(){
        console.log('employeeNumber change to ' + $(this).val());
        var cmp             = $(this),
            callLogView     = $('#agentCallLogPop'),
            callHr          = callLogView.find('.call-time-hour'),
            callMin         = callLogView.find('.call-time-minute'),
            callAmPm        = callLogView.find('.call-time-ampm'),
            callsForAgent   = SBI.callList[cmp.val()],
            hasCallList     = callsForAgent && callsForAgent.length > 0,
            optionHtml      = '',
            callListCmp     = callLogView.find('#callListDDOptions'),
            callListInput   = callLogView.find('#callListInput');
        if(hasCallList){
            for(var i=0; i<callsForAgent.length; i++){
                var call    = callsForAgent[i],
                    val     = 'Session ID ' + call.sessionId + (call.ani ? ' from ' + call.ani : ''),
                    time    = call.startDate.substr(call.startDate.indexOf('t') + 1),
                    liStyle = call.hasCallLog ? 'color: #ccc;' : '';
                optionHtml += '<li role="presentation" style="' + liStyle + '"><input role="menuitem" tabindex="-1" type="checkbox" value="' + val + '" time="' + time + '"';
                optionHtml += ' sessionId="' + call.sessionId + '" ' + (call.hasCallLog ? 'disabled' : '') + '>';
                optionHtml += '&nbsp;Session ID ' + call.sessionId + (call.ani ? ' from ' + call.ani : '') + ' at ' + time;
                optionHtml += '</input></li>';
            }
        }
        callListCmp.html(optionHtml);
        callListInput.val(null);
        callListInput.attr('sessionId', '');
        callHr.attr('disabled', false).val(8);
        callMin.attr('disabled', false).val(0);
        callAmPm.attr('disabled', false).val('AM');
    });

    $('#callListInput').change(function(){
        console.log('callList change to ' + $(this).val());
        var cmp             = $(this),
            callLogView     = $('#agentCallLogPop'),
            callHr          = callLogView.find('.call-time-hour'),
            callMin         = callLogView.find('.call-time-minute'),
            callAmPm        = callLogView.find('.call-time-ampm'),
            selectedCall    = callLogView.find('#callListDDOptions li input[value="' + cmp.val() + '"]'),
            time,
            hour, minute;
        if(selectedCall && selectedCall.length){
            time = selectedCall.attr('time').split(':');
            minute = +time[1];
            hour = +time[0] + (minute > 55 ? 1 : 0);
            minute = minute > 55 ? 0 : Math.round(minute/5) * 5;
            callHr.attr('disabled', true).val(hour);
            callMin.attr('disabled', true).val(minute);
            callAmPm.attr('disabled', true).val(hour >= 12 ? 'PM' : 'AM');
            cmp.attr('sessionId', selectedCall.attr('sessionId'));
        }else{
            callHr.attr('disabled', false).val(8);
            callMin.attr('disabled', false).val(0);
            callAmPm.attr('disabled', false).val('AM');
            cmp.attr('sessionId', '');
        }
    });

    var doOpenCallLogDetail = function(link, action){
        var cmp             = $(link),
            tr              = cmp.parents('tr'),
            table           = cmp.parents('table'),
            uuid            = tr.attr('uuid'),
            screenType      = tr.attr('agentType'),
            callCenter      = tr.attr('callCenter'),
            reason          = tr.attr('reason'),
            resolution      = tr.attr('resolution'),
            trs             = [],
            tds             = [],
            callLog         = {
                uuid        : uuid
            },
            callLogView     = $('#agentCallLogPop'),
            title           = callLogView.find('.modal-title'),
            uuidCmp         = callLogView.find('#uuid'),
            sessionIdCmp    = callLogView.find('#sessionId'),
            employeeNumber  = callLogView.find('#employeeNumberInput'),
            employeeDiv     = employeeNumber.parents('div.dropdown'),
            screenTypeCmp   = callLogView.find('#screenTypeInput'),
            callCenterCmp   = callLogView.find('#callCenterInput'),
            reasonCmp       = callLogView.find('#reasonInput'),
            resolutionCmp   = callLogView.find('#resolutionInput'),
            fileId          = null,
            playbackCmp     = callLogView.find('#playbackCmp'),
            downloadCmp     = callLogView.find('#downloadFileCmp');
        if(uuid){
            trs = table.find('[uuid=' + uuid + '][employeeNumber=' + tr.attr('employeeNumber') + ']' + (screenType ? '[agentType=' + screenType + ']' : ''));
        }else{
            trs = [tr];
        }
        $('#patientInfoBody').html('');
        employeeDiv.find('input[type=checkbox]').attr('checked', false);
        if(action.toLowerCase() == 'edit'){
            for(var i=0; i<trs.length; i++){
                tr = $(trs[i]);
                if(tr.attr('agentType') != screenType){
                    continue;
                }
                tds = tr.find('td')
                callLog = {};
                for(var j=0; j<tds.length; j++){
                    var attrName    = $(tds[j]).attr('name'),
                        attrValue   = $(tds[j]).attr('value');
                    if(attrName == 'fileId'){
                        fileId = +attrValue;
                    }else{
                        callLog[attrName] = attrValue;
                    }
                }
                var apptDateTime    = callLog.appointmentDateTime,
                    apptDate        = apptDateTime ? apptDateTime.split(' ')[0] : '',
                    apptTime        = apptDateTime ? apptDateTime.split(' ')[1] : '';
                callLog['nextAppointmentDate']  = apptDate;
                callLog['nextAppointmentTime']  = apptTime.split(':').join('');
                callLog['patientId'] = callLog['tempPatientId'];

                SBI.addPatientForm(callLog, $('#patientInfoBody'), false, $('.report-date[name=datepicker]').datepicker('getDate'));
            }
            $('#patientInfoBody').parents('.panel').css('display', trs.length > 0 ? 'block' : 'none');
            employeeDiv.removeAttr('data-toggle');
            employeeNumber.attr('disabled', true);
            var recordEmpNumber = tr.attr('employeeNumber'),
                editEmpNumber   = SBI.getCookies('APPTRACKER', 'EMPLOYEE_NUMBER'),
                editAdmin       = SBI.getCookies('CALL_CENTER_SUPPORT', 'CAN_EDIT_CALL_LOG') === 'true';
            if(editEmpNumber == recordEmpNumber || editAdmin || callLog['editable'] === 'true'){
                $('#addPatientInfoBtn').css('display', 'block');
                callLogView.find('#saveCallLog').attr('disabled', false);
            }else{
                $('#addPatientInfoBtn').css('display', 'none');
                $('#patientInfoBody').find('input').attr('disabled', true);
                $('#patientInfoBody').find('select').attr('disabled', true);
                callLogView.find('#saveCallLog').attr('disabled', true);
            }
        }else{
//            $('#patientInfoBody').parents('.panel').css('display', callLogView.find('#screenTypeInput').val() == 'PSR' ? 'block' : 'none');
            employeeNumber.parents('div.dropdown-toggle').attr('data-toggle', 'dropdown');
            employeeNumber.attr('disabled', true);
            $('#addPatientInfoBtn').css('display', 'block');
            callLogView.find('#saveCallLog').attr('disabled', false);
        }
        $('div.form-group[action][action!=' + action.toLowerCase() +']').css('display', 'none');
        $('div.form-group[action][action=' + action.toLowerCase() +']').css('display', 'block');
        uuidCmp.val(callLog.uuid);
        sessionIdCmp.val(callLog.sessionId);
        employeeNumber.changeVal(callLog.employeeNumber || SBI.getCookies('APPTRACKER', 'EMPLOYEE_NUMBER'));
        employeeDiv.find('input[type=checkbox][value=' + employeeNumber.val() + ']').attr('checked', true);
        screenTypeCmp.attr('disabled', action.toLowerCase() == 'edit').val(screenType ? screenType.toUpperCase() : 'PSR');
        callCenterCmp.val(callCenter ? callCenter : 'Irvine');
        reasonCmp.val(reason ? reason.toUpperCase() : 'NONE');
        resolutionCmp.val(resolution ? resolution.toUpperCase() : 'NONE');

        playbackCmp.parents('.form-group').css('display', fileId ? 'block' : 'none');
        playbackCmp.attr('src', fileId ? 'http://cti.bnd.corp/callcenter/secure/stream-file/' + fileId : '');
        downloadCmp.attr('fileId', fileId ? fileId : '');
//        playbackCmp.attr('src', fileId ? '/callcenter/images/test.wav' : '');
        if(fileId){
            playbackCmp[0].load();
        }

        title.html(action + ' call log');
        callLogView.modal();
    };

    $('#agentCallLogPop').on('hidden.bs.modal', function(){
        console.log('on #agentCallLogPop dismissed');
        var playbackCmp = $('#playbackCmp');
        playbackCmp.attr('src', '');
        playbackCmp[0].load();
    });

    $('#downloadFileCmp').click(function(){
        var cmp = $(this);
        if(cmp.attr('fileId')){
            downloadURL('http://cti.bnd.corp/callcenter/secure/stream-file/' + cmp.attr('fileId') + '?attachment=true');
        }
    });

    $('#addPatientInfoBtn').click(function(){
        var body = $('#patientInfoBody');
        SBI.addPatientForm(null, body, true, $('.report-date[name=datepicker]').datepicker('getDate'));
    });

    $('#saveCallLog').click(function(){
        var callLogView     = $('#agentCallLogPop'),
            uuid            = callLogView.find('#uuid'),
            sessionIdCmp    = callLogView.find('#sessionId'),
            employeeNumber  = callLogView.find('#employeeNumberInput'),
            callCenter      = callLogView.find('#callCenterInput'),
            screenType      = callLogView.find('#screenTypeInput'),
            callDate        = callLogView.find('#callDate'),
            callHr          = callLogView.find('.call-time-hour'),
            callMin         = callLogView.find('.call-time-minute'),
            callAmPm        = callLogView.find('.call-time-ampm'),
            reason          = callLogView.find('#reasonInput'),
            resolution      = callLogView.find('#resolutionInput'),
            patientInfoBody = callLogView.find('#patientInfoBody'),
            callListCmp     = callLogView.find('#callListDDOptions'),
            callListInput   = callLogView.find('#callListInput'),
            forms           = patientInfoBody.find('.patient-info-form'),
            callListGroup   = callListCmp.parents('.form-group'),
            callLog         = {},
            patients        = [],
            patient         = null,
            editAdmin       = SBI.getCookies('CALL_CENTER_SUPPORT', 'CAN_EDIT_CALL_LOG') === 'true',
            sessionId;
        if(patientInfoBody.find('.has-error').length > 0){
            SBI.showDialog('Error', 'Form is invalid.', SBI.SBI_ALERT_LEVEL_ERROR);
//        }else if(callListGroup && callListGroup.length > 0 && callListGroup.css('display') != 'none'
//                && !callListInput.attr('sessionId') && callListCmp.find('li').length > 0){
//            SBI.showDialog('Error', 'Please select call from Call List.', SBI.SBI_ALERT_LEVEL_ERROR);
        }else if(!employeeNumber.val()){
            SBI.showDialog('Error', 'Employee is missing for call log.', SBI.SBI_ALERT_LEVEL_ERROR);
        }else{
            sessionId = sessionIdCmp.val() || callListInput.attr('sessionId');
            callLog = {
                uuid            : uuid.val(),
                employeeNumber  : employeeNumber.val(),
                callCenter      : callCenter.val(),
                screenType      : screenType.val(),
                callDateTime    : SBI.formatDateTime(SBI.setTimeFromCmp(callDate.datepicker('getDate'), callHr.val(), callMin.val(), callAmPm.val())),
                reason          : reason.val(),
                resolution      : resolution.val(),
                sessionId       : sessionId
            };
            for(var i=0; i<forms.length; i++){
                var form            = $(forms[i]),
                    patientId       = form.find('.patient-id-input'),
                    apptDate        = $(form.find('.patient-date-input')[0]),
                    date            = apptDate.datepicker('getDate'),
                    apptTimeHour    = $(form.find('.patient-appointment-hour')[0]),
                    apptTimeMinute  = $(form.find('.patient-appointment-minute')[0]),
                    apptTimeAmPm    = $(form.find('.patient-appointment-ampm')[0]),
                    allowCredit     = editAdmin ? form.find('.patient-allow-credit') : null;
                if(patientId.val()){
                    patient = {
                        agentCallLogId          : form.find('.agent-call-log-id').val(),
                        apptCallLogId           : form.find('.appt-call-log-id').val(),
                        patientId               : patientId.val().trim(),
                        facilityId              : form.find('.facility-id-input').val(),
                        nextAppointmentDateTime : SBI.formatDateTime(SBI.setTimeFromCmp(date, apptTimeHour.val(), apptTimeMinute.val(), apptTimeAmPm.val())),
                        emailCaptured           : form.find('.patient-email-captured')[0].checked,
                        insuranceWaiting        : form.find('.patient-insurance-waiting')[0].checked,
                        isOrtho                 : form.find('.patient-ortho')[0].checked
                    };
                    if(editAdmin == true && allowCredit.length > 0){
                        patient.allowCredit = allowCredit[0].checked;
                    }
                    patients.push(patient);
                }
            }
            callLog['patients'] = patients;
            SBI.showDialog('Message', 'Are you sure you would like to update call log?', SBI.SBI_ALERT_LEVEL_WARNING, 'No', null, 'Yes', doSaveCallLog, callLog);
        }
    });

    var doSaveCallLog = function(e){
        var callLog = e.data;
        console.log('about to save call log: %o', callLog);
        if(callLog){
            $('#saveCallLog').attr('disabled', true)
            $.postJSON(
                '/callcenter/secure/update-call-log',
                 callLog,
                 function(json){
                    if(json.success){
                        SBI.showDialog('Message', json.message, SBI.SBI_ALERT_LEVEL_MESSAGE, 'Close', reloadPage);
                    }else{
                        SBI.showDialog('Alert', json.message, SBI.SBI_ALERT_LEVEL_ERROR, 'Close', null);
                        $('#saveCallLog').attr('disabled', false);
                    }
                 }
            );
        }
    };

    /********************************************************************************************************************************************
                    END CALL LOG ACTION
    **********************************************************************************************************************************************/
    var downloadCookie = "";
    $('#exportBtn').click(function(){
        var btn             = $(this);
        if(btn.attr('from') == 'local'){
            exportReportLocal();
        }else{
            exportReportFromServer(btn);
        }
    });
    var exportReportLocal = function(){
        var content         = '<?xml version="1.0"?>',
            table           = $('table.main'),
            trRows          = table.find('tr:not([style*=none])'),
            reportDate      = $('.report-date'),
            selectedDate    = reportDate.datepicker('getDate'),
            formatDate      = SBI.formatDateTime(selectedDate, 'm-d-Y'),
            endDateCmp      = $('.agent-call-date.toDate'),
            endDate         = endDateCmp.length > 0 ? endDateCmp.datepicker('getDate') : null
            reportType      = $('#reportType').val(),
            title           = '';
        if(reportType == 1){
            title = 'Inbound Call Summary';
        }else if(reportType == 2){
            title = 'Call Log Report';
        }else if(reportType == 3){
            title = 'Agent Activities';
        }else if(reportType == 4){
            title = 'Call Center Detail';
        }else if(reportType == 5){
            title = 'Screen Pop Report from ' + formatDate + ' to ' + (endDate ? SBI.formatDateTime(endDate, 'm-d-Y') : '');
        }
        content += '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">';
        content += '<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office"><Title>' + 'Hello World' + '</Title></DocumentProperties>';
        content += '<OfficeDocumentSettings xmlns="urn:schemas-microsoft-com:office:office"><AllowPNG/></OfficeDocumentSettings>';
        content += '<ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">';
        content += '<ProtectStructure>False</ProtectStructure>';
        content += '<ProtectWindows>False</ProtectWindows>';
        content += '</ExcelWorkbook>';
        content += '<Styles>';
        content += '<Style ss:ID="title">';
        content += '<Borders/>';
        content += '<Font ss:Bold="1"/>';
        content += '<Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>';
        content += '<NumberFormat ss:Format="@"/>';
        content += '</Style>';
        content += '</Styles>';
        content += '<Worksheet ss:Name="Report">';
        content += '<Names>';
        content += '<NamedRange ss:Name="Print_Titles" ss:RefersTo="=\'Report\'!R1:R2">';
        content += '</NamedRange></Names>';

        for(var i=0; i<trRows.length; i++){
            var row     = $(trRows[i]),
                tdCols  = row.find('td:not([style*=none])');
            if(tdCols.length == 0){
                tdCols = row.find('th:not([style*=none])');
            }
            if(i==0){
                content += '<Table x:FullColumns="1" x:FullRows="1" ss:DefaultColumnWidth="100" ss:DefaultRowHeight="15" ';
                content += 'ss:ExpandedColumnCount="' + (tdCols.length + 2) + '" ss:ExpandedRowCount="' + trRows.length + '">';
            }
            content += '<Row>'
            for(var j=0; j<tdCols.length; j++){
                var col     = $(tdCols[j]),
                    value   = i==0? col.text() : col.attr('value');
                content += '<Cell' + (i==0 ? ' ss:StyleID="title">' : '>');
                content += '<Data ss:Type="' + (isNaN(value) ? 'String' : 'Number') + '">' + value + '</Data></Cell>';
            }
            content += '</Row>'
        }
        content += '</Table>';
        content += '<WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">';
        content += '<PageLayoutZoom>0</PageLayoutZoom>';
        content += 'Selected/>';
        content += '<Panes>';
        content += '<Pane>';
        content += '<Number>3</Number>';
        content += '<ActiveRow>2</ActiveRow>';
        content += '</Pane>';
        content += '</Panes>';
        content += '<ProtectObjects>False</ProtectObjects>';
        content += '<ProtectScenarios>False</ProtectScenarios>';
        content += '</WorksheetOptions>';
        content += '</Worksheet>';
        content += '</Workbook>';
        var link        = document.createElement('a'),
            mimeType    = 'data:application/vnd.ms-excel;base64',
            blob        = new Blob([content],{type: mimeType}),
            url         = URL.createObjectURL(blob);
        link.href = url;
        link.setAttribute('download', title + "-" + formatDate + '-' + new Date().getTime() + '.xls');
        link.innerHTML = "Export to XLS";
        link.click();
    };
    var exportReportFromServer = function(btn){
        var reportDate      = $('.report-date'),
            selectedDate    = reportDate.datepicker('getDate'),
            formatDate      = SBI.formatDateTime(selectedDate, 'm-d-Y'),
            agentId         = $('#agentId').val(),
            reportType      = $('#reportType').val(),
            url             = '/callcenter/secure/export/',
            downloadId      = reportType + '-' + new Date().getTime(),
            endDateCmp      = $('.agent-call-date.toDate'),
            endDate         = endDateCmp.length > 0 ? endDateCmp.datepicker('getDate') : null;
        if(reportType && !isNaN(reportType)){
            url += reportType + '?downloadId=' + downloadId;
            if(agentId){
                url += '&requestAgentIdStr=' + agentId;
            }
            if(formatDate){
                url += '&date=' + formatDate;
            }
            if(endDate){
                url += '&endDate=' + SBI.formatDateTime(endDate, 'm-d-Y');
            }
            btn.attr('disabled', true);
            setupWaiting(downloadId);
            downloadURL(url);
        }else{
            SBI.showDialog('Error', 'Cannot determine what type of report to be exported.');
        }
    };
    var checkingCount = 0;
    var setupWaiting = function(downloadId){
        checkingCount = 0;
        $('.report-progress').css('display', 'block');
        $('.report-progress').find('span.progress-message').html('Please wait while pulling report...');
        this.checkDownloadInterval = setInterval(function(){
            checkingCount++;
            if(checkingCount == 10){
                stopWaiting();
            }else{
                $.getJSON(
                    '/callcenter/secure/check-download-status/' + downloadId,
                    function(json){
                        if(json.success){
                            stopWaiting();
                        }
                    }
                );
            }
        },1000);
    };

    var stopWaiting = function(){
        clearInterval(this.checkDownloadInterval);
        $('#exportBtn').removeAttr('disabled');
        $('.report-progress').css('display', 'none');
    };
    $('.report-progress').css('display', 'none');
    console.log('time taken in reportAction.js: %o', (new Date().getTime()-dt.getTime()));
})