package com.smilebrands.callcentersupport.domain;

import java.util.List;

/**
 * Created by phongpham on 10/23/14.
 */
public class Provider extends Employee {

    public Provider(String firstName, String lastName, String title) {
        super(firstName, lastName, title);
    }

    private Integer serviceProviderId;
    private Integer qsiProviderId;
    private String taxonomyCode;
    private String specialty;
    private String licenseNumber;
    private String licenseState;
    private Long npiNumber;

    private String institutionName;
    private String degreeEarned;
    private String degreeSuffix;
    private Integer degreeEarnedYear;
    private Integer startPracticeYear;

    private String languages;
    private String scheduleNotes;

    private List<ProviderInsurance> providerInsurances;

    public Integer getServiceProviderId() {
        return serviceProviderId;
    }

    public void setServiceProviderId(Integer serviceProviderId) {
        this.serviceProviderId = serviceProviderId;
    }

    public Integer getQsiProviderId() {
        return qsiProviderId;
    }

    public void setQsiProviderId(Integer qsiProviderId) {
        this.qsiProviderId = qsiProviderId;
    }

    public String getTaxonomyCode() {
        return taxonomyCode;
    }

    public void setTaxonomyCode(String taxonomyCode) {
        this.taxonomyCode = taxonomyCode;
    }

    public String getSpecialty() {
        return specialty;
    }

    public void setSpecialty(String specialty) {
        this.specialty = specialty;
    }

    public String getLicenseNumber() {
        return licenseNumber;
    }

    public void setLicenseNumber(String licenseNumber) {
        this.licenseNumber = licenseNumber;
    }

    public String getLicenseState() {
        return licenseState;
    }

    public void setLicenseState(String licenseState) {
        this.licenseState = licenseState;
    }

    public Long getNpiNumber() {
        return npiNumber;
    }

    public void setNpiNumber(Long npiNumber) {
        this.npiNumber = npiNumber;
    }

    public String getInstitutionName() {
        return institutionName;
    }

    public void setInstitutionName(String institutionName) {
        this.institutionName = institutionName;
    }

    public String getDegreeEarned() {
        return degreeEarned;
    }

    public void setDegreeEarned(String degreeEarned) {
        this.degreeEarned = degreeEarned;
    }

    public String getDegreeSuffix() {
        return degreeSuffix;
    }

    public void setDegreeSuffix(String degreeSuffix) {
        this.degreeSuffix = degreeSuffix;
    }

    public Integer getDegreeEarnedYear() {
        return degreeEarnedYear;
    }

    public void setDegreeEarnedYear(Integer degreeEarnedYear) {
        this.degreeEarnedYear = degreeEarnedYear;
    }

    public Integer getStartPracticeYear() {
        return startPracticeYear;
    }

    public void setStartPracticeYear(Integer startPracticeYear) {
        this.startPracticeYear = startPracticeYear;
    }

    public String getLanguages() {
        return languages;
    }

    public void setLanguages(String languages) {
        this.languages = languages;
    }

    public String getScheduleNotes() {
        return scheduleNotes;
    }

    public void setScheduleNotes(String scheduleNotes) {
        this.scheduleNotes = scheduleNotes;
    }

    public List<ProviderInsurance> getProviderInsurances() {
        return providerInsurances;
    }

    public void setProviderInsurances(List<ProviderInsurance> providerInsurances) {
        this.providerInsurances = providerInsurances;
    }
}
