package com.smilebrands.callcentersupport.domain;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * User: <PERSON><PERSON>
 * Date: 7/8/14
 */
@Document
public class ZipFacility {

    @Id
    private String zipCode;
    private List<Facility> facilities;

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public List<Facility> getFacilities() {
        return facilities;
    }

    public void setFacilities(List<Facility> facilities) {
        this.facilities = facilities;
    }
}
