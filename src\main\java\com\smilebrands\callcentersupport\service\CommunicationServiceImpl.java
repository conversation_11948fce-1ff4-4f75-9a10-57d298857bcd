package com.smilebrands.callcentersupport.service;

import com.smilebrands.callcentersupport.domain.CommunicationTemplate;
import com.smilebrands.callcentersupport.domain.PhoneNumber;
import com.smilebrands.callcentersupport.domain.mandrill.SendRequest;
import com.smilebrands.callcentersupport.domain.mandrill.To;
import com.smilebrands.callcentersupport.repo.JdbcRepository;
import com.smilebrands.callcentersupport.repo.MongoRepository;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import com.twilio.sdk.TwilioRestClient;
import com.twilio.sdk.TwilioRestException;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.MailParseException;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.mail.javamail.MimeMessagePreparator;
import org.springframework.stereotype.Service;
import org.springframework.ui.ModelMap;

import javax.annotation.PostConstruct;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by phongpham on 7/14/14.
 */
@Service
public class CommunicationServiceImpl extends BaseService implements CommunicationService{

    public static JavaMailSenderImpl mailSender  = new JavaMailSenderImpl();


    private static final String SMS_STATUS_CALLBACK = "https://secure.smilebrands.com/communicator/twilio-sms-inbound";
    private static final String SMS_FROM_FORMAT = "(714) 352-0016";

    private static final String SMS_FOOTER = "Please do not reply as this is an unmonitored number.  To contact us, please call #NUMBER#.";

    /** Twilio Client. **/
    private static TwilioRestClient client = new TwilioRestClient("**********************************", "da262f643a732e02e0e4031469f2d905");

    public static String DEVELOPER_EMAIL = "<EMAIL>";

    public static String CISCO_ADMIN_EMAIL = "";


//    public static final String DEVELOPER_EMAIL = "<EMAIL>";

    @Autowired
    private CallService callService;

    @Autowired
    private MandrillService mandrillService;

    @Autowired
    private AmazonSesService amazonSesService;

    @Autowired
    private JdbcRepository jdbcRepository;

    @Autowired
    protected MongoRepository mongoRepository;

    @PostConstruct
    public void init(){
        updateEmailRecipients();
    }

    static{
        // -- initialize the email sender smpt address.
        mailSender.setHost("smtprelay.bnd.corp");
    }

    @Override
    public boolean sendNotificationViaEmail(String emailAddress, String message, String subject) {
        boolean result = true;
        mailSender.send(new MimeMessagePreparatorImpl(null, emailAddress, message, subject, null));
        return result;
    }

    @Override
    public boolean sendNotificationViaEmailAsPlainText(String emailAddress, String message, String subject) {
        boolean result = true;
        if(emailAddress.indexOf(",") != -1){
            mailSender.send(new MimeMessagePreparatorImpl(emailAddress.split(","), message, subject, null, false));
        }else {
            mailSender.send(new MimeMessagePreparatorImpl(null, emailAddress, message, subject, null, false));
        }
        return result;
    }

    @Override
    public boolean sendNotificationViaEmail(String from, String emailAddress, Map model, String template, String subject) {
        boolean result = true;
        String body = VelocityUtils.convert(model, template);
        try {
            mailSender.send(new MimeMessagePreparatorImpl(from, emailAddress, body, subject, null));
        } catch (MailParseException mpe) {
            logger.error(mpe.getMessage(), mpe);
        }

        return result;
    }

    @Override
    public boolean sendNotificationViaEmail(String emailAddress, Map model, String template, String subject, String attachment, boolean deleteAttachment) {
        boolean result = true;
        try {
            String body = VelocityUtils.convert(model, template);
            File file = new File(attachment);
            mailSender.send(new MimeMessagePreparatorImpl(null, emailAddress, body, subject, file));

            // -- delete the attachment after it's been sent.
            if (file.exists() && deleteAttachment) {
                file.delete();
            }

        }catch(Exception e){
            e.printStackTrace();
            result = false;
        }
        return result;
    }

    @Override
    public boolean sendNotificationViaEmail(String[] emailAddresses, Map model, String template, String subject, String attachment, boolean deleteAttachment){
        boolean result = true;
        try{
            String body = "";
            if(model != null && template != null){
                body = VelocityUtils.convert(model, template);
            }
            File file = new File(attachment);
            mailSender.send(new MimeMessagePreparatorImpl(emailAddresses, body, subject, file, true));
            if(file.exists() && deleteAttachment){
                file.delete();
            }
        }catch (Exception ex){
            ex.printStackTrace();
            result = false;
        }
        return result;
    }

    @Override
    public String sendSMS(String to, String message, Integer facilityId, Long replyTo) {
        List<String> sids = new ArrayList<String>();

        String noReply = "";
        if(replyTo != null){
            noReply = mergePhoneNumberIntoMessage(SMS_FOOTER, replyTo);
        } else if(facilityId != null){
            noReply = mergeFacilityPhoneNumberIntoMessage(SMS_FOOTER, facilityId);
        }
        List<String> messages = breakMessageToDeliverableBlocks(message, noReply);

        logger.info("The original message of " + message.length() + " created " + messages.size() + " messages"
                + " including the follow up message.");

        for (String msg : messages) {
            String sid = doSendOutboundSmsMessage(to, msg);
            /** Persist the message. **/
            if (sid != null) {
                sids.add(sid);
                logger.debug("SID: " + sid);
            }
        }
        return StringUtils.join(sids, ',');
    }

    public String sendNotification(String from, String fromName, String recipient, Map model, String template, String subject, String token){
        String body = VelocityUtils.convert(model, template);

        SendRequest sendRequest = new SendRequest();
        sendRequest.getMessage().setHtml(body);
        sendRequest.getMessage().setSubject(subject);
        sendRequest.getMessage().setFromEmail(from);
        sendRequest.getMessage().setFromName(fromName);
        sendRequest.getMessage().getTo().add(new To(recipient, ""));
        sendRequest.getMessage().getTags().add("webrequest");
        sendRequest.getMessage().getMetadata().put("request-token", token);

        return sendNotificationViaAmazon(sendRequest);
    }

    @Override
    public String sendNotificationViaMandrill(SendRequest sendRequest) {
        String result = null;

        List<LinkedHashMap> responses = mandrillService.sendNotificationEmail(sendRequest);
        for(LinkedHashMap response : responses){
            if(response.get("status").equals("sent") && response.get("reject_reason") == null){
                logger.debug("successfully sent email");
                result = (String)response.get("_id");
            }else{
                String msg = String.format("Status: [%s]  Reject Reason: [%s]", response.get("status"), response.get("reject_reason"));
                logger.debug("fail to send email with msg: {}", msg);
            }
        }
        return result;
    }

    @Override
    public String sendNotificationViaAmazon(SendRequest sendRequest) {
        String result = null;

        List<LinkedHashMap> responses = amazonSesService.sendNotificationEmail(sendRequest);
        for(LinkedHashMap response : responses){
            if(response.get("status").equals("sent") && response.get("reject_reason") == null){
                logger.debug("successfully sent email");
                result = (String)response.get("_id");
            }else{
                String msg = String.format("Status: [%s]  Reject Reason: [%s]", response.get("status"), response.get("reject_reason"));
                logger.debug("fail to send email with msg: {}", msg);
            }
        }
        return result;
    }

    @Override
    public void sendApplicationErrorToDeveloper(Integer employeeNumber, Exception exception, HttpServletRequest request, String requestBody, String subject) {

        String emailAddress = DEVELOPER_EMAIL;
        String url = request.getRequestURL() + "?" + (request.getQueryString() != null ? request.getQueryString() : "");
        String body = "An error occurred on [" + VelocityUtils.NODE_NAME + "] ";
        if(employeeNumber != null && !employeeNumber.equals(0)){
            body += "when employee[" + employeeNumber + "] tried to call<br/>";
        }else{
            body += "for request<br/>";
        }
        body += "(" + request.getMethod() + ") <a href=" + url + ">" + url + "</a><br/><br/>";
        Map<String, String[]> map = request.getParameterMap();
        String parameters = "";
        boolean isFromSecure = url.indexOf("secure") != -1;
        boolean isFromProd = url.indexOf("cti") != -1;
        for(String key : map.keySet()){
            Object value = map.get(key);
            if(value instanceof ModelMap){
                continue;
            }else if(value instanceof String[]){
                String tmp = "";
                for(int i=0; i<((String[])value).length; i++){
                    tmp += ((String[])value)[i] + ",";
                }
                value = tmp.substring(0, tmp.length()-1);
            }
            parameters += "&nbsp&nbsp&nbsp&nbsp" + key + ":&nbsp" + value + "<br/>";
        }
        body += "Parameters: <br/>" + parameters + "<br/><br/>";

        if(request.getMethod().equalsIgnoreCase("POST")){
            body += "Body: <br/>" + requestBody + "<br/><br/>";
        }


        body += "Exception:<br/> " + exception.getMessage() + "<br/><br/>";
        if(isFromProd && !isFromSecure){
//            emailAddress += ",<EMAIL>";
//            emailAddress += ",<EMAIL>";
            emailAddress += "," + CISCO_ADMIN_EMAIL;
        }

        String stackTrace = ExceptionUtils.getStackTrace(exception);
        body += "Stack Trace:<br/>" + stackTrace;

        mailSender.send(new MimeMessagePreparatorImpl(emailAddress.split(","), body, subject, null, true));
    }

    @Override
    public void updateEmailRecipients(){
        DEVELOPER_EMAIL = getRecipients("CTI_APP_ERROR");
        CISCO_ADMIN_EMAIL = getRecipients("CTI_CISCO_ERROR");

        logger.debug("DEV EMAIL: {}", DEVELOPER_EMAIL);
        logger.debug("Cisco Email: {}", CISCO_ADMIN_EMAIL);
    }

    @Override
    public CommunicationTemplate getCommunicationTemplateById(Long templateId) {
        return mongoRepository.getActiveCommunicationTemplateById(templateId);
    }

    private String getRecipients(String reportType){
        List<String> devRecipients = jdbcRepository.getEmailByReport(reportType);
        String result = "";
        for(int i=0; i<devRecipients.size(); i++){
            String r = devRecipients.get(i);
            if(!r.contains("@")){
                r += "@smilebrands.com";
            }
            result += r;
            if(i < devRecipients.size() - 1){
                result += ",";
            }
        }
        if(result.trim().length() == 0){
            result = "<EMAIL>";
        }
        return result;
    }

    /** Produces and sends outbound sms messages. **/
    private String doSendOutboundSmsMessage(String to, String message) {
        List<NameValuePair> params = new ArrayList<NameValuePair>();
        params.add(new BasicNameValuePair("To", to));
        params.add(new BasicNameValuePair("From", SMS_FROM_FORMAT));
        params.add(new BasicNameValuePair("Body", message));
        params.add(new BasicNameValuePair("StatusCallback", SMS_STATUS_CALLBACK));

        try {
            // send the text message.
            return client.getAccount().getMessageFactory().create(params).getSid();

        } catch (TwilioRestException e) {
            e.printStackTrace();
            return null;
        }
    }

    private List<String> breakMessageToDeliverableBlocks(String originalMessage, String noReply){
        List<String> messages = breakInto1600CharTextMessages(originalMessage);
        if(!StringUtils.isBlank(noReply)){
            // -- if the last message is short, append the no reply message.  else add it to the list
            if (messages.get(messages.size()-1).length() + noReply.length() < 1599) {
                String _m = messages.remove(messages.size()-1);
                messages.add(_m + " " + noReply);
            } else {
                messages.add(noReply);
            }
        }
        return messages;
    }

    /** Break original text into 1600 blocks. Prevents adding broken words to the end of char blocks. **/
    private List<String> breakInto1600CharTextMessages(String originalMessage) {
        String chunks[] = originalMessage.split(" ");
        List<String> messages = new ArrayList<String>();
        StringWriter sw = new StringWriter();

        for (int x=0; x < chunks.length; x++) {
            String _s = chunks[x];

            // -- if this chunk will overflow the 1600 limit, add the existing writer and create a new one.
            if (sw.toString().length() + _s.length() >= 1599) {
                messages.add(sw.toString());
                sw = new StringWriter();
            }

            sw.append(_s + " ");
        }
        messages.add(sw.toString());

        return messages;
    }

    private String mergeFacilityPhoneNumberIntoMessage(String originalMessage, Integer facilityId) {
        String _message = "your local office";
        PhoneNumber phoneNumber = mongoRepository.findPhoneNumberByFacility(facilityId, "ML");

        if (phoneNumber != null) {
            String mergedMsg = mergePhoneNumberIntoMessage(originalMessage, phoneNumber.getPhoneNumber());
            if(!StringUtils.isBlank(mergedMsg)){
                return mergedMsg;
            }
        }

        return originalMessage.replaceAll("#NUMBER#", _message);
    }

    /** Merge Phone Number. **/
    private String mergePhoneNumberIntoMessage(String originalMessage, Long phoneNumber) {
        if (phoneNumber != null && phoneNumber.toString().length() == 10) {
            String number = phoneNumber.toString();
            String _number = "(" + number.substring(0, 3) + ") " + number.substring(3,6) + "-" + number.substring(6);

            return originalMessage.replaceAll("#NUMBER#", _number);
        }
        return "";
    }

    /** Inner Class to construct a HTML email. **/
    private class MimeMessagePreparatorImpl implements MimeMessagePreparator {

        private String body;
        private String from;
        private String[] emailAddresses;
        private String subject;
        private File attachment;
        private boolean html = true;

        public MimeMessagePreparatorImpl(String from, String emailAddress, String body, String subject, File attachment) {
            this.from = from;
            if(emailAddress != null && emailAddress.contains(",")){
                this.emailAddresses = emailAddress.split(",");
            }else{
                this.emailAddresses = new String[]{emailAddress};
            }
            this.body = body;
            this.subject = subject;
            this.attachment = attachment;
        }

        public MimeMessagePreparatorImpl(String from, String emailAddress, String body, String subject, File attachment, boolean html) {
            this.from = from;
            if(emailAddress != null && emailAddress.contains(",")){
                this.emailAddresses = emailAddress.split(",");
            }else{
                this.emailAddresses = new String[]{emailAddress};
            }
            this.body = body;
            this.subject = subject;
            this.attachment = attachment;
            this.html = html;
        }

        public MimeMessagePreparatorImpl(String[] emailAddresses, String body, String subject, File attachment, boolean html) {
            this.emailAddresses = emailAddresses;
            this.body = body;
            this.subject = subject;
            this.attachment = attachment;
            this.html = html;
        }

        public void prepare(MimeMessage mimeMessage) throws MessagingException {
            MimeMessageHelper message = new MimeMessageHelper(mimeMessage, true, "UTF-8");
            if (from == null || from.equals("")) {
                message.setFrom("<EMAIL>");
            } else {
                message.setFrom(from);
            }
            message.setTo(emailAddresses);
            message.setSubject(subject);
            message.setText(body, html);

            if (attachment != null) {
                message.addAttachment(attachment.getName(), attachment);
            }
        }
    }
}
