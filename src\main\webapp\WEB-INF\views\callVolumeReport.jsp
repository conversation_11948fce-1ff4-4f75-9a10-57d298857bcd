<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd" >
<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<%@ taglib uri="../customTag.tld" prefix="custom" %>

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Smile Brands Call Center - Phone Number Tester</title>
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/bootstrap.min.css" >
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/style/redmond/jquery-ui-1.8.20.custom.css" />
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/call-template.css" >
</head>
<body>
    <input type="text" style="display:none;" id="reportType" value="${reportType}"/>
    <input type="text" style="display:none;" id="agentId" value="${agentId}"/>
    <input type="text" style="display:none;" id="reportDate" value="${reportDate}"/>

    <div class="modal fade" id="compareCallVolume" tabindex="-1" role="dialog" aria-labelledby="compareCallVolumeModalLabel" aria-hidden="true" style="top:100px;">
        <div class="modal-dialog" style="width: 900px; height: 500px;">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 class="modal-title" id="compareCallVolumeModalLabel">Call Volume Trend for </h4>
                </div>
                <div class="modal-body" style="padding-bottom:0px;">
                    <div class="progress" style="display:none; width: 50%; margin-left: 25%">
                        <div class="progress-bar progress-bar-striped active"  role="progressbar" aria-valuenow="45" aria-valuemin="0" aria-valuemax="100" style="width: 100%">
                            <span class="progress-message">Please wait while loading data...</span>
                        </div>
                    </div>
                    <div id="compareChartDiv" style="width: 850px height: 450px;">
                        <canvas id="compareChartCanvas" width="800" height="400" style="width:800px; height: 400px;"></canvas>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <br/>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h1 class="panel-title gray">Volume Call Report</h1>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-1">Load For:</div>
                    <div class="col-md-3">
                        <input class="agent-call-date report-date" name="datepicker" maxDate="0"></input>
                    </div>
                    <div class="col-md-3">
                        <button id="exportBtn" class="btn btn-primary">Export</button>
                    </div>
                    <div class="progress report-progress" style="display:none; width: 40%;">
                        <div class="progress-bar progress-bar-striped active"  role="progressbar" aria-valuenow="45" aria-valuemin="0" aria-valuemax="100" style="width: 100%">
                            <span class="progress-message">Please wait while syncing Agent Call with Call Log...</span>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 col-md-offset-1 call-center-volume">
                        <table class="table">
                            <thead>
                                <th>Call Center</th>
                                <th>Call Count</th>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-md-5 call-center-volume-chart">
                        <canvas/>
                    </div>
                </div>
                <hr/>
                <div class="row">
                    <div class="col-md-6 col-md-offset-3 call-center-report-control" style="display: none;">
                        <div class="btn-toolbar" role="toolbar">
                            <div class="btn-group">
                                <button type="button" class="btn btn-default report" reportType="phoneType">Phone Type Report</button>
                            </div>
                            <div class="btn-group">
                                <button type="button" class="btn btn-default report" reportType="numberPress">Number Press Report</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row padding-10">
                    <div class="col-md-6 col-md-offset-1 call-center-report-detail">
                        <table class="table table-striped">
                        </table>
                    </div>
                    <div class="col-md-5 call-center-detail-chart">
                        <canvas/>
                    </div>
                </div>

                <table id="inboundCallSummaryList" class="table table-striped" style="display:none;">
                    <thead>
                        <tr>
                            <th>Call Center Name<span class="glyphicon padding-left-5"></span></th>
                            <th>Phone Type<span class="glyphicon padding-left-5"></span></th>
                            <th>Menu Press<span class="glyphicon padding-left-5"></span></th>
                            <th>Call Count<span class="glyphicon padding-left-5"></span></th>
                            <th>Call Center Supported Count<span class="glyphicon padding-left-5"></span></th>
                            <th>New Patient Count<span class="glyphicon padding-left-5"></span></th>
                            <th>Existing Patient Count<span class="glyphicon padding-left-5"></span></th>
                            <th>No PSR Count<span class="glyphicon padding-left-5"></span></th>
                            <th>No CSR Count<span class="glyphicon padding-left-5"></span></th>
                            <th>Transfer Office Count<span class="glyphicon padding-left-5"></span></th>
                            <th>Office Office Count<span class="glyphicon padding-left-5"></span></th>
                            <th>PSR Pop Count<span class="glyphicon padding-left-5"></span></th>
                            <th>CSR Pop Count<span class="glyphicon padding-left-5"></span></th>
                            <th>ANI Recognition Count<span class="glyphicon padding-left-5"></span></th>
                            <th>CSR PSR Count<span class="glyphicon padding-left-5"></span></th>
                            <th>Query Timeout Count<span class="glyphicon padding-left-5"></span></th>
                            <th>Search not Found Count<span class="glyphicon padding-left-5"></span></th>
                        </tr>
                    </thead>
                    <tbody>
                        <c:forEach var="ics" items="${inboundCallSummaryList}">
                                <tr callCenterName="${ics.summaryId.callCenterName}">
                                    <td name="callCenterName" value="${ics.summaryId.callCenterName}">${ics.summaryId.callCenterName}</td>
                                    <td name="phoneType" displayName="Phone Type" value="${ics.summaryId.phoneType}">${ics.summaryId.phoneType}</td>
                                    <td name="menuPress" displayName="Number Press" value="${ics.summaryId.menuPress}">${ics.summaryId.menuPress}</td>
                                    <td name="callCount" value="${ics.value.callCount}">${ics.value.callCount}</td>
                                    <td name="supportedCount" displayName="Call Center Supported" eventCount="true" value="${ics.value.supportedCount}">${ics.value.supportedCount}</td>
                                    <td name="newPatCount" displayName="New Patient" eventCount="true" value="${ics.value.newPatCount}">${ics.value.newPatCount}</td>
                                    <td name="extPatCount" displayName="Existing Patient" eventCount="true" value="${ics.value.extPatCount}">${ics.value.extPatCount}</td>
                                    <td name="noPSRCount" displayName="No PSR" eventCount="true" value="${ics.value.noPSRCount}">${ics.value.noPSRCount}</td>
                                    <td name="noCSRCount" displayName="No CSR" eventCount="true" value="${ics.value.noCSRCount}">${ics.value.noCSRCount}</td>
                                    <td name="trnOffCount" displayName="Transfer to Office" eventCount="true" value="${ics.value.trnOffCount}">${ics.value.trnOffCount}</td>
                                    <td name="offOffCount" displayName="Office to Office" eventCount="true" value="${ics.value.offOffCount}">${ics.value.offOffCount}</td>
                                    <td name="psrPopCount" displayName="PSR Pop" eventCount="true" value="${ics.value.psrPopCount}">${ics.value.psrPopCount}</td>
                                    <td name="csrPopCount" displayName="CSR Pop" eventCount="true" value="${ics.value.csrPopCount}">${ics.value.csrPopCount}</td>
                                    <td name="aniRecCount" displayName="ANI Recognition" eventCount="true" value="${ics.value.aniRecCount}">${ics.value.aniRecCount}</td>
                                    <td name="csrPsrCount" displayName="CSR - PSR" eventCount="true" value="${ics.value.csrPsrCount}">${ics.value.csrPsrCount}</td>
                                    <td name="queryTimeoutCount" displayName="Query Timeout" eventCount="true" value="${ics.value.queryTimeoutCount}">${ics.value.queryTimeoutCount}</td>
                                    <td name="searchNFCount" displayName="Search not Found" eventCount="true" value="${ics.value.searchNotFoundCount}">${ics.value.searchNotFoundCount}</td>
                                    <td name="transferToRDICount" displayName="Transfer to RDI" eventCount="true" value="${ics.value.transferToRDI}">${ics.value.transferToRDI}</td>
                                    <td name="receivedByRDICount" displayName="Received by RDI" eventCount="true" value="${ics.value.receivedByRDI}">${ics.value.receivedByRDI}</td>
                                    <td name="requestCallbackCount" displayName="Request to call back" eventCount="true" value="${ics.value.requestCallback}">${ics.value.requestCallback}</td>
                                    <td name="handledCallbackCount" displayName="Handle call back request" eventCount="true" value="${ics.value.handledCallback}">${ics.value.handledCallback}</td>
                                </tr>
                        </c:forEach>
                    </tbody>
                </table>

            </div>
        </div>
    </div>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-1.7.2.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-ui-1.8.20.custom.min.js"></script>
    <script src="${pageContext.request.contextPath}/bootstrap/js/bootstrap.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/chart/Chart.js"></script>

    <script type="text/javascript" src="${pageContext.request.contextPath}/script/sbi-script.js?_id=${buildNumber}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/reportActions.js?_id=${buildNumber}"></script>
</body>
</html>