package com.smilebrands.callcentersupport.domain;

import com.smilebrands.callcentersupport.domain.helper.DateIsoDeSerializer;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * User: <PERSON><PERSON>
 * Date: 6/26/14
 */
@Document
public class AgentCall implements Serializable {

    @Id
    private String uuid;
    private String sessionId;
    private Long ani;
    private Long dnis;
    private String callCenterName;
    private String phoneType;
    private Date screenPopOpenDateTime;
    private Date screenPopCloseDateTime;
    private Integer employeeNumber;
    private List<Integer> agentEmployeeNumbers = new ArrayList<Integer>();
    private List<LoadedEmployee> loadedEmployees = new ArrayList<LoadedEmployee>();
    private Integer currentFacilityId;
    private List<LoadedFacility> loadedFacilities = new ArrayList<LoadedFacility>();
    private Long callerPhoneNumber;
    private Date phoneNumberLinkDateTime;
    private List<Patient> patients = new ArrayList<Patient>();

    private String screenType;

    private String callRegisteredDate;
    private String callRegisteredTime;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Integer getEmployeeNumber() {
        return employeeNumber;
    }

    public void setEmployeeNumber(Integer employeeNumber) {
        this.employeeNumber = employeeNumber;
    }

    public List<Integer> getAgentEmployeeNumbers() {
        return agentEmployeeNumbers;
    }

    public void setAgentEmployeeNumbers(List<Integer> agentEmployeeNumbers) {
        this.agentEmployeeNumbers = agentEmployeeNumbers;
    }

    public List<LoadedEmployee> getLoadedEmployees() {
        return loadedEmployees;
    }

    public void setLoadedEmployees(List<LoadedEmployee> loadedEmployees) {
        this.loadedEmployees = loadedEmployees;
    }

    public List<Patient> getPatients() {
        return patients;
    }

    public void setPatients(List<Patient> patients) {
        this.patients = patients;
    }

    public Date getScreenPopOpenDateTime() {
        return screenPopOpenDateTime;
    }

    public void setScreenPopOpenDateTime(Date screenPopOpenDateTime) {
        this.screenPopOpenDateTime = screenPopOpenDateTime;
    }

    public Date getScreenPopCloseDateTime() {
        return screenPopCloseDateTime;
    }

    public void setScreenPopCloseDateTime(Date screenPopCloseDateTime) {
        this.screenPopCloseDateTime = screenPopCloseDateTime;
    }

    public List<LoadedFacility> getLoadedFacilities() {
        return loadedFacilities;
    }

    public void setLoadedFacilities(List<LoadedFacility> loadedFacilities) {
        this.loadedFacilities = loadedFacilities;
    }

    public Integer getCurrentFacilityId() {
        return currentFacilityId;
    }

    public void setCurrentFacilityId(Integer currentFacilityId) {
        this.currentFacilityId = currentFacilityId;
    }

    public Long getAni() {
        return ani;
    }

    public void setAni(Long ani) {
        this.ani = ani;
    }

    public Long getDnis() {
        return dnis;
    }

    public void setDnis(Long dnis) {
        this.dnis = dnis;
    }

    public Long getCallerPhoneNumber() {
        return callerPhoneNumber;
    }

    public void setCallerPhoneNumber(Long callerPhoneNumber) {
        this.callerPhoneNumber = callerPhoneNumber;
    }

    public String getPhoneType() {
        return phoneType;
    }

    public void setPhoneType(String phoneType) {
        this.phoneType = phoneType;
    }

    public Date getPhoneNumberLinkDateTime() {
        return phoneNumberLinkDateTime;
    }

    public void setPhoneNumberLinkDateTime(Date phoneNumberLinkDateTime) {
        this.phoneNumberLinkDateTime = phoneNumberLinkDateTime;
    }

    public String getScreenType() {
        return screenType;
    }

    public void setScreenType(String screenType) {
        this.screenType = screenType;
    }

    public String getCallRegisteredDate() {
        return callRegisteredDate;
    }

    public void setCallRegisteredDate(String callRegisteredDate) {
        this.callRegisteredDate = callRegisteredDate;
    }

    public String getCallRegisteredTime() {
        return callRegisteredTime;
    }

    public void setCallRegisteredTime(String callRegisteredTime) {
        this.callRegisteredTime = callRegisteredTime;
    }

    public String getCallCenterName() {
        return callCenterName;
    }

    public void setCallCenterName(String callCenterName) {
        this.callCenterName = callCenterName;
    }

    public Patient findExistingPatient(Long patientId, Integer facilityId, Integer sequence, Integer empEntered){
        Patient patient = null;
        if(this.patients != null && this.patients.size() > 0){
            for(Patient existing : patients){
                if(existing.getSequence().equals(sequence) && existing.getPatientId().equals(patientId)
                        && existing.getFacilityId().equals(facilityId) && existing.getEmpEntered().equals(empEntered)){
                    patient = existing;
                    break;
                }
            }
        }
        return patient;
    }

    public LoadedEmployee findLoadedEmployee(Integer employeeNumber, String screenType){
        LoadedEmployee employee = null;
        if(this.getLoadedEmployees() != null && this.getLoadedEmployees().size() > 0){
            for(LoadedEmployee e : this.getLoadedEmployees()){
                if(e.getEmployeeNumber().equals(employeeNumber) && e.getScreenType().equalsIgnoreCase(screenType)){
                    employee = e;
                    break;
                }
            }
        }
        return  employee;
    }
}
