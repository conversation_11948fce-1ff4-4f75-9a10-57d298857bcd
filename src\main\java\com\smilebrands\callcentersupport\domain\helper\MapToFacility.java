package com.smilebrands.callcentersupport.domain.helper;

import com.smilebrands.callcentersupport.domain.Facility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * User: <PERSON><PERSON>
 * Date: 7/8/14
 */
public class MapToFacility {

    final static Logger logger = LoggerFactory.getLogger(MapToFacility.class);

    public static Facility convert(Map<String, Object> officeMap) {
        Facility facility = new Facility();

        facility.setFacilityId(Integer.valueOf(officeMap.get("FACILITY_ID").toString()));
        facility.setName(officeMap.get("FACILITY_NAME").toString());
        facility.setAddress(officeMap.get("ADDRESS").toString());
        facility.setCity(officeMap.get("CITY").toString());
        facility.setState(officeMap.get("STATE").toString());
        facility.setZip(officeMap.get("ZIPCODE").toString());
        if(officeMap.get("DESCRIPTION") != null){
            facility.setAddressDescription(officeMap.get("DESCRIPTION").toString());
        }

        if (officeMap.get("DISTANCE") != null ) {
            try {
                facility.setDistance(Integer.valueOf(officeMap.get("DISTANCE").toString()));
            } catch (NumberFormatException nfx) {
                logger.warn(facility.getFacilityId() + " does not have a valid distance!  Value[" + officeMap.get("DISTANCE") + "].");
            }
        }

        if (officeMap.get("TRAVEL_TIME") != null) {
            try {
                facility.setTravelTime(Integer.valueOf(officeMap.get("TRAVEL_TIME").toString()));
            } catch (NumberFormatException nfx) {
                logger.warn(facility.getFacilityId() + " does not have a valid travel time!  Value[" + officeMap.get("TRAVEL_TIME") + "].");
            }
        }

        if (officeMap.get("PHONE") != null) {
            facility.setPhoneNumber(Long.valueOf(officeMap.get("PHONE").toString()));
        }

        if (officeMap.get("FAX") != null) {
            facility.setFaxNumber(Long.valueOf(officeMap.get("FAX").toString()));
        }
        if(officeMap.get("LIBERTY_SUPPORTED") != null){
            facility.setLibertySupported(new Boolean(officeMap.get("LIBERTY_SUPPORTED").toString()));
        }else{
            facility.setLibertySupported(false);
        }
        if(officeMap.get("BRAND_NAME") != null){
            facility.setBrandName(officeMap.get("BRAND_NAME").toString());
        }

        return facility;
    }
}
