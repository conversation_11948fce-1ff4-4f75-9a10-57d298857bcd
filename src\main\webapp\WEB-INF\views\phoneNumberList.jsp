<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd" >
<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<%@ taglib uri="../customTag.tld" prefix="custom" %>

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Smile Brands Call Center - Phone Number Tester</title>
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/bootstrap.min.css" >
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/style/redmond/jquery-ui-1.8.20.custom.css" />
    <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/call-template.css" >
</head>
<body>

    <div class="container">
        <br/>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h1 class="panel-title gray">Phone Number List</h1>
            </div>
            <div class="panel-body">
                <div class="row filter-cmp">
                    <div class="col-md-1">Filter By:</div>
                    <div class="col-md-3">
                        <select filterOptionFor="phoneNumberList" style="width:100%;">
                            <option value="phoneNumber">Phone Number</option>
                            <option value="brandName">Brand Name</option>
                            <option value="facilityId">Facility ID</option>
                            <option value="campaignName">Campaign Name</option>
                            <option value="phoneType">Phone Type</option>
                            <option value="callCenterSupported">Call Center Supported</option>
                            <option value="callCenterName">Call Center Name</option>
                            <option value="callCenterQueueName">Call Center Queue Name</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <input type="text" filterInputFor="phoneNumberList" style="width:90%;"/>
                        <span class="glyphicon glyphicon-remove pointer" removeFilterFor="phoneNumberList"/>
                    </div>
                </div>
                <table id="phoneNumberList" class="table table-striped">
                    <thead>
                        <tr>
                            <th sortField="phoneNumber" class="pointer">Phone Number<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="brandName" class="pointer">Brand Name<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="facilityId" class="pointer">Facility ID<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="campaignName" class="pointer">Campaign Name<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="phoneType" class="pointer">Phone Type<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="callCenterSupported" class="pointer">Call Center Supported<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="callCenterName" class="pointer">Call Center<span class="glyphicon padding-left-5"></span></th>
                            <th sortField="callCenterQueueName" class="pointer">Call Center Queue<span class="glyphicon padding-left-5"></span></th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <c:forEach var="number" items="${phoneNumberList}">

                            <tr
                                <c:if test="${number.active == false}">
                                    style="text-decoration:line-through;"
                                </c:if>
                            >
                                <td name="phoneNumber" value="${number.phoneNumber}">${number.phoneNumber}</td>
                                <td name="brandName" value="${number.brandName}">${number.brandName}</td>
                                <td name="facilityId" value="${number.facilityId}">${number.facilityId}</td>
                                <td name="campaignName" value="${number.campaignName}">${number.campaignName}</td>
                                <td name="phoneType" value="${number.phoneType}">${number.phoneType}</td>
                                <td name="callCenterSupported" value="${number.callCenterSupported}">${number.callCenterSupported}</td>
                                <td name="callCenterName" value="${number.callCenterName}">${number.callCenterName}</td>
                                <td name="callCenterQueueName" value="${number.callCenterQueueName}">${number.callCenterQueueName}</td>
                                <td><a class="glyphicon glyphicon-pencil pointer edit-phoneNumber" data-toggle="tooltip" title="Edit" style="color:#333;"
                                        href="${pageContext.request.contextPath}/secure/phoneNumberEdit?phoneNumber=${number.phoneNumber}" target="_blank">
                                    </a>
                                </td>
                            </tr>

                        </c:forEach>
                    </tbody>
                </table>

            </div>
        </div>
    </div>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-1.7.2.min.js"></script>
    <script src="${pageContext.request.contextPath}/bootstrap/js/bootstrap.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/sbi-script.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/configurationActions.js"></script>
</body>
</html>