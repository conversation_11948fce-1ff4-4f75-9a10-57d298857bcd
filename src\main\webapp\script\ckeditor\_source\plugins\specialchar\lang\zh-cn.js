﻿/*
Copyright (c) 2003-2012, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/

CKEDITOR.plugins.setLang( 'specialchar', 'zh-cn',
{
	euro: '欧元符号',
	lsquo: '左单引号',
	rsquo: '右单引号',
	ldquo: '左双引号',
	rdquo: '右双引号',
	ndash: '短划线',
	mdash: '破折号',
	iexcl: '竖翻叹号',
	cent: '分币标记',
	pound: '英镑标记',
	curren: '货币标记',
	yen: '日元标记',
	brvbar: '间断条',
	sect: '节标记',
	uml: '分音符',
	copy: '版权所有标记',
	ordf: '阴性顺序指示符',
	laquo: '左指双尖引号',
	not: '非标记',
	reg: '注册标记',
	macr: '长音符',
	deg: '度标记',
	sup2: '上标二',
	sup3: '上标三',
	acute: '锐音符',
	micro: '微符',
	para: '段落标记',
	middot: '中间点',
	cedil: '下加符',
	sup1: '上标一',
	ordm: '阳性顺序指示符',
	raquo: '右指双尖引号',
	frac14: '普通分数四分之一',
	frac12: '普通分数二分之一',
	frac34: '普通分数四分之三',
	iquest: '竖翻问号',
	Agrave: '带抑音符的拉丁文大写字母 A',
	Aacute: '带锐音符的拉丁文大写字母 A',
	Acirc: '带扬抑符的拉丁文大写字母 A',
	Atilde: '带颚化符的拉丁文大写字母 A',
	Auml: '带分音符的拉丁文大写字母 A',
	Aring: '带上圆圈的拉丁文大写字母 A',
	AElig: '拉丁文大写字母 Ae',
	Ccedil: '带下加符的拉丁文大写字母 C',
	Egrave: '带抑音符的拉丁文大写字母 E',
	Eacute: '带锐音符的拉丁文大写字母 E',
	Ecirc: '带扬抑符的拉丁文大写字母 E',
	Euml: '带分音符的拉丁文大写字母 E',
	Igrave: '带抑音符的拉丁文大写字母 I',
	Iacute: '带锐音符的拉丁文大写字母 I',
	Icirc: '带扬抑符的拉丁文大写字母 I',
	Iuml: '带分音符的拉丁文大写字母 I',
	ETH: '拉丁文大写字母 Eth',
	Ntilde: '带颚化符的拉丁文大写字母 N',
	Ograve: '带抑音符的拉丁文大写字母 O',
	Oacute: '带锐音符的拉丁文大写字母 O',
	Ocirc: '带扬抑符的拉丁文大写字母 O',
	Otilde: '带颚化符的拉丁文大写字母 O',
	Ouml: '带分音符的拉丁文大写字母 O',
	times: '乘号',
	Oslash: '带粗线的拉丁文大写字母 O',
	Ugrave: '带抑音符的拉丁文大写字母 U',
	Uacute: '带锐音符的拉丁文大写字母 U',
	Ucirc: '带扬抑符的拉丁文大写字母 U',
	Uuml: '带分音符的拉丁文大写字母 U',
	Yacute: '带抑音符的拉丁文大写字母 Y',
	THORN: '拉丁文大写字母 Thorn',
	szlig: '拉丁文小写字母清音 S',
	agrave: '带抑音符的拉丁文小写字母 A',
	aacute: '带锐音符的拉丁文小写字母 A',
	acirc: '带扬抑符的拉丁文小写字母 A',
	atilde: '带颚化符的拉丁文小写字母 A',
	auml: '带分音符的拉丁文小写字母 A',
	aring: '带上圆圈的拉丁文小写字母 A',
	aelig: '拉丁文小写字母 Ae',
	ccedil: '带下加符的拉丁文小写字母 C',
	egrave: '带抑音符的拉丁文小写字母 E',
	eacute: '带锐音符的拉丁文小写字母 E',
	ecirc: '带扬抑符的拉丁文小写字母 E',
	euml: '带分音符的拉丁文小写字母 E',
	igrave: '带抑音符的拉丁文小写字母 I',
	iacute: '带锐音符的拉丁文小写字母 I',
	icirc: '带扬抑符的拉丁文小写字母 I',
	iuml: '带分音符的拉丁文小写字母 I',
	eth: '拉丁文小写字母 Eth',
	ntilde: '带颚化符的拉丁文小写字母 N',
	ograve: '带抑音符的拉丁文小写字母 O',
	oacute: '带锐音符的拉丁文小写字母 O',
	ocirc: '带扬抑符的拉丁文小写字母 O',
	otilde: '带颚化符的拉丁文小写字母 O',
	ouml: '带分音符的拉丁文小写字母 O',
	divide: '除号',
	oslash: '带粗线的拉丁文小写字母 O',
	ugrave: '带抑音符的拉丁文小写字母 U',
	uacute: '带锐音符的拉丁文小写字母 U',
	ucirc: '带扬抑符的拉丁文小写字母 U',
	uuml: '带分音符的拉丁文小写字母 U',
	yacute: '带抑音符的拉丁文小写字母 Y',
	thorn: '拉丁文小写字母 Thorn',
	yuml: '带分音符的拉丁文小写字母 Y',
	OElig: '拉丁文大写连字 Oe',
	oelig: '拉丁文小写连字 Oe',
	'372': '带扬抑符的拉丁文大写字母 W',
	'374': '带扬抑符的拉丁文大写字母 Y',
	'373': '带扬抑符的拉丁文小写字母 W',
	'375': '带扬抑符的拉丁文小写字母 Y',
	sbquo: '单下 9 形引号',
	'8219': '单高横翻 9 形引号',
	bdquo: '双下 9 形引号',
	hellip: '水平省略号',
	trade: '商标标志',
	'9658': '实心右指指针',
	bull: '加重号',
	rarr: '向右箭头',
	rArr: '向右双线箭头',
	hArr: '左右双线箭头',
	diams: '实心方块纸牌',
	asymp: '约等于'
});
