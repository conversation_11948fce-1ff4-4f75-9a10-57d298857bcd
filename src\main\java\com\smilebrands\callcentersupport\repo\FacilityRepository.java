package com.smilebrands.callcentersupport.repo;

import com.smilebrands.callcentersupport.domain.Employee;
import com.smilebrands.callcentersupport.domain.Facility;
import com.smilebrands.callcentersupport.domain.Provider;
import com.smilebrands.callcentersupport.domain.ProviderInsurance;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * User: <PERSON><PERSON>
 * Date: 6/25/14
 */
public interface FacilityRepository {

    public Facility findFacilityByFacilityId(Integer facilityId);

    public List<Facility> findFacilitiesByZipCode(String zipCode);

    public List<Facility> findAllFacilities(boolean fullLoad);

    public void populateFacilityByAttributeValues(Facility facility);

    public List<Employee> findFacilityProvider(Integer facilityId);

    public void populateProviderByAttributeValues(Provider provider, Integer facilityId);

    public List<Employee> findFacilityOfficeManager(Integer facilityId);

    public ArrayList<Employee> findFacilityManagement(Integer facilityId);

    public List<ProviderInsurance> getProviderInsuranceByFacility(Integer facilityId, Integer serviceProviderId);

}
