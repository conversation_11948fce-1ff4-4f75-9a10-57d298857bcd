<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/callcenter.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/callcenter.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- Special logger for incomplete requests -->
    <appender name="REQUEST_ERRORS" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/request-errors.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/request-errors.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- Configure Jetty logging -->
    <logger name="org.eclipse.jetty" level="INFO" />
    <logger name="org.eclipse.jetty.server.HttpChannel" level="DEBUG" />
    <logger name="org.eclipse.jetty.server.HttpInput" level="DEBUG" />
    
    <!-- Configure our custom filter logging -->
    <logger name="com.smilebrands.callcenter.web.filter.IncompleteRequestFilter" level="DEBUG">
        <appender-ref ref="REQUEST_ERRORS" />
    </logger>
    <logger name="com.smilebrands.callcenter.web.error.CustomErrorHandler" level="DEBUG">
        <appender-ref ref="REQUEST_ERRORS" />
    </logger>
    
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </root>
</configuration>
