package com.smilebrands.callcentersupport.controllers;

import com.bugsnag.Bugsnag;
import com.smilebrands.callcentersupport.async.BackgroundExecutor;
import com.smilebrands.callcentersupport.service.CommunicationService;
import org.codehaus.jackson.map.DeserializationConfig;
import org.codehaus.jackson.map.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * User: <PERSON><PERSON>
 * Date: 7/9/14
 */
public class AbstractController {

    final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private BackgroundExecutor backgroundExecutor;

    @Autowired
    private CommunicationService communicationService;

    @Autowired
    private Bugsnag bugsnag;

    private String requestBody;
    public void setRequestData(String body){
        this.requestBody = body;
    }

    public Object convertJson(String json, Class clazz) {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationConfig.Feature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        try {
            return mapper.readValue(json, clazz);

        } catch (Exception e) {
            logger.error("JsonConversionException " + e.getMessage());
            e.printStackTrace();

            throw new RuntimeException("Unable to convert Json [" + json + "] to Class [" + clazz.getName() + "]<br/> Msg: " + e.getMessage()+ "!");
        }
    }

    public Cookie getCookie(HttpServletRequest request, String cookieName){
        if (request.getCookies() == null) {
            return null;
        }

        Cookie[] cookies = request.getCookies();
        Cookie result = null;
        for(int i=0; i<cookies.length; i++){
            Cookie cookie = cookies[i];
            if(cookie.getName().equals(cookieName)){
                result = cookie;
                break;
            }
        }
        return result;
    }

    public Integer getEmployeeNumberFromCookie(HttpServletRequest request){
        Integer employeeNumber = 0;
        Cookie employeeNumberCookie = getCookie(request, "APPTRACKER.EMPLOYEE_NUMBER");
        if (employeeNumberCookie != null){
            try {
                employeeNumber = Integer.valueOf(employeeNumberCookie.getValue());
            }catch(Exception ex){
                logger.debug("Fail to get employee number from cookie with value[" + employeeNumberCookie.getValue() + "].");
            }
        }
        return employeeNumber;
    }

    public void logAction(String uuid, String action, String message){
        backgroundExecutor.logAction(uuid, action, message);
    }

    @ExceptionHandler(Exception.class)
    public @ResponseBody
    ModelMap handleGenericException(Exception ex,HttpServletRequest request, HttpServletResponse response){

        ex.printStackTrace();

        String subjectSuffix = "";
        Integer employeeNumber = getEmployeeNumberFromCookie(request);
        communicationService.sendApplicationErrorToDeveloper(employeeNumber, ex, request, this.requestBody, "CTI Exception " + subjectSuffix);

        bugsnag.notify(ex);

        ModelMap map = null;
        if(ex != null && ex.getMessage() != null && ex.getMessage().equalsIgnoreCase("Access is denied")){
            try{
                response.sendError(403);
            }catch (Exception sendErrorEx){
                logger.debug("Redirecting to 403 has exception {}", sendErrorEx.getMessage());
            }
            return null;
        }else{
            if(map == null){
                map = new ModelMap();
            }
            map.addAttribute("success", false);
            map.addAttribute("message", ex.getMessage());
            return map;
        }
    }
}
