$(document).ready(function() {

    var uuid = $('#uuid').val();
    var agentId = $('#employeeNumber').val();
    var language = $('#language').val();
    var menuOptions = $('#numberPress').val();
    var screenType = $('#screenType').val();
    var isOld = $('#isOld').val();

    var alreadySetResolution = false;
    var routeToDifferentScreen = false;
    var updateCsrCall = false;

    if(isOld === 'true'){
        var a = document.createElement('script'),
            callbackName = 'ip_callback_' + (new Date().getTime());
        window[callbackName] = function(){
            if(arguments[0]){
                $.getJSON(
                    '/callcenter/register-ip',
                    {
                        ip              : arguments[0].ip,
                        uuid            : uuid,
                        employeeNumber  : agentId,
                        screenType      : screenType
                    }
                );
            }
            if(document.getElementById('check_ip_script')){
                document.getElementById('check_ip_script').remove();
            }
        };
        a.src = "http://libertyassets.bnd.corp/ip.php?format=json&callback="+callbackName;
        a.id = 'check_ip_script';
        a.type = "text/javascript";
        document.getElementsByTagName("head")[0].appendChild(a);
    }

    if(screenType == 'csr'){
        if(menuOptions > 0 && menuOptions < 6){
            $('#csrMenuSelection').val(menuOptions);
        }
        if($('#reasonCode').val()){
            $('#reasonInput').val($('#reasonCode').val());
        }
        if($('#resolutionCode').val()){
            alreadySetResolution = true;
            $('#resolutionInput').val($('#resolutionCode').val());
        }
        $.getJSON(
            '/callcenter/get-agent-call',
            {
                uuid    : uuid
            },
            function(json){
                $('#patientInfoBody').html('');
                var data    = json.data,
                    body    = $('#patientInfoBody');
                if(data){
                    for(var i=0; i<data.patients.length; i++){
                        var patient = data.patients[i];
                        if(patient.screenType == screenType && patient.empEntered == agentId){
                            SBI.addPatientForm(patient, body, false);
                        }
                    }
                }
            }
        );
    }else if($('#targetFacilityId') && $('#targetFacilityId').length && !$('#targetFacilityId').val()){
        $('#facilitySearchByZipCode').modal();
    }

    var popDateTime = SBI.formatDateTime(new Date());

    var screenPopHealthCheckInterval = setInterval(function(){
        $.ajax({
            type    : 'GET',
            url     : '/callcenter/pulse-check-screen-pop',
            data    : {
                uuid            : uuid,
                employeeNumber  : $('#employeeNumber').val(),
                screenType      : $('#screenType').val(),
                popDateTime     : popDateTime
            }
        });
    }, 1000*20);    //20 SECONDS

    var screenOpenInterval = setInterval(function(){
        clearInterval(screenOpenInterval);
        sendScreenOpenInterval();
        var count = 0;
        var afterLongOpenInterval = setInterval(function(){
            sendScreenOpenInterval();
            if(count == 10){
                clearInterval(afterLongOpenInterval);
            }else{
                count++;
            }
        }, 1000*60*10);  //10 MINUTES CHECK
    }, 1000*60*60);     //1 HOUR CHECK

    var sendScreenOpenInterval = function(){
        console.log('sendScreenOpenInterval');
        $.ajax({
            type    : 'GET',
            async   : false,
            url     : '/callcenter/screen-open-interval',
            data    : {
                uuid            : uuid,
                employeeNumber  : $('#employeeNumber').val(),
                screenType      : $('#screenType').val(),
                popDateTime     : popDateTime,
                currentDateTime : SBI.formatDateTime(new Date())
            }
        });
    };

    $('#zipCodeSearchBtn').click(function(){
        $('#facilitySearchByZipCode').modal();
    });
    $('#facilitySearchByZipCode').on('shown.bs.modal', function(evt){
        $('#fsbzInput').focus();
    });

    $('#facilitySearchByZipCode #display_list').keyup(function(event){
        var keyCode         = event.keyCode,
            list            = $(this),
            items           = list.find('.list-group-item'),
            selectedItem    = list.find('.list-group-item-info')[0],
            selectedIdx     = items.index(selectedItem),
            nextIdx         = -1,
            screenType      = $('#screenType').val();
        if(keyCode == 13){          //ENTER
            doSelectOfficeFromSearch(selectedItem);
        }else if(keyCode == 32 && screenType == 'csr'){
            var selectedCB = $(selectedItem).find('.office-selected')[0];
            if(selectedCB && selectedCB.checked){
                selectedCB.checked = false;
            }else if(selectedCB){
                var cbs = list.find('.office-selected');
                for(var i=0; i<cbs.length; i++){
                    cbs[i].checked = false;
                }
                selectedCB.checked = true;
            }
        }else if(keyCode == 38      //ARROW_UP
                || keyCode == 40){  //ARROW_DOWN
            if(selectedIdx == -1){
                nextIdx = 0;
            }else{
                if(selectedIdx == 0){
                    nextIdx = keyCode == 38 ? items.length -1 : selectedIdx + 1;
                }else if(selectedIdx == (items.length -1)){
                    nextIdx = keyCode == 38 ? selectedIdx - 1 : 0;
                }else{
                    nextIdx = keyCode == 38 ? selectedIdx - 1 : selectedIdx + 1;
                }
            }
            if(nextIdx != -1 && nextIdx < items.length){
                $(selectedItem).removeClass('list-group-item-info');
                var nextItem = $(items[nextIdx]);
                nextItem.focus();
                nextItem.addClass('list-group-item-info');
            }
        }
    });

    $('#fsbzInput').keyup(function(event){
        var value       = this.value,
            keyValue    = event.keyCode,
            displayList = $('#facilitySearchByZipCode #display_list')[0],
            position    = displayList.getAttribute('display-position');
        if((keyValue == 40 && position == 'bottom')         //ARROW DOWN
            || (keyValue == 39 && position == 'right')){   //ARROW RIGHT
            var officeDetail = $(displayList).find('.list-group-item')[0];
            if(officeDetail){
                officeDetail.focus();
                $('#facilitySearchByZipCode #display_list .list-group-item').removeClass('list-group-item-info');
                $(officeDetail).addClass('list-group-item-info');
            }
        }else if(SBI.isNumeric(value) && value.trim().length == 5){
            $.getJSON(
                '/callcenter/closest-facilities.json',
                {
                    zip             : value,
                    uuid            : uuid,
                    employeeNumber  : agentId
                },
                function(json){
                    if(json.success){
                        var list                = json.data,
                            targetFacilityId    = $('#targetFacilityId').val(),
                            result              = '',
                            screenType          = $('#screenType').val();
                        for(var i=0; i<list.length; i++){
                            var office          = list[i],
                                isCurrentTarget = office.facilityId == targetFacilityId;
                            result += '<a href="javascript:void(0);" class="list-group-item" value="' + office.facilityId + '" style="padding: 5px 15px;"><div>';
                            result += '<span class="fl">Site name:</span><span class="content fr title">' + office.name + ' (' + office.facilityId + ')</span><br/>';
                            result += '<span class="fl">  Address:</span><span class="content fr">' + office.fullAddress;
                            if(screenType == 'csr'){
                                result += '<input type="checkbox" class="office-selected fr" facilityId="' + office.facilityId + '"/>';
                                result += '<span class="glyphicon glyphicon-share csr-facility-detail-link pointer fr" style="padding-right:20px;" data-toggle="tooltip" title="Show detail." facilityId="' + office.facilityId + '"></span>';
                            }else{
                                result += (isCurrentTarget ? '<span class="glyphicon glyphicon-star fr current-facility-cls"/>' : '');
                            }
                            result += '</span><br/>';
                            result += '<span class="fl"> Distance:</span><span class="content fr phone-sp">' + office.distance + ' miles (' + $.trim(office.travelTime)+ ' minutes)</span><br/>';
                            result += '</div></a>';
                        }
                        $('#facilitySearchByZipCode #display_list').html(result);
                        registerOfficeDetailClickFromSearch();
                    }else{
                        alert(json.message);
                    }
                }
            );
        }else{
            $('#facilitySearchByZipCode #display_list').html('');
        }
    });

    var registerOfficeDetailClickFromSearch = function(){
        $('#facilitySearchByZipCode #display_list .current-facility-cls').tooltip({title: 'Current Facility', placement: 'bottom'});
        $('#facilitySearchByZipCode #display_list .list-group-item').dblclick(function(){
            doSelectOfficeFromSearch(this);
        });

        $('#facilitySearchByZipCode #display_list .list-group-item').click(function(){
            $('#facilitySearchByZipCode #display_list .list-group-item').removeClass('list-group-item-info');
            $(this).addClass('list-group-item-info');
            if($(this).find('.office-selected').attr('checked')){
                $('#facilitySearchByZipCode #display_list .office-selected').attr('checked', false);
                $(this).find('.office-selected').attr('checked', true);
            }
        });
    };

    $('#facilitySearchByZipCode #setOfficeBtn').click(function(){
        var selectedOffice = $('#facilitySearchByZipCode #display_list .list-group-item-info')[0];
        doSelectOfficeFromSearch(selectedOffice);
    });

    var doSelectOfficeFromSearch = function(selectedOffice){
        if(selectedOffice){
            var screenType = $('#screenType').val();
            if(screenType == 'csr'){
            }else{
                var facilityId = selectedOffice.getAttribute('value');
                if(facilityId){
                    var hrefArr = location.href.split('/');
                    hrefArr[hrefArr.length - 1] = facilityId;
                    routeToDifferentScreen = true;
                    location.href = hrefArr.join('/');
                }
            }
        }
    };

    $('.extra-info-cls').click(function(){
        var cmp = $(this),
            patientId   = 0,
            phoneNumber = $('#appointmentPhone').val(),
            facilityId  = $('#targetFacilityId').val(),
            popTitle    = '';
        if(cmp.hasClass('patient')){
            console.log('load extra patient using phone number %o', $('#appointmentPhone').val());
            popTitle = 'Patient Appointments associated with phone number ' +  SBI.formatPhoneNumber($('#appointmentPhone').val());
        }else if(cmp.hasClass('appointment')){
            console.log('load extra appointment for patient %o', $('#foundPatientId').val());
            popTitle = 'Additional Appointments for patient ' + $('#patientName').val();
            patientId = $('#foundPatientId').val()
        }
        $.getJSON(
            '/callcenter/appointment-search.json',
            {
                patientId   : patientId,
                phoneNumber : phoneNumber,
                facilityId  : facilityId,
            },
            function(json){
                if(json.success){
                    var list    = json.data,
                        result  = '';
                    for(var i=0; i<list.length; i++){
                        var appointment = list[i];
                        result += "<a href='javascript:void(0);' class='list-group-item' style='padding: 5px 15px;'><div>";
                        result += "<span class='fl'>Patient:</span><span class='content fr title'>" + appointment.patient.patientFullName + " (" + appointment.patient.dateOfBirth + ")</span><br/>";
                        result += "<span class='fl'>  Appointment:</span><span class='content fr'>" + appointment.formattedDateTime + " at "+ appointment.facility.name +"</span><br/>";
                        result += "</div></a>";
                    }
                    $('#appointmentSearch #appointment-display_list').html(result);
                    $('#appointmentSearchModalLabel').text(popTitle);
                    $('#appointmentSearch').modal();
                }
            }
        );
    });

    $('#libSchedulerBtn').click(function(){
        var schedulerUrl        = $('#schedulerUrl').val();
        if(schedulerUrl){
            window.open(schedulerUrl);
        }
    });

    var facilityServiceProviders = {};
    var fsp = $('.provider-info');
    for(var i=0; i<fsp.length; i++){
        var tmp = $(fsp[i]);
        facilityServiceProviders['' + tmp.attr('providerId')] = {
            providerId      : tmp.attr('providerId'),
            qsiId           : tmp.attr('qsiId'),
            providerName    : tmp.attr('providerName'),
            scheduleNote    : tmp.attr('scheduleNote'),
            gender          : tmp.attr('gender'),
            languages       : tmp.attr('languages'),
            institution     : tmp.attr('institution'),
            degreeEarned    : tmp.attr('degreeEarned'),
            degreeSuffix    : tmp.attr('degreeSuffix'),
            graduatedIn     : tmp.attr('graduatedIn'),
            startPracticeIn : tmp.attr('startPracticeIn')
        };
        tmp.detach();
    }

    var showProviderDetailInfo = function(){
        var cmp         = $(this),
            providerId  = cmp.attr('providerId'),
            providerPop = $('.popover.provider-detail'),
            location    = this.getClientRects()[0],
            provider    = facilityServiceProviders[providerId],
            scrollTop   = 0,
            scrollLeft  = 0;
        if(providerPop.attr('providerId') == providerId){
            providerPop.removeClass('in').css('display', 'none').css('top', 0).css('left', 0);
            providerPop.removeAttr('providerId');
        }else if(location && provider){
            scrollTop = $(window).scrollTop();
            scrollLeft = $(window).scrollLeft();
            providerPop.attr('providerId', providerId);
            providerPop.addClass('in');
            providerPop.find('.fsp-id').html(provider.providerId + ' - ' + provider.providerName + (provider.qsiId ? (' (QSI ID: ' + provider.qsiId + ')') : ''));
            providerPop.find('.fsp-schedule-note').html(provider.scheduleNote);
            providerPop.find('.fsp-gender').html(provider.gender);
            providerPop.find('.fsp-language').html(provider.languages);
            var educationStr = provider.institution;
            if(provider.degreeEarned){
                educationStr += '<br/>' + provider.degreeEarned;
            }
            if(provider.degreeSuffix){
                educationStr += ' (' + provider.degreeSuffix + ')';
            }
            if(provider.graduatedIn){
                educationStr += '<br/>Graduated in ' + provider.graduatedIn;
            }
            if(provider.startPracticeIn){
                var currentYear = new Date().getFullYear(),
                    year        = currentYear - provider.startPracticeIn;
                if(provider.graduatedIn){
                    educationStr += ' - ';
                }else{
                    educationStr += '<br/>';
                }
                educationStr += year + ' year' + (year > 1 ? 's ' : ' ') + 'of experience'
            }
            providerPop.find('.fsp-education').html(educationStr);
            providerPop.css('display', 'block').css('left', (location.right - cmp.width()/2) + scrollLeft - providerPop.width()/2).css('top', scrollTop + location.bottom);
        }
    };

    $('span.provider-detail').live('mouseenter', showProviderDetailInfo);

    if($('#zipCode').val() && $('#fsbzInput').length > 0){
        $('#fsbzInput').val($('#zipCode').val());
        $('#fsbzInput').triggerHandler('keyup');
    }

    $('span.provider-detail').live('mouseleave', function(){
        $('.popover.provider-detail').removeClass('in').css('display', 'none').css('top', 0).css('left', 0).removeAttr('providerId');
    });

    if($('.facility-special-services').length > 0){
        var showPopover = function () {
            $(this).popover('show');
        }
        , hidePopover = function () {
            $(this).popover('hide');
        };
        $('.facility-special-services').popover({
            trigger: 'manual'
        }).mouseenter(showPopover).mouseleave(hidePopover);
    }


//    if($('.popover').length > 0){
//        $('body').click(function(event){
//            var target = $(event.target);
//            if(!target.hasClass('provider-detail') && $('.popover.provider-detail.in').length > 0){
//                console.log('about to remove popover for provider info');
//                $('.popover.provider-detail').removeClass('in').css('display', 'none').css('top', 0).css('left', 0).removeAttr('providerId');
//            }
//        });
//    }

    /********************************************************************************************************************************************
                    BEGIN CALL DETAIL LOGIC
    **********************************************************************************************************************************************/

    var needToLoadAgentCall = true;
    var facilityIdSelect = $('#facilityIDs');
    SBI.facilityIdsList = [];
    if(facilityIdSelect.length > 0){
        var facIds = facilityIdSelect.find('option');
        for(var i=0; i<facIds.length; i++){
            SBI.facilityIdsList.push(facIds[i].value);
        };
        facilityIdSelect.remove();
    }

    $('#callDetailsBtn').click(function(){

        $.getJSON(
            '/callcenter/get-agent-call',
            {
                uuid    : uuid
            },
            function(json){
                $('#patientInfoBody').html('');
                doOpenCallDetail(json);
            }
        );
    });

    var getAgentCallDetailForEmployee = function(json){
        var data    = {
                patients    : []
            };
        if(agentId && json && json.data){
            data.ani = json.data.ani;
            if(json.data.loadedEmployees){
                for(var i=json.data.loadedEmployees.length - 1; i >= 0; i--){
                    var employee = json.data.loadedEmployees[i];
                    if(employee.employeeNumber == agentId && employee.screenType == screenType){
                        data.reasonCode = employee.reasonCode;
                        data.resolutionCode = employee.resolutionCode;
                        if(employee.resolutionDate){
                            data.resolutionDateTime = employee.resolutionDate;
                            //RESOLUTION TIME IS FORMATTED AS HH:MM:SS
                            data.resolutionDateTime += ' ' + employee.resolutionTime.substr(0,2) + ':' + employee.resolutionTime.substr(2,2) + ':' + employee.resolutionTime.substr(4,2);
                        }
                        data.employeeNumber = agentId;
                        break;
                    }
                }
            }
            if(json.data.patients){
                for(var i=0; i<json.data.patients.length; i++){
                    if(json.data.patients[i].empEntered == agentId){
                        data.patients.push(json.data.patients[i]);
                    }
                }
            }
        }
        return data;

    };

    var doOpenCallDetail = function(json){
        var data            = getAgentCallDetailForEmployee(json),
            aniValue        = data.ani || $('#ani').val(),
            reason          = data.reasonCode || 'NONE',
            resolution      = data.resolutionCode || 'NONE',
            lastUpdatedEmp  = data.employeeNumber || 0,
            lastUpdatedDate = data.resolutionDateTime ? data.resolutionDateTime : '',
            popupTitle      = 'Set Call Detail';

        if(aniValue && !$('#aniInput').val()){
            $('#aniInput').val(aniValue);
            $('#aniInput').triggerHandler('change');
        }
        if(reason != 'NONE'){
            $('#reasonInput').val(reason);
        }
        if(resolution != 'NONE'){
            $('#resolutionInput').val(resolution);
        }
        if(lastUpdatedDate){
            popupTitle += ' <small>(Last Updated on ' + lastUpdatedDate + ' by ' + lastUpdatedEmp + ')</small>';
        }
        if(json){
            $('#callDetailModalLabel')[0].innerHTML = popupTitle;
        }

        $('#callDetail').modal();
//        if(screenType != 'csr'){
            var body = $('#patientInfoBody');
            if(data.patients && data.patients.length > 0){
                for(var i=0; i<data.patients.length; i++){
                    var patient = data.patients[i];
                    if(patient.screenType == screenType && patient.empEntered == agentId){
                        SBI.addPatientForm(patient, body, false);
                    }
                }
            }
//            else if(body && body.children().length == 0){
//                SBI.addPatientForm(null, body, false);
//            }
//        }
    };

    $('.form-control.required').change(function(){
        var cmp         = $(this),
            parent      = cmp.parents('.form-group'),
            errorMsg    = '',
            value       = cmp.val();
        if(value){
            if((this.getAttribute('data-type') == 'number' && !SBI.isNumeric(cmp.val()))
                || (this.getAttribute('length') && this.getAttribute('length') != value.length)
                || (this.getAttribute('maxLength') && this.get('maxLength') < value.length)
                || (this.getAttribute('minLength') && this.get('minLength') > value.length)){
                errorMsg = 'is invalid';
            }
        }else{
            errorMsg = 'is required';
        }
        SBI.updateValidationComponent(cmp, '.form-group', '.validation-cmp', errorMsg);
    });

    $('#addPatientInfoBtn').click(function(){
        var body = $('#patientInfoBody');
        SBI.addPatientForm(null, body, true);
    });

    $('#saveCallDetailBtn').click(function(){
        var callDetailView  = $('#callDetail'),
            aniInput        = $('#aniInput'),
            reasonInput     = $('#reasonInput'),
            resolutionInput = $('#resolutionInput'),
            isFormValid     = callDetailView.find('.has-error').length == 0,
            screenType      = $('#screenType').val(),
            csrPatientId    = null,
            callDetail      = {},
            patients        = [];
        console.log('is form valid: %o', isFormValid);
        if(isFormValid){
            patients = getAppointmentInfo();
            callDetail = {
                uuid                : uuid,
                currentFacilityId   : $('#targetFacilityId').val(),
                ani                 : aniInput.val(),
                patients            : patients,
                loadedEmployees     : [{
                    employeeNumber      : agentId,
                    screenType          : $('#screenType').val(),
                    reasonCode          : reasonInput.val(),
                    reasonDateTime      : SBI.formatDateTime(new Date()),
                    resolutionCode      : resolutionInput.val(),
                    resolutionDateTime  : SBI.formatDateTime(new Date()),
                }]
            };
            doUpdateCallDetail(callDetail);
        }
    });

    var getAppointmentInfo = function(){
        var patientInfoBody = $('#patientInfoBody'),
            forms           = patientInfoBody.find('.patient-info-form'),
            patients        = [],
            screenType      = $('#screenType').val();
        for(var i=0; i<forms.length; i++){
            var form = $(forms[i]),
                patientIdInput  = $(form.find('.patient-id-input')[0]),
                patientIdValue  = patientIdInput.val().trim(),
                facilityIdInput = $(form.find('.facility-id-input')[0]),
                facilityId      = facilityIdInput.val(),
                apptDate        = $(form.find('.patient-date-input')[0]),
                date            = apptDate.datepicker('getDate'),
                apptTimeHour    = $(form.find('.patient-appointment-hour')[0]),
                apptTimeMinute  = $(form.find('.patient-appointment-minute')[0]),
                apptTimeAmPm    = $(form.find('.patient-appointment-ampm')[0]);
            if(patientIdValue || facilityId){
                patients.push({
                    sequence                : (i+1),
                    patientId               : patientIdValue,
                    facilityId              : facilityId,
                    nextAppointmentDateTime : SBI.formatDateTime(SBI.setTimeFromCmp(date, apptTimeHour.val(), apptTimeMinute.val(), apptTimeAmPm.val())),
                    patientLinkDateTime     : SBI.formatDateTime(new Date()),
                    emailCaptured           : form.find('.patient-email-captured')[0].checked,
                    insuranceWaiting        : form.find('.patient-insurance-waiting')[0].checked,
                    isOrtho                 : form.find('.patient-ortho')[0].checked,
                    empEntered              : agentId,
                    screenType              : screenType,
                });
            }
        }
        return patients;
    };

    var doUpdateCallDetail = function(callDetail){
        $.postJSON(
            '/callcenter/update-call-detail',
             callDetail,
             function(json){
                if(json.success){
                    $('#callDetail').modal('hide');
                    alreadySetResolution = true;
                    needToLoadAgentCall = true;
                    doCheckForWebRequestTriggerEmail(json.data);
                }
             }
        );
    };
    var triggerWREmailCode = ['BUSY', 'VM-MACHINE', 'VM-PERSON', 'EMAIL', 'NO-ANS', 'WEB-WRONG-NUMBER'];
    var doCheckForWebRequestTriggerEmail = function(json){
        var screenType      = $('#screenType').val(),
            emailStatus     = $('#wrEmailStatus'),
            emailStatusVal  = emailStatus.html(),
            resolution      = json.loadedEmployees[0] ? json.loadedEmployees[0].resolutionCode : '';
        if(screenType == 'wr' && (!emailStatusVal || emailStatusVal.toLowerCase().indexOf('email sent on') == -1)){
            emailStatus.html(triggerWREmailCode.indexOf(resolution) == -1 ? '' : 'Email will be sent to patient.');
        }
    };

    /********************************************************************************************************************************************
                        END CALL DETAIL LOGIC
    **********************************************************************************************************************************************/


    /********************************************************************************************************************************************
                        BEGIN CSR CALL ACTION
    **********************************************************************************************************************************************/

    var MESSAGE_OFFICE_SELECT = 1;
    var MESSAGE_TRANSFER_CALL = 2;
    var callInfoSelectedFacilityId = 0;
    var callInfoSelectedFacilityName = '';

    $('#updateCallInfoBtn').click(function(){
        var btn             = $(this),
            numberPress     = $('#numberPress').val(),
            selectedOption  = $('#csrMenuSelection').val(),
            zipInput        = $('#fsbzInput').val(),
            displayList     = $('#display_list'),
            offices         = [],
            alertCmp        = $('#csrUpdateCallInfoAlert'),
            isFormValid     = $('.has-error').length == 0;
        if(!isFormValid){
            SBI.showDialog('Error', 'Form is invalid.', SBI.SBI_ALERT_LEVEL_ERROR);
            return;
        }
        callInfoSelectedFacilityId = $('#targetFacilityId').val();
        callInfoSelectedFacilityName = $('#targetFacilityName').val();
        if(zipInput){
            offices = displayList.find('.office-selected');
            for(var i=0; i<offices.length; i++){
                if(offices[i].checked){
                    callInfoSelectedFacilityId = offices[i].getAttribute('facilityId');
                    callInfoSelectedFacilityName = $(offices[i]).parents('.list-group-item').find('.title').html();
                    break;
                }
            }
            if(!callInfoSelectedFacilityId){
                $(alertCmp.find('.modal-body')).html('<p>Would you like to select office?</p>');
                alertCmp[0].setAttribute('messageType', MESSAGE_OFFICE_SELECT);
                alertCmp.modal();
            }else{
                doUpdateCallInfo();
            }
        }else{
            doUpdateCallInfo();
        }
    });

    $('#csrUpdateCallInfoAlert_NoBtn').click(function(){
        var btn         = $(this),
            alertCmp    = $('#csrUpdateCallInfoAlert'),
            messageType = alertCmp[0].getAttribute('messageType');
        alertCmp.modal('hide');
        if(messageType == MESSAGE_OFFICE_SELECT){
            doUpdateCallInfo();
        }
    });

    $('#csrUpdateCallInfoAlert_YesBtn').click(function(){
        var btn         = $(this),
            alertCmp    = $('#csrUpdateCallInfoAlert'),
            messageType = alertCmp[0].getAttribute('messageType');
        alertCmp.modal('hide');
        if(messageType == MESSAGE_OFFICE_SELECT){

        }
    });

    var doUpdateCallInfo = function(){
        $.getJSON(
            '/callcenter/csr-call-update.json',
            {
                uuid            : uuid,
                menuNumberPress : $('#csrMenuSelection').val(),
                facilityId      : callInfoSelectedFacilityId,
            },
            function(json){
                if(json.success){
                    var selectedOfficeCmp = $('.selected-office-info');
                    if(selectedOfficeCmp.length > 0 && callInfoSelectedFacilityName){
                        selectedOfficeCmp.html('Selected Facility: ' + callInfoSelectedFacilityName);
                    }
                    $('#targetFacilityId').val(callInfoSelectedFacilityId);
                    $.postJSON(
                        '/callcenter/update-call-detail',
                         {
                            uuid            : uuid,
                            loadedEmployees : [{
                                employeeNumber      : agentId,
                                screenType          : $('#screenType').val(),
                                reasonCode          : $('#reasonInput').val(),
                                reasonDateTime      : SBI.formatDateTime(new Date()),
                                resolutionCode      : $('#resolutionInput').val(),
                                resolutionDateTime  : SBI.formatDateTime(new Date()),
                            }],
                            patients        : getAppointmentInfo()
                         },
                         function(json){
                            if(json.success){
                                alreadySetResolution = true;
                                updateCsrCall = true;
                                SBI.showDialog('Message', 'Successfully update call info.', SBI.SBI_ALERT_LEVEL_MESSAGE);
                            }
                         }
                    );
                }
            }
        );
    };
    var mapLink     = 'https://www.brightnow.com/locations/dental-office/office/office/$facilityId/map',
        mapImgUrl   = $('#mapImgUrl').val();
    $('.csr-facility-detail-link').live('click',function(){
        var cmp     = $(this),
            popup   = $('#csrFacilityDetail');
        popup.find('.facility-detail').css('display', 'none');
        popup.find('.loading-facility-detail').css('display', 'block');
        popup.modal();
        $.getJSON(
            '/callcenter/facility-detail.json',
            {
                facilityId: cmp.attr('facilityId')
            },
            function(json){

                console.log('%o', json);
                if(json.success && json.data){
                    var data    = json.data,
                        fields  = popup.find('.csr-facility-detail');

                    popup.find('#csrFacilityDetailModalLabel').html(data.name);
                    facilityServiceProviders = {};

                    for(var i=0; i<fields.length; i++){
                        var field       = $(fields[i]),
                            dataField   = field.attr('dataField'),
                            value       = data[dataField];
                        if(dataField && dataField.indexOf('Alert') != -1){
                            var alert       = null,
                                createdOn   = null;
                            for(var j=0; data.alerts && j<data.alerts.length; j++){
                                if((dataField == 'immediateAlert' && data.alerts[j].attributeId == 26458)
                                || (dataField == 'generalAlert' && data.alerts[j].attributeId == 26459)){
                                    alert = data.alerts[j];
                                    break;
                                }
                            }
                            if(alert){
                                createdOn = SBI.isDate(alert.createdOn) ? alert.createdOn : new Date(alert.createdOn);
                                value = '<span attributeId="' + alert.attributeId + '">' + alert.value + '</span><span class="fr"> (created on ' + SBI.formatDateTime(createdOn, 'Y/m/d H:i:s') + ' by ' + alert.createdByName + ')</span>';
                                field.html(value);
                            }else{
                                value = null;
                            }

                            field.css('display', alert ? 'block' : 'none');

                        }else if(dataField == 'facilityName'){
                            value = data.name + ' <a target="_blank" href="' + mapLink.replace(/\$facilityId/g, data.facilityId) + '" data-toggle="tooltip" data-original-title="View Location Map" data-placement="right">';
                            value += '<img class="map-link" src="' + mapImgUrl + '"/></a>';
                            field.html(value);
                            field.attr('facilityName', data.name);
                        }else if(dataField == 'website'){
                            field.attr('href', 'https://www.brightnow.com/locations/dental-office/office/office/' + data.facilityId);
                        }else if(dataField == 'feedback'){
                            field.attr('href', 'https://smilebrands.service-now.com/sp?id=sc_cat_item&sys_id=d03015451bda3b00f0f48661cd4bcb4b&sysparm_category=eae0d5811bda3b00f0f48661cd4bcbf5');
                        }else if(dataField == 'treatments'){
                            field.attr('href', 'https://openbook.smilebrands.com/treatments/viewer/?siteId=' + data.facilityId);
                        }else if(dataField == 'cityStateZip'){
                            value = data.city + ', ' + data.state + ' ' + data.zip;
                            field.html(value);
                        }else if(dataField == 'doctorOnCall'){
                            value = data.doctorOnCallName;
                            var formattedPhone = SBI.formatPhoneNumber(data.doctorOnCallNumber);;
                            if(formattedPhone){
                                if(value) value += ' - ' + formattedPhone;
                                else value = formattedPhone;
                            }
                            field.html(value);
                        }else if(dataField == 'specialtyServices'){
                            if(data.specialServices){
                                var specialtyTxt = '';
                                for(var j=0; j<data.specialServices.length; j++){
                                    if(data.specialServices[j].value
                                        && (data.specialServices[j].value.toLowerCase() == 'true' || data.specialServices[j].value.toLowerCase() == 'yes')){
                                        specialtyTxt += '<li>' + data.specialServices[j].name + '</li>';
                                    }
                                }
                                if(specialtyTxt){
                                    value = '<ul>' + specialtyTxt + '</ul>';
                                    field.attr('data-content', value);
                                }
                            }
                        }else if(dataField == 'phoneNumber' || dataField == 'transferNumber' || dataField == 'faxNumber'){
                            if(dataField == 'transferNumber' && data.associatedPhoneNumber && data.associatedPhoneNumber.transferNumber){
                                value = data.associatedPhoneNumber.transferNumber;
                            }
                            field.html(SBI.formatPhoneNumber(value));
                        }else if(dataField == 'managementHierarchy'){
                            if(data.employees.Managers){
                                var managers    = data.employees.Managers,
                                    manager     = null;
                                value = '';
                                for(var j=0; j<managers.length; j++){
                                    manager = managers[j];
                                    value += '<p>' + manager.firstName + ' ' + manager.lastName;
                                    value += ' - <i>' + manager.title + '</i> (' + manager.email + ')</p>';
                                }
                                field.html(value);
                            }
                        }else if(dataField == 'providersInfo'){
                            if(data.employees.Dentists){
                                var dentists    = data.employees.Dentists,
                                    dentist     = null;
                                value = '';
                                for(var j=0; j<dentists.length; j++){
                                    dentist = dentists[j];
                                    value += '<p>' + (dentist.prefix || '') + ' ' + dentist.firstName + ' ' + dentist.lastName;
                                    value += ' - <i>' + dentist.title + '</i>';
                                    value += '<span class="glyphicon glyphicon-tag pointer padding-left-10 provider-detail" providerId="' + dentist.serviceProviderId + '"></span>';
                                    if(dentist.scheduleNotes){
                                        value += '<br/><b style="padding-left: 20px;">' + dentist.scheduleNotes + '</b>';
                                    }
                                    value += '</p>';
                                    facilityServiceProviders['' + dentist.serviceProviderId] = {
                                        providerId      : dentist.serviceProviderId,
                                        qsiId           : dentist.qsiProviderId,
                                        providerName    : (dentist.prefix || '') + ' ' + dentist.firstName + ' ' + dentist.lastName,
                                        scheduleNote    : dentist.scheduleNotes,
                                        gender          : dentist.gender,
                                        languages       : dentist.languages,
                                        institution     : dentist.institutionName,
                                        degreeEarned    : dentist.degreeEarned,
                                        degreeSuffix    : dentist.degreeSuffix,
                                        graduatedIn     : dentist.degreeEarnedYear,
                                        startPracticeIn : dentist.startPracticeYear
                                    };
                                }
                                field.html(value);
                            }
                        }else if(dataField == 'neighboringOffices'){
                            var left    = '<div class="col-md-6">',
                                right   = '<div class="col-md-6">',
                                txt     = '',
                                office  = null;
                            for(var j=0; data.neighborFacilities && j<data.neighborFacilities.length; j++){
                                office = data.neighborFacilities[j];
                                txt = '<p><div class="blue bold">' + office.name + ' (' + office.distance  + ' miles away)</div>';
                                txt += '<div>' + office.address + '</div>';
                                txt += '<div>' + office.city + ', ' + office.state + '</div>';
                                txt += '<div>' + SBI.formatPhoneNumber(office.phoneNumber) + '</div>';
                                if(office.specialtySchedule && office.specialtySchedule.length > 0){
                                    txt += '<div>';
                                    for(var noteIdx=0; noteIdx < office.specialtySchedule.length; noteIdx++){
                                        var ss = office.specialtySchedule[noteIdx];
                                        txt += '<span style="font-weight: bold;">' + ss.name + ': </span><span style="font-style: italic;">' + ss.note + '</span><br/>'
                                    }
                                    txt += '</div>';
                                }
                                txt += '</p>';
                                if(j%2 == 0){
                                    left += txt;
                                }else{
                                    right += txt;
                                }
                            }
                            left += '</div>';
                            right += '</div>';
                            value = left + right;
                            field.html(value);
                        }else{
                            field.html(value);
                        }
                        var row = field.parent('.row');
                        if(row && row.length == 1){
                            if(value === null || value === undefined){
//                                console.log('need to hide field: %o', dataField);
                                row.css('display', 'none');
                            }else{
                                row.css('display', 'block');
                            }
                        }
                    }
                    $('#frontOfficeEmailLink').attr('href', 'mailto:' + data.facilityId + '-<EMAIL>');

                    // -- If this Facility is Open Book Scheduling Enabled, show the Launcher icon / link
                    if (data.openBookSchedulingEnabled) {

                        var facilityId  = data.facilityId;
                        var agentId     = $('#employeeNumber').val();
                        var lang        = $('#language').val();
                        var ani         = $('#ani').val();

                        var win = 'https://openbook.smilebrands.com/scheduling/ui/?siteId=' + facilityId + '&agentId=' + agentId + '&callerId=' + ani + '&lang=' + lang + '&mobile=0';
                        $('#openBookSchedulingLink').attr('href', win);
                        $('#openBookSchedulingLink').css('display', 'block');

                    } else {
                        $('#openBookSchedulingLink').css('display', 'none');
                    }

                }
                popup.find('.facility-detail').css('display', 'block');
                popup.find('.loading-facility-detail').css('display', 'none');
            }
        );

    });

    /********************************************************************************************************************************************
                        END CSR CALL ACTION
    **********************************************************************************************************************************************/


    /********************************************************************************************************************************************
                        BEGIN UNLOAD PAGE
    **********************************************************************************************************************************************/
    window.onbeforeunload = function(e) {
        if(!alreadySetResolution && !routeToDifferentScreen && !updateCsrCall){
            $('#callDetailsBtn').triggerHandler('click');
            return false;
        }
    };
    window.onunload = function(e) {
        if(!routeToDifferentScreen){
            $.ajax({
                type    : 'GET',
                async   : false,
                url     : '/callcenter/unload-screen-pop',
                data    : {
                  uuid            : uuid,
                  employeeNumber  : $('#employeeNumber').val(),
                  facilityId      : $('#targetFacilityId').val(),
                  screenType      : $('#screenType').val(),
                  unloadDateTime  : SBI.formatDateTime(new Date())
                }
            });
        }
    };
    $('.other-site').click(function(){
        routeToDifferentScreen = true;
    });
    /********************************************************************************************************************************************
                        END UNLOAD PAGE
    **********************************************************************************************************************************************/


    /********************************************************************************************************************************************
                        BEGIN PROVIDER INSURANCE
    **********************************************************************************************************************************************/

    var loadProviderInsurance = true;
    $('a.view-provider-insurance').click(function(){
        var popup           = $('#providerInsuranceView'),
            providerInfoPop = $('.popover.provider-detail'),
            currentFacilityId = popup.attr('facilityId'),
            facilityId = getTargetFacilityId();

        if(providerInfoPop.hasClass('in')){
            //REMOVE POPOVER IF ANY OPEN
            providerInfoPop.removeClass('in').css('display', 'none').css('top', 0).css('left', 0);
            providerInfoPop.removeAttr('providerId');
        }
        popup.modal();
        if(loadProviderInsurance || currentFacilityId != facilityId){
            $.getJSON(
                '/callcenter/provider-insurance',
                {
                    facilityId: facilityId
                },
                function(json){
                    if(json.success){
                        updateProviderInsuranceTitleForCSR();
                        doShowProviderInsurance(json, facilityId);
                        loadProviderInsurance = false;
                        popup.find('.progress').css('display', 'none');
                        popup.find('.insurance-list').css('display', 'block');
                    }else{
                        SBI.showDialog('Error', 'Fail to load provider insurance.', SBI.SBI_ALERT_LEVEL_ERROR);
                    }
                }
            );
        }
    });

    var getTargetFacilityId = function(){
        var result = $('#targetFacilityId').val();
        var csrFacilityDetail = $('#csrFacilityDetail');
        if(csrFacilityDetail && csrFacilityDetail.length){
            var facilityIdField = csrFacilityDetail.find('.csr-facility-detail[datafield=facilityId]')[0];
            if(facilityIdField){
                result = $(facilityIdField).html();
            }
        }
        return result;
    };

    /**
    * Since user can load different facility detail on CSR screen-pop,
    * we need to update the title to correct facility name. PSR and WR is bound to specific facility.
    **/
    var updateProviderInsuranceTitleForCSR = function(){
        var providerInsuranceTitle = $('#providerInsuranceView #providerInsuranceLabel'),
            facilityNameField = $('#csrFacilityDetail .csr-facility-detail[dataField=facilityName]');
        if(providerInsuranceTitle && providerInsuranceTitle.length
            && facilityNameField && facilityNameField.length){
            providerInsuranceTitle.html('Provider Insurances for ' + facilityNameField.attr('facilityName'));
        }
    };

    $('#providerInsuranceView').on('hidden.bs.modal', function(){
        console.log('on #providerInsuranceView dismissed');
        if($('.modal.fade.in').length > 0){
            $(document.body).addClass('modal-open');
        }
    });

    var doShowProviderInsurance = function(json, facilityId){
        var popup           = $('#providerInsuranceView'),
            progress        = popup.find('.loading-insurances'),
            insuranceList   = popup.find('.insurance-list'),
            data            = json ? json.data : [],
            insuranceHtml   = '',
            providerName    = '',
            provider        = null,
            insurance       = null;
        popup.attr('facilityId', facilityId);
        for(var i=0; i<data.length; i++){
            provider = data[i];
            if(provider.providerInsurances && provider.providerInsurances.length > 0){
                providerName = provider.firstName + ' ' + provider.lastName;
                insuranceHtml += '<div class="provider-insurance" providerName="' + providerName + '">';
                insuranceHtml += '<div class="insurance-provider-name">' + (provider.prefix ? provider.prefix + ' ' : '') + providerName + '</div>';
                insuranceHtml += '<div class="insurance-provider-info"><span>Specialty: ' + provider.specialty + '</span><span style="padding-left: 20px;">NPI: ' + provider.npiNumber + '</span>';
                insuranceHtml += '<span style="padding-left: 20px;">License #:' + provider.licenseNumber + '</span></div>';
                insuranceHtml += '<table class="table table-striped">';
                insuranceHtml += '<thead><th>Payer</th><th>Status</th><th>Status Date</th><th>Process Start</th><th>Effective Date</th><th>End Date</th></thead>';
                for(var j=0; j<provider.providerInsurances.length; j++){
                    insurance = provider.providerInsurances[j];
                    insuranceHtml += '<tr payerName="' + insurance.payerName + '">';
                    insuranceHtml += '<td>' + insurance.payerName + '</td><td>' + insurance.payerStatus + '</td>';
                    insuranceHtml += '<td>' + (insurance.statusDate ? SBI.formatDateTime(new Date(insurance.statusDate), 'm/d/Y') : '') + '</td>';
                    insuranceHtml += '<td>' + (insurance.startDate ? SBI.formatDateTime(new Date(insurance.startDate), 'm/d/Y') : '') + '</td>';
                    insuranceHtml += '<td>' + (insurance.effectiveDate || '') + '</td>';
                    insuranceHtml += '<td>' + (insurance.endDate ? SBI.formatDateTime(new Date(insurance.endDate), 'm/d/Y') : '') + '</td>';
                    insuranceHtml += '</tr>';
                }
                insuranceHtml += '</table>';
                insuranceHtml += '</div>';
            }
        }
        if(insuranceHtml){
            popup.find('.filter-cmp').css('display', 'block');
            insuranceList.html(insuranceHtml);
        }else{
            popup.find('.filter-cmp').css('display', 'none');
            insuranceList.html('<div style="text-align: center; padding-top: 20px; font-weight: bold;">No Accepted Insurance Found</div>');
        }
    };

    var filterInsuranceTimeout = null;
    $('#providerInsuranceView .filter-cmp input[type=text]').keyup(function(){
        clearTimeout(filterInsuranceTimeout);
        filterInsuranceTimeout = setTimeout(function(){doFilterInsurance()}, 500);
    });
    $('#providerInsuranceView .filter-cmp select').change(function(){
        var cmp         = $(this),
            filterCmp   = cmp.parents('.filter-cmp'),
            inputCmp    = filterCmp.find('.filter-value');
        if(inputCmp.val()){
            inputCmp.val('');
            doFilterInsurance();
        }
    });
    $('#providerInsuranceView .filter-cmp .remove-filter-insurance').click(function(){
         var cmp         = $(this),
             filterCmp   = cmp.parents('.filter-cmp'),
             inputCmp    = filterCmp.find('.filter-value');
         if(inputCmp.val()){
             inputCmp.val('');
             doFilterInsurance();
         }
    });

    var doFilterInsurance = function(){
        var filterCmp       = $('#providerInsuranceView .filter-cmp'),
            filterValue     = filterCmp.find('.filter-value').val().toLowerCase(),
            filterCriteria  = filterCmp.find('.filter-criteria').val(),
            providerDivs    = $('.provider-insurance'),
            providerDiv     = null,
            insuranceTrs    = null,
            found           = false,
            payerName       = null,
            providerName    = null;
        for(var i=0; i<providerDivs.length; i++){
            found = false;
            providerDiv = $(providerDivs[i]);
            if(filterCriteria == 'provider'){
                providerName = providerDiv.attr('providerName');
                providerName = providerName ? providerName.toLowerCase() : '';
                providerDiv.find('tr[payerName]').css('display', 'table-row');
                if(!filterValue || providerName.indexOf(filterValue) != -1){
                    found = true;
                }else{
                    found = false;
                }
            }else if(filterCriteria == 'payer'){
                insuranceTrs = providerDiv.find('tr[payerName]');
                for(var j=0; j<insuranceTrs.length; j++){
                    payerName = $(insuranceTrs[j]).attr('payerName');
                    payerName = payerName ? payerName.toLowerCase() : '';
                    if(!filterValue || payerName.indexOf(filterValue) != -1){
                        $(insuranceTrs[j]).css('display', 'table-row');
                        found = true;
                    }else{
                        $(insuranceTrs[j]).css('display', 'none');
                        found = found || false;
                    }
                }
            }
            providerDiv.css('display', found ? 'block' : 'none');
        }
    };

    /********************************************************************************************************************************************
                        END PROVIDER INSURANCE
    **********************************************************************************************************************************************/


    /********************************************************************************************************************************************
                        BEGIN SMS
    **********************************************************************************************************************************************/

    $('#sms-btn').click(function(){
        if(isOld === 'false' || !isOld){
            $('#smsDialog').modal();
            var templateInput = $('#smsTemplateInput');
            if(templateInput && templateInput.val()){
                doPopulateSMSPreview(templateInput);
            }
        }else{
            SBI.showDialog('Error', 'Cannot send SMS on this screen-pop because it\'s expired!', SBI.SBI_ALERT_LEVEL_ERROR);
        }
    });

    $('#smsTemplateInput').change(function(){
        var cmp = $(this);
        if(cmp.val()){
            doPopulateSMSPreview(cmp);
        }
    });

    var doPopulateSMSPreview = function(cmp){
        var previewCmp = $('#smsPreview'),
            options = cmp.find('option'),
            selectedId = cmp.val(),
            content = '', i=0,
            opt;
        for(i; i<options.length; i++){
            opt = $(options[i]);
            if(opt.attr('value') === selectedId){
                content = opt.attr('content');
                break;
            }
        }
        previewCmp.val(content);
        validateSMSForm();
    };

    var validateSMSForm = function(){
        var phoneNumberCmp = $('#smsInput'),
            previewCmp = $('#smsPreview');
        phoneNumberCmp.triggerHandler('change');
        previewCmp.triggerHandler('change');
    };

    $('#sendSMSBtn').click(function(){
        var sendBtn = $(this),
            smsDialog = $('#smsDialog'),
            phoneNumberCmp = $('#smsInput'),
            previewCmp = $('#smsPreview'),
            templateSelCmp = $('#smsTemplateInput'),
            facilityIdCmp = $('#targetFacilityId'),
            smsData = {},
            url;
        validateSMSForm();
        if(smsDialog.find('.has-error').length == 0){
            smsData['phoneNumber'] = phoneNumberCmp.val();
            smsData['msg'] = previewCmp.val();
            smsData['uuid'] = uuid;
            url = '/callcenter/send-sms?employeeNumber=' + agentId;
            url += '&facilityId=' + facilityIdCmp.val(),
            url += '&templateId=' + templateSelCmp.val(),
            sendBtn.attr('disabled', 'disabled')
            $.postJSON(
                url,
                {
                    phoneNumber: phoneNumberCmp.val(),
                    msg: previewCmp.val(),
                    uuid: uuid,
                    screenType: screenType
                },
                function(json){
                    sendBtn.removeAttr('disabled');
                    if(json.success){
                        SBI.showDialog('Message', 'Successfully send SMS to ' + smsData['phoneNumber'] + '.', SBI.SBI_ALERT_LEVEL_MESSAGE);
                    }else{
                        SBI.showDialog('Error', 'Fail to send SMS to ' + smsData['phoneNumber'] + '.', SBI.SBI_ALERT_LEVEL_ERROR);
                    }
                }
            );
        }
    });


    /********************************************************************************************************************************************
                        END SMS
    **********************************************************************************************************************************************/

    /********************************************************************************************************************************************
                            BEGIN OPEN BOOK SCHEDULING
    **********************************************************************************************************************************************/

    $('#obs-btn').click(function(){
        $('#obSchedulingDialog').modal();
    });

    $('#obSchedulingBtn').click(function(){
        var sendBtn = $(this),
            obFacility = $('#obSchedulingFacility');

        var facilityId  = obFacility.val();
        var agentId     = $('#employeeNumber').val();
        var lang        = $('#language').val();
        var ani         = $('#ani').val();
        var isTF        = $('#isTF').val();

        var win = window.open('https://openbook.smilebrands.com/scheduling/ui/?siteId=' + facilityId + '&agentId=' + agentId + '&callerId=' + ani + '&lang=' + lang + '&mobile=0&isTF=' + isTF, '_blank');
        if (win) {
            win.focus();
        } else {
            alert('Unable to launch Open Book Scheduling!');
        }

    });


    /********************************************************************************************************************************************
                        END OPEN BOOK SCHEDULING
    **********************************************************************************************************************************************/

})