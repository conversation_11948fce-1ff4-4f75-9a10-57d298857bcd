CREATE OR REPLACE PROCEDURE "CISCOADM"."SP_UPDATE_PATIENT_INFO"

/*---------------------------------------------------------------------------------*/
/* Procedure: SP_UPDATE_PATIENT_INFO                                               */
/*                                                                                 */
/* Author: <PERSON>                                                          */
/* Date Written: 08/08/2014                                                        */
/*                                                                                 */
/* Purpose: This stored procedure updates the Agent Call Log with the Associated   */
/*          Patient Information                                                    */
/*                                                                                 */
/* Input Variables:                                                                */
/*                                                                                 */
/*                                                                                 */
/*                                                                                 */
/*                                                                                 */
/* Returns:                                                                        */
/*                                                                                 */
/*                                                                                 */
/* Revisions                                                                       */
/*                                                                                 */
/*                                                                                 */
/*                                                                                 */
/*---------------------------------------------------------------------------------*/

AS

V_CLINIC_ID                     APPT_CALL_LOG.CLINIC_ID%TYPE;
V_UNIQUE_ID                     PATIENTS.UNIQUE_ID%TYPE;
V_PATIENT_ID                    PATIENTS.PATIENT_ID%TYPE;
V_FACILITY_ID                   NUMBER;
V_PATIENT_FOUND                 NUMBER(1,0) := 0;
V_CLINIC_FOUND                  NUMBER(1,0) := 0;

V_CALL_LOG_STATUS               VARCHAR2(15);
V_APPT_LOG_ID                   APPT_CALL_LOG.APPT_LOG_ID%TYPE;
V_APPT_LOG_STATUS               APPT_CALL_LOG.APPT_LOG_STATUS%TYPE;
V_ALLOW_CREDIT                  AGENT_CALL_LOG.ALLOW_CREDIT%TYPE;

ERR_NUM                         NUMBER;
ERR_MSG                         VARCHAR2(512);
V_STORED_PROCEDURE              VARCHAR2(30) := $$PLSQL_UNIT;


BEGIN

/*---------------------------------------------------------------------------*/
/* Set Read Loop                                                             */
/*---------------------------------------------------------------------------*/
    FOR R IN (SELECT CL.CALL_LOG_ID,
                     CL.SESSION_ID,
                     CL.EMPLOYEE_NUMBER,
                     CL.FACILITY_ID,
                     CL.TEMP_PATIENT_ID,
                     CL.CLINIC_ID,
                     CL.PATIENT_ID,
                     CL.UNIQUE_ID,
                     CL.CALL_LOG_STATUS,
                     UPPER(NVL(FV.FAC_ATTR_VALUE,'FALSE')) AS IS_LIBERTY,
                     (TRUNC(CL.CREATE_DATETIME) + 3)  AS MISSING_DATE,
                    AL.IS_ORTHO,
                    nvl(CL.ALLOW_CREDIT, 0) as ALLOW_CREDIT,
                    nvl(CL.ADMIN_OVERRIDDEN, 0) as ADMIN_OVERRIDDEN,
                    TRUNC(CL.CREATE_DATETIME) AS CL_CREATE_DATE

            FROM AGENT_CALL_LOG CL
            JOIN APPT_CALL_LOG AL ON AL.APPT_LOG_ID = CL.LAST_APPT_LOG_ID
            LEFT OUTER JOIN FACILITY_ATTR_VALUES FV
                ON FV.FACILITY_ID = CL.FACILITY_ID AND
                   FV.FAC_ATTRIBUTE_ID = 69
            WHERE UPPER(CALL_LOG_STATUS) = 'ACTIVE' AND
                  CL.UNIQUE_ID IS NULL)

    LOOP

    V_CLINIC_FOUND := 0;
    V_PATIENT_FOUND := 0;
    V_ALLOW_CREDIT := R.ALLOW_CREDIT;

/*---------------------------------------------------------------------------*/
/* Get Clinic Id                                                             */
/*---------------------------------------------------------------------------*/
    IF R.IS_ORTHO = 1 THEN
        BEGIN
            V_CLINIC_ID := 9;
            R.IS_LIBERTY := 'FALSE';
            V_CLINIC_FOUND := 1;
        END;
    ELSE
        BEGIN


        SELECT QE.CLINIC_ID
          INTO V_CLINIC_ID
        FROM QSI_CLINIC_EXTENSION QE
        WHERE QE.FACILITY_ID = R.FACILITY_ID AND
              QE.PRIMARY_CLINIC = 'Y' AND
              ROWNUM = 1;

        V_CLINIC_FOUND := 1;

        EXCEPTION WHEN OTHERS THEN

        V_CLINIC_ID := NULL;
        V_CLINIC_FOUND := 0;

        END;
    END IF;
/*---------------------------------------------------------------------------*/
/* Get Clinic Id                                                             */
/*---------------------------------------------------------------------------*/
    IF V_CLINIC_FOUND = 0 THEN

    BEGIN


    SELECT QE.CLINIC_ID
      INTO V_CLINIC_ID
    FROM QSI_CLINIC_EXTENSION QE
    WHERE QE.FACILITY_ID = R.FACILITY_ID AND
          ROWNUM = 1;

    V_CLINIC_FOUND := 1;

    EXCEPTION WHEN OTHERS THEN

    V_CLINIC_ID := NULL;
    V_CLINIC_FOUND := 0;

    END;
    END IF;
/*---------------------------------------------------------------------------*/
/* Get Patient Info - Non Liberty                                            */
/*---------------------------------------------------------------------------*/


    V_UNIQUE_ID := R.UNIQUE_ID;
    V_PATIENT_ID := R.PATIENT_ID;

    IF V_UNIQUE_ID IS NULL AND
       R.IS_LIBERTY = 'FALSE' THEN

    BEGIN

    SELECT PATIENT_ID,
           UNIQUE_ID

      INTO V_PATIENT_ID,
           V_UNIQUE_ID

    FROM PATIENTS PA
    WHERE CLINIC_ID = V_CLINIC_ID AND
          PATIENT_ID = R.TEMP_PATIENT_ID AND
          PA.DELETED_PATIENT_YN = 'N';

    V_PATIENT_FOUND := 1;

    EXCEPTION WHEN OTHERS THEN

    V_PATIENT_ID := NULL;
    V_UNIQUE_ID := NULL;
    V_PATIENT_FOUND := 0;

    END;
    END IF;
/*---------------------------------------------------------------------------*/
/* Get Patient Info - Non Liberty                                            */
/*---------------------------------------------------------------------------*/


    IF V_UNIQUE_ID IS NULL AND
       R.IS_LIBERTY = 'FALSE' THEN

    BEGIN

    SELECT PATIENT_ID,
           UNIQUE_ID

      INTO V_PATIENT_ID,
           V_UNIQUE_ID

    FROM TEMP_PATIENTS PA
    WHERE CLINIC_ID = V_CLINIC_ID AND
          PATIENT_ID = R.TEMP_PATIENT_ID AND
          /*GET THE TEMP PATIENT RECORD IN PROPER DATE RANGE*/
          TRUNC(CREATE_DATETIME) BETWEEN R.CL_CREATE_DATE AND R.MISSING_DATE;

    V_PATIENT_FOUND := 1;

    EXCEPTION WHEN OTHERS THEN

    V_PATIENT_ID := NULL;
    V_UNIQUE_ID := NULL;
    V_PATIENT_FOUND := 0;

    END;
    END IF;

/*---------------------------------------------------------------------------*/
/* Get Patient Info - Liberty                                                */
/*---------------------------------------------------------------------------*/
    IF V_UNIQUE_ID IS NULL AND
       R.IS_LIBERTY <> 'FALSE' THEN

    BEGIN
    /* Make sure TEMP_PATIENT_ID enter correctly */

    SELECT PATIENT_ID, PATIENT_ID
        INTO V_PATIENT_ID,
           V_UNIQUE_ID
    FROM LIBERTY_PATIENT
    WHERE PATIENT_ID = R.TEMP_PATIENT_ID;

    V_PATIENT_FOUND := 1;

    EXCEPTION WHEN OTHERS THEN

    V_UNIQUE_ID := NULL;
    V_PATIENT_ID := NULL;
    V_PATIENT_FOUND := 0;

    END;
    END IF;

/*---------------------------------------------------------------------------*/
/* Get Patient Info - check for Ortho from Patients: clinic ID 9 for now     */
/*---------------------------------------------------------------------------*/
/*  REMOVE THE LOGIC TO LOOKING UP PATIENT IN CLINIC 9 UNSURELY BECAUSE WE HAVE A FLAG IS_ORTHO TO DETERMINE
    IF V_UNIQUE_ID IS NULL THEN

    BEGIN

    SELECT PA.PATIENT_ID,
           PA.UNIQUE_ID,
           PA.CLINIC_ID

      INTO V_PATIENT_ID,
           V_UNIQUE_ID,
           V_CLINIC_ID

    FROM PATIENTS PA
        JOIN ORTHO_TEAMS T ON T.CLINIC_ID = PA.CLINIC_ID AND T.PROVIDER_ID = PA.PROVIDER_ID
    WHERE T.SITE_CODE = R.FACILITY_ID
          AND PATIENT_ID = R.TEMP_PATIENT_ID;

    V_PATIENT_FOUND := 1;

    EXCEPTION WHEN OTHERS THEN

    V_PATIENT_ID := NULL;
    V_UNIQUE_ID := NULL;
    V_PATIENT_FOUND := 0;

    END;
    END IF;
*/
/*----------------------------------------------------------------------------*/
/* Get Patient Info - check for Ortho from Temp_Patients: clinic ID 9 for now */
/*----------------------------------------------------------------------------*/
/* -- REMOVE LOGIC TO CHECK PATIENT WITH ORTHO IN TEMP_PATIENTS BECAUSE THERE IS NO PROVIDER_ID
   IF V_UNIQUE_ID IS NULL THEN

    BEGIN

    SELECT PATIENT_ID,
           UNIQUE_ID,
           CLINIC_ID

      INTO V_PATIENT_ID,
           V_UNIQUE_ID,
           V_CLINIC_ID

    FROM TEMP_PATIENTS PA
        JOIN ORTHO_TEAMS T ON T.CLINIC_ID = PA.CLINIC_ID AND T.PROVIDER_ID = PA.PROVIDER_ID
    WHERE T.SITE_CODE = R.FACILITY_ID
          AND PATIENT_ID = R.TEMP_PATIENT_ID;

    V_PATIENT_FOUND := 1;

    EXCEPTION WHEN OTHERS THEN

    V_PATIENT_ID := NULL;
    V_UNIQUE_ID := NULL;
    V_PATIENT_FOUND := 0;

    END;
    END IF;
*/

/*---------------------------------------------------------------------------*/
/* Update Agent Call Log                                                     */
/*---------------------------------------------------------------------------*/
    BEGIN

    UPDATE AGENT_CALL_LOG
      SET CLINIC_ID = V_CLINIC_ID,
          PATIENT_ID = V_PATIENT_ID,
          UNIQUE_ID = V_UNIQUE_ID,
          UPDATE_TIMESTAMP = SYSDATE
    WHERE CALL_LOG_ID = R.CALL_LOG_ID;


    END;

/*---------------------------------------------------------------------------*/
/* Patient Not Found during grace period                                     */
/*---------------------------------------------------------------------------*/

    IF V_PATIENT_FOUND = 0 AND
       R.MISSING_DATE < SYSDATE THEN

    V_APPT_LOG_STATUS := 'PATIENT_NOT_FOUND';
    V_CALL_LOG_STATUS := 'CLOSED';

    IF R.ADMIN_OVERRIDDEN = 0 THEN
        V_ALLOW_CREDIT := 0;
    END IF;

    BEGIN

    INSERT INTO APPT_CALL_LOG(
                APPT_LOG_ID,
                CALL_LOG_ID,
                CLINIC_ID,
                UNIQUE_ID,
                PROVIDER_ID,
                APPT_LOG_STATUS,
                APPT_DATE,
                APPT_VALUE,
                LAST_UPD_PROGRAM,
                CREATE_EMPLOYEE,
                CREATE_DATETIME,
                UPDATE_EMPLOYEE,
                UPDATE_TIMESTAMP)

	     VALUES(APPT_CALL_LOG_ID_SEQ.NEXTVAL,
                R.CALL_LOG_ID,
                V_CLINIC_ID,
                V_UNIQUE_ID,
                0,
                V_APPT_LOG_STATUS,
                NULL,
                0,
                V_STORED_PROCEDURE,
                110026,
                SYSDATE,
                NULL,
                NULL)
        RETURNING APPT_LOG_ID
        INTO V_APPT_LOG_ID;

    END;


/*---------------------------------------------------------------------------*/
/* Close Agent Call Log on Terminal Event                                    */
/*---------------------------------------------------------------------------*/

    BEGIN

    UPDATE AGENT_CALL_LOG
       SET CALL_LOG_STATUS = V_CALL_LOG_STATUS,
           LAST_APPT_LOG_ID = V_APPT_LOG_ID,
           UPDATE_EMPLOYEE = 110026,
           UPDATE_TIMESTAMP = SYSDATE,
           ALLOW_CREDIT = V_ALLOW_CREDIT
    WHERE CALL_LOG_ID = R.CALL_LOG_ID;

    END;
    END IF;

/*---------------------------------------------------------------------------*/
/* Error Occured - Add error to log                                          */
/*---------------------------------------------------------------------------*/
    END LOOP;

    EXCEPTION WHEN OTHERS THEN

    ERR_NUM := SQLCODE;
    ERR_MSG := SUBSTR(SQLERRM, 1, 512);

    INSERT INTO APPLICATION_ERROR_LOG(
                ERROR_ID,
                STORED_PROCEDURE,
                ERROR_NUMBER,
                ERROR_MSG,
                ERROR_DATE)
      VALUES(
               APPLICATION_ERROR_LOG_SEQ.NEXTVAL,
               V_STORED_PROCEDURE,
               ERR_NUM,
               ERR_MSG,
               SYSDATE);
END;