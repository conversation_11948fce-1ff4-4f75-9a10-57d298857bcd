package com.smilebrands.callcentersupport.domain;

import javax.persistence.*;
import java.util.Date;

/**
 * Created by phongpham on 4/7/15.
 */
@Entity
@Table(name = "CALL_LOG_FILE")
public class CallLogFile {
    private Long callLogFileId;
    private Integer employeeNumber;
    private String uuid;
    private String filePath;
    private String fileType;
    private String source;
    private Date createdDateTime = new Date();
    private Boolean active = true;
    private Date inactivatedOn;
    private Integer inactivatedBy;

    @Id
    @GeneratedValue(generator = "CallLogFileSeq")
    @SequenceGenerator(name = "CallLogFileSeq", sequenceName = "CALL_LOG_FILE_ID_SEQ", allocationSize = 1)
    @Column(name = "CALL_LOG_FILE_ID")
    public Long getCallLogFileId() {
        return callLogFileId;
    }

    public void setCallLogFileId(Long callLogFileId) {
        this.callLogFileId = callLogFileId;
    }

    @Column(name = "EMPLOYEE_NUMBER")
    public Integer getEmployeeNumber() {
        return employeeNumber;
    }

    public void setEmployeeNumber(Integer employeeNumber) {
        this.employeeNumber = employeeNumber;
    }

    @Column(name = "UUID")
    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @Column(name = "FILE_PATH")
    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    @Column(name = "FILE_TYPE")
    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    @Column(name = "FILE_SOURCE")
    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    @Column(name = "CREATE_DATETIME")
    public Date getCreatedDateTime() {
        return createdDateTime;
    }

    public void setCreatedDateTime(Date createdDateTime) {
        this.createdDateTime = createdDateTime;
    }

    @Column(name = "IS_ACTIVE")
    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    @Column(name = "INACTIVE_DATETIME")
    public Date getInactivatedOn() {
        return inactivatedOn;
    }

    public void setInactivatedOn(Date inactivatedOn) {
        this.inactivatedOn = inactivatedOn;
    }

    @Column(name = "INACTIVE_EMPLOYEE")
    public Integer getInactivatedBy() {
        return inactivatedBy;
    }

    public void setInactivatedBy(Integer inactivatedBy) {
        this.inactivatedBy = inactivatedBy;
    }
}
