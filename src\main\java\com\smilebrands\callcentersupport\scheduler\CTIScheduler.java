package com.smilebrands.callcentersupport.scheduler;

import com.smilebrands.callcentersupport.domain.schedule.Task;
import com.smilebrands.callcentersupport.service.ScheduleService;
import com.smilebrands.callcentersupport.service.ScheduleServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by phongpham on 9/12/15.
 */
@Component
@EnableScheduling
@EnableAsync
public class CTIScheduler {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ScheduleService scheduleService;

    private boolean running = false;
    private boolean webRequestRunning = false;
    private List<Task> taskList;

    @Scheduled(cron = "0 */1 5-20 ? * MON-SAT", zone = "US/Pacific")
    public void executeTasks(){
        if(taskList == null){
            taskList = ScheduleServiceImpl.taskList;
        }
        if(!running){
            running = true;
        }else{
            return;
        }
        for(Task t : taskList) {
            processTask(t);
        }
        running = false;
    }

    List<String> sundayTasks = new ArrayList<String>(Arrays.asList("processWebRequest", "checkLongWebRequest"));

    //Run every minute from 9AM to 7PM (Pacific time) on Sunday
    @Scheduled(cron = "0 */1 9-19 ? * SUN", zone = "US/Pacific")
    public void processWebRequestOnSunday(){
        if(taskList == null){
            taskList = ScheduleServiceImpl.taskList;
        }
        if(!webRequestRunning){
            webRequestRunning = true;
        }else{
            return;
        }
        for(Task t : taskList) {
            if(sundayTasks.indexOf(t.getTaskName()) == -1){
                continue;
            }
            logger.debug("about to process task[{}]", t.getTaskName());
            processTask(t);
        }
        webRequestRunning = false;
    }

    private void processTask(Task t){
        Task task = null;
        try {
            task = scheduleService.claimOpenTask(t.getTaskId());
            if (task != null) {
                if(task.isParallel()) {
                    scheduleService.processTaskAsync(task);
                    Thread.sleep(1000); //wait 1 second before go to other task
                }else{
                    scheduleService.processTask(task);
                }
            } else {
                logger.debug("task[" + t.getTaskName() + "] is not available...");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            if (task != null && !task.isParallel()) {
                scheduleService.releaseTask(task);
            }
        }
    }
}
