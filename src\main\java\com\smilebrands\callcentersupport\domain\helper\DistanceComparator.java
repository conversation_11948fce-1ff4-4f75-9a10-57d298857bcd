package com.smilebrands.callcentersupport.domain.helper;

import com.smilebrands.callcentersupport.domain.Facility;

import java.util.Comparator;

/**
 * User: <PERSON><PERSON>
 * Date: 7/8/14
 */
public class DistanceComparator implements Comparator<Facility> {

    @Override
    public int compare(Facility facility, Facility facility2) {
        if (facility.getDistance().equals(facility2.getDistance())) {
            return 0;
        }

        return facility.getDistance() < facility2.getDistance() ? -1 : 1;
    }
}
