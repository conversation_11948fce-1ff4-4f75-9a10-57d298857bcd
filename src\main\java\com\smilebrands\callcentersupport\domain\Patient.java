package com.smilebrands.callcentersupport.domain;

import com.smilebrands.callcentersupport.domain.helper.DateIsoDeSerializer;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;
import java.text.DateFormat;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * User: <PERSON><PERSON>
 * Date: 6/26/14
 */
@Document
public class Patient implements Serializable {

    private Long patientId;
    private Long tempPatientId;
    private String sourceSystem;
    private String patientFirstName;
    private String patientLastName;
    private String patientGender;
    private Date dateOfBirth;
    private Date patientLinkDateTime;
    private String emailAddress;
    private Date emailCaptureDateTime;
    private Date emailQueueDateTime;
    private boolean emailImmediately;
    private boolean emailCaptured;
    private boolean insuranceWaiting;
    private boolean isOrtho;

    private Integer clinicId;
    private Integer facilityId;
    private Long phoneNumber;
    private Date nextAppointmentDateTime;
    private String nextAppointmentDate;
    private String nextAppointmentTime;
    private Date lastAppointmentDate;
    private Integer sequence;
    private Integer empEntered;
    private String screenType;
    private String createDate;
    private String createTime;
    private String updateDate;
    private String updateTime;
    private Long appointmentCallLogId;

    @Transient
    private Date createDateTime;
    @Transient
    private Date updateDateTime;

    public Long getPatientId() { return patientId; }

    public void setPatientId(Long patientId) {
        this.patientId = patientId;
    }

    public Long getTempPatientId() {
        return tempPatientId;
    }

    public void setTempPatientId(Long tempPatientId) {
        this.tempPatientId = tempPatientId;
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }

    public Date getPatientLinkDateTime() {
        return patientLinkDateTime;
    }

    @JsonDeserialize(using=DateIsoDeSerializer.class)
    public void setPatientLinkDateTime(Date patientLinkDateTime) {
        this.patientLinkDateTime = patientLinkDateTime;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public Date getEmailCaptureDateTime() {
        return emailCaptureDateTime;
    }

    public void setEmailCaptureDateTime(Date emailCaptureDateTime) {
        this.emailCaptureDateTime = emailCaptureDateTime;
    }

    public Date isEmailQueueDateTime() {
        return emailQueueDateTime;
    }

    public void setEmailQueueDateTime(Date emailQueueDateTime) {
        this.emailQueueDateTime = emailQueueDateTime;
    }

    public boolean isEmailImmediately() {
        return emailImmediately;
    }

    public void setEmailImmediately(boolean emailImmediately) {
        this.emailImmediately = emailImmediately;
    }

    public boolean isEmailCaptured() {
        return emailCaptured;
    }

    public void setEmailCaptured(boolean emailCaptured) {
        this.emailCaptured = emailCaptured;
    }

    public boolean isInsuranceWaiting() {
        return insuranceWaiting;
    }

    public void setInsuranceWaiting(boolean insuranceWaiting) {
        this.insuranceWaiting = insuranceWaiting;
    }

    public boolean isOrtho() {
        return isOrtho;
    }

    public void setIsOrtho(boolean isOrtho) {
        this.isOrtho = isOrtho;
    }

    public String getPatientFirstName() {
        return patientFirstName;
    }

    public void setPatientFirstName(String patientFirstName) {
        this.patientFirstName = patientFirstName;
    }

    public String getPatientLastName() {
        return patientLastName;
    }

    public void setPatientLastName(String patientLastName) {
        this.patientLastName = patientLastName;
    }

    public Date getEmailQueueDateTime() {
        return emailQueueDateTime;
    }

    public String getPatientGender() {
        return patientGender;
    }

    public void setPatientGender(String patientGender) {
        this.patientGender = patientGender;
    }

    public Date getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(Date dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public Integer getClinicId() {
        return clinicId;
    }

    public void setClinicId(Integer clinicId) {
        this.clinicId = clinicId;
    }

    public Integer getFacilityId() {
        return facilityId;
    }

    public void setFacilityId(Integer facilityId) {
        this.facilityId = facilityId;
    }

    public Long getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(Long phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public Date getNextAppointmentDateTime() {
        return nextAppointmentDateTime;
    }

    @JsonDeserialize(using = DateIsoDeSerializer.class)
    public void setNextAppointmentDateTime(Date nextAppointmentDateTime) {
        this.nextAppointmentDateTime = nextAppointmentDateTime;
    }

    public String getNextAppointmentDate() {
        return nextAppointmentDate;
    }

    public void setNextAppointmentDate(String nextAppointmentDate) {
        this.nextAppointmentDate = nextAppointmentDate;
    }

    public String getNextAppointmentTime() {
        return nextAppointmentTime;
    }

    public void setNextAppointmentTime(String nextAppointmentTime) {
        this.nextAppointmentTime = nextAppointmentTime;
    }

    public Date getLastAppointmentDate() {
        return lastAppointmentDate;
    }

    public void setLastAppointmentDate(Date lastAppointmentDate) {
        this.lastAppointmentDate = lastAppointmentDate;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Integer getEmpEntered() {
        return empEntered;
    }

    public void setEmpEntered(Integer empEntered) {
        this.empEntered = empEntered;
    }

    public String getScreenType() {
        return screenType;
    }

    public void setScreenType(String screenType) {
        this.screenType = screenType;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(Date createDateTime) {
        this.createDateTime = createDateTime;
    }

    public Date getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(Date updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    public Long getAppointmentCallLogId() {
        return appointmentCallLogId;
    }

    public void setAppointmentCallLogId(Long appointmentCallLogId) {
        this.appointmentCallLogId = appointmentCallLogId;
    }

    public String getPatientFullName(){
        String result = "";
        String[] temp = null;
        if(patientFirstName != null){
            temp = patientFirstName.toLowerCase().split(" ");
            for(int i=0; i<temp.length; i++){
                result += temp[i].substring(0, 1).toUpperCase() + temp[i].substring(1) + " ";
            }
        }
        if(patientLastName != null){
            temp = patientLastName.toLowerCase().split(" ");
            for(int i=0; i<temp.length; i++){
                result += temp[i].substring(0, 1).toUpperCase() + temp[i].substring(1) + " ";
            }
            result = result.trim();
        }
        return result.trim().length() == 0 ? this.patientId + "" : result;
    }

    @XmlElement
    public String getDateOfBirthAsDigit(){
       String result = "";
        if(dateOfBirth != null){
            Format df = new SimpleDateFormat("MMddyyyy");
            result = df.format(dateOfBirth);
        }
        return result;
    }

    @XmlElement(name = "newPatient")
    public boolean isNewPatient(){
        boolean result = true;
        if(this.lastAppointmentDate != null){
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DAY_OF_YEAR, -365);
            result = cal.getTime().after(this.lastAppointmentDate);
        }
        return result;
    }
}

