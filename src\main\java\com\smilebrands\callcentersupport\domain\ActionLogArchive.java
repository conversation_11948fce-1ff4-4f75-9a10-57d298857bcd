package com.smilebrands.callcentersupport.domain;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by phongpham on 8/29/14.
 */
@Document
public class ActionLogArchive {

    @Id
    private String id;
    private String uuid;
    private String actionType = "inboundCall";
    private List<String> logs = new ArrayList<String>();
    private String createDate;

    public ActionLogArchive(){}

    public ActionLogArchive(ActionLog actionLog){
        this.id = actionLog.getId();
        this.uuid = actionLog.getUuid();
        this.actionType = actionLog.getActionType();
        this.logs = actionLog.getLogs();
        this.createDate = actionLog.getCreateDate();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public List<String> getLogs() {
        return logs;
    }

    public void setLogs(List<String> logs) {
        this.logs = logs;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }
}
