package com.smilebrands.callcentersupport;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * User: <PERSON><PERSON>
 * Date: 3/2/15
 */
@ContextConfiguration(locations = {"classpath:spring-mongo.xml", "classpath:test-datasource.xml"})
public class MongoReplicaSetTest extends AbstractJUnit4SpringContextTests {

    @Autowired
    MongoOperations mongoOperations;

    @Test
    public void getConnection() {
        //mongoOperations.createCollection("Sample");
        logger.debug("Sample Collection exists: " + mongoOperations.collectionExists("Sample"));
        com.mongodb.ReadPreference.primaryPreferred();
    }

    @Test
    public void insert() {

        Map m = new HashMap();
        m.put("time", new Date().getTime());
        logger.debug("New record created: " + m.get("time"));
        mongoOperations.insert(m, "Sample");
        logger.debug("New record saved: " + m.get("time"));
    }

    @Test
    public void find()  {
        while (true) {
             try {

                 insert();
                 logger.debug("Sample Collection contains " + mongoOperations.findAll(HashMap.class, "Sample").size() + " entries.");

                 Thread.sleep(1000);
             } catch (Exception x) {
                logger.error(x.getMessage());
             }
        }
    }
}
