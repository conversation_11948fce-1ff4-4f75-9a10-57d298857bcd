package com.smilebrands.callcentersupport.service;

import com.smilebrands.callcentersupport.domain.*;

/**
 * User: <PERSON><PERSON>
 * Date: 11/04/14
 */
public interface WebRequestService {

    public WebRequest saveWebRequest(WebRequest webRequest, boolean submitRequest);

    public WebRequest findWebRequest(String uuid);

    public void updateWebRequestScreenResult(String uuid, Integer agentId, boolean result);

    public WebRequest saveThenSendEmailOrScreenPopWebRequest(WebRequest wr);

    public void sendEmailToNonContactWebRequest(String uuid, Integer employeeNumber);

    public boolean isValidRequest(WebRequest wr);
}
