package com.smilebrands.callcentersupport.domain;

import com.smilebrands.callcentersupport.domain.constants.State;
import com.smilebrands.callcentersupport.util.TranslateUtil;
import com.smilebrands.callcentersupport.util.VelocityUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlTransient;
import java.util.*;

/**
 * User: <PERSON><PERSON>
 * Date: 6/25/14
 */
@Document
public class Facility {

    @Id
    private Integer facilityId;
    private Integer qsiClinicId;
    private String qisEnvironment;
    private String name;
    private String brandName;
    private String address;
    private String city;
    private String state;
    private String zip;
    private String addressDescription;
    private String crossStreets;
    private Long phoneNumber;
    private Long faxNumber;
    private Long transferNumber;
    private Integer distance;
    private Integer travelTime;
    private boolean libertySupported;
    private boolean openBookSchedulingEnabled;
    private boolean psrSupported = true;
    private boolean csrSupported = true;
    private Integer psrPriority = 3;
    private Integer csrPriority = 3;
    private String message;
    private Map<String, ArrayList<Employee>> employees = new HashMap<String, ArrayList<Employee>>();
    private List<Facility> neighborFacilities;

    private List<AttributeValue> specialServices = new ArrayList<AttributeValue>();
    private List<AttributeValue> alerts = new ArrayList<AttributeValue>();

    @Transient
    private String screenPopUrl;

    @Transient
    private int counter;

    @Transient
    private String language;

    @Transient
    private PhoneNumber associatedPhoneNumber;

    private String mondayHour;
    private String tuesdayHour;
    private String wednesdayHour;
    private String thursdayHour;
    private String fridayHour;
    private String saturdayHour;
    private String sundayHour;
    private String hoursNotes;

    private String languagesSpoken;
    private String specialNotes;
    private String doctorOnCallName;
    private String doctorOnCallNumber;
    private String ageLimit;
    private String nitrousOffered;
    private String ivSedation;
    private String acceptedStatePlans;

    public Integer getFacilityId() {
        return facilityId;
    }

    public void setFacilityId(Integer facilityId) {
        this.facilityId = facilityId;
    }

    public Integer getQsiClinicId() {
        return qsiClinicId;
    }

    public void setQsiClinicId(Integer qsiClinicId) {
        this.qsiClinicId = qsiClinicId;
    }

    public String getQisEnvironment() {
        return qisEnvironment;
    }

    public void setQisEnvironment(String qisEnvironment) {
        this.qisEnvironment = qisEnvironment;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getZip() {
        return zip;
    }

    public void setZip(String zip) {
        this.zip = zip;
    }

    public String getAddressDescription() {
        return addressDescription;
    }

    public void setAddressDescription(String addressDescription) {
        this.addressDescription = addressDescription;
    }

    public String getCrossStreets() {
        return crossStreets;
    }

    public void setCrossStreets(String crossStreets) {
        this.crossStreets = crossStreets;
    }

    public Long getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(Long phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public Long getFaxNumber() {
        return faxNumber;
    }

    public void setFaxNumber(Long faxNumber) {
        this.faxNumber = faxNumber;
    }

    public Long getTransferNumber() {
        return transferNumber;
    }

    public void setTransferNumber(Long transferNumber) {
        this.transferNumber = transferNumber;
    }

    public Integer getDistance() {
        return distance;
    }

    public void setDistance(Integer distance) {
        this.distance = distance;
    }

    public Integer getTravelTime() {
        return travelTime;
    }

    public void setTravelTime(Integer travelTime) {
        this.travelTime = travelTime;
    }

    public boolean isLibertySupported() {
        return libertySupported;
    }

    public void setLibertySupported(boolean libertySupported) {
        this.libertySupported = libertySupported;
    }

    @XmlTransient
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @XmlTransient
    public Map<String, ArrayList<Employee>> getEmployees() {
        return employees;
    }

    public void setEmployees(Map<String, ArrayList<Employee>> employees) {
        this.employees = employees;
    }

    @XmlTransient
    public List<Facility> getNeighborFacilities() {
        return neighborFacilities;
    }

    public void setNeighborFacilities(List<Facility> neighborFacilities) {
        this.neighborFacilities = neighborFacilities;
    }

    public List<AttributeValue> getSpecialServices() {
        return specialServices;
    }

    public void setSpecialServices(List<AttributeValue> specialServices) {
        this.specialServices = specialServices;
    }

    public List<AttributeValue> getAlerts() {
        return alerts;
    }

    public void setAlerts(List<AttributeValue> alerts) {
        this.alerts = alerts;
    }

    public String getMondayHour() {
        return mondayHour;
    }

    public void setMondayHour(String mondayHour) {
        this.mondayHour = mondayHour;
    }

    public String getTuesdayHour() {
        return tuesdayHour;
    }

    public void setTuesdayHour(String tuesdayHour) {
        this.tuesdayHour = tuesdayHour;
    }

    public String getWednesdayHour() {
        return wednesdayHour;
    }

    public void setWednesdayHour(String wednesdayHour) {
        this.wednesdayHour = wednesdayHour;
    }

    public String getThursdayHour() {
        return thursdayHour;
    }

    public void setThursdayHour(String thursdayHour) {
        this.thursdayHour = thursdayHour;
    }

    public String getFridayHour() {
        return fridayHour;
    }

    public void setFridayHour(String fridayHour) {
        this.fridayHour = fridayHour;
    }

    public String getSaturdayHour() {
        return saturdayHour;
    }

    public void setSaturdayHour(String saturdayHour) {
        this.saturdayHour = saturdayHour;
    }

    public String getSundayHour() {
        return sundayHour;
    }

    public void setSundayHour(String sundayHour) {
        this.sundayHour = sundayHour;
    }

    public String getHoursNotes() {
        return hoursNotes;
    }

    public void setHoursNotes(String hoursNotes) {
        this.hoursNotes = hoursNotes;
    }

    public String getLanguagesSpoken() {
        return languagesSpoken;
    }

    public void setLanguagesSpoken(String languagesSpoken) {
        this.languagesSpoken = languagesSpoken;
    }

    public String getSpecialNotes() {
        return specialNotes;
    }

    public void setSpecialNotes(String specialNotes) {
        this.specialNotes = specialNotes;
    }

    public String getDoctorOnCallName() {
        return doctorOnCallName;
    }

    public void setDoctorOnCallName(String doctorOnCallName) {
        this.doctorOnCallName = doctorOnCallName;
    }

    public String getDoctorOnCallNumber() {
        return doctorOnCallNumber;
    }

    public void setDoctorOnCallNumber(String doctorOnCallNumber) {
        this.doctorOnCallNumber = doctorOnCallNumber;
    }

    public String getAgeLimit() {
        return ageLimit;
    }

    public void setAgeLimit(String ageLimit) {
        this.ageLimit = ageLimit;
    }

    public String getNitrousOffered() {
        return nitrousOffered;
    }

    public void setNitrousOffered(String nitrousOffered) {
        this.nitrousOffered = nitrousOffered;
    }

    public String getIvSedation() {
        return ivSedation;
    }

    public void setIvSedation(String ivSedation) {
        this.ivSedation = ivSedation;
    }

    public String getAcceptedStatePlans() {
        return acceptedStatePlans;
    }

    public void setAcceptedStatePlans(String acceptedStatePlans) {
        this.acceptedStatePlans = acceptedStatePlans;
    }

    public String toString() {
        String result = "\n" + facilityId + " - " + name + " - Liberty: " + libertySupported + " - ";
        result += "\n\tMONDAY: " + mondayHour;
        result += "\n\tTUESDYA: " + tuesdayHour;
        result += "\n\tWEDNESDAY: " + wednesdayHour;
        result += "\n\tTHURSDAY: " + thursdayHour;
        result += "\n\tFRIDAY: " + fridayHour;
        result += "\n\tSATURDAY: " + saturdayHour;
        result += "\n\tSUNDAY: " + sundayHour;
        result += "\n\tPSR Supported: " + psrSupported;
        result += "\n\tPSR Queue Priority: " + psrPriority;
        result += "\n\tCSR Supported: " + csrSupported;
        result += "\n\tCSR Queue Priority: " + csrPriority;
        result += "\n\tOpen Book Scheduling Supported: " + openBookSchedulingEnabled;

        return result;
    }

    public String getScreenPopUrl() {
        return screenPopUrl;
    }

    public void setScreenPopUrl(String screenPopUrl) {
        this.screenPopUrl = screenPopUrl;
    }

    @XmlElement(name = "phoneNumber")
    public PhoneNumber getAssociatedPhoneNumber() {
        return associatedPhoneNumber;
    }

    public void setAssociatedPhoneNumber(PhoneNumber associatedPhoneNumber) {
        this.associatedPhoneNumber = associatedPhoneNumber;
    }

    public String getFullAddress(){
        String result = address + ".";
        if(city != null){
            result += " " + city;
            if(state != null){
                result += ", " + state;
            }
        }else if(state != null){
            result += " " + state;
        }
        result += " " + zip;

        return result;
    }

    public String getShortAddress(){
        StringBuilder sb = new StringBuilder();
        String addressStr = address + ".";
        addressStr = addressStr.toLowerCase();
        addressStr = addressStr.replace("blvd", "boulevard");
        addressStr = addressStr.replace("bldg", "building");
        addressStr = addressStr.replace(" rd ", " road ");
        addressStr = addressStr.replace(" rd. ", " road ");
        addressStr = addressStr.replace(" dr ", " drive ");
        addressStr = addressStr.replace(" dr. ", " drive ");
        addressStr = addressStr.replace(" ave ", " avenue ");
        addressStr = addressStr.replace(" ave. ", " avenue ");

        boolean isSpanish = this.language != null && this.language.equalsIgnoreCase("sp");


        if(addressStr != null && addressStr.length() > 0){

            String[] tmp = addressStr.split(" ");
            for(int idx = 0; idx < tmp.length ; idx++){
                String s = tmp[idx];
                if(!s.equalsIgnoreCase(".")){
                    s = s.replace(".", "");
                }
                boolean hasOtherLetter = false;
                int bIndex = -1;
                for(int i = 0; i<s.length(); i++){
                    char c = s.charAt(i);
                    if(c != 'b' && c >= 'a' && c <= 'z'){
                        hasOtherLetter = true;
                    }
                    if(c == 'b'){
                        bIndex = i;
                    }
                }
                if(!hasOtherLetter && bIndex != -1 && isSpanish){
                    s = s.replace("b", "beh");
                }else if(s.length() == 1 || s.length() == 2){
                    String tmpDirection = convertSpanishDirection(s.charAt(0), s.length(), isSpanish);
                    if(tmpDirection != null){
                        String direction = tmpDirection;
                        if(s.length() == 2){
                            tmpDirection = convertSpanishDirection(s.charAt(1), s.length(), isSpanish);
                            if(tmpDirection != null){
                                direction += " " + tmpDirection;
                            }
                        }
                        s = direction;
                    }

                }
                sb.append(s + " ");
            }

        }else if(addressStr != null && addressStr.length() > 0){
            sb.append(addressStr);
        }
        if(city != null){
            sb.append(". " + city);
        }

        return sb.toString().toUpperCase();
    }

    private String convertSpanishDirection(char s, int length, boolean isSpanish){
        if(s == 's'){
            return isSpanish ? "sur" : "south";
        }else if(s == 'n' && length == 1){
            return isSpanish ? "norte" : "north";
        }else if(s == 'n' && length > 1){
            return isSpanish ? "nor" : "north";
        }else if(s == 'e'){
            return isSpanish ? "este" : "east";
        }else if(s == 'w'){
            return isSpanish ? "o-este" : "west";
        }else{
            return null;
        }
    }

    @XmlTransient
    public String getWebsite(){
        String result = null;
        String name = this.getBrandName();
        if(name != null){
            String domain = null;
            name = name.toLowerCase();
            if(name.indexOf("bright now") != -1){
                domain = "brightnow";
            }else if(name.indexOf("castle dental") != -1){
                domain = "castledental";
            }else if(name.indexOf("monarch dental") != -1){
                domain = "monarchdental";
            }
            if(domain != null && this.getCity() != null && this.getCity().trim().length() > 0
                    && this.getName() != null && this.getName().trim().length() > 0
                    && this.getFacilityId() != null){
                result = "http://www." + domain + ".com/locations/dental-office/" + this.getCity().trim() + "/" + this.getName().trim() + "/" + this.getFacilityId();
            }
        }
        return result;
    }

    @XmlTransient
    public List<Map<String, String>> getSpecialtySchedule(){
        Map<String, String> map = new HashMap<String, String>();
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        List<Employee> dentists = this.employees != null ? this.employees.get("Dentists") : null;
        if(dentists != null && dentists.size() > 0){
            for(Employee emp : dentists){
                Provider provider = (Provider) emp;
                String specialty = provider.getSpecialty();
                if(provider.getScheduleNotes() != null && provider.getScheduleNotes().trim().length() > 0
                        && specialty != null
                        && !specialty.equalsIgnoreCase("Dental Hygienist")
                        && !specialty.equalsIgnoreCase("Dentist")
                        && !specialty.equalsIgnoreCase("General Practice")){
                    String note = map.get(specialty);
                    if(note == null){
                        note = provider.getScheduleNotes().trim();
                    }else{
                        note += provider.getScheduleNotes().trim();
                        if(provider.getScheduleNotes().charAt(provider.getScheduleNotes().trim().length()-1) != '.'){
                            note += ".";
                        }
                    }
                    map.put(specialty, note);
                }
            }
        }
        for(String key : map.keySet()){
            Map<String, String> m = new HashMap<String, String>();
            m.put("name", VelocityUtils.getAbbreviationForSpecialty(key));
            m.put("note", map.get(key));
            result.add(m);
        }
        return result;
    }

    @XmlElement(name = "ttsFacility")
    public String getTTSFacility(){
        String result = "";
        if(brandName != null){
            result += brandName.replace("!", " ");
        }
        if(addressDescription != null){
            result +=  (result.trim().length() > 0 ? ", " : "") + addressDescription;
        }
        if(address != null){
            result += (result.trim().length() > 0 ? ", " : "") + getFullAddress();
        }
        return result;
    }

    @XmlElement(name = "ttsFacilityShort")
    public String getTTSFacilityShort(){
        String result = this.language != null && this.language.equalsIgnoreCase("sp")
                ? "Oprima numero " + getCounter() + " para conectarse a"
                : "Press " + getCounter() + " to be connected to";
        if(brandName != null){
            result += brandName.replace("!", " ");
        }
        if(addressDescription != null){
            result +=  (result.trim().length() > 0 ? ", " : "") + addressDescription;
        }
        if(address != null){
            result += (result.trim().length() > 0 ? ", " : "") + getShortAddress();
        }
        return result;
    }

//    @XmlElement(name = "ttsFacilityHour")
    public String getTTSFacilityHour(String language, boolean doTranslate){
        String result = "";
        try{
            if(mondayHour != null && mondayHour.trim().length() > 0){
                result += doTranslate
                            ? (TranslateUtil.translateWeekDay(Calendar.MONDAY, language) + " " + convertFacilityHour(mondayHour, language) + " ,,")
                            : ("Monday " + mondayHour + " ,, ");
            }
            if(tuesdayHour != null && tuesdayHour.trim().length() > 0){
                result += doTranslate
                            ? (TranslateUtil.translateWeekDay(Calendar.TUESDAY, language) + " " + convertFacilityHour(tuesdayHour, language) + " ,,")
                            : ("Tuesday " + tuesdayHour + " ,, ");
            }
            if(wednesdayHour != null && wednesdayHour.trim().length() > 0){
                result += doTranslate
                            ? (TranslateUtil.translateWeekDay(Calendar.WEDNESDAY, language) + " " + convertFacilityHour(wednesdayHour, language) + " ,,")
                            : ("Wednesday " + wednesdayHour + " ,, ");
            }
            if(thursdayHour != null && thursdayHour.trim().length() > 0){
                result += doTranslate
                            ? (TranslateUtil.translateWeekDay(Calendar.THURSDAY, language) + " " + convertFacilityHour(thursdayHour, language) + " ,,")
                            : ("Thursday " + thursdayHour + " ,, ");
            }
            if(fridayHour != null && fridayHour.trim().length() > 0){
                result += doTranslate
                            ? (TranslateUtil.translateWeekDay(Calendar.FRIDAY, language) + " " + convertFacilityHour(fridayHour, language) + " ,,")
                            : ("Friday " + fridayHour + " ,, ");
            }
            if(saturdayHour != null && saturdayHour.trim().length() > 0){
                result += doTranslate
                            ? (TranslateUtil.translateWeekDay(Calendar.SATURDAY, language) + " " + convertFacilityHour(saturdayHour, language) + " ,,")
                            : ("Saturday " + saturdayHour + " ,, ");
            }
            if(sundayHour != null && sundayHour.trim().length() > 0){
                result += doTranslate
                            ? (TranslateUtil.translateWeekDay(Calendar.SUNDAY, language) + " " + convertFacilityHour(sundayHour, language))
                            : ("Sunday " + sundayHour);
            }
        }catch (Exception ex){
//            System.out.println("Error on translate facility hours");
            result = this.getTTSFacilityHour(language, false);
        }
        return result;
    }

    //HOUR SHOULD BE IN FORMAT 9:00AM - 6:00PM or CLOSED
    public String convertFacilityHour(String facilityHour, String language){
        String result = facilityHour;
        if(language.equalsIgnoreCase("SP")){
            if(facilityHour.indexOf("-") != -1){
                String[] arr = facilityHour.split("-");
                String start = arr[0];
                String[] startArr = start.split(":");
                Integer startHour = Integer.valueOf(startArr[0].trim());
                Integer startMinute = Integer.valueOf(startArr[1].substring(0, 2));
                String startAmPm = startArr[1].substring(2, 4);
                result = TranslateUtil.translateNumber(startHour, "time", language);
                result += " " + TranslateUtil.translateNumber(startMinute, "count", language);
                result += " " + startAmPm;
                if(arr.length == 2){
                    String end = arr[1];
                    String[] endArr = end.split(":");
                    Integer endHour = Integer.valueOf(endArr[0].trim());
                    Integer endMinute = Integer.valueOf(endArr[1].substring(0, 2));
                    String endAmPm = endArr[1].substring(2, 4);
                    result += " - " + TranslateUtil.translateNumber(endHour, "time", language);
                    result += " " + TranslateUtil.translateNumber(endMinute, "count", language);
                    result += " " + endAmPm;
                }

            }else{
                result = TranslateUtil.translatePhrase(facilityHour, language).toUpperCase();
            }
        }
        return result;
    }

    @Transient
    public int getTimeOffset(){
        int result = -1;
        if(this.state != null && this.state.trim().length() == 2){
            try{
                State st = State.valueOf(this.state.toUpperCase());
                if(st != null){
                    result = st.getOffset();
                }
            }catch (Exception ex){

            }
        }
        if(result == -1){
            Calendar cal = Calendar.getInstance();
            TimeZone timeZone = cal.getTimeZone();
            result = timeZone.getRawOffset() / (1000 * 60 * 60);
        }
        return result;
    }

    public int getCounter() {
        return counter;
    }

    public void setCounter(int counter) {
        this.counter = counter;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public boolean isPsrSupported() {
        return psrSupported;
    }

    public void setPsrSupported(boolean psrSupported) {
        this.psrSupported = psrSupported;
    }

    public boolean isCsrSupported() {
        return csrSupported;
    }

    public void setCsrSupported(boolean csrSupported) {
        this.csrSupported = csrSupported;
    }

    public Integer getPsrPriority() {
        return psrPriority;
    }

    public void setPsrPriority(Integer psrPriority) {
        this.psrPriority = psrPriority;
    }

    public Integer getCsrPriority() {
        return csrPriority;
    }

    public void setCsrPriority(Integer csrPriority) {
        this.csrPriority = csrPriority;
    }

    public boolean isOpenBookSchedulingEnabled() {
        return openBookSchedulingEnabled;
    }

    public void setOpenBookSchedulingEnabled(boolean openBookSchedulingEnabled) {
        this.openBookSchedulingEnabled = openBookSchedulingEnabled;
    }
}
