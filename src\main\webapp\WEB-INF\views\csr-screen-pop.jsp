<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>
<%@ taglib uri="../customTag.tld" prefix="custom" %>
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="${pageContext.request.contextPath}/favicon.ico">

    <title>${languageText} Call for ${brandName}</title>

    <!-- Bootstrap core CSS -->
        <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/bootstrap/css/bootstrap.min.css" >
        <link type="text/css" rel="stylesheet" href="${pageContext.request.contextPath}/style/redmond/jquery-ui-1.8.20.custom.css" />

        <!-- Custom styles for this template -->
        <link href="${pageContext.request.contextPath}/bootstrap/css/call-template.css?_id=20180220" rel="stylesheet">

  </head>

  <body>

        <input type="text" style="display:none;" id="uuid" value="${uniqueCallId}"/>
        <input type="text" style="display:none;" id="employeeNumber" value="${employeeNumber}"/>
        <input type="text" style="display:none;" id="language" value="${language}"/>
        <input type="text" style="display:none;" id="numberPress" value="${numberPress}"/>
        <input type="text" style="display:none;" id="ani" value="${ani}"/>
        <input type="text" style="display:none;" id="screenType" value="csr"/>
        <input type="text" style="display:none;" id="targetFacilityId" value="${facility.facilityId}"/>
        <input type="text" style="display:none;" id="targetFacilityName" value="${facility.name}"/>
        <input type="text" style="display:none;" id="reasonCode" value="${reasonCode}"/>
        <input type="text" style="display:none;" id="resolutionCode" value="${resolutionCode}"/>
        <input type="text" style="display:none;" id="zipCode" value="${zipCode}"/>
        <input type="text" style="display:none;" id="mapImgUrl" value="${pageContext.request.contextPath}/images/google-map-logo2.png"/>
        <input type="text" style="display:none;" id="isOld" value="${isOld}"/>
        <input type="text" style="display:none;" id="isTF" value="${isTF}"/>

        <div class="popover fade bottom provider-detail" role="tooltip" style="display: block;">
            <div class="arrow"></div>
            <div class="popover-content">
                <div class="row">
                    <div class="col-md-2 gray bold" style="text-align: right;">Provider</div>
                    <div class="col-md-10 action fsp-id"></div>
                </div>
                <div class="row" style="padding-left: 20px;">
                    <b class="col-md-12 action fsp-schedule-note"></b>
                </div>
                <div class="row">
                    <div class="col-md-2 gray bold" style="text-align: right;">Gender</div>
                    <div class="col-md-10 action fsp-gender"></div>
                </div>
                <div class="row">
                    <div class="col-md-2 gray bold" style="text-align: right;">Language</div>
                    <div class="col-md-10 action fsp-language"></div>
                </div>
                <div class="row">
                    <div class="col-md-2 gray bold" style="text-align: right;">Education</div>
                    <div class="col-md-10 action fsp-education"></div>
                </div>
            </div>
        </div>

        <jsp:include page="components/smsPopWindow.jsp"/>
        <jsp:include page="components/openBookCsrScheduling.jsp"/>

        <div class="modal fade" id="csrFacilityDetail" tabindex="-1" role="dialog" aria-labelledby="csrFacilityDetailModalLabel" aria-hidden="true">
            <div class="modal-dialog" style="width: 80%;">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                        <h4 class="modal-title" id="csrFacilityDetailModalLabel">Facility Detail</h4>
                    </div>
                    <div class="modal-body" style="padding-bottom:0px;">
                        <div class="progress loading-facility-detail">
                            <div class="progress-bar progress-bar-striped active"  role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%">
                                <span class="progress-message">Please wait while loading facility detail</span>
                            </div>
                        </div>
                        <div class="row facility-detail">
                            <div class="alert alert-danger csr-facility-detail" dataField="immediateAlert" role="alert" style="margin: 0px 15px 5px 15px;">
                            </div>
                            <div class="alert alert-warning csr-facility-detail" dataField="generalAlert" role="alert" style="margin: 0px 15px 10px 15px;">
                            </div>
                            <div class="col-md-6">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                      <h3 class="panel-title gray">Facility Details
                                        <a target="_blank" href="https://www.brightnow.com/dental-office/${fn:replace(fn:toLowerCase(facility.city), ' ', '-')}-dentist/${facility.facilityId}" data-toggle="tooltip" data-original-title="View Web Page" class="fr" data-placement="right">
                                            <img class="map-link" style="margin-top: -5px; width: 28px;" src="${pageContext.request.contextPath}/images/earth.png">
                                        </a>
                                        <a id="frontOfficeEmailLink" target="_blank" href="mailto:${facility.facilityId}-<EMAIL>" data-toggle="tooltip" data-original-title="Email Front Office" class="fr" data-placement="right" style="margin-right: 10px;">
                                            <img class="map-link" style="margin-top: -8px; padding-left: 10px; width: 35px;" src="${pageContext.request.contextPath}/images/email_template.png">
                                        </a>
                                        <a id="employerSearchLink" target="_blank" href="https://cti.smilebrands.com/employerPlanSearch" data-toggle="tooltip" data-original-title="Search Employee Plans" class="fr" data-placement="right" style="margin-right: 2px;">
                                            <img class="ins-search-link" style="margin-top: -5px; width: 27px;" src="${pageContext.request.contextPath}/images/insurance-search-2.png">
                                        </a>
                                        <a target="_blank" dataField="feedback" data-toggle="tooltip" data-original-title="Capture Patient Feedback" class="fr csr-facility-detail" data-placement="right" style="margin-right: 2px;">
                                            <img class="ins-search-link" style="margin-top: -5px; margin-right: 5px; width: 25px;" src="${pageContext.request.contextPath}/images/file_upload.png">
                                            <!-- href="https://smilebrands.service-now.com/sp?id=sc_cat_item&sys_id=d03015451bda3b00f0f48661cd4bcb4b&sysparm_category=eae0d5811bda3b00f0f48661cd4bcbf5" -->
                                        </a>
                                        <a target="_blank" dataField="treatments" data-toggle="tooltip" data-toggle="tooltip" data-original-title="Launch Open Book Treatment Estimator" class="fr csr-facility-detail" data-placement="right" style="margin-right: 2px;">
                                            <img class="ins-search-link" style="margin-top: -5px; margin-right: 5px; width: 25px;" src="${pageContext.request.contextPath}/images/calculator.png">
                                            <!-- href="https://openbook.smilebrands.com/treatments/viewer/?siteId=${facility.facilityId}" -->
                                        </a>
                                        <a id="openBookSchedulingLink"  style="display: none;" target="_blank" href="" data-toggle="tooltip" data-original-title="Launch Open Book Scheduler" class="fr" data-placement="right" style="margin-right: 10px;">
                                            <span class="ob-btn" style="margin-right: 4px;"></span>
                                        </a>

                                      </h3>
                                    </div>
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Facility ID</div>
                                            <div class="col-md-8 csr-facility-detail" dataField="facilityId">10230</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Facility Name</div>
                                            <div class="col-md-8 blue action csr-facility-detail" dataField="facilityName">Bright Now! Dental - Temecula Parkway
                                                <a target="_blank" href="http://www.brightnow.com/locations/dental-office/office/office/10230/map">
                                                <img class="map-link" src="${pageContext.request.contextPath}/images/google-map-logo2.png"></a>
                                            </div>

                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">QSI ID</div>
                                            <div class="col-md-8 csr-facility-detail" dataField="qsiClinicId">90</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Address</div>
                                            <div class="col-md-8 csr-facility-detail" dataField="address">30571 Temecula Parkway Suite D</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold"></div>
                                            <div class="col-md-8 csr-facility-detail" dataField="cityStateZip">Temecula, CA 92592</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Directions</div>
                                            <div class="col-md-8 csr-facility-detail" dataField="addressDescription">Creekside Plaza</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Cross Streets</div>
                                            <div class="col-md-8 csr-facility-detail" dataField="crossStreets">Pechanga Parkway & Temecula Parkway</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Notes</div>
                                            <div class="col-md-8 csr-facility-detail" dataField="specialNotes">facility.specialNotes</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Languages</div>
                                            <div class="col-md-8 csr-facility-detail" dataField="languagesSpoken">facility.languagesSpoken</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">On Call Doctor</div>
                                            <div class="col-md-8 csr-facility-detail" dataField="doctorOnCall">facility.doctorOnCallName - <custom:fmtPhone phone="1234567890"/></div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Accepted State Plans</div>
                                            <div class="col-md-8 csr-facility-detail" dataField="acceptedStatePlans">facility.acceptedStatePlans</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Age Limit</div>
                                            <div class="col-md-8 csr-facility-detail" dataField="ageLimit">facility.ageLimit</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">Nitrous Offered</div>
                                            <div class="col-md-8 csr-facility-detail" dataField="nitrousOffered">facility.nitrousOffered</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">IV Sedation</div>
                                            <div class="col-md-8 csr-facility-detail" dataField="ivSedation">facility.ivSedation</div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 gray bold">
                                                Special Services
                                                <span class="glyphicon glyphicon-tag pointer padding-left-10 facility-special-services csr-facility-detail" data-toggle="popover" data-html=true data-container=".facility-special-services"
                                                    data-content="${specialServiceTxt}"  dataField="specialtyServices">
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                      <h3 class="panel-title gray">Office Numbers & Hours</h3>
                                    </div>
                                    <div class="panel-body">
                                        <div class="col-md-4" style="padding-left: 0px; padding-right: 0px; text-align: right; width: 40%;">
                                            <div class="row">
                                                <div class="col-md-3 gray bold text-align-left">Phone</div>
                                                <div class="col-md-8 csr-facility-detail" dataField="phoneNumber"><custom:fmtPhone phone="9493315916"/></div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-3 gray bold text-align-left">Transfer</div>
                                                <div class="col-md-8 csr-facility-detail" dataField="transferNumber"><custom:fmtPhone phone="9493315916"/></div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-3 gray bold text-align-left">Fax</div>
                                                <div class="col-md-8 csr-facility-detail" dataField="faxNumber"><custom:fmtPhone phone="9492326583"/></div>
                                            </div>
                                        </div>
                                        <div class="col-md-8" style="width: 60%; padding-right: 0px; border-left: 1px solid #ccc;">
                                            <div class="row">
                                                <div class="col-md-4 gray bold">Monday</div>
                                                <div class="col-md-8 csr-facility-detail" dataField="mondayHour">facility.mondayHour</div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-4 gray bold">Tuesday</div>
                                                <div class="col-md-8 csr-facility-detail" dataField="tuesdayHour">facility.tuesdayHour</div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-4 gray bold">Wednesday</div>
                                                <div class="col-md-8 csr-facility-detail" dataField="wednesdayHour">facility.wednesdayHour</div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-4 gray bold">Thursday</div>
                                                <div class="col-md-8 csr-facility-detail" dataField="thursdayHour">facility.thursdayHour</div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-4 gray bold">Friday</div>
                                                <div class="col-md-8 csr-facility-detail" dataField="fridayHour">facility.fridayHour</div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-4 gray bold">Saturday</div>
                                                <div class="col-md-8 csr-facility-detail" dataField="saturdayHour">facility.saturdayHour</div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-4 gray bold">Sunday</div>
                                                <div class="col-md-8 csr-facility-detail" dataField="sundayHour">facility.sundayHour</div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-4 gray bold" style="text-decoration: underline;">*Notes:</div>
                                                <div class="col-md-8 csr-facility-detail" dataField="hoursNote">facility.hoursNotes</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row facility-detail">
                            <div class="col-md-6">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                      <h3 class="panel-title gray">Management</h3>
                                    </div>
                                    <div class="panel-body csr-facility-detail" dataField="managementHierarchy">

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                      <h3 class="panel-title gray">Providers
                                        <a href="javascript:void(0);" class="view-provider-insurance fr" style="color: #257DB0">View Credentialing</a>
                                      </h3>
                                    </div>
                                    <div class="panel-body csr-facility-detail" dataField="providersInfo">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="panel panel-default facility-detail">
                            <div class="panel-heading">
                              <h3 class="panel-title gray">Messages</h3>
                            </div>
                            <div class="panel-body">
                                <p class="csr-facility-detail" dataField="message">facility.message</p>
                            </div>
                        </div>
                        <div class="panel panel-default facility-detail">
                            <div class="panel-heading">
                              <h3 class="panel-title gray">Neighboring Facilities</h3>
                            </div>
                            <div class="panel-body">
                                <div class="row csr-facility-detail" dataField="neighboringOffices">
                                    <div class="col-md-6">
                                    </div>
                                    <div class="col-md-6">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="providerInsuranceView" tabindex="-1" role="dialog" aria-labelledby="providerInsuranceLabel" aria-hidden="true">
            <div class="modal-dialog" style="width: 80%;">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                        <h4 class="modal-title" id="providerInsuranceLabel">Provider Insurances for ${facility.name}</h4>
                        <div class="row filter-cmp" style="display: none; padding-top: 10px;">
                            <div class="col-md-1 col-sm-1" style="width: 80px; padding-right: 0px;">Search By:</div>
                            <div class="col-md-3 col-sm-3">
                                <select class="filter-criteria" style="width:100%;">
                                    <option value="payer">Payer</option>
                                    <option value="provider">Provider</option>
                                </select>
                            </div>
                            <div class="col-md-5 col-sm-5">
                                <input type="text" class="filter-value" style="width:90%;"/>
                                <span class="glyphicon glyphicon-remove pointer remove-filter-insurance"></span>
                            </div>
                        </div>
                    </div>
                    <div class="modal-body" style="overflow-y: scroll; max-height: 500px;">
                        <div class="progress loading-insurances">
                            <div class="progress-bar progress-bar-striped active"  role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%">
                                <span class="progress-message">Please wait while loading accepted insurances</span>
                            </div>
                        </div>
                        <div class="insurance-list">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <select id="facilityIDs">
                <c:forEach var="facilityId" items="${facilityList}">
                    <option value="${facilityId}"> ${facilityId}</option>
                </c:forEach>
            </select>
            <div class="panel panel-${langButton}">
                <div class="panel-heading">
                    <h3 class="panel-title">This is a ${languageText} Call
                        <a id="sms-btn" href="javascript:void(0);" data-toggle="tooltip" data-original-title="Send SMS" class="fr" data-placement="right" style="margin-left: 7px;">
                            <span class="glyphicon glyphicon-comment"></span>
                        </a>
                        <a id="obs-btn" href="javascript:void(0);" data-toggle="tooltip" data-original-title="Launch Open Book Scheduling" class="fr" data-placement="right">
                            <span class="ob-btn"></span>
                        </a>
                    </h3>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-7">
                            <h2 class="blue top">${brandName}
                                <small>${extraBrandNameInfo}</small>
                                <c:if test="${isCampaign}">
                                    <small>
                                        (${campaignName})
                                    </small>
                                </c:if>
                            </h2>
                            <div class="alert alert-success" role="alert">
                                <div class="row">
                                    <div class="col-md-3"><strong>Menu Selection - ${numberPress}</strong></div>
                                    <div class="col-md-9">${numberPressText}</div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3"><strong>Number Dialed - ${phoneType}</strong></div>
                                    <div class="col-md-8">${phoneTypeText}.</div>
                                </div>
                                <c:if test="${zipCode != null}">
                                    <div class="row">
                                        <div class="col-md-3"><strong>Zip Code Entered </strong></div>
                                        <div class="col-md-8">${zipCode}.</div>
                                    </div>
                                </c:if>
                                <jsp:include page="patientInfo.jsp"/>
                            </div>
                        </div>
                    </div>

                    <div id="facilitySearchByZipCode" class="panel panel-default">
                        <div class="panel-heading">
                          <h3 class="panel-title gray">Call Action</h3>
                        </div>
                        <div class="panel-body">
                            <form class="form-horizontal call-detail-form" role="form">
                                <div class="form-group">
                                    <label class="col-md-3 control-label">Select Menu Option</label>
                                    <div class="col-md-7">
                                        <select id="csrMenuSelection" class="form-control input-md">
                                            <option value="1">Press 1: The Caller would like to schedule a new appointment.</option>
                                            <option value="2">Press 2: The Caller would like to update an existing appointment.</option>
                                            <option value="3">Press 3: The Caller would like to confirm an existing appointment, find office information.</option>
                                            <option value="4">Press 4: The Caller has questions about billing, insurance coverage or payment information.</option>
                                            <option value="5">Press 5: The Caller is not patient or has other inquiries.</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="reasonInput" class="col-md-3 control-label">Reason for Call</label>
                                    <div class="col-md-7">
                                        <select id="reasonInput" class="form-control input-md">
                                            <c:forEach var="reason" items="${reasonList}">
                                                <option value="${reason.code}"> ${reason.description}</option>
                                            </c:forEach>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="resolutionInput" class="col-md-3 control-label">Call Resolution</label>
                                    <div class="col-sm-7">
                                        <select id="resolutionInput" class="form-control input-md">
                                            <c:forEach var="resolutionCode" items="${resolutionCodeList}">
                                                <option value="${resolutionCode.code}"> ${resolutionCode.description}</option>
                                            </c:forEach>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">Search Office by Zip Code</label>
                                    <div class="col-md-3">
                                        <input id="fsbzInput" type="text" class="form-control" placeholder="5-digit zip code">
                                    </div>
                                    <c:if test="${facility != null}">
                                        <div class="col-md-7 col-md-offset-3" style="padding-top:5px;">
                                            <span class="control-label selected-office-info">Selected Facility: ${facility.name} (${facility.facilityId})</span>
                                            <span class="glyphicon glyphicon-ok csr-ok-to-transfer" style="padding-left:20px; color:#008000;" data-toggle="tooltip" title="Ready to transfer."></span>
                                            <span class="glyphicon glyphicon-share csr-facility-detail-link pointer" style="padding-left:20px;" data-toggle="tooltip" title="Show detail." facilityId="${facility.facilityId}"></span>
                                        </div>
                                    </c:if>
                                </div>
                                <div class="form-group">
                                    <div class="col-md-7 col-md-offset-3">
                                        <div id="display_list" class="list-group" display-position="bottom">

                                        </div>
                                    </div>
                                </div>
                            </form>
                            <hr/>
                            <div class="panel panel-default">
                                <div class="panel-heading" style="font-weight:bold; font-size:15px;">
                                    <span>Patient Information<span id="addPatientInfoBtn" style="cursor:pointer;" class="glyphicon glyphicon-plus fr" data-toggle="tooltip" data-placement="left" title="Add Patient"></span></span>
                                </div>
                                <div id="patientInfoBody" class="panel-body" style="max-height:400px; overflow:auto;">

                                </div>
                            </div>
                        </div>
                        <div class="panel-footer">
                            <button id="updateCallInfoBtn" type="button" class="btn btn-primary">Update Call Info</button>
                            <div id="csrUpdateCallInfoAlert" class="modal" tabindex="-1" role="dialog" aria-hidden="true" style="top:300px;">
                                <div class="modal-dialog modal-sm">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                                            <h4 class="modal-title">Message</h4>
                                        </div>
                                        <div class="modal-body" style="padding-bottom:0px;">
                                        </div>
                                        <div class="modal-footer">
                                            <button id="csrUpdateCallInfoAlert_NoBtn" type="button" class="btn btn-default btn-sm">No</button>
                                            <button id="csrUpdateCallInfoAlert_YesBtn" type="button" class="btn btn-primary btn-sm">Yes</button>
                                        </div>
                                    </div>
                                </div>
                            </div
                        </div>
                    </div>
                </div>
            </div>
        </div><!-- /.container -->
    <!-- Bootstrap core JavaScript
    ================================================== -->
    <!-- Placed at the end of the document so the pages load faster -->
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-1.7.2.min.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/jquery-ui-1.8.20.custom.min.js"></script>
    <script src="${pageContext.request.contextPath}/bootstrap/js/bootstrap.js"></script>

    <script type="text/javascript" src="${pageContext.request.contextPath}/script/screenPopActions.js?_id=20190918"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/script/sbi-script.js?_id=20190918"></script>
  </body>
</html>