package com.smilebrands.callcentersupport.service;

import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailServiceClient;
import com.amazonaws.services.simpleemail.model.*;
import com.smilebrands.callcentersupport.domain.mandrill.SendRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by phongpham on 6/28/16.
 */
@Service
public class AmazonSesServiceImpl implements AmazonSesService {
    final static Logger logger = LoggerFactory.getLogger(AmazonSesService.class);

    private static final String ACCESS_KEY_ID = "********************";
    private static final String SECRET_ACCESS_KEY = "6m1giG4f4k5y9bdK8/o66yphQrzFisWILg+njn+C";

    AmazonSimpleEmailServiceClient sesClient = null;

    public List<LinkedHashMap> sendNotificationEmail(SendRequest sendRequest) {
        String fromStr = sendRequest.getMessage().getFromEmail();
        String toStr = sendRequest.getMessage().getTo().get(0).getEmail();
        String subjectStr = sendRequest.getMessage().getSubject();
        String htmlBodyStr = sendRequest.getMessage().getHtml();
        String textBodyStr = sendRequest.getMessage().getText();
        return sendNotificationEmail(fromStr, toStr, subjectStr, htmlBodyStr, textBodyStr);
    }

    public List<LinkedHashMap> sendNotificationEmail(String fromStr, String toStr, String subjectStr, String htmlBodyStr, String textBodyStr) {
        List<LinkedHashMap> sendResponses = new ArrayList<LinkedHashMap>();
        Destination destination = new Destination().withToAddresses(toStr);

        Content subject = new Content().withData(subjectStr);
        Content htmlBody = new Content().withData(htmlBodyStr);
        Content textBody = new Content().withData(textBodyStr);
        Body body = new Body().withHtml(htmlBody).withText(textBody);

        Message message = new Message().withSubject(subject).withBody(body);
        SendEmailRequest request = new SendEmailRequest().withSource(fromStr).withDestination(destination).withMessage(message);

        try {
            if (sesClient == null) {
                logger.debug("Initializing SES Client...");
                sesClient = new AmazonSimpleEmailServiceClient(new BasicAWSCredentials(ACCESS_KEY_ID, SECRET_ACCESS_KEY));
                sesClient.setRegion(Region.getRegion(Regions.US_EAST_1));
            }
            SendEmailResult sendEmailResult = sesClient.sendEmail(request);
            LinkedHashMap<String, String> response = new LinkedHashMap<String, String>();
            response.put("status", "sent");
            response.put("_id", sendEmailResult.getMessageId());
            sendResponses.add(response);
            logger.debug(String.format("sendNotificationEmail: SES email successfully sent to [%s]", toStr));
        } catch (Exception e) {
            logger.error(String.format("Error sending SES email to [%s]: %s", toStr, e.getMessage()));
            LinkedHashMap<String, String> response = new LinkedHashMap<String, String>();
            response.put("status", "rejected");
            String rejectReason = e.getMessage() == null ? "<none>" : e.getMessage();
            if (rejectReason.length() > 50) rejectReason = rejectReason.substring(0, 50);
            response.put("reject_reason", rejectReason);
            sendResponses.add(response);
        }
        return sendResponses;
    }
}
