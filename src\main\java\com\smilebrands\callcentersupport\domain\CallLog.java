package com.smilebrands.callcentersupport.domain;

import com.smilebrands.callcentersupport.util.VelocityUtils;

import javax.persistence.*;
import java.util.Date;

/**
 * User: <PERSON><PERSON>
 * Date: 8/13/14
 */
@Entity
@Table(name = "AGENT_CALL_LOG")
public class CallLog implements Cloneable {

    private Long callLogId;
    private String uuid;
    private String sessionId;
    private String callCenterName;
    private Integer employeeNumber;
    private String agentCallCenterName;
    private String queueName;
    @Transient
    private String employeeName;
    private Integer directSupervisor;
    private Integer supervisor;
    private Integer facilityId;
    private Long tempPatientId;
    private Long patientId;
    private Long qsiUniqueId;
    private Date callDateTime;
    private Double callDuration;
    private String entrySource;
    private boolean emailCaptured;
    private boolean emailVerified;
    private boolean insuranceWaiting;
    private String agentType;
    private String phoneType;
    private String reasonCode;
    private String resolutionCode;
    private String logStatus = "ACTIVE";
    private Long appointmentCallLogId;
    private Integer facilityViewCount;
    private boolean isLiberty;
    private boolean isOpenBook;
    private Integer callLogUpdated = 0;
    private Integer createEmployeeNumber;
    private Date createDateTime;
    private Integer updateEmployeeNumber;
    private Date updateDateTime;
    private Boolean allowCredit = false;

    private boolean isEditable = false;

    private String partnerId;
    private String partnerSessionId;
    private Long partnerStartDateTime;
    private String partnerTimezone;
    private Integer partnerRingTime;
    private Integer partnerTalkTime;
    private Integer partnerHoldTime;
    private Integer partnerWorkTime;
    private Long partnerCalledNumber;
    private Integer partnerTeamId;
    private Integer partnerMetServiceLevel;

    private boolean adminOverridden = false;

    public CallLog copy() throws CloneNotSupportedException {
        return (CallLog) this.clone();
    }

    @Id
    @GeneratedValue(generator = "CallLogSeq")
    @SequenceGenerator(name = "CallLogSeq", sequenceName = "AGENT_CALL_LOG_ID_SEQ", allocationSize = 1)
    @Column(name = "CALL_LOG_ID")
    public Long getCallLogId() {
        return callLogId;
    }

    public void setCallLogId(Long callLogId) {
        this.callLogId = callLogId;
    }

    @Column(name = "UUID")
    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @Column(name = "SESSION_ID")
    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    @Column(name = "CALL_CENTER")
    public String getCallCenterName() {
        return callCenterName;
    }

    public void setCallCenterName(String callCenterName) {
        this.callCenterName = callCenterName;
    }

    @Column(name = "EMPLOYEE_NUMBER")
    public Integer getEmployeeNumber() {
        return employeeNumber;
    }

    public void setEmployeeNumber(Integer employeeNumber) {
        this.employeeNumber = employeeNumber;
    }

    @Column(name = "AGENT_CALL_CENTER")
    public String getAgentCallCenterName() {
        return agentCallCenterName;
    }

    public void setAgentCallCenterName(String agentCallCenterName) {
        this.agentCallCenterName = agentCallCenterName;
    }

    @Column(name = "QUEUE_NAME")
    public String getQueueName() {
        return queueName;
    }

    public void setQueueName(String queueName) {
        this.queueName = queueName;
    }

    @Transient
    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    @Column(name = "DIRECT_SUPERVISOR")
    public Integer getDirectSupervisor() {
        return directSupervisor;
    }

    public void setDirectSupervisor(Integer directSupervisor) {
        this.directSupervisor = directSupervisor;
    }

    @Column(name = "SUPERVISOR")
    public Integer getSupervisor() {
        return supervisor;
    }

    public void setSupervisor(Integer supervisor) {
        this.supervisor = supervisor;
    }

    @Column(name = "FACILITY_ID")
    public Integer getFacilityId() {
        return facilityId;
    }

    public void setFacilityId(Integer facilityId) {
        this.facilityId = facilityId;
    }

    @Column(name = "TEMP_PATIENT_ID")
    public Long getTempPatientId() {
        return tempPatientId;
    }

    public void setTempPatientId(Long tempPatientId) {
        this.tempPatientId = tempPatientId;
    }

    @Column(name = "PATIENT_ID")
    public Long getPatientId() {
        return patientId;
    }

    public void setPatientId(Long patientId) {
        this.patientId = patientId;
    }

    @Column(name = "UNIQUE_ID")
    public Long getQsiUniqueId() {
        return qsiUniqueId;
    }

    public void setQsiUniqueId(Long qsiUniqueId) {
        this.qsiUniqueId = qsiUniqueId;
    }

    @Column(name = "CALL_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCallDateTime() {
        return callDateTime;
    }

    public void setCallDateTime(Date callDateTime) {
        this.callDateTime = callDateTime;
    }

    @Column(name = "CALL_DURATION")
    public Double getCallDuration() {
        return callDuration;
    }

    public void setCallDuration(Double callDuration) {
        this.callDuration = callDuration;
    }

    @Column(name = "ENTRY_SOURCE")
    public String getEntrySource() {
        return entrySource;
    }

    public void setEntrySource(String entrySource) {
        this.entrySource = entrySource;
    }

    @Column(name = "EMAIL_CAPTURED")
    public boolean isEmailCaptured() {
        return emailCaptured;
    }

    public void setEmailCaptured(boolean emailCaptured) {
        this.emailCaptured = emailCaptured;
    }

    @Column(name = "WAITING_ON_INSURANCE")
    public boolean isInsuranceWaiting() {
        return insuranceWaiting;
    }

    public void setInsuranceWaiting(boolean insuranceWaiting) {
        this.insuranceWaiting = insuranceWaiting;
    }

    @Column(name = "EMAIL_VERIFIED")
    public boolean isEmailVerified() {
        return emailVerified;
    }

    public void setEmailVerified(boolean emailVerified) {
        this.emailVerified = emailVerified;
    }

    @Column(name = "AGENT_TYPE")
    public String getAgentType() {
        return agentType;
    }

    public void setAgentType(String agentType) {
        this.agentType = agentType;
    }

    @Column(name = "PHONE_TYPE")
    public String getPhoneType() {
        return phoneType;
    }

    public void setPhoneType(String phoneType) {
        this.phoneType = phoneType;
    }

    @Column(name = "REASON_CODE")
    public String getReasonCode() {
        return reasonCode;
    }

    public void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }

    @Column(name = "RESOLUTION_CODE")
    public String getResolutionCode() {
        return resolutionCode;
    }

    public void setResolutionCode(String resolutionCode) {
        this.resolutionCode = resolutionCode;
    }

    @Column(name = "CALL_LOG_STATUS")
    public String getLogStatus() {
        return logStatus;
    }

    public void setLogStatus(String logStatus) {
        this.logStatus = logStatus;
    }

    @Column(name = "LAST_APPT_LOG_ID")
    public Long getAppointmentCallLogId() {
        return appointmentCallLogId;
    }

    public void setAppointmentCallLogId(Long appointmentCallLogId) {
        this.appointmentCallLogId = appointmentCallLogId;
    }

    @Column(name = "FACILITY_VIEW_COUNT")
    public Integer getFacilityViewCount() {
        return facilityViewCount;
    }

    public void setFacilityViewCount(Integer facilityViewCount) {
        this.facilityViewCount = facilityViewCount;
    }

    @Column(name = "IS_LIBERTY")
    public boolean isLiberty() {
        return isLiberty;
    }

    public void setLiberty(boolean isLiberty) {
        this.isLiberty = isLiberty;
    }

    @Column(name = "IS_OPEN_BOOK")
    public boolean isOpenBook() {
        return isOpenBook;
    }

    public void setOpenBook(boolean isOpenBook) {
        this.isOpenBook = isOpenBook;
    }

    @Column(name = "CALL_LOG_UPDATED")
    public Integer getCallLogUpdated() {
        return callLogUpdated;
    }

    public void setCallLogUpdated(Integer callLogUpdated) {
        this.callLogUpdated = callLogUpdated;
    }

    @Column(name = "CREATE_EMPLOYEE")
    public Integer getCreateEmployeeNumber() {
        return createEmployeeNumber;
    }

    public void setCreateEmployeeNumber(Integer createEmployeeNumber) {
        this.createEmployeeNumber = createEmployeeNumber;
    }

    @Column(name = "CREATE_DATETIME")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(Date createDateTime) {
        this.createDateTime = createDateTime;
    }

    @Column(name = "UPDATE_EMPLOYEE")
    public Integer getUpdateEmployeeNumber() {
        return updateEmployeeNumber;
    }

    public void setUpdateEmployeeNumber(Integer updateEmployeeNumber) {
        this.updateEmployeeNumber = updateEmployeeNumber;
    }

    @Column(name = "UPDATE_TIMESTAMP")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(Date updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    @Column(name = "ALLOW_CREDIT")
    public Boolean getAllowCredit() {
        return allowCredit;
    }

    public void setAllowCredit(Boolean allowCredit) {
        this.allowCredit = allowCredit;
    }

    @Transient
    public String getCallTime(){
        return VelocityUtils.getDateAsString(this.callDateTime, "HH:mm:ss");
    }

    @Transient
    public boolean isEditable() {
        return isEditable;
    }

    public void setEditable(boolean isEditable) {
        this.isEditable = isEditable;
    }

    @Column(name = "PARTNER_ID")
    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    @Column(name = "PARTNER_SESSION_ID")
    public String getPartnerSessionId() {
        return partnerSessionId;
    }

    public void setPartnerSessionId(String partnerSessionId) {
        this.partnerSessionId = partnerSessionId;
    }

    @Column(name = "PARTNER_START_DATE_TIME")
    public Long getPartnerStartDateTime() {
        return partnerStartDateTime;
    }

    public void setPartnerStartDateTime(Long partnerStartDateTime) {
        this.partnerStartDateTime = partnerStartDateTime;
    }

    @Column(name = "PARTNER_TIMEZONE")
    public String getPartnerTimezone() {
        return partnerTimezone;
    }

    public void setPartnerTimezone(String partnerTimezone) {
        this.partnerTimezone = partnerTimezone;
    }

    @Column(name = "PARTNER_RING_TIME")
    public Integer getPartnerRingTime() {
        return partnerRingTime;
    }

    public void setPartnerRingTime(Integer partnerRingTime) {
        this.partnerRingTime = partnerRingTime;
    }

    @Column(name = "PARTNER_TALK_TIME")
    public Integer getPartnerTalkTime() {
        return partnerTalkTime;
    }

    public void setPartnerTalkTime(Integer partnerTalkTime) {
        this.partnerTalkTime = partnerTalkTime;
    }

    @Column(name = "PARTNER_HOLD_TIME")
    public Integer getPartnerHoldTime() {
        return partnerHoldTime;
    }

    public void setPartnerHoldTime(Integer partnerHoldTime) {
        this.partnerHoldTime = partnerHoldTime;
    }

    @Column(name = "PARTNER_WORK_TIME")
    public Integer getPartnerWorkTime() {
        return partnerWorkTime;
    }

    public void setPartnerWorkTime(Integer partnerWorkTime) {
        this.partnerWorkTime = partnerWorkTime;
    }

    @Column(name = "PARTNER_CALLED_NUMBER")
    public Long getPartnerCalledNumber() {
        return partnerCalledNumber;
    }

    public void setPartnerCalledNumber(Long partnerCalledNumber) {
        this.partnerCalledNumber = partnerCalledNumber;
    }

    @Column(name = "PARTNER_TEAM_ID")
    public Integer getPartnerTeamId() {
        return partnerTeamId;
    }

    public void setPartnerTeamId(Integer partnerTeamId) {
        this.partnerTeamId = partnerTeamId;
    }

    @Column(name = "PARTNER_MET_SERVICE_LEVEL")
    public Integer getPartnerMetServiceLevel() {
        return partnerMetServiceLevel;
    }

    public void setPartnerMetServiceLevel(Integer partnerMetServiceLevel) {
        this.partnerMetServiceLevel = partnerMetServiceLevel;
    }

    @Column(name = "ADMIN_OVERRIDDEN")
    public boolean isAdminOverridden() {
        return adminOverridden;
    }

    public void setAdminOverridden(boolean adminOverridden) {
        this.adminOverridden = adminOverridden;
    }
}
